import React, { useEffect, useState } from 'react';

import { CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { defaultIdleLearningConfig, defaultRpcLearningConfig } from '@iso/lms/types/organization.type';
import { Form, Typography } from 'antd';
import { useRouter } from 'next/router';
import { bool, func } from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';

import ConfirmModal from '@components/atoms/confirmModal';
import DetailCard from '@components/molecules/cards/detailCard';
import CreateOrganizationGeneralForm from '@components/molecules/forms/createOrganizationGeneralForm';
import CreateOrganizationObjectiveForm from '@components/molecules/forms/createOrganizationObjectiveForm';
import FullscreenStepsDrawer from '@components/organisms/drawers/fullscreenStepsDrawer';
import { CreateOrganizationStepEnum } from '@constants/organization';
import messageActions from '@redux/message/actions';
import organizationAction from '@redux/organization/actions';
import { organizationStore } from '@redux/organization/state';

const { createOrganizationAction, checkDuplicatedDomainAction } = organizationAction;
const { set_message } = messageActions;

const CreateOrganizationDrawer = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [formGeneralStep] = Form.useForm();
  const [formObjectiveStep] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(CreateOrganizationStepEnum.GENERAL);
  const [isOpenCreateModal, setIsOpenCreateModal] = useState(false);
  const [isOpenDiscardModal, setIsOpenDiscardModal] = useState(false);

  const { checkDuplicatedDomain, createOrganization } = useSelector((state) => ({
    checkDuplicatedDomain: state.Organization.get(organizationStore.checkDuplicatedDomain),
    createOrganization: state.Organization.get(organizationStore.createOrganization),
  }));

  const {
    data,
    isSuccess: isSuccessCreateOrganization,
    isLoading: isLoadingCreateOrganization,
    isError: isErrorCreateOrganization,
  } = createOrganization;

  const splitApplicantType = (applicantType) => {
    const [applicant, licenseType] = applicantType.split(':');
    return { applicantType: applicant, licenseType };
  };

  const mapApplicantType = (objective) => {
    if (objective.regulator === RegulatorEnum.OIC) {
      return objective.list.flatMap((item) =>
        item.applicantType.map((type) => {
          const { applicantType, licenseType } = splitApplicantType(type);
          return {
            trainingCenterName: objective.trainingCenterName,
            trainingCenterCode: objective.trainingCenterCode,
            licenseRenewal: item.licenseRenewal,
            applicantType,
            licenseType,
          };
        }),
      );
    } else if (objective.regulator === RegulatorEnum.TFAC) {
      return objective.list.flatMap((item) =>
        item.applicantType.map((type) => {
          const { applicantType, licenseType } = splitApplicantType(type);
          return {
            trainingCenterName: objective.trainingCenterName,
            trainingCenterCode: objective.trainingCenterCode,
            applicantType,
            licenseType,
          };
        }),
      );
    } else if (objective.regulator === RegulatorEnum.TSI) {
      return objective.list.map((item) => ({
        applicantType: item.applicantType,
        trainingCenterName: objective.trainingCenterName,
        trainingCenterCode: objective.trainingCenterCode,
      }));
    }

    // Training none regulator
    return [
      {
        trainingCenterName: objective.trainingCenterName,
        trainingCenterCode: objective.trainingCenterCode,
      },
    ];
  };

  const onClickSubmitCreateModal = () => {
    const formGeneralValues = formGeneralStep.getFieldsValue();
    const formObjectiveValues = formObjectiveStep.getFieldsValue();

    const formValues = { ...formGeneralValues, ...formObjectiveValues };

    const payload = {
      domain: formValues.domain,
      nameEng: formValues.nameEng,
      name: formValues.name,
      objectives: [],
    };

    // Process objectives
    for (const objective of formValues.objectives) {
      let existingCourseObjective = payload.objectives.find(
        (obj) => obj.courseObjective === objective.objectiveType && obj.regulator === objective.regulator,
      );

      if (!existingCourseObjective) {
        existingCourseObjective = {
          courseObjective: objective.objectiveType,
          regulator: objective.regulator,
          trainingCenterConfigs: [],
          list: [],
        };
        payload.objectives.push(existingCourseObjective);
      }

      const { idle, isIdentityVerificationEnabled } = objective.learningConfig ?? {};

      if (!idle?.isEnabled && !isIdentityVerificationEnabled) {
        objective.learningConfig = {
          idle: defaultIdleLearningConfig,
          rpc: defaultRpcLearningConfig,
          isIdentityVerificationEnabled: false,
          isLivenessEnabled: false,
        };
      } else if (!idle?.isEnabled && isIdentityVerificationEnabled) {
        objective.learningConfig = {
          ...objective.learningConfig,
          idle: defaultIdleLearningConfig,
        };
      } else if (idle?.isEnabled && !isIdentityVerificationEnabled) {
        objective.learningConfig = {
          ...objective.learningConfig,
          rpc: defaultRpcLearningConfig,
          isIdentityVerificationEnabled: false,
          isLivenessEnabled: false,
        };
      }

      if (objective.objectiveType === CourseObjectiveTypeEnum.TRAINING) {
        const newList = mapApplicantType(objective);

        existingCourseObjective.list.push(...newList);
        existingCourseObjective.trainingCenterConfigs.push({
          trainingCenterCode: objective.trainingCenterCode,
          learningConfig: objective.learningConfig,
        });
      } else {
        // CourseObjectiveTypeEnum.REGULAR
        existingCourseObjective.learningConfig = objective.learningConfig;
      }
    }

    dispatch(createOrganizationAction.request(payload));
  };

  const onValidateNextStep = async (step) => {
    if (step === CreateOrganizationStepEnum.GENERAL) {
      formGeneralStep
        .validateFields()
        .then((_res) => {
          const { domain } = _res;
          const payload = { domain };
          dispatch(checkDuplicatedDomainAction.request(payload));
        })
        .catch((_err) => false);
      return false;
    }

    if (step === CreateOrganizationStepEnum.OBJECTIVE) {
      return formObjectiveStep
        .validateFields()
        .then((_res) => true)
        .catch((_err) => false);
    }
  };

  const onValidateBackStep = async (step) => {
    if (step) {
      return true;
    }
    return false;
  };

  const onClickBackStep = async () => {
    const backStep = currentStep - 1;
    const isValidBackStep = (await onValidateBackStep?.(currentStep)) ?? true;
    if (!isValidBackStep) return;

    setCurrentStep(backStep);
  };

  const onClickNextStep = async () => {
    const nextStep = currentStep + 1;
    const isValidNextStep = (await onValidateNextStep?.(currentStep)) ?? true;
    if (!isValidNextStep) return;

    setCurrentStep(nextStep);
  };

  const onClickSubmit = async () => {
    const isValidNextStep = (await onValidateNextStep?.(currentStep)) ?? true;
    if (!isValidNextStep) return;

    setIsOpenCreateModal(true);
  };

  const onClickClose = () => {
    setIsOpenDiscardModal(true);
  };

  const onReset = () => {
    setIsOpenDiscardModal(false);
    formGeneralStep.resetFields();
    formObjectiveStep.resetFields();
    setCurrentStep(CreateOrganizationStepEnum.GENERAL);
  };

  useEffect(() => {
    if (!checkDuplicatedDomain?.data) return;

    dispatch(checkDuplicatedDomainAction.reset());
    if (checkDuplicatedDomain.data?.isDuplicated) {
      formGeneralStep.setFields([{ name: ['domain'], errors: ['ชื่อโดเมนย่อยนี้มีอยู่แล้วในระบบ กรุณากรอกใหม่'] }]);
    } else {
      setCurrentStep(CreateOrganizationStepEnum.OBJECTIVE);
    }
  }, [checkDuplicatedDomain?.data?.isDuplicated]);

  useEffect(() => {
    if (!isSuccessCreateOrganization) return;
    dispatch(set_message('success', 'สร้างบริษัทสำเร็จ'));
    onClose();
    router.push(`/organizations/${data.id}`);
    dispatch(createOrganizationAction.reset());
  }, [isSuccessCreateOrganization]);

  useEffect(() => {
    if (!isErrorCreateOrganization) return;
    dispatch(messageActions.set_message('error', 'เกิดข้อผิดพลาด โปรดลองใหม่อีกครั้ง'));
    setIsOpenCreateModal(false);
  }, [isErrorCreateOrganization]);

  return (
    <>
      <FullscreenStepsDrawer
        title={<Typography.Title level={4}>สร้างบริษัท</Typography.Title>}
        isOpen={isOpen}
        isLoadingSubmitButton={isLoadingCreateOrganization}
        onClose={onClickClose}
        onClickSubmit={onClickSubmit}
        onClickBackStep={onClickBackStep}
        onClickNextStep={onClickNextStep}
        currentStep={currentStep}
      >
        <DetailCard title="ข้อมูลทั่วไป">
          <CreateOrganizationGeneralForm form={formGeneralStep} />
        </DetailCard>
        <DetailCard title="วัตถุประสงค์การเรียน">
          <CreateOrganizationObjectiveForm form={formObjectiveStep} />
        </DetailCard>
      </FullscreenStepsDrawer>

      <ConfirmModal
        title="ยืนยันการบันทึกข้อมูลบริษัท"
        open={isOpenCreateModal}
        content={<Typography.Text>ต้องการยืนยันการบันทึกเพื่อสร้างข้อมูลบริษัทหรือไม่</Typography.Text>}
        okText="ตกลง"
        cancelText="ยกเลิก"
        onCancel={() => setIsOpenCreateModal(false)}
        onOk={onClickSubmitCreateModal}
      />

      <ConfirmModal
        title="ต้องการปิดการสร้างข้อมูลทั้งหมดหรือไม่"
        open={isOpenDiscardModal}
        content={<Typography.Text>เมื่อดำเนินการละทิ้งแล้วไม่สามารถนำกลับมาได้</Typography.Text>}
        okText="ตกลง"
        cancelText="ยกเลิก"
        onCancel={() => setIsOpenDiscardModal(false)}
        onOk={() => {
          onReset();
          onClose();
        }}
      />
    </>
  );
};

CreateOrganizationDrawer.propTypes = {
  isOpen: bool,
  onClose: func,
};

export default CreateOrganizationDrawer;
