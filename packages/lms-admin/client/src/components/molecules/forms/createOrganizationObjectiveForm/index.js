import React from 'react';

import FontAwesomeIcon from '@iso/components/fontAwesomeIcon';
import { fasXmarkLarge, fasPlus } from '@iso/components/fontAwesomeIcon/solid';
import {
  CourseObjectiveTypeEnum,
  RegulatorEnum,
  LicenseRenewalEnum,
  ApplicantTypeEnum,
  ApplicantTypeTHEnum,
  LicenseTypeEnum,
  LicenseTypeTHEnum,
} from '@iso/lms/enums/course.enum';
import { defaultRegularLearningConfig, defaultTrainingLearningConfig } from '@iso/lms/types/organization.type';
import { Button, Card, Col, Form, Input, Row, Select, Space, Switch, Typography } from 'antd';
import { get, isNil } from 'lodash';
import { object, string } from 'prop-types';

import LicenseRenewalFormItem from '@components/atoms/licenseRenewalFormItem';
import { ContentTooltip, CustomLabel } from '@components/molecules/customLabel';
import { defaultLicenseRenewal } from '@constants/course';
import { OrganizationFormFieldNameEnum } from '@constants/organization';
import { learningConfigValidator } from '@helpers/formItemValidator';

const maximumCharacter = 200;

const regulatorMasterConfigs = [
  {
    objectiveType: CourseObjectiveTypeEnum.REGULAR,
    objectiveTypeName: 'การเรียนทั่วไป',
    trainingCenterCode: false,
    regulator: '',
    licenseRenewal: false,
    learningConfig: defaultRegularLearningConfig,
  },
  {
    objectiveType: CourseObjectiveTypeEnum.TRAINING,
    objectiveTypeName: 'การอบรม',
    trainingCenterCode: true,
    regulator: RegulatorEnum.TSI,
    licenseRenewal: {
      [LicenseRenewalEnum.NONE]: {
        name: null,
        applicantType: {
          [ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER]: {
            name: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
            licenseType: {
              INVESTMENT: LicenseTypeEnum.INVESTMENT,
            },
          },
        },
      },
    },
    learningConfig: defaultTrainingLearningConfig,
  },
  {
    objectiveType: CourseObjectiveTypeEnum.TRAINING,
    objectiveTypeName: 'การอบรม',
    trainingCenterCode: true,
    regulator: RegulatorEnum.OIC,
    licenseRenewal: defaultLicenseRenewal,
    learningConfig: defaultTrainingLearningConfig,
  },
  {
    objectiveType: CourseObjectiveTypeEnum.TRAINING,
    objectiveTypeName: 'การอบรม',
    trainingCenterCode: true,
    regulator: RegulatorEnum.TFAC,
    licenseRenewal: {
      [LicenseRenewalEnum.NONE]: {
        name: null,
        applicantType: {
          [ApplicantTypeEnum.ACCOUNTANT]: {
            name: ApplicantTypeTHEnum.ACCOUNTANT,
            licenseType: {
              CPA: { name: LicenseTypeTHEnum.CPA },
              RA: { name: LicenseTypeTHEnum.RA },
            },
          },
        },
      },
    },
    learningConfig: defaultTrainingLearningConfig,
  },
];

const ObjectiveTypeFormItem = ({ fieldName }) => {
  const form = Form.useFormInstance();

  const onChangeObjectiveType = (_val) => {
    form.resetFields([
      [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.TrainingCenterName],
    ]);
    form.resetFields([
      [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.TrainingCenterCode],
    ]);
    form.resetFields([
      [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.Regulator],
    ]);
    form.resetFields([
      [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.LicenseRenewalGroup],
    ]);
    form.resetFields([
      [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.LearningConfig],
    ]);

    const currentObjective = form.getFieldValue([
      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
      fieldName,
      OrganizationFormFieldNameEnum.ObjectiveType,
    ]);
    const objective = regulatorMasterConfigs.find((item) => item.objectiveType === currentObjective);
    const { learningConfig } = objective;
    form.setFieldValue(
      [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.LearningConfig],
      learningConfig,
    );
  };

  const objectiveTypeOptions = [];
  for (const mapItem of regulatorMasterConfigs) {
    const exisObjectiveType = objectiveTypeOptions.find((item) => item.value === mapItem.objectiveType);
    if (!exisObjectiveType) {
      objectiveTypeOptions.push({
        value: mapItem.objectiveType,
        label: mapItem.objectiveTypeName,
      });
    }
  }

  return (
    <Form.Item
      label="วัตถุประสงค์การเรียน"
      className="mb-0"
      name={[fieldName, OrganizationFormFieldNameEnum.ObjectiveType]}
      rules={[
        { required: true, message: 'กรุณาเลือกวัตถุประสงค์การเรียน' },
        () => ({
          validator(_, value) {
            if (!value) return Promise.resolve();

            for (const [index, allValue] of form
              .getFieldValue([OrganizationFormFieldNameEnum.ObjectiveTypeGroup])
              .entries()) {
              if (fieldName === index) continue;

              const thisObjectiveType = form.getFieldValue([
                OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                fieldName,
                OrganizationFormFieldNameEnum.ObjectiveType,
              ]);

              if (allValue?.objectiveType === thisObjectiveType && value === CourseObjectiveTypeEnum.REGULAR) {
                return Promise.reject(new Error('วัตถุประสงค์การเรียนเนื้อหาทั่วไปซ้ำ'));
              }
            }

            return Promise.resolve();
          },
        }),
      ]}
    >
      <Select
        placeholder="กรุณาเลือกวัตถุประสงค์การเรียน"
        options={objectiveTypeOptions}
        onChange={(val) => onChangeObjectiveType(val)}
      />
    </Form.Item>
  );
};

ObjectiveTypeFormItem.propTypes = {
  fieldName: string,
};

const TrainingCenterFormItem = ({ fieldName }) => {
  const form = Form.useFormInstance();

  const regulatorOptions = [];
  const regularTrainings = regulatorMasterConfigs.filter(
    (val) => val.objectiveType === CourseObjectiveTypeEnum.TRAINING,
  );
  for (const mapItem of regularTrainings) {
    regulatorOptions.push({
      value: mapItem.regulator,
      label: mapItem.regulator,
    });
  }

  const onChangeRegulator = (_val) => {
    //form validate name and code
    form.getFieldValue([
      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
      fieldName,
      OrganizationFormFieldNameEnum.TrainingCenterName,
    ]) &&
      form.validateFields([
        [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.TrainingCenterName],
      ]);
    form.getFieldValue([
      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
      fieldName,
      OrganizationFormFieldNameEnum.TrainingCenterCode,
    ]) &&
      form.validateFields([
        [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.TrainingCenterCode],
      ]);

    form.resetFields([
      [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, fieldName, OrganizationFormFieldNameEnum.LicenseRenewalGroup],
    ]);
  };

  return (
    <Row gutter={[16, 16]}>
      <Col sm={12} xs={24}>
        <Form.Item
          label="ชื่อศูนย์อบรม"
          className="mb-0"
          name={[fieldName, OrganizationFormFieldNameEnum.TrainingCenterName]}
          rules={[
            { required: true, message: 'กรุณากรอกชื่อศูนย์อบรม' },
            () => ({
              validator(_, value) {
                if (!value) return Promise.resolve();
                if (value && value.length > maximumCharacter) {
                  return Promise.reject(new Error('ชื่อศูนย์อบรม ควรมีความยาวไม่เกิน 200 ตัวอักษร'));
                }

                for (const [index, allValue] of form
                  .getFieldValue([OrganizationFormFieldNameEnum.ObjectiveTypeGroup])
                  .entries()) {
                  if (fieldName === index) continue;

                  const thisObjectiveType = form.getFieldValue([
                    OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                    fieldName,
                    OrganizationFormFieldNameEnum.ObjectiveType,
                  ]);

                  const thisregulator = form.getFieldValue([
                    OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                    fieldName,
                    OrganizationFormFieldNameEnum.Regulator,
                  ]);

                  if (
                    allValue?.objectiveType === thisObjectiveType &&
                    allValue?.regulator === thisregulator &&
                    allValue?.trainingCenterName === value
                  ) {
                    return Promise.reject(new Error('ชื่อศูนย์อบรมนี้ซ้ำ กรุณากรอกใหม่'));
                  }
                }

                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input placeholder="กรุณากรอกชื่อศูนย์อบรม" />
        </Form.Item>
      </Col>

      <Col sm={12} xs={24}>
        <Form.Item
          label="รหัสศูนย์อบรม"
          className="mb-0"
          name={[fieldName, OrganizationFormFieldNameEnum.TrainingCenterCode]}
          rules={[
            { required: true, message: 'กรุณากรอกรหัสศูนย์อบรม' },
            () => ({
              validator(_, value) {
                if (!value) return Promise.resolve();
                if (!/^([A-Z0-9_]{1,20})$/.test(value)) {
                  return Promise.reject(new Error('กรุณากรอก A-Z หรือ 0-9 หรือ _ เท่านั้น'));
                }

                for (const [index, allValue] of form
                  .getFieldValue([OrganizationFormFieldNameEnum.ObjectiveTypeGroup])
                  .entries()) {
                  if (fieldName === index) continue;

                  const thisObjectiveType = form.getFieldValue([
                    OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                    fieldName,
                    OrganizationFormFieldNameEnum.ObjectiveType,
                  ]);

                  const thisregulator = form.getFieldValue([
                    OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                    fieldName,
                    OrganizationFormFieldNameEnum.Regulator,
                  ]);

                  if (
                    allValue?.objectiveType === thisObjectiveType &&
                    allValue?.regulator === thisregulator &&
                    allValue?.trainingCenterCode === value
                  ) {
                    return Promise.reject(new Error('รหัสศูนย์อบรมนี้ซ้ำ กรุณากรอกใหม่'));
                  }
                }

                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input placeholder="กรุณากรอกรหัสศูนย์อบรม" maxLength={20} />
        </Form.Item>
      </Col>

      <Col sm={12} xs={24}>
        <Form.Item
          label="หน่วยงานกำกับดูแล"
          className="mb-0"
          name={[fieldName, OrganizationFormFieldNameEnum.Regulator]}
        >
          <Select
            placeholder="กรุณาเลือกหน่วยงานกำกับดูแล"
            options={regulatorOptions}
            onChange={(val) => onChangeRegulator(val)}
            allowClear
          />
        </Form.Item>
      </Col>
    </Row>
  );
};

TrainingCenterFormItem.propTypes = {
  fieldName: string,
};

const LearningConfigFormItem = ({ objectiveType, fieldName }) => {
  const form = Form.useFormInstance();

  const onChangeIsIdentityVerification = (val) => {
    if (!val) {
      form.setFieldValue(
        [
          OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
          fieldName,
          OrganizationFormFieldNameEnum.LearningConfig,
          OrganizationFormFieldNameEnum.RPC,
          'isEnabled',
        ],
        false,
      );
      form.setFieldValue(
        [
          OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
          fieldName,
          OrganizationFormFieldNameEnum.LearningConfig,
          'isLivenessEnabled',
        ],
        false,
      );
    }
  };

  if (objectiveType) {
    const objective = regulatorMasterConfigs.find((item) => item.objectiveType === objectiveType);

    const defaultLearningConfig = objective.learningConfig;
    const learningConfigFormValue =
      form.getFieldValue([
        OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
        fieldName,
        OrganizationFormFieldNameEnum.LearningConfig,
      ]) || [];

    return (
      <>
        <Row gutter={[0, 24]}>
          <Form.Item
            name={[
              fieldName,
              OrganizationFormFieldNameEnum.LearningConfig,
              OrganizationFormFieldNameEnum.Idle,
              'isEnabled',
            ]}
            label={
              <CustomLabel
                text="การยืนยันความสนใจระหว่างเรียน"
                tooltipText="การยืนยันความสนใจระหว่างเรียน"
                contentTooltip={
                  <ContentTooltip
                    textList={[
                      'ระยะเวลาที่ไม่ตอบสนองต่อบทเรียน(นาที)เมื่อผู้เรียนไม่ได้มีการตอบสนองต่อบทเรียน เกินกว่าเวลาที่กำหนด ระบบจะแสดงการแจ้งเตือนเพื่อให้ผู้เรียนยืนยนัความสนใจระหว่างเรียน',
                    ]}
                  />
                }
              />
            }
          >
            <Switch defaultChecked={defaultLearningConfig?.idle.isEnabled} />
          </Form.Item>
        </Row>
        <Row gutter={[16, 16]}>
          {(!isNil(learningConfigFormValue.idle?.isEnabled)
            ? learningConfigFormValue.idle?.isEnabled
            : defaultLearningConfig.idle?.isEnabled) && (
            <>
              <Col span={12}>
                <Form.Item
                  name={[
                    fieldName,
                    OrganizationFormFieldNameEnum.LearningConfig,
                    OrganizationFormFieldNameEnum.Idle,
                    'timeoutMinute',
                  ]}
                  label={
                    <CustomLabel
                      text="IDLE_TIME_OUT_MINUTES"
                      tooltipText="IDLE_TIME_OUT_MINUTES"
                      contentTooltip={
                        <ContentTooltip
                          textList={[
                            'ระยะเวลาที่ไม่ตอบสนองต่อบทเรียน (นาที) เมื่อผู้เรียนไม่ได้มีการตอบสนองต่อบทเรียน เกินกว่าเวลาที่กำหนด ระบบจะแสดงการแจ้งเตือนเพื่อให้ผู้เรียนยืนยันความสนใจระหว่างเรียน',
                          ]}
                        />
                      }
                    />
                  }
                  rules={learningConfigValidator({
                    required: 'กรุณากรอก IDLE_TIME_OUT_MINUTES',
                    invalid: 'กรุณากรอก IDLE_TIME_OUT_MINUTES เป็นจำนวนเต็ม',
                  })}
                >
                  <Input initialValue={defaultLearningConfig.idle.timeoutMinute} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={[
                    fieldName,
                    OrganizationFormFieldNameEnum.LearningConfig,
                    OrganizationFormFieldNameEnum.Idle,
                    'stayTimeoutMinute',
                  ]}
                  label={
                    <CustomLabel
                      text="STAY_TIME_OUT_MINUTES"
                      tooltipText="STAY_TIME_OUT_MINUTES"
                      contentTooltip={
                        <ContentTooltip
                          textList={[
                            'ระยะเวลาในการยืนยันความสนใจระหว่างเรียน (นาที) เมื่อแสดงการแจ้งเตือนเพื่อยืนยันความสนใจ หากไม่มีการยืนยันเกินกว่าเวลาที่กำหนด ผู้เรียนจะถูกบังคับให้ออกจากระบบ แต่หากยืนยันสำเร็จภายในเวลาที่กำหนด ผู้เรียนจะกลับสู่บทเรียนตามเดิม',
                          ]}
                        />
                      }
                    />
                  }
                  className="mb-0"
                  rules={learningConfigValidator({
                    required: 'กรุณากรอก STAY_TIME_OUT_MINUTES',
                    invalid: 'กรุณากรอก STAY_TIME_OUT_MINUTES เป็นจำนวนเต็ม',
                  })}
                >
                  <Input initialValue={defaultLearningConfig.idle.stayTimeoutMinute} />
                </Form.Item>
              </Col>
            </>
          )}
          {objectiveType !== CourseObjectiveTypeEnum.REGULAR && (
            <>
              <Col span={24}>
                <Form.Item
                  name={[fieldName, OrganizationFormFieldNameEnum.LearningConfig, 'isIdentityVerificationEnabled']}
                  label={
                    <CustomLabel
                      text="การยืนยันตัวตน"
                      tooltipText="การยืนยันตัวตน"
                      contentTooltip={
                        <ContentTooltip
                          textList={[
                            'การยืนยันตัวตนของผู้เรียน โดยการถ่ายรูปบัตรประชาชนและใบหน้าในการเข้าเรียน และสุ่มถ่ายใบหน้าเพื่อยืนยันตัวตนระหว่างการเรียน ',
                          ]}
                        />
                      }
                    />
                  }
                  className="mb-0"
                >
                  <Switch
                    defaultChecked={defaultLearningConfig?.isIdentityVerificationEnabled}
                    onChange={(val) => onChangeIsIdentityVerification(val)}
                  />
                </Form.Item>
              </Col>
              {(!isNil(learningConfigFormValue?.isIdentityVerificationEnabled)
                ? learningConfigFormValue?.isIdentityVerificationEnabled
                : defaultLearningConfig.isIdentityVerificationEnabled) && (
                <>
                  <Col span={22} offset={2}>
                    <Form.Item
                      name={[
                        fieldName,
                        OrganizationFormFieldNameEnum.LearningConfig,
                        OrganizationFormFieldNameEnum.RPC,
                        'isEnabled',
                      ]}
                      label={
                        <CustomLabel
                          text="สุ่มถ่ายใบหน้ายืนยันตัวระหว่างเรียน"
                          tooltipText="สุ่มถ่ายใบหน้ายืนยันตัวระหว่างเรียน"
                          contentTooltip={
                            <ContentTooltip
                              textList={[
                                'ในระหว่างการเรียน จะมีสุ่มเวลาแสดงหน้าต่างให้ผู้เรียนทำการยืนยันตัวตนโดยการถ่ายรูปใบหน้า',
                              ]}
                            />
                          }
                        />
                      }
                      className="mb-0"
                    >
                      <Switch defaultChecked={defaultLearningConfig?.rpc.isEnabled} />
                    </Form.Item>
                  </Col>
                  <Col span={11} offset={2}>
                    <Form.Item
                      name={[
                        fieldName,
                        OrganizationFormFieldNameEnum.LearningConfig,
                        OrganizationFormFieldNameEnum.RPC,
                        'durationMinute',
                      ]}
                      label={
                        <CustomLabel
                          text="RPC_MINUTES"
                          tooltipText="RPC_MINUTES"
                          contentTooltip={
                            <ContentTooltip
                              textList={[
                                'รอบช่วงเวลาที่แสดงการยืนยันตัวตนของผู้เรียนระหว่างเรียน (นาที) การกำหนดรอบช่วงเวลาที่ต้องการให้ผู้เรียนยืนยันตัวตน โดยจะสุ่มแสดงเพียงหนึ่งครั้งในแต่ละรอบ ภายในความยาวของบทเรียนนั้นสุ่มถ่ายใบหน้ายืนยันตัวตนระหว่างเรียน',
                              ]}
                            />
                          }
                        />
                      }
                      className="mb-0"
                      rules={learningConfigValidator({
                        required: 'กรุณากรอก RPC_MINUTES',
                        invalid: 'กรุณากรอก RPC_MINUTES เป็นจำนวนเต็ม',
                      })}
                    >
                      <Input initialValue={defaultLearningConfig?.rpc.durationMinute} />
                    </Form.Item>
                  </Col>
                  <Col span={11}>
                    <Form.Item
                      name={[fieldName, 'learningConfig', OrganizationFormFieldNameEnum.RPC, 'delayMinute']}
                      className="mb-0"
                      label={
                        <CustomLabel
                          text="RPC_DELAY_MINUTES"
                          tooltipText="RPC_DELAY_MINUTES"
                          contentTooltip={
                            <ContentTooltip
                              textList={[
                                'ระยะเวลาที่จะไม่เกิดการสุ่มแสดงการยืนยันตัวตนระหว่างเรียน (นาที) เมื่อเริ่มต้นเข้าสู่ช่วงเวลายืนยันตัวตนในแต่ละรอบ จะมีช่วงหน่วงเวลาที่จะไม่เกิดการสุ่มแสดงการยืนยันตัตน เพื่อลดโอกาสแสดงการยืนยันตัวตนในเวลาที่ติดกันเกินไปเมื่อเริ่มรอบถัดไป',
                              ]}
                            />
                          }
                        />
                      }
                      rules={learningConfigValidator({
                        required: 'กรุณากรอก RPC_DELAY_MINUTES',
                        invalid: 'กรุณากรอก RPC_DELAY_MINUTES เป็นจำนวนเต็ม',
                      })}
                    >
                      <Input initialValue={defaultLearningConfig?.rpc.delayMinute} />
                    </Form.Item>
                  </Col>
                  <Col span={11} offset={2}>
                    <Form.Item
                      name={[
                        fieldName,
                        OrganizationFormFieldNameEnum.LearningConfig,
                        OrganizationFormFieldNameEnum.RPC,
                        'stayTimeoutMinute',
                      ]}
                      label={
                        <CustomLabel
                          text="RPC_STAY_TIME_OUT_MINUTES"
                          tooltipText="RPC_STAY_TIME_OUT_MINUTES"
                          contentTooltip={
                            <ContentTooltip
                              textList={[
                                'ระยะเวลาในการยืนยันตัวตนระหว่างเรียน (นาที) เมื่อแสดงหน้าต่างยืนยันตัวตน หากไม่มีการยืนยันเกินกว่าเวลาที่กำหนด ผู้เรียนจะถูกบังคับให้ออกจากระบบ แต่หากยืนยันสำเร็จภายในเวลาที่กำหนด ผู้เรียนจะกลับสู่บทเรียนตามเดิม',
                              ]}
                            />
                          }
                        />
                      }
                      rules={learningConfigValidator({
                        required: 'กรุณากรอก RPC_STAY_TIME_OUT_MINUTES',
                        invalid: 'กรุณากรอก RPC_STAY_TIME_OUT_MINUTES เป็นจำนวนเต็ม',
                      })}
                    >
                      <Input initialValue={defaultLearningConfig?.rpc.stayTimeoutMinute} />
                    </Form.Item>
                  </Col>

                  <Col span={24} offset={2}>
                    <CustomLabel
                      text="เกี่ยวข้องกับการยืนยันความสนใจระหว่างเรียน"
                      tooltipText="เกี่ยวข้องกับการยืนยันความสนใจระหว่างเรียน"
                      contentTooltip={
                        <ContentTooltip
                          textList={[
                            'ระยะเวลาการแสดงยืนยันตัวตนที่มีความข้องกับเวลาการแจ้งเตือนการยืนยันความสนใจระหว่างเรียน',
                          ]}
                        />
                      }
                    />
                  </Col>
                  <Col span={11} offset={2}>
                    <Form.Item
                      name={[
                        fieldName,
                        OrganizationFormFieldNameEnum.LearningConfig,
                        OrganizationFormFieldNameEnum.RPC,
                        'gapIdleMinute',
                      ]}
                      label={
                        <CustomLabel
                          text="RPC_GAP_IDLE_MINUTES"
                          tooltipText="RPC_GAP_IDLE_MINUTES"
                          contentTooltip={
                            <ContentTooltip
                              textList={[
                                'ระยะห่างในแสดงการยืนยันตัวตนถัดจากการแจ้งเตือนความสนใจระหว่างเรียน (นาที) การกำหนดระยะห่างในแสดงการยืนยันตัวตนถัดจากการแจ้งเตือนความสนใจระหว่างเรียน เพื่อป้องกันไม่ให้เกิดแสดง "การยืนยันตัวตน" ทันทีหลังจากยืนยันความสนใจ หากระบบจะตรวจสอบว่ามีระยะห่างน้อยกว่าที่กำหนด ระบบจะเลื่อนเวลาแสดงการยืนยันตัวตนออกไปตามเวลาที่หนด',
                              ]}
                            />
                          }
                        />
                      }
                      rules={learningConfigValidator({
                        required: 'กรุณากรอก RPC_GAP_IDLE_MINUTES',
                        invalid: 'กรุณากรอก RPC_GAP_IDLE_MINUTES เป็นจำนวนเต็ม',
                      })}
                    >
                      <Input initialValue={defaultLearningConfig?.rpc.gapIdleMinute} />
                    </Form.Item>
                  </Col>
                  <Col span={11}>
                    <Form.Item
                      name={[
                        fieldName,
                        OrganizationFormFieldNameEnum.LearningConfig,
                        OrganizationFormFieldNameEnum.RPC,
                        'forwardDelayMinute',
                      ]}
                      label={
                        <CustomLabel
                          text="RPC_FORWARD_DELAY_MINUTES"
                          tooltipText="RPC_FORWARD_DELAY_MINUTES"
                          contentTooltip={
                            <ContentTooltip
                              textList={[
                                'ระยะเวลาที่ต้องเลื่อนแสดงการยืนยันตัวตนออกไป (นาที) การกำหนดระยะเวลา เพื่อเลื่อนการยืนยันตัวตนออกไปจากเวลาเดิม',
                              ]}
                            />
                          }
                        />
                      }
                      rules={learningConfigValidator({
                        required: 'กรุณากรอก RPC_FORWARD_DELAY_MINUTES',
                        invalid: 'กรุณากรอก RPC_FORWARD_DELAY_MINUTES เป็นจำนวนเต็ม',
                      })}
                    >
                      <Input initialValue={defaultLearningConfig?.rpc.forwardDelayMinute} />
                    </Form.Item>
                  </Col>
                  <Col span={24} offset={2}>
                    <Form.Item
                      name={[fieldName, OrganizationFormFieldNameEnum.LearningConfig, 'isLivenessEnabled']}
                      label={
                        <CustomLabel
                          text="ตรวจสอบการปลอมแปลงใบหน้า"
                          tooltipText="ตรวจสอบการปลอมแปลงใบหน้า"
                          contentTooltip={
                            <ContentTooltip
                              textList={[
                                'การตรวจสอบว่ารูปถ่ายใบหน้าที่ผู้เรียนทำการยืนยันตัวตนระหว่างเรียน เป็นภาพคนจริง และใบหน้าตรงกับบุคคลที่ยืนยันตัวตนก่อนเข้าเรียน',
                              ]}
                            />
                          }
                        />
                      }
                    >
                      <Switch initialValues={defaultLearningConfig?.isLivenessEnabled} />
                    </Form.Item>
                  </Col>
                </>
              )}
            </>
          )}
        </Row>
      </>
    );
  }
};

LearningConfigFormItem.propTypes = {
  objectiveType: string,
  fieldName: string,
};

const CreateOrganizationObjectiveForm = (props) => {
  const { form } = props;

  const onBuilderFormListItem = (field) => {
    const selectedObjective = form.getFieldValue([
      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
      field.name,
      OrganizationFormFieldNameEnum.ObjectiveType,
    ]);
    const selectedRegulator = form.getFieldValue([
      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
      field.name,
      OrganizationFormFieldNameEnum.Regulator,
    ]);

    const mapFields = regulatorMasterConfigs.find((masterItem) => masterItem.objectiveType === selectedObjective);

    let TrainingCenterComponent = null;
    let LicenseRenewalComponent = null;
    let LearningConfigComponent = null;

    if (mapFields?.regulator) {
      if (mapFields[OrganizationFormFieldNameEnum.TrainingCenterCode]) {
        TrainingCenterComponent = <TrainingCenterFormItem fieldName={field.name} />;
      }

      if (selectedRegulator && mapFields[OrganizationFormFieldNameEnum.LicenseRenewal]) {
        const mapFieldsRegulator = regulatorMasterConfigs.find(
          (val) => val.objectiveType === selectedObjective && val.regulator === selectedRegulator,
        );

        LicenseRenewalComponent = (
          <LicenseRenewalFormItem fieldName={field.name} regulatorConfig={mapFieldsRegulator} />
        );
      }
    }

    LearningConfigComponent = <LearningConfigFormItem objectiveType={selectedObjective} fieldName={field.name} />;

    return (
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <ObjectiveTypeFormItem fieldName={field.name} />
        </Col>
        {TrainingCenterComponent && <Col span={24}>{TrainingCenterComponent}</Col>}
        {LicenseRenewalComponent && <Col span={24}>{LicenseRenewalComponent}</Col>}
        {LearningConfigComponent && <Col span={24}>{LearningConfigComponent}</Col>}
      </Row>
    );
  };

  return (
    <Form form={form} layout="vertical">
      <Form.List name={OrganizationFormFieldNameEnum.ObjectiveTypeGroup}>
        {(fields, { add, remove }) => {
          const isRequiredFormAtLeastOne = fields.length === 0;
          const isRemovable = fields.length > 1;

          if (isRequiredFormAtLeastOne) {
            add();
          }

          return (
            <Row gutter={[0, 24]}>
              {fields.map((field) => (
                <Col key={field.key} span={24}>
                  <Card
                    title={`วัตถุประสงค์การเรียนที่ ${field.name + 1}`}
                    extra={
                      isRemovable && (
                        <Button
                          type="text"
                          onClick={() => {
                            remove(field.name);
                          }}
                          style={{ padding: 8, display: 'flex' }}
                        >
                          <FontAwesomeIcon icon={fasXmarkLarge} fontSize={16} style={{ margin: 0 }} />
                        </Button>
                      )
                    }
                  >
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) => {
                        const strPrevValues = JSON.stringify(
                          get(prevValues, [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, field.name]),
                        );

                        const strCurrentValues = JSON.stringify(
                          get(currentValues, [OrganizationFormFieldNameEnum.ObjectiveTypeGroup, field.name]),
                        );

                        return strPrevValues !== strCurrentValues;
                      }}
                    >
                      {() => onBuilderFormListItem(field)}
                    </Form.Item>
                  </Card>
                </Col>
              ))}

              <Col span={24}>
                <Button type="dashed" onClick={() => add()} block>
                  <Space size={8}>
                    <FontAwesomeIcon icon={fasPlus} fontSize={16} style={{ margin: 0 }} />
                    <Typography.Text>เพิ่มวัตถุประสงค์การเรียน</Typography.Text>
                  </Space>
                </Button>
              </Col>
            </Row>
          );
        }}
      </Form.List>
    </Form>
  );
};

CreateOrganizationObjectiveForm.propTypes = {
  form: object.isRequired,
};

export default CreateOrganizationObjectiveForm;
