import React, { useEffect, useState } from 'react';

import FontAwesomeIcon from '@iso/components/fontAwesomeIcon';
import { fasPlus } from '@iso/components/fontAwesomeIcon/solid';
import {
  CourseObjectiveTypeEnum,
  RegulatorEnum,
  LicenseRenewalEnum,
  ApplicantTypeEnum,
  ApplicantTypeTHEnum,
  LicenseTypeEnum,
  LicenseRenewalTHEnum,
  LicenseTypeTHEnum,
} from '@iso/lms/enums/course.enum';
import { Button, Card, Col, Divider, Form, Input, Row, Select, Space, Switch, Typography, TreeSelect } from 'antd';
import { useRouter } from 'next/router';
import { number, object, oneOf, string } from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';

import ConfirmModal from '@components/atoms/confirmModal';
import { ContentTooltip, CustomLabel } from '@components/molecules/customLabel';
import ConfirmInputModal from '@components/molecules/modal/confirmInputModal';
import { defaultLicense<PERSON><PERSON><PERSON> } from '@constants/course';
import { OrganizationFormFieldNameEnum } from '@constants/organization';
import { learningConfigValidator } from '@helpers/formItemValidator';
import messageActions from '@redux/message/actions';
import organizationAction from '@redux/organization/actions';
import { organizationStore } from '@redux/organization/state';

const { set_message } = messageActions;

const { updateOrganizationObjectiveAction } = organizationAction;

const masterMapping = [
  {
    objectiveType: CourseObjectiveTypeEnum.TRAINING,
    objectiveTypeName: 'การอบรม',
    trainingCenterCode: true,
    regulator: RegulatorEnum.TSI,
    licenseRenewal: {
      [LicenseRenewalEnum.NONE]: {
        name: null,
        applicantType: {
          [ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER]: {
            name: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
            licenseType: {
              INVESTMENT: LicenseTypeEnum.INVESTMENT,
            },
          },
        },
      },
    },
  },
  {
    objectiveType: CourseObjectiveTypeEnum.TRAINING,
    objectiveTypeName: 'การอบรม',
    trainingCenterCode: true,
    regulator: RegulatorEnum.OIC,
    licenseRenewal: defaultLicenseRenewal,
  },
  {
    objectiveType: CourseObjectiveTypeEnum.TRAINING,
    objectiveTypeName: 'การอบรม',
    trainingCenterCode: true,
    regulator: RegulatorEnum.TFAC,
    licenseRenewal: {
      [LicenseRenewalEnum.NONE]: {
        name: null,
        applicantType: {
          [ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER]: {
            name: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
            licenseType: {
              TFAC_LICENSE: { name: 'ใบอนุญาต TFAC' },
            },
          },
        },
      },
    },
  },
];

const transformTrainingCenterData = (trainingCenter, objectiveType) => {
  const {
    name: trainingCenterName,
    key: trainingCenterCode,
    regulator,
    learningConfig: tcLearningConfig,
  } = trainingCenter;
  const learningConfig = tcLearningConfig || {};

  let licenseTypeList;
  let applicantType;

  if (regulator === RegulatorEnum.TSI) {
    applicantType = trainingCenter.applicantTypes?.[0]?.key;
  } else if (regulator === RegulatorEnum.OIC) {
    licenseTypeList = trainingCenter.licenseRenewals?.map((licenseRenewal) => ({
      licenseRenewal: licenseRenewal.key,
      applicantType: trainingCenter.licenseTypes
        ?.filter((licenseType) => licenseType.licenseRenewalKeys.includes(licenseRenewal.key))
        .flatMap((licenseType) =>
          licenseType.applicantTypeKeys.map((applicantKey) => `${applicantKey}:${licenseType.key}`),
        ),
    }));
  } else if (regulator === RegulatorEnum.TFAC) {
    applicantType = trainingCenter.licenseTypes?.map(
      (licenseType) => `${licenseType.applicantTypeKeys?.[0]}:${licenseType.key}`,
    );
  }

  return {
    objectiveType,
    trainingCenterName,
    trainingCenterCode,
    regulator,
    learningConfig,
    licenseTypeList,
    applicantType,
  };
};

const ObjectiveTypeFormItem = ({ index }) => {
  const excludeKeys = [];

  const objectiveTypeOptions = [];
  for (const mapItem of masterMapping) {
    objectiveTypeOptions.push({
      value: mapItem.objectiveType,
      label: mapItem.objectiveTypeName,
      disabled: excludeKeys.length > 0 && excludeKeys.includes(mapItem.objectiveType),
    });
  }

  return (
    <Form.Item
      name={[OrganizationFormFieldNameEnum.ObjectiveTypeGroup, index, OrganizationFormFieldNameEnum.ObjectiveType]}
      label="วัตถุประสงค์การเรียน"
      className="mb-0"
      rules={[
        {
          required: true,
        },
      ]}
    >
      <Select placeholder="กรุณาเลือกวัตถุประสงค์การเรียน" options={objectiveTypeOptions} disabled />
    </Form.Item>
  );
};

ObjectiveTypeFormItem.propTypes = {
  index: oneOf([string, number]),
};

const TrainingCenterFormItem = ({ field, index }) =>
  field.objectiveType !== CourseObjectiveTypeEnum.REGULAR && (
    <Row gutter={[16, 16]}>
      <Col sm={12} xs={24}>
        <Form.Item
          label="ชื่อศูนย์อบรม"
          className="mb-0"
          name={[
            OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
            index,
            OrganizationFormFieldNameEnum.TrainingCenterName,
          ]}
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Input placeholder="กรุณากรอกชื่อศูนย์อบรม" disabled />
        </Form.Item>
      </Col>
      <Col sm={12} xs={24}>
        <Form.Item
          label="รหัสศูนย์อบรม"
          className="mb-0"
          name={[
            OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
            index,
            OrganizationFormFieldNameEnum.TrainingCenterCode,
          ]}
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Input placeholder="กรุณากรอกรหัสศูนย์อบรม" maxLength={20} disabled />
        </Form.Item>
      </Col>
      <Col sm={12} xs={24}>
        <Form.Item
          label="หน่วยงานกำกับดูแล"
          className="mb-0"
          name={[OrganizationFormFieldNameEnum.ObjectiveTypeGroup, index, OrganizationFormFieldNameEnum.Regulator]}
        >
          <Select disabled />
        </Form.Item>
      </Col>
    </Row>
  );

TrainingCenterFormItem.propTypes = {
  field: string,
  index: oneOf([string, number]),
};

const LicenseRenewalFormItem = ({ field, index }) => {
  const licenseTypeOptions = [
    {
      value: LicenseRenewalEnum.NEW,
      label: LicenseRenewalTHEnum.OIC0,
      disabled: false,
    },
    {
      value: LicenseRenewalEnum.RENEW1,
      label: LicenseRenewalTHEnum.OIC1,
      disabled: false,
    },
    {
      value: LicenseRenewalEnum.RENEW2,
      label: LicenseRenewalTHEnum.OIC2,
      disabled: false,
    },
    {
      value: LicenseRenewalEnum.RENEW3,
      label: LicenseRenewalTHEnum.OIC3,
      disabled: false,
    },
    {
      value: LicenseRenewalEnum.RENEW4,
      label: LicenseRenewalTHEnum.OIC4,
      disabled: false,
    },
    {
      value: LicenseRenewalEnum.UL,
      label: LicenseRenewalTHEnum.UL,
      disabled: false,
    },
    {
      value: LicenseRenewalEnum.UK,
      label: LicenseRenewalTHEnum.UK,
      disabled: false,
    },
  ];

  const applicantTypeOptions = [
    {
      key: ApplicantTypeEnum.AGENT,
      value: ApplicantTypeEnum.AGENT,
      title: ApplicantTypeTHEnum.AGENT,
      children: [
        {
          title: `${ApplicantTypeTHEnum.AGENT}${LicenseTypeTHEnum['NON-LIFE']}`,
          value: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.NONLIFE}`,
          key: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.NONLIFE}`,
        },
        {
          title: `${ApplicantTypeTHEnum.AGENT}${LicenseTypeTHEnum.LIFE}`,
          value: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.LIFE}`,
          key: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.LIFE}`,
        },
        {
          title: `${ApplicantTypeTHEnum.AGENT}${LicenseTypeTHEnum.BOTH}`,
          value: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.BOTH}`,
          key: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.BOTH}`,
        },
        {
          title: `${ApplicantTypeTHEnum.AGENT}${LicenseTypeTHEnum.UNIT_LINKED}`,
          value: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.UNIT_LINKED}`,
          key: `${ApplicantTypeEnum.AGENT}:${LicenseTypeEnum.UNIT_LINKED}`,
        },
      ],
    },
    {
      key: ApplicantTypeEnum.BROKER,
      value: ApplicantTypeEnum.BROKER,
      title: ApplicantTypeTHEnum.BROKER,
      children: [
        {
          title: `${ApplicantTypeTHEnum.BROKER}${LicenseTypeTHEnum['NON-LIFE']}`,
          value: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.NONLIFE}`,
          key: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.NONLIFE}`,
        },
        {
          title: `${ApplicantTypeTHEnum.BROKER}${LicenseTypeTHEnum.LIFE}`,
          value: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.LIFE}`,
          key: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.LIFE}`,
        },
        {
          title: `${ApplicantTypeTHEnum.BROKER}${LicenseTypeTHEnum.BOTH}`,
          value: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.BOTH}`,
          key: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.BOTH}`,
        },
        {
          title: `${ApplicantTypeTHEnum.BROKER}${LicenseTypeTHEnum.UNIT_LINKED}`,
          value: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.UNIT_LINKED}`,
          key: `${ApplicantTypeEnum.BROKER}:${LicenseTypeEnum.UNIT_LINKED}`,
        },
      ],
    },
    {
      key: ApplicantTypeEnum.ACCOUNTANT,
      value: ApplicantTypeEnum.ACCOUNTANT,
      title: ApplicantTypeTHEnum.ACCOUNTANT,
      children: [
        {
          title: `${ApplicantTypeTHEnum.ACCOUNTANT}${LicenseTypeTHEnum.CPA}`,
          value: `${ApplicantTypeEnum.ACCOUNTANT}:${LicenseTypeEnum.CPA}`,
          key: `${ApplicantTypeEnum.ACCOUNTANT}:${LicenseTypeEnum.CPA}`,
        },
        {
          title: `${ApplicantTypeTHEnum.ACCOUNTANT}${LicenseTypeTHEnum.RA}`,
          value: `${ApplicantTypeEnum.ACCOUNTANT}:${LicenseTypeEnum.RA}`,
          key: `${ApplicantTypeEnum.ACCOUNTANT}:${LicenseTypeEnum.RA}`,
        },
      ],
    },
  ];

  const tsiApplicantTypeOptions = [
    {
      value: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
      label: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
      disabled: false,
    },
  ];

  const { licenseTypeList, objectiveType, regulator } = field;

  let BaseComponent;

  if (objectiveType === CourseObjectiveTypeEnum.TRAINING && regulator === RegulatorEnum.TSI) {
    BaseComponent = (
      <Col span={24} key={`applicantType_${index}`}>
        <Form.Item
          label="ประเภทการลงทุน"
          className="mb-0"
          name={[OrganizationFormFieldNameEnum.ObjectiveTypeGroup, index, OrganizationFormFieldNameEnum.ApplicantType]}
          rules={[
            {
              required: true,
            },
          ]}
        >
          <Select placeholder="" options={tsiApplicantTypeOptions} disabled />
        </Form.Item>
      </Col>
    );
  } else if (objectiveType === CourseObjectiveTypeEnum.TRAINING && regulator === RegulatorEnum.OIC) {
    BaseComponent = (
      <>
        {licenseTypeList?.map((val, subIndex) => (
          <>
            <Col span={12} key={`licenseType_${subIndex}_${val}`}>
              <Form.Item
                label="ประเภทใบอนุญาต"
                className="mb-0"
                name={[
                  OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                  index,
                  'licenseTypeList',
                  subIndex,
                  OrganizationFormFieldNameEnum.LicenseRenewal,
                ]}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  placeholder="กรุณาเลือกประเภทใบอนุญาต"
                  options={licenseTypeOptions}
                  defaultValue={val.licenseRenewal}
                  disabled
                />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label="ประเภทผู้สมัครและประเภทการประกัน"
                className="mb-0"
                name={[
                  OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                  index,
                  'licenseTypeList',
                  subIndex,
                  OrganizationFormFieldNameEnum.ApplicantType,
                ]}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <TreeSelect
                  showSearch
                  dropdownStyle={{
                    maxHeight: 400,
                    minWidth: 240,
                    overflow: 'auto',
                  }}
                  placeholder="กรุณาเลือกประเภทผู้สมัครและประเภทการประกัน"
                  allowClear
                  multiple
                  treeDefaultExpandAll
                  treeData={applicantTypeOptions}
                  defaultValue={val.applicantType}
                  treeCheckable
                  disabled
                />
              </Form.Item>
            </Col>
          </>
        ))}
        <Col span={24} style={{ marginTop: 8 }}>
          <Button type="dashed" block disabled>
            <Space size={8}>
              <FontAwesomeIcon icon={fasPlus} fontSize={16} style={{ margin: 0 }} />
              <Typography.Text>เพิ่มประเภทใบอนุญาต</Typography.Text>
            </Space>
          </Button>
        </Col>
      </>
    );
  } else if (objectiveType === CourseObjectiveTypeEnum.TRAINING && regulator === RegulatorEnum.TFAC) {
    BaseComponent = (
      <Col span={24} key={`applicantType_${index}`}>
        <Form.Item
          label="ประเภทผู้สมัครและประเภทการประกัน"
          className="mb-0"
          name={[OrganizationFormFieldNameEnum.ObjectiveTypeGroup, index, OrganizationFormFieldNameEnum.ApplicantType]}
          rules={[
            {
              required: true,
            },
          ]}
        >
          <TreeSelect
            showSearch
            dropdownStyle={{
              maxHeight: 400,
              minWidth: 240,
              overflow: 'auto',
            }}
            placeholder="กรุณาเลือกประเภทผู้สมัครและประเภทการประกัน"
            allowClear
            multiple
            treeDefaultExpandAll
            treeData={applicantTypeOptions}
            treeCheckable
            disabled
          />
        </Form.Item>
      </Col>
    );
  }

  return <Row gutter={[16, 16]}>{BaseComponent}</Row>;
};

LicenseRenewalFormItem.propTypes = {
  field: object,
  index: oneOf([string, number]),
};

const LearningConfigFormItem = ({ index }) => {
  const form = Form.useFormInstance();
  const learningConfigFormValue = form.getFieldValue([OrganizationFormFieldNameEnum.ObjectiveTypeGroup, index]) || [];

  return (
    <>
      <Row gutter={[0, 24]}>
        <Form.Item
          name={[
            OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
            index,
            OrganizationFormFieldNameEnum.LearningConfig,
            OrganizationFormFieldNameEnum.Idle,
            'isEnabled',
          ]}
          label={
            <CustomLabel
              text="การยืนยันความสนใจระหว่างเรียน"
              tooltipText="การยืนยันความสนใจระหว่างเรียน"
              contentTooltip={
                <ContentTooltip
                  textList={[
                    'ระยะเวลาที่ไม่ตอบสนองต่อบทเรียน(นาที)เมื่อผู้เรียนไม่ได้มีการตอบสนองต่อบทเรียน เกินกว่าเวลาที่กำหนด ระบบจะแสดงการแจ้งเตือนเพื่อให้ผู้เรียนยืนยนัความสนใจระหว่างเรียน',
                  ]}
                />
              }
            />
          }
        >
          <Switch initialValues={learningConfigFormValue.learningConfig?.idle.isEnabled} />
        </Form.Item>
      </Row>
      <Row gutter={[16, 16]}>
        {learningConfigFormValue.learningConfig?.idle.isEnabled && (
          <>
            <Col span={12}>
              <Form.Item
                name={[
                  OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                  index,
                  OrganizationFormFieldNameEnum.LearningConfig,
                  OrganizationFormFieldNameEnum.Idle,
                  'timeoutMinute',
                ]}
                label={
                  <CustomLabel
                    text="IDLE_TIME_OUT_MINUTES"
                    tooltipText="IDLE_TIME_OUT_MINUTES"
                    contentTooltip={
                      <ContentTooltip
                        textList={[
                          'ระยะเวลาที่ไม่ตอบสนองต่อบทเรียน (นาที) เมื่อผู้เรียนไม่ได้มีการตอบสนองต่อบทเรียน เกินกว่าเวลาที่กำหนด ระบบจะแสดงการแจ้งเตือนเพื่อให้ผู้เรียนยืนยันความสนใจระหว่างเรียน',
                        ]}
                      />
                    }
                  />
                }
                rules={learningConfigValidator({
                  required: 'กรุณากรอก IDLE_TIME_OUT_MINUTES',
                  invalid: 'กรุณากรอก IDLE_TIME_OUT_MINUTES เป็นจำนวนเต็ม',
                })}
              >
                <Input initialValues={learningConfigFormValue.learningConfig?.idle.timeoutMinute} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={[
                  OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                  index,
                  OrganizationFormFieldNameEnum.LearningConfig,
                  OrganizationFormFieldNameEnum.Idle,
                  'stayTimeoutMinute',
                ]}
                label={
                  <CustomLabel
                    text="STAY_TIME_OUT_MINUTES"
                    tooltipText="STAY_TIME_OUT_MINUTES"
                    contentTooltip={
                      <ContentTooltip
                        textList={[
                          'ระยะเวลาในการยืนยันความสนใจระหว่างเรียน (นาที) เมื่อแสดงการแจ้งเตือนเพื่อยืนยันความสนใจ หากไม่มีการยืนยันเกินกว่าเวลาที่กำหนด ผู้เรียนจะถูกบังคับให้ออกจากระบบ แต่หากยืนยันสำเร็จภายในเวลาที่กำหนด ผู้เรียนจะกลับสู่บทเรียนตามเดิม',
                        ]}
                      />
                    }
                  />
                }
                className="mb-0"
                rules={learningConfigValidator({
                  required: 'กรุณากรอก STAY_TIME_OUT_MINUTES',
                  invalid: 'กรุณากรอก STAY_TIME_OUT_MINUTES เป็นจำนวนเต็ม',
                })}
              >
                <Input initialValues={learningConfigFormValue.learningConfig?.idle.stayTimeoutMinute} />
              </Form.Item>
            </Col>
          </>
        )}
        {learningConfigFormValue.objectiveType !== CourseObjectiveTypeEnum.REGULAR && (
          <>
            <Col span={24}>
              <Form.Item
                name={[
                  OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                  index,
                  OrganizationFormFieldNameEnum.LearningConfig,
                  'isIdentityVerificationEnabled',
                ]}
                label={
                  <CustomLabel
                    text="การยืนยันตัวตน"
                    tooltipText="การยืนยันตัวตน"
                    contentTooltip={
                      <ContentTooltip
                        textList={[
                          'การยืนยันตัวตนของผู้เรียน โดยการถ่ายรูปบัตรประชาชนและใบหน้าในการเข้าเรียน และสุ่มถ่ายใบหน้าเพื่อยืนยันตัวตนระหว่างการเรียน ',
                        ]}
                      />
                    }
                  />
                }
                className="mb-0"
              >
                <Switch initialValues={learningConfigFormValue.learningConfig?.isIdentityVerificationEnabled} />
              </Form.Item>
            </Col>

            {learningConfigFormValue?.learningConfig.isIdentityVerificationEnabled && (
              <>
                <Col span={22} offset={2}>
                  <Form.Item
                    name={[
                      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                      index,
                      OrganizationFormFieldNameEnum.LearningConfig,
                      OrganizationFormFieldNameEnum.RPC,
                      'isEnabled',
                    ]}
                    label={
                      <CustomLabel
                        text="สุ่มถ่ายใบหน้ายืนยันตัวระหว่างเรียน"
                        tooltipText="สุ่มถ่ายใบหน้ายืนยันตัวระหว่างเรียน"
                        contentTooltip={
                          <ContentTooltip
                            textList={[
                              'ในระหว่างการเรียน จะมีสุ่มเวลาแสดงหน้าต่างให้ผู้เรียนทำการยืนยันตัวตนโดยการถ่ายรูปใบหน้า',
                            ]}
                          />
                        }
                      />
                    }
                    className="mb-0"
                  >
                    <Switch initialValues={learningConfigFormValue.learningConfig?.rpc.isEnabled} />
                  </Form.Item>
                </Col>
                <Col span={11} offset={2}>
                  <Form.Item
                    name={[
                      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                      index,
                      OrganizationFormFieldNameEnum.LearningConfig,
                      OrganizationFormFieldNameEnum.RPC,
                      'durationMinute',
                    ]}
                    label={
                      <CustomLabel
                        text="RPC_MINUTES"
                        tooltipText="RPC_MINUTES"
                        contentTooltip={
                          <ContentTooltip
                            textList={[
                              'รอบช่วงเวลาที่แสดงการยืนยันตัวตนของผู้เรียนระหว่างเรียน (นาที) การกำหนดรอบช่วงเวลาที่ต้องการให้ผู้เรียนยืนยันตัวตน โดยจะสุ่มแสดงเพียงหนึ่งครั้งในแต่ละรอบ ภายในความยาวของบทเรียนนั้นสุ่มถ่ายใบหน้ายืนยันตัวตนระหว่างเรียน',
                            ]}
                          />
                        }
                      />
                    }
                    className="mb-0"
                    rules={learningConfigValidator({
                      required: 'กรุณากรอก RPC_MINUTES',
                      invalid: 'กรุณากรอก RPC_MINUTES เป็นจำนวนเต็ม',
                    })}
                  >
                    <Input initialValues={learningConfigFormValue.learningConfig?.rpc.durationMinute} />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item
                    name={[
                      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                      index,
                      OrganizationFormFieldNameEnum.LearningConfig,
                      OrganizationFormFieldNameEnum.RPC,
                      'delayMinute',
                    ]}
                    className="mb-0"
                    label={
                      <CustomLabel
                        text="RPC_DELAY_MINUTES"
                        tooltipText="RPC_DELAY_MINUTES"
                        contentTooltip={
                          <ContentTooltip
                            textList={[
                              'ระยะเวลาที่จะไม่เกิดการสุ่มแสดงการยืนยันตัวตนระหว่างเรียน (นาที) เมื่อเริ่มต้นเข้าสู่ช่วงเวลายืนยันตัวตนในแต่ละรอบ จะมีช่วงหน่วงเวลาที่จะไม่เกิดการสุ่มแสดงการยืนยันตัตน เพื่อลดโอกาสแสดงการยืนยันตัวตนในเวลาที่ติดกันเกินไปเมื่อเริ่มรอบถัดไป',
                            ]}
                          />
                        }
                      />
                    }
                    rules={learningConfigValidator({
                      required: 'กรุณากรอก RPC_DELAY_MINUTES',
                      invalid: 'กรุณากรอก RPC_DELAY_MINUTES เป็นจำนวนเต็ม',
                    })}
                  >
                    <Input initialValues={learningConfigFormValue.learningConfig?.rpc.delayMinute} />
                  </Form.Item>
                </Col>
                <Col span={11} offset={2}>
                  <Form.Item
                    name={[
                      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                      index,
                      OrganizationFormFieldNameEnum.LearningConfig,
                      OrganizationFormFieldNameEnum.RPC,
                      'stayTimeoutMinute',
                    ]}
                    label={
                      <CustomLabel
                        text="RPC_STAY_TIME_OUT_MINUTES"
                        tooltipText="RPC_STAY_TIME_OUT_MINUTES"
                        contentTooltip={
                          <ContentTooltip
                            textList={[
                              'ระยะเวลาในการยืนยันตัวตนระหว่างเรียน (นาที) เมื่อแสดงหน้าต่างยืนยันตัวตน หากไม่มีการยืนยันเกินกว่าเวลาที่กำหนด ผู้เรียนจะถูกบังคับให้ออกจากระบบ แต่หากยืนยันสำเร็จภายในเวลาที่กำหนด ผู้เรียนจะกลับสู่บทเรียนตามเดิม',
                            ]}
                          />
                        }
                      />
                    }
                    className="mb-0"
                    rules={learningConfigValidator({
                      required: 'กรุณากรอก RPC_STAY_TIME_OUT_MINUTES',
                      invalid: 'กรุณากรอก RPC_STAY_TIME_OUT_MINUTES เป็นจำนวนเต็ม',
                    })}
                  >
                    <Input initialValues={learningConfigFormValue.learningConfig?.rpc.stayTimeoutMinute} />
                  </Form.Item>
                </Col>

                <Col span={24} offset={2}>
                  <CustomLabel
                    text="เกี่ยวข้องกับการยืนยันความสนใจระหว่างเรียน"
                    tooltipText="เกี่ยวข้องกับการยืนยันความสนใจระหว่างเรียน"
                    contentTooltip={
                      <ContentTooltip
                        textList={[
                          'ระยะเวลาการแสดงยืนยันตัวตนที่มีความข้องกับเวลาการแจ้งเตือนการยืนยันความสนใจระหว่างเรียน',
                        ]}
                      />
                    }
                  />
                </Col>
                <Col span={11} offset={2}>
                  <Form.Item
                    name={[
                      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                      index,
                      OrganizationFormFieldNameEnum.LearningConfig,
                      OrganizationFormFieldNameEnum.RPC,
                      'gapIdleMinute',
                    ]}
                    label={
                      <CustomLabel
                        text="RPC_GAP_IDLE_MINUTES"
                        tooltipText="RPC_GAP_IDLE_MINUTES"
                        contentTooltip={
                          <ContentTooltip
                            textList={[
                              'ระยะห่างในแสดงการยืนยันตัวตนถัดจากการแจ้งเตือนความสนใจระหว่างเรียน (นาที) การกำหนดระยะห่างในแสดงการยืนยันตัวตนถัดจากการแจ้งเตือนความสนใจระหว่างเรียน เพื่อป้องกันไม่ให้เกิดแสดง "การยืนยันตัวตน" ทันทีหลังจากยืนยันความสนใจ หากระบบจะตรวจสอบว่ามีระยะห่างน้อยกว่าที่กำหนด ระบบจะเลื่อนเวลาแสดงการยืนยันตัวตนออกไปตามเวลาที่หนด',
                            ]}
                          />
                        }
                      />
                    }
                    rules={learningConfigValidator({
                      required: 'กรุณากรอก RPC_GAP_IDLE_MINUTES',
                      invalid: 'กรุณากรอก RPC_GAP_IDLE_MINUTES เป็นจำนวนเต็ม',
                    })}
                  >
                    <Input initialValues={learningConfigFormValue.learningConfig?.rpc.gapIdleMinute} />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item
                    name={[
                      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                      index,
                      OrganizationFormFieldNameEnum.LearningConfig,
                      OrganizationFormFieldNameEnum.RPC,
                      'forwardDelayMinute',
                    ]}
                    label={
                      <CustomLabel
                        text="RPC_FORWARD_DELAY_MINUTES"
                        tooltipText="RPC_FORWARD_DELAY_MINUTES"
                        contentTooltip={
                          <ContentTooltip
                            textList={[
                              'ระยะเวลาที่ต้องเลื่อนแสดงการยืนยันตัวตนออกไป (นาที) การกำหนดระยะเวลา เพื่อเลื่อนการยืนยันตัวตนออกไปจากเวลาเดิม',
                            ]}
                          />
                        }
                      />
                    }
                    rules={learningConfigValidator({
                      required: 'กรุณากรอก RPC_FORWARD_DELAY_MINUTES',
                      invalid: 'กรุณากรอก RPC_FORWARD_DELAY_MINUTES เป็นจำนวนเต็ม',
                    })}
                  >
                    <Input initialValues={learningConfigFormValue.learningConfig?.rpc.forwardDelayMinute} />
                  </Form.Item>
                </Col>
                <Col span={24} offset={2}>
                  <Form.Item
                    name={[
                      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                      index,
                      OrganizationFormFieldNameEnum.LearningConfig,
                      'isLivenessEnabled',
                    ]}
                    label={
                      <CustomLabel
                        text="ตรวจสอบการปลอมแปลงใบหน้า"
                        tooltipText="ตรวจสอบการปลอมแปลงใบหน้า"
                        contentTooltip={
                          <ContentTooltip
                            textList={[
                              'การตรวจสอบว่ารูปถ่ายใบหน้าที่ผู้เรียนทำการยืนยันตัวตนระหว่างเรียน เป็นภาพคนจริง และใบหน้าตรงกับบุคคลที่ยืนยันตัวตนก่อนเข้าเรียน',
                            ]}
                          />
                        }
                      />
                    }
                  >
                    <Switch initialValues={learningConfigFormValue.learningConfig?.isLivenessEnabled} />
                  </Form.Item>
                </Col>
              </>
            )}
          </>
        )}
      </Row>
    </>
  );
};

LearningConfigFormItem.propTypes = {
  index: oneOf([string, number]),
};

const EditOrganizationObjectiveForm = (props) => {
  const { organizationData, adminData } = props;
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const router = useRouter();

  const [isVisibleConfirmUpdateModal, setIsVisibleConfirmUpdateModal] = useState(false);
  const [isVisibleConfirmCancelUpdateModal, setIsVisibleConfirmCancelUpdateModal] = useState(false);

  const { updateOrganizationObjective } = useSelector((state) => ({
    updateOrganizationObjective: state.Organization.get(organizationStore.updateOrganizationObjective),
  }));

  const { isSuccess: isSuccessUpdateOrganizationObjective, isError: isErrorUpdateOrganizationObjective } =
    updateOrganizationObjective;

  const onInitialData = () => {
    const result = organizationData.courseObjectiveConfigs.flatMap((item) => {
      if (item.objectiveType === CourseObjectiveTypeEnum.REGULAR) {
        return [
          {
            objectiveType: item.objectiveType,
            learningConfig: item.defaultLearningConfig,
            list: [],
          },
        ];
      }

      const list = item.trainingCenters?.map((tc) => transformTrainingCenterData(tc, item.objectiveType));

      return {
        objectiveType: item.objectiveType,
        learningConfig: item.learningConfig || {},
        list,
      };
    });

    const flatMappedTrainingCenters = result.flatMap((item) => {
      if (item.list.length === 0) {
        return [item];
      }
      return item.list.map((trainingCenter) => ({
        objectiveType: item.objectiveType,
        trainingCenterCode: trainingCenter.trainingCenterCode,
        trainingCenterName: trainingCenter.trainingCenterName,
        regulator: trainingCenter.regulator,
        learningConfig: trainingCenter.learningConfig || {},
        licenseTypeList: trainingCenter.licenseTypeList || [],
        applicantType: trainingCenter.applicantType || null,
      }));
    });

    form.setFieldsValue({
      objectives: flatMappedTrainingCenters,
    });
  };

  const onClickSubmitUpdateOrganization = () => {
    form
      .validateFields()
      .then(() => setIsVisibleConfirmUpdateModal(true))
      .catch(() => setIsVisibleConfirmUpdateModal(false));
  };

  const onClickCancelUpdateOrganization = () => {
    setIsVisibleConfirmCancelUpdateModal(true);
  };

  const onCloseUpdateOrganizationConfirmModal = () => {
    form.setFieldValue('email', '');
    setIsVisibleConfirmUpdateModal(false);
  };

  const onSubmitUpdateOrganizationConfirm = () => {
    form.validateFields(['email']).then(() => {
      if (!organizationData?.id) return;
      const formValues = form.getFieldValue([OrganizationFormFieldNameEnum.ObjectiveTypeGroup]) || [];

      const payload = {
        id: organizationData.id,
        form: { objectives: [] },
      };

      for (const objective of formValues) {
        let existingCourseObjective = payload.form.objectives.find(
          (obj) => obj.courseObjective === objective.objectiveType && obj.regulator === objective.regulator,
        );

        if (!existingCourseObjective) {
          existingCourseObjective = {
            courseObjective: objective.objectiveType,
            regulator: objective.regulator,
            trainingCenterConfigs: [],
            list: [],
          };
          payload.form.objectives.push(existingCourseObjective);
        }

        if (objective.objectiveType === CourseObjectiveTypeEnum.REGULAR) {
          existingCourseObjective.learningConfig = objective.learningConfig;
        }

        if (objective.objectiveType === CourseObjectiveTypeEnum.TRAINING) {
          existingCourseObjective.list.push({
            trainingCenterName: objective.trainingCenterName,
            trainingCenterCode: objective.trainingCenterCode,
          });
          existingCourseObjective.trainingCenterConfigs.push({
            trainingCenterCode: objective.trainingCenterCode,
            learningConfig: objective.learningConfig,
          });
        }
      }

      dispatch(updateOrganizationObjectiveAction.request(payload));
      onCloseUpdateOrganizationConfirmModal();
    });
  };

  const onSubmitConfirmCancel = () => {
    onInitialData();
    router.push(`/organizations/${organizationData.id}`);
    setIsVisibleConfirmCancelUpdateModal(false);
  };

  const onCloseConfirmCancelModal = () => setIsVisibleConfirmCancelUpdateModal(false);

  const onBuilderFormListItem = (field, index) => {
    return (
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <ObjectiveTypeFormItem index={index} field={field} />
        </Col>
        <Col span={24}>
          <TrainingCenterFormItem index={index} field={field} />
        </Col>
        <Col span={24}>
          <LicenseRenewalFormItem index={index} field={field} />
        </Col>
        <Divider />
        <Col span={24}>
          <LearningConfigFormItem index={index} field={field} />
        </Col>
      </Row>
    );
  };

  useEffect(() => {
    if (!organizationData) return;

    onInitialData();
  }, [organizationData?.domain]);

  useEffect(() => {
    if (!isSuccessUpdateOrganizationObjective) return;

    dispatch(set_message('success', 'บันทึกข้อมูลสําเร็จ'));
    dispatch(updateOrganizationObjectiveAction.reset());

    router.push(`/organizations/${organizationData.id}`);
  }, [isSuccessUpdateOrganizationObjective]);

  useEffect(() => {
    if (!isErrorUpdateOrganizationObjective) return;
    dispatch(messageActions.set_message('error', 'เกิดข้อผิดพลาด โปรดลองใหม่อีกครั้ง'));
  }, [isErrorUpdateOrganizationObjective]);

  return (
    <>
      <Form form={form} layout="vertical" onFinish={onClickSubmitUpdateOrganization}>
        <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.objectives !== curValues.objectives}>
          {({ getFieldValue }) => {
            const courseObjectiveTypeList = getFieldValue('objectives') || [];

            return (
              <Row gutter={[0, 24]}>
                {courseObjectiveTypeList.map((field, index) => (
                  <Col key={`objective_${index}_${field}`} span={24}>
                    <Card title={`วัตถุประสงค์การเรียนที่ ${index + 1}`}>{onBuilderFormListItem(field, index)}</Card>
                  </Col>
                ))}
              </Row>
            );
          }}
        </Form.Item>
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 16 }}>
          <Button type="default" htmlType="button" style={{ marginRight: 8 }} onClick={onClickCancelUpdateOrganization}>
            ยกเลิก
          </Button>
          <Button type="primary" htmlType="submit">
            บันทึก
          </Button>
        </div>
      </Form>
      <ConfirmModal
        title="ยืนยันการยกเลิกแก้ไขข้อมูล"
        open={isVisibleConfirmCancelUpdateModal}
        content={<Typography.Text>เมื่อยกเลิกการแก้ไข ไม่สามารถนำข้อมูลกลับคืนมาได้</Typography.Text>}
        okText="ยืนยัน"
        cancelText="ยกเลิก"
        onCancel={onCloseConfirmCancelModal}
        onOk={onSubmitConfirmCancel}
      />

      <ConfirmInputModal
        title="ยืนยันการบันทึกตั้งค่าเว็บไซต์"
        open={isVisibleConfirmUpdateModal}
        onCancel={onCloseUpdateOrganizationConfirmModal}
        onOk={onSubmitUpdateOrganizationConfirm}
        inputMatch={adminData.email}
      />
    </>
  );
};

EditOrganizationObjectiveForm.defaultProps = {
  organizationData: {},
  adminData: {},
};

EditOrganizationObjectiveForm.propTypes = {
  organizationData: object,
  adminData: object,
};

export default EditOrganizationObjectiveForm;
