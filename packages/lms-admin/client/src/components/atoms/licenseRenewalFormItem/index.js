import React, { useEffect } from 'react';

import FontAwesomeIcon from '@iso/components/fontAwesomeIcon';
import { fasPlus, fasXmarkLarge } from '@iso/components/fontAwesomeIcon/solid';
import { LicenseRenewalEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { Button, Col, Form, Row, Select, Space, Typography, TreeSelect } from 'antd';
import { compact } from 'lodash';
import { element, object, string } from 'prop-types';
import { useTheme } from 'styled-components';

import { OrganizationFormFieldNameEnum } from '@constants/organization';

const SelectLicenseTypeFormItem = (props) => {
  const { subField, subOpt, children, ...rest } = props;
  const { token } = useTheme();

  const isRemovable = subField?.name > 0;
  return (
    <Row>
      <Col flex="1 1 48px">{React.cloneElement(children, rest)}</Col>
      {isRemovable && (
        <Col style={{ paddingLeft: 16 }}>
          <Button
            type="text"
            onClick={() => {
              subOpt.remove(subField.name);
            }}
            style={{ padding: 8, display: 'flex', color: token.colorIcon }}
          >
            <FontAwesomeIcon icon={fasXmarkLarge} fontSize={16} style={{ margin: 0 }} />
          </Button>
        </Col>
      )}
    </Row>
  );
};

SelectLicenseTypeFormItem.propTypes = {
  children: element,
  subField: object,
  subOpt: object,
};

const LicenseRenewalFormItem = ({ fieldName, regulatorConfig }) => {
  const form = Form.useFormInstance();

  const licenseRenewalValues =
    form.getFieldValue([
      OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
      fieldName,
      OrganizationFormFieldNameEnum.LicenseRenewalGroup,
    ]) || [];
  const selectedLicenseRenewalValues = compact(licenseRenewalValues.map((formValue) => formValue?.licenseRenewal));

  const licenseRenewalOptions = Object.entries(regulatorConfig?.licenseRenewal ?? {});
  const licenseTypeOptions = licenseRenewalOptions.map(([key, value]) => ({
    value: key,
    label: value.name,
    disabled: selectedLicenseRenewalValues.includes(key),
  }));

  const isDisplayLicenseRenewal = licenseRenewalOptions.length > 1;

  const onChangeLicenseRenewal = (subFieldName, _value) => {
    form.resetFields([
      [
        OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
        fieldName,
        OrganizationFormFieldNameEnum.LicenseRenewalGroup,
        subFieldName,
        OrganizationFormFieldNameEnum.ApplicantType,
      ],
    ]);
  };

  useEffect(() => {
    if (regulatorConfig?.regulator !== RegulatorEnum.TSI) return;

    const [, value] = licenseRenewalOptions[0];
    const applicantTypePairs = Object.entries(value.applicantType);
    const [applicantTypePair] = applicantTypePairs;
    const [applicantTypeKey] = applicantTypePair;

    form.setFieldValue(
      [
        OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
        fieldName,
        OrganizationFormFieldNameEnum.LicenseRenewalGroup,
        0,
        OrganizationFormFieldNameEnum.ApplicantType,
      ],
      applicantTypeKey,
    );

    return () => {
      form.resetFields([
        [
          OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
          fieldName,
          OrganizationFormFieldNameEnum.LicenseRenewalGroup,
        ],
      ]);
    };
  }, [regulatorConfig?.regulator]);

  const lableText =
    regulatorConfig?.regulator === RegulatorEnum.TSI ? 'ประเภทการลงทุน' : 'ประเภทผู้สมัครและประเภทการประกัน';

  return (
    <Form.List name={[fieldName, OrganizationFormFieldNameEnum.LicenseRenewalGroup]}>
      {(subFields, subOpt) => {
        const isRequiredFormAtLeastOne = subFields.length === 0;

        if (isRequiredFormAtLeastOne) {
          subOpt.add();
        }

        return (
          <Row gutter={[16, 16]}>
            {subFields.map((subField) => {
              const applicantTypes = [];
              let applicantTypePairs = [];

              let BaseComponent;
              let SelectLicenseTypeComponent;

              const licenseRenewalValue = form.getFieldValue([
                OrganizationFormFieldNameEnum.ObjectiveTypeGroup,
                fieldName,
                OrganizationFormFieldNameEnum.LicenseRenewalGroup,
                subField.name,
                OrganizationFormFieldNameEnum.LicenseRenewal,
              ]);

              for (const [key, value] of licenseRenewalOptions) {
                if (key === licenseRenewalValue) {
                  applicantTypePairs = Object.entries(value.applicantType);
                  break;
                }
                if (key === LicenseRenewalEnum.NONE) {
                  applicantTypePairs = Object.entries(value.applicantType);
                  break;
                }
              }

              if (regulatorConfig?.regulator === RegulatorEnum.OIC) {
                if (licenseRenewalValue) {
                  if (applicantTypePairs.length > 1) {
                    for (const [aKey, aValue] of applicantTypePairs) {
                      const licenseTypePairs = Object.entries(aValue?.licenseType ?? {});
                      applicantTypes.push({
                        key: aKey,
                        value: aKey,
                        title: aValue.name,
                        children: licenseTypePairs.map(([lKey, lValue]) => ({
                          title: `${aValue.name}${lValue.name}`,
                          value: `${aKey}:${lKey}`,
                          key: `${aKey}:${lKey}`,
                        })),
                      });
                    }

                    SelectLicenseTypeComponent = (
                      <TreeSelect
                        showSearch
                        dropdownStyle={{
                          maxHeight: 400,
                          minWidth: 240,
                          overflow: 'auto',
                        }}
                        placeholder="กรุณาเลือกประเภทผู้สมัครและประเภทการประกัน"
                        allowClear
                        multiple
                        treeDefaultExpandAll
                        treeData={applicantTypes}
                        treeCheckable
                      />
                    );
                  }
                } else {
                  for (const [aKey, aValue] of applicantTypePairs) {
                    applicantTypes.push({
                      value: aKey,
                      label: aValue.name,
                    });
                  }
                  SelectLicenseTypeComponent = (
                    <Select
                      style={{
                        width: '100%',
                      }}
                      options={applicantTypes}
                      disabled
                    />
                  );
                }

                BaseComponent = (
                  <>
                    <Col sm={8} xs={24}>
                      <Form.Item
                        label="ประเภทใบอนุญาต"
                        className="mb-0"
                        name={[subField.name, OrganizationFormFieldNameEnum.LicenseRenewal]}
                        rules={[
                          {
                            required: true,
                            message: 'กรุณาเลือกประเภทใบอนุญาต',
                          },
                        ]}
                      >
                        <Select
                          placeholder="กรุณาเลือกประเภทใบอนุญาต"
                          onChange={(val) => onChangeLicenseRenewal(subField.name, val)}
                          options={licenseTypeOptions}
                        />
                      </Form.Item>
                    </Col>
                    <Col sm={16} xs={24}>
                      <Form.Item
                        label={lableText}
                        className="mb-0"
                        name={[subField.name, OrganizationFormFieldNameEnum.ApplicantType]}
                        rules={[
                          {
                            required: true,
                            message: 'กรุณาเลือกประเภทผู้สมัครและประเภทการประกัน',
                          },
                        ]}
                      >
                        <SelectLicenseTypeFormItem subField={subField} subOpt={subOpt}>
                          {SelectLicenseTypeComponent}
                        </SelectLicenseTypeFormItem>
                      </Form.Item>
                    </Col>
                  </>
                );
              } else if (regulatorConfig?.regulator === RegulatorEnum.TFAC) {
                for (const [aKey, aValue] of applicantTypePairs) {
                  const licenseTypePairs = Object.entries(aValue?.licenseType ?? {});

                  applicantTypes.push({
                    key: aKey,
                    value: aKey,
                    title: aValue.name,
                    children: licenseTypePairs.map(([lKey, lValue]) => ({
                      title: `${aValue.name}${lValue.name}`,
                      value: `${aKey}:${lKey}`,
                      key: `${aKey}:${lKey}`,
                    })),
                  });
                }

                SelectLicenseTypeComponent = (
                  <TreeSelect
                    showSearch
                    dropdownStyle={{
                      maxHeight: 400,
                      minWidth: 240,
                      overflow: 'auto',
                    }}
                    placeholder="กรุณาเลือกประเภทผู้สมัครและประเภทการประกัน"
                    allowClear
                    multiple
                    treeDefaultExpandAll
                    treeData={applicantTypes}
                    treeCheckable
                  />
                );

                BaseComponent = (
                  <Col xs={24}>
                    <Form.Item
                      label={lableText}
                      className="mb-0"
                      name={[subField.name, OrganizationFormFieldNameEnum.ApplicantType]}
                      rules={[
                        {
                          required: true,
                          message: 'กรุณาเลือกประเภทผู้สมัครและประเภทการประกัน',
                        },
                      ]}
                    >
                      <SelectLicenseTypeFormItem subField={subField} subOpt={subOpt}>
                        {SelectLicenseTypeComponent}
                      </SelectLicenseTypeFormItem>
                    </Form.Item>
                  </Col>
                );
              } else if (regulatorConfig?.regulator === RegulatorEnum.TSI) {
                for (const [aKey, aValue] of applicantTypePairs) {
                  applicantTypes.push({
                    value: aKey,
                    label: aValue.name,
                  });
                }

                SelectLicenseTypeComponent = (
                  <Select
                    style={{
                      width: '100%',
                    }}
                    options={applicantTypes}
                    disabled={!licenseRenewalValue}
                  />
                );

                BaseComponent = (
                  <Col xs={24}>
                    <Form.Item
                      label={lableText}
                      className="mb-0"
                      name={[subField.name, OrganizationFormFieldNameEnum.ApplicantType]}
                      rules={[
                        {
                          required: true,
                          message: 'กรุณาเลือกประเภทผู้สมัครและประเภทการประกัน',
                        },
                      ]}
                    >
                      <SelectLicenseTypeFormItem subField={subField} subOpt={subOpt}>
                        {SelectLicenseTypeComponent}
                      </SelectLicenseTypeFormItem>
                    </Form.Item>
                  </Col>
                );
              }

              return (
                <Col key={subField.key} span={24}>
                  <Row gutter={[16, 16]}>{BaseComponent}</Row>
                </Col>
              );
            })}

            {isDisplayLicenseRenewal && (
              <Col span={24} style={{ marginTop: 8 }}>
                <Button type="dashed" onClick={() => subOpt.add()} block>
                  <Space size={8}>
                    <FontAwesomeIcon icon={fasPlus} fontSize={16} style={{ margin: 0 }} />
                    <Typography.Text>เพิ่มประเภทใบอนุญาต</Typography.Text>
                  </Space>
                </Button>
              </Col>
            )}
          </Row>
        );
      }}
    </Form.List>
  );
};

LicenseRenewalFormItem.propTypes = {
  fieldName: string,
  regulatorConfig: object,
};

export default LicenseRenewalFormItem;
