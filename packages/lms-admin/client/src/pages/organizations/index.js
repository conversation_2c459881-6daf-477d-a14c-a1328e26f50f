import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';

import { PlusOutlined } from '@ant-design/icons';
import { Col, Row, Button, Grid, Card } from 'antd';
import { useDispatch, useSelector } from 'react-redux';

import PageHeader from '@components/molecules/pageHeader';
import CreateOrganizationDrawer from '@components/organisms/drawers/createOrganizationDrawer';
import SearchOrganizationForm from '@components/organisms/forms/searchOrganizationForm';
import SearchFilterContainer from '@components/organisms/searchFilterContainer';
import OrganizationTable from '@components/organisms/tables/organizationTable';
import AdminTemplate from '@components/templates/admin';
import { pickExistsValue } from '@helpers/pickExistsValue';
import useFilterCondition from '@hooks/useFilterCondition';
import organizationAction from '@redux/organization/actions';
import { organizationStore } from '@redux/organization/state';

import Style from './style';

const { useBreakpoint } = Grid;

const { fetchOrganizationListAction } = organizationAction;

const TITLE_TEXT = 'บริษัท';

const DEFAULT_PAGE_SIZE = 20;

const OrganizationPage = () => {
  const screens = useBreakpoint();

  const [isOpenCreateDrawer, setIsOpenCreateDrawer] = useState(false);
  const searchOrganizationFormRef = useRef(null);

  const dispatch = useDispatch();

  const { fetchOrganizationList } = useSelector((state) => ({
    fetchOrganizationList: state.Organization.get(organizationStore.fetchOrganizationList),
  }));

  const { isLoading, data } = fetchOrganizationList;

  const filterCondition = useFilterCondition({ isLoading });

  const onResetFilter = useCallback(() => {
    filterCondition.setValue(filterCondition.initialValue);
    dispatch(fetchOrganizationListAction.request({ page: 1, limit: DEFAULT_PAGE_SIZE }));
    searchOrganizationFormRef.current.form.resetFields();
  }, [filterCondition]);

  const onResetInitial = useCallback(() => {
    filterCondition.setValue({});
    dispatch(fetchOrganizationListAction.request({ page: 1, limit: DEFAULT_PAGE_SIZE }));
  }, []);

  const onChangePagination = useCallback(
    (page) => {
      dispatch(fetchOrganizationListAction.request({ ...filterCondition.value, page, limit: DEFAULT_PAGE_SIZE }));
      window.scrollTo(0, 0);
    },
    [filterCondition.value],
  );

  const onSubmit = () => {
    searchOrganizationFormRef.current.form.submit();
  };

  const pagination = useMemo(
    () => ({
      total: data?.total || 0,
      page: data?.page || 1,
      limit: data?.limit || DEFAULT_PAGE_SIZE,
      onChangePagination,
    }),
    [data],
  );

  const onSearch = (values) => {
    const identifyValues = pickExistsValue(values);
    filterCondition.setValue(identifyValues);

    dispatch(
      fetchOrganizationListAction.request({
        ...identifyValues,
        page: 1,
        limit: DEFAULT_PAGE_SIZE,
      }),
    );
  };

  const ActionMenuButtons = useMemo(
    () => (
      <Button
        type="primary"
        icon={<PlusOutlined style={{ fontSize: 12 }} />}
        block={screens.xs}
        onClick={() => setIsOpenCreateDrawer(true)}
      >
        สร้างบริษัท
      </Button>
    ),
    [screens.xs],
  );

  useEffect(() => {
    filterCondition.setValue({});
    dispatch(fetchOrganizationListAction.request({ page: 1, limit: DEFAULT_PAGE_SIZE }));
  }, []);

  return (
    <Style>
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <PageHeader title={TITLE_TEXT} extra={ActionMenuButtons} />
        </Col>
        <Col span={24}>
          <Card bordered={false}>
            <Row gutter={[24, 24]}>
              <Col span={24}>
                <SearchFilterContainer
                  onReset={onResetFilter}
                  onSubmit={onSubmit}
                  isShowTotalSearchResult={filterCondition.isShowTotalSearchResult}
                >
                  <SearchOrganizationForm ref={searchOrganizationFormRef} onSearch={onSearch} onReset={onResetFilter} />
                </SearchFilterContainer>
              </Col>
              <Col span={24}>
                <OrganizationTable
                  key="domain"
                  isLoading={isLoading}
                  dataSource={data?.items || []}
                  pagination={pagination}
                  onResetInitial={onResetInitial}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <CreateOrganizationDrawer isOpen={isOpenCreateDrawer} onClose={() => setIsOpenCreateDrawer(false)} />
    </Style>
  );
};

OrganizationPage.Layout = AdminTemplate;

export default OrganizationPage;
