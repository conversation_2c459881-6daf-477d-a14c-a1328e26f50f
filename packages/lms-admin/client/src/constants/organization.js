export const CreateOrganizationStepEnum = {
  GENERAL: 1,
  OBJECTIVE: 2,
};

export const OrganizationTabEnum = Object.freeze({
  GENERAL: 'general',
  OBJECTIVE: 'objective',
  COURSE_MARKETPLACE: 'courseMarketplace',
  PLAN: 'plan',
  REPORT: 'report',
  COLUMN_SETTING: 'columnSetting',
  CERTIFICATE: 'certificate',
  CLEAN_ORGANIZATION: 'cleanOrganization',
});

export const OrganizationReportTypeEnum = {
  STANDARD: 'standard',
  CUSTOM: 'custom',
};

export const OrganizationReportTypeLocalizeMapper = {
  [OrganizationReportTypeEnum.STANDARD]: 'มาตรฐาน',
  [OrganizationReportTypeEnum.CUSTOM]: 'กำหนดเอง',
};

export const OrganizationReportMenuKeyEnum = {
  MyTeamReport: 'MyTeamReport',
  OICReport: 'OICReport',
  TSIReport: 'TSIReport',
  EnrollmentReport: 'EnrollmentReport',
  KnowledgeContentReport: 'KnowledgeContentReport',
};

export const OrganizationReportMenuKeyLocalizeMapper = {
  [OrganizationReportMenuKeyEnum.MyTeamReport]: 'รายงานทีมของฉัน',
  [OrganizationReportMenuKeyEnum.OICReport]: 'รายงานวิชาชีพประกันภัย',
  [OrganizationReportMenuKeyEnum.TSIReport]: 'รายงานวิชาชีพการลงทุน',
  [OrganizationReportMenuKeyEnum.EnrollmentReport]: 'รายงานการเรียน',
  [OrganizationReportMenuKeyEnum.KnowledgeContentReport]: 'รายงานสื่อความรู้',
};

export const OrganizationCertificateContentTypeEnum = Object.freeze({
  COURSE: 'COURSE',
  LEARNING_PATH: 'LEARNING_PATH',
  ACHIEVEMENT_COURSE: 'ACHIEVEMENT_COURSE',
  ACHIEVEMENT_LEARNING_PATH: 'ACHIEVEMENT_LEARNING_PATH',
});

export const OrganizationCertificateContentTypeMapper = Object.freeze({
  COURSE: 'หลักสูตร',
  LEARNING_PATH: 'แผนการเรียนรู้',
  ACHIEVEMENT_COURSE: 'ความสำเร็จการเรียนหลักสูตร',
  ACHIEVEMENT_LEARNING_PATH: 'ความสำเร็จการเรียนแผนการเรียนรู้',
});

export const OrganizationFormFieldNameEnum = {
  ObjectiveTypeGroup: 'objectives',
  ObjectiveType: 'objectiveType',
  LicenseRenewalGroup: 'list',
  TrainingCenterName: 'trainingCenterName',
  TrainingCenterCode: 'trainingCenterCode',
  Regulator: 'regulator',
  LicenseRenewal: 'licenseRenewal',
  ApplicantType: 'applicantType',
  LearningConfig: 'learningConfig',
  RPC: 'rpc',
  Idle: 'idle',
};
