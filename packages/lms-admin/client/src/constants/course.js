import {
  LicenseRenewalEnum,
  LicenseRenewalTHEnum,
  ApplicantTypeEnum,
  ApplicantTypeTHEnum,
  LicenseTypeEnum,
  LicenseTypeTHEnum,
} from '@iso/lms/enums/course.enum';

export const defaultLicenseRenewal = Object.freeze({
  [LicenseRenewalEnum.NEW]: {
    name: LicenseRenewalTHEnum.OIC0,
    applicantType: {
      [ApplicantTypeEnum.AGENT]: {
        name: ApplicantTypeTHEnum.AGENT,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
      [ApplicantTypeEnum.BROKER]: {
        name: ApplicantTypeTHEnum.BROKER,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
    },
  },
  [LicenseRenewalEnum.RENEW1]: {
    name: LicenseRenewalTHEnum.OIC1,
    applicantType: {
      [ApplicantTypeEnum.AGENT]: {
        name: ApplicantTypeTHEnum.AGENT,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
      [ApplicantTypeEnum.BROKER]: {
        name: ApplicantTypeTHEnum.BROKER,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
    },
  },
  [LicenseRenewalEnum.RENEW2]: {
    name: LicenseRenewalTHEnum.OIC2,
    applicantType: {
      [ApplicantTypeEnum.AGENT]: {
        name: ApplicantTypeTHEnum.AGENT,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
      [ApplicantTypeEnum.BROKER]: {
        name: ApplicantTypeTHEnum.BROKER,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
    },
  },
  [LicenseRenewalEnum.RENEW3]: {
    name: LicenseRenewalTHEnum.OIC3,
    applicantType: {
      [ApplicantTypeEnum.AGENT]: {
        name: ApplicantTypeTHEnum.AGENT,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
      [ApplicantTypeEnum.BROKER]: {
        name: ApplicantTypeTHEnum.BROKER,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
    },
  },
  [LicenseRenewalEnum.RENEW4]: {
    name: LicenseRenewalTHEnum.OIC4,
    applicantType: {
      [ApplicantTypeEnum.AGENT]: {
        name: ApplicantTypeTHEnum.AGENT,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
          [LicenseTypeEnum.BOTH]: {
            name: 'ประกันชีวิตและประกันวินาศภัย',
          },
        },
      },
      [ApplicantTypeEnum.BROKER]: {
        name: ApplicantTypeTHEnum.BROKER,
        licenseType: {
          [LicenseTypeEnum.NONLIFE]: {
            name: LicenseTypeTHEnum['NON-LIFE'],
          },
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
          [LicenseTypeEnum.BOTH]: {
            name: 'ประกันชีวิตและประกันวินาศภัย',
          },
        },
      },
    },
  },
  [LicenseRenewalEnum.UL]: {
    name: LicenseRenewalTHEnum.UL,
    applicantType: {
      [ApplicantTypeEnum.AGENT]: {
        name: ApplicantTypeTHEnum.AGENT,
        licenseType: {
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
      [ApplicantTypeEnum.BROKER]: {
        name: ApplicantTypeTHEnum.BROKER,
        licenseType: {
          [LicenseTypeEnum.LIFE]: {
            name: LicenseTypeTHEnum.LIFE,
          },
        },
      },
    },
  },
  [LicenseRenewalEnum.UK]: {
    name: LicenseRenewalTHEnum.UK,
    applicantType: {
      [ApplicantTypeEnum.AGENT]: {
        name: ApplicantTypeTHEnum.AGENT,
        licenseType: {
          [LicenseTypeEnum.UNIT_LINKED]: {
            name: LicenseTypeTHEnum.UNIT_LINKED,
          },
        },
      },
      [ApplicantTypeEnum.BROKER]: {
        name: ApplicantTypeTHEnum.BROKER,
        licenseType: {
          [LicenseTypeEnum.UNIT_LINKED]: {
            name: LicenseTypeTHEnum.UNIT_LINKED,
          },
        },
      },
    },
  },
});
