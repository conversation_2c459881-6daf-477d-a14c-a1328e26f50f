import { date } from '@domains/utils/date.util';
import { PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import { PlanPackage } from '@iso/lms/models/planPackage.model';
import { PlanPackageParams } from '@iso/lms/types/planPackage.type';
import { v4 } from 'uuid';

const createMockPlanPackage = (props?: Partial<PlanPackageParams>) => {
  return new PlanPackage({
    planId: props?.planId || v4(),
    packageId: props?.packageId || v4(),
    name: props?.name || 'plan package name',
    description: props?.description || 'description',
    type: props?.type || PackageTypeEnum.PLATFORM,
    content: props?.content || {
      totalLicense: 20,
      totalTransferLicense: 20,
      remainLicense: 20,
      remainTransferLicense: 20,
    },
    totalUsageDay: props?.totalUsageDay || 10,
    startDate: props?.startDate || date('2025-04-28T17:00:00.000+00:00').toDate(),
    gracingDate: props?.gracingDate || date('2025-04-29T17:00:00.000+00:00').toDate(),
    endDate: props?.endDate || date('2025-05-10T16:59:59.999+00:00').toDate(),
    ...props,
  });
};

export { createMockPlanPackage };
