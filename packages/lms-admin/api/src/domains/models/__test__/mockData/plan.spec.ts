import { date } from '@domains/utils/date.util';
import { SaleOrderStatusEnum } from '@iso/lms/enums/plan.enum';
import { Plan } from '@iso/lms/models/plan.model';
import { PlanParams } from '@iso/lms/types/plan.type';
import { v4 } from 'uuid';

const createMockPlan = (props?: Partial<PlanParams>) => {
  return new Plan({
    organizationId: props?.organizationId || v4(),
    name: props?.name || 'paln name',
    saleOrderStatus: props?.saleOrderStatus || SaleOrderStatusEnum.PENDING,
    startDate: props?.startDate || date('2025-04-28T17:00:00.000+00:00').toDate(),
    gracingDate: props?.gracingDate || date('2025-04-29T17:00:00.000+00:00').toDate(),
    endDate: props?.endDate || date('2025-05-10T16:59:59.999+00:00').toDate(),
    ...props,
  });
};

export { createMockPlan };
