import { IBaseRepository } from '@domains/interfaces/infrastructures/repositories/base.repository.interface';
import { EnrollmentPlanPackageLicense } from '@iso/lms/models/enrollmentPlanPackageLicense.model';
import { EnrollmentPlanPackageLicenseParams } from '@iso/lms/types/enrollmentPlanPackageLicense.type';

export interface IEnrollmentPlanPackageLicenseRepository
  extends IBaseRepository<EnrollmentPlanPackageLicense, EnrollmentPlanPackageLicenseParams> {}
