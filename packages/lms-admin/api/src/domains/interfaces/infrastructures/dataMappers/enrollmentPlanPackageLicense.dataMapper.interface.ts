import { EnrollmentPlanPackageLicense } from '@iso/lms/models/enrollmentPlanPackageLicense.model';
import { EnrollmentPlanPackageLicenseParams } from '@iso/lms/types/enrollmentPlanPackageLicense.type';

import { IDataMapper } from './base.dataMapper.interface';

export interface IEnrollmentPlanPackageLicenseDataMapper
  extends IDataMapper<
    EnrollmentPlanPackageLicense,
    EnrollmentPlanPackageLicenseParams,
    EnrollmentPlanPackageLicenseParams
  > {}
