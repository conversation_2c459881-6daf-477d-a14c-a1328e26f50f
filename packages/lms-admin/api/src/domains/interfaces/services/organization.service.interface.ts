import { InputOrganizationObjectiveParams } from '@domains/constants/types/organization.type';
import { CertificatePropertyTypeEnum } from '@iso/lms/enums/certificate.enum';
import { OrganizationCertificateProperty } from '@iso/lms/models/organizationCertificate.model';
import { CertificateParams, CertificatePropertyParams } from '@iso/lms/types/certificate.type';
import {
  OrganizationCourseObjectiveConfigParams,
  OrganizationLearningConfigParams,
} from '@iso/lms/types/organization.type';
import { OrganizationCertificatePropertyParams } from '@iso/lms/types/organizationCertificate.type';
import { PlanParams } from '@iso/lms/types/plan.type';

export interface IOrganizationService {
  convertToObjectiveTypeConfig(params: InputOrganizationObjectiveParams[]): OrganizationCourseObjectiveConfigParams[];
  convertDefaultLearningConfig(params: InputOrganizationObjectiveParams): OrganizationLearningConfigParams;
  validateDomain(domain: string): boolean;
  newOrganizationCertificatePropertiesByCertificate(
    certificate: CertificateParams,
  ): OrganizationCertificatePropertyParams[];
  isValidCompareCertificateProperties(
    certificateProperties: CertificatePropertyParams[],
    organizationCertificateProperties: OrganizationCertificatePropertyParams[],
    dynamicFields: Record<string, { value: string; type: string }>,
  ): boolean;
  updateCertificatePropertyTypeWhenIsEnabledSetting(property: {
    type: CertificatePropertyTypeEnum;
    key: string;
    isEnabledSetting: boolean;
  }): CertificatePropertyTypeEnum;
  transformToOrganizationCertificateProperties(
    certificateProperties: CertificatePropertyParams[],
    organizationCertificateProperties: OrganizationCertificateProperty[],
    dynamicFields: Record<string, { value: string; type: string }>,
  ): OrganizationCertificateProperty[];
  checkSaleOrderApproved(plans: PlanParams[]): boolean;
}
