import { OutputProductSKUCourseItemParams } from '@domains/constants/types/productSKUCourse.type';
import { ICourseService } from '@domains/interfaces/services/course.service.interface';
import { GenericID } from '@iso/constants/commonTypes';
import {
  ContentTypeEnum,
  ContentProviderTypeEnum,
  ExternalContentTypeEnum,
  LicenseRenewalEnum,
  RegulatorEnum,
  CourseObjectiveTypeEnum,
} from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { MaterialMediaTypeEnum } from '@iso/lms/enums/materialMedia.enum';
import { ProductSKUCourseTypeEnum } from '@iso/lms/enums/productSKUCourse.enum';
import { CreateCourseParams, PreCourseConfig } from '@iso/lms/types/course.type';
import { CreateCourseVersionParams, PreCourseVersionConfig } from '@iso/lms/types/courseVersion.type';
import { ProductSKUCourseParams, ProductSKUCoursePartParams } from '@iso/lms/types/productSKUCourse.type';
import randomstring from 'randomstring';

export class CourseService implements ICourseService {
  buildCreateCourse(
    payload: ProductSKUCourseParams,
    organizationId: GenericID,
    externalContentProviderType: ExternalContentTypeEnum,
  ): CreateCourseParams {
    const randomDefaultCode = randomstring.generate(8).toLocaleUpperCase();
    const { description, url, thumbnailUrl, videoPreviewImage, disableIntro: isDisableIntro, type } = payload;

    let preCourseConfig: Partial<CreateCourseParams>;
    let isEnabled = false;
    let regulator = RegulatorEnum.NONE;

    if (type === ProductSKUCourseTypeEnum.CPD) {
      preCourseConfig = PreCourseConfig[CourseObjectiveTypeEnum.TRAINING];
      regulator = null;
    } else {
      preCourseConfig = PreCourseConfig[CourseObjectiveTypeEnum.REGULAR];
      isEnabled = true;
    }

    return {
      ...preCourseConfig,
      organizationId,
      productSKUCourseId: payload.id,
      contentType: ContentTypeEnum.E_LEARNING,
      contentProviderType: ContentProviderTypeEnum.EXTERNAL,
      externalContentTypes: [externalContentProviderType],
      code: randomDefaultCode,
      description,
      url,
      regulatorInfo: {
        regulator,
        trainingCenter: '',
        licenseRenewal: LicenseRenewalEnum.NONE,
        applicantType: '',
        licenseType: [],
        isDeduct: false,
        isRequireDeductDocument: false,
      },
      thumbnailId: null,
      thumbnailUrl,
      videoPreviewImage,
      disableIntro: isDisableIntro,
      isEnabled,
    };
  }

  buildCreateCourseVersion(payload: ProductSKUCourseParams, courseId: GenericID): CreateCourseVersionParams {
    const { name, parts, type } = payload;

    let preCourseVersionConfig: Partial<CreateCourseVersionParams>;
    let status = CourseVersionStatusEnum.DRAFT;
    if (type === ProductSKUCourseTypeEnum.CPD) {
      preCourseVersionConfig = PreCourseVersionConfig[CourseObjectiveTypeEnum.TRAINING];
    } else {
      preCourseVersionConfig = PreCourseVersionConfig[CourseObjectiveTypeEnum.REGULAR];
      status = CourseVersionStatusEnum.PUBLISHED;
    }

    const {
      totalVideos,
      totalQuizzes,
      totalArticles,
      totalSurveys,
      totalDurationSec,
      totalDurationArticleSec,
      totalCourseItems,
    } = this.getTotalCourseItemsInParts(parts);

    return {
      ...preCourseVersionConfig,
      status,
      courseId,
      version: 1,
      name,
      instructorIds: [],
      totalCourseItems,
      totalVideos,
      totalQuizzes,
      totalArticles,
      totalSurveys,
      totalDurationSec,
      totalDurationArticleSec,
    };
  }

  getTotalCourseItemsInParts(parts: ProductSKUCoursePartParams[]): OutputProductSKUCourseItemParams {
    let videoIndex = 0;
    let quizIndex = 0;
    let articleIndex = 0;
    let surveyIndex = 0;
    let infographicIndex = 0;
    let itemIndex = 0;
    let totalDurationSec = 0;
    let totalDurationArticleSec = 0;

    for (const i in parts) {
      const part = parts[i];

      for (const j in part.courseItems) {
        const courseItem = part.courseItems[j];
        itemIndex++;

        switch (courseItem.type) {
          case MaterialMediaTypeEnum.QUIZ:
            quizIndex++;
            break;
          case MaterialMediaTypeEnum.ARTICLE:
            articleIndex++;
            totalDurationArticleSec += courseItem.duration;
            break;
          case MaterialMediaTypeEnum.SURVEY:
            surveyIndex++;
            break;
          case MaterialMediaTypeEnum.INFOGRAPHIC:
            infographicIndex++;
            break;
          case MaterialMediaTypeEnum.VIDEO:
          default:
            videoIndex++;
            totalDurationSec += courseItem.duration;
            break;
        }
      }
    }

    return {
      totalVideos: videoIndex,
      totalQuizzes: quizIndex,
      totalArticles: articleIndex,
      totalSurveys: surveyIndex,
      totalInfographics: infographicIndex,
      totalDurationSec,
      totalDurationArticleSec,
      totalCourseItems: itemIndex,
    };
  }
}
