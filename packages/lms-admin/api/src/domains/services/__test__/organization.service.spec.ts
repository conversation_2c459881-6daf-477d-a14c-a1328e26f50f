import { InputOrganizationObjectiveParams } from '@domains/constants/types/organization.type';
import { createMockPlan } from '@domains/models/__test__/mockData/plan.spec';
import { OrganizationService } from '@domains/services/organization.service';
import {
  ApplicantTypeEnum,
  ApplicantTypeTHEnum,
  CourseObjectiveTypeEnum,
  LicenseRenewalEnum,
  LicenseRenewalTHEnum,
  LicenseTypeEnum,
  LicenseTypeTHEnum,
  RegulatorEnum,
} from '@iso/lms/enums/course.enum';
import { SaleOrderStatusEnum } from '@iso/lms/enums/plan.enum';

describe('OrganizationService', () => {
  let organizationService: OrganizationService;

  beforeEach(() => {
    organizationService = new OrganizationService();
  });

  describe('convertToRegulatorConfig', () => {
    it('when they are 1 objective, expect correctly', () => {
      const input: InputOrganizationObjectiveParams[] = [
        {
          courseObjective: CourseObjectiveTypeEnum.REGULAR,
          regulator: RegulatorEnum.NONE,
          trainingCenterConfigs: [],
          list: [],
          learningConfig: {
            idle: { isEnabled: false, timeoutMinute: 25, stayTimeoutMinute: 5 },
            rpc: {
              isEnabled: false,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: false,
            isLivenessEnabled: false,
          },
        },
      ];

      const expectedOutput = [
        {
          objectiveType: CourseObjectiveTypeEnum.REGULAR,
          regulators: [],
          trainingCenters: [],
          defaultLearningConfig: {
            idle: {
              isEnabled: false,
              timeoutMinute: 25,
              stayTimeoutMinute: 5,
            },
            rpc: {
              isEnabled: false,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: false,
            isLivenessEnabled: false,
          },
        },
      ];

      const data = organizationService.convertToObjectiveTypeConfig(input);
      expect(data).toEqual(expectedOutput);
    });

    it('when they are 1 objective and 2 regulator and 3 trainingCenter, expect correctly', () => {
      const input: InputOrganizationObjectiveParams[] = [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.TSI,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'SKILLLANE',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 10,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'SkillLane',
              trainingCenterCode: 'SKILLLANE',
              applicantType: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
            },
          ],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.OIC,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'TII',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              trainingCenterCode: 'TIPA',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW1
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW2
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW3
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            //
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW1
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW2
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW3
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW4
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.BOTH,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.BOTH,
            },
          ],
        },
      ];

      const expectedOutput = [
        {
          objectiveType: CourseObjectiveTypeEnum.TRAINING,
          regulators: [
            {
              name: RegulatorEnum.TSI,
              trainingCenterKeys: ['SKILLLANE'],
            },
            {
              name: RegulatorEnum.OIC,
              trainingCenterKeys: ['TII', 'TIPA'],
            },
          ],
          trainingCenters: [
            {
              key: 'SKILLLANE',
              name: 'SkillLane',
              regulator: RegulatorEnum.TSI,
              licenseRenewals: [],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
                  name: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
                  licenseRenewalKeys: [],
                },
              ],
              licenseTypes: [],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 10,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              key: 'TII',
              name: 'TII',
              regulator: RegulatorEnum.OIC,
              licenseRenewals: [
                {
                  key: LicenseRenewalEnum.NEW,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
                },
                {
                  key: LicenseRenewalEnum.RENEW1,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW1],
                },
                {
                  key: LicenseRenewalEnum.RENEW2,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW2],
                },
                {
                  key: LicenseRenewalEnum.RENEW3,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW3],
                },
              ],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.AGENT,
                  name: ApplicantTypeTHEnum.AGENT,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                },
                {
                  key: ApplicantTypeEnum.BROKER,
                  name: ApplicantTypeTHEnum.BROKER,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                },
              ],
              licenseTypes: [
                {
                  key: LicenseTypeEnum.LIFE,
                  name: LicenseTypeTHEnum.LIFE,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
                {
                  key: LicenseTypeEnum.NONLIFE,
                  name: LicenseTypeTHEnum['NON-LIFE'],
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
              ],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              key: 'TIPA',
              name: 'TIPA',
              regulator: RegulatorEnum.OIC,
              licenseRenewals: [
                {
                  key: LicenseRenewalEnum.NEW,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
                },
                {
                  key: LicenseRenewalEnum.RENEW1,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW1],
                },
                {
                  key: LicenseRenewalEnum.RENEW2,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW2],
                },
                {
                  key: LicenseRenewalEnum.RENEW3,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW3],
                },
                {
                  key: LicenseRenewalEnum.RENEW4,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW4],
                },
              ],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.AGENT,
                  name: ApplicantTypeTHEnum.AGENT,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                  ],
                },
                {
                  key: ApplicantTypeEnum.BROKER,
                  name: ApplicantTypeTHEnum.BROKER,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                  ],
                },
              ],
              licenseTypes: [
                {
                  key: LicenseTypeEnum.LIFE,
                  name: LicenseTypeTHEnum.LIFE,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
                {
                  key: LicenseTypeEnum.NONLIFE,
                  name: LicenseTypeTHEnum['NON-LIFE'],
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
                {
                  key: LicenseTypeEnum.BOTH,
                  name: LicenseTypeTHEnum.BOTH,
                  licenseRenewalKeys: [LicenseRenewalEnum.RENEW4],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
              ],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          defaultLearningConfig: {
            idle: {
              isEnabled: true,
              timeoutMinute: 25,
              stayTimeoutMinute: 5,
            },
            rpc: {
              isEnabled: true,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: true,
            isLivenessEnabled: true,
          },
        },
      ];

      const data = organizationService.convertToObjectiveTypeConfig(input);
      expect(data).toEqual(expectedOutput);
    });

    it('when they are 2 objective and 1 regulator and 1 trainingCenter, expect correctly', () => {
      const input: InputOrganizationObjectiveParams[] = [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.OIC,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'TII',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW1
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW2
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW3
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW4
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // UK
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.UK,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.UK,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            // UL
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.UL,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.UL,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
          ],
        },
      ];

      const expectedOutput = [
        {
          objectiveType: CourseObjectiveTypeEnum.TRAINING,
          regulators: [
            {
              name: RegulatorEnum.OIC,
              trainingCenterKeys: ['TII'],
            },
          ],
          trainingCenters: [
            {
              key: 'TII',
              name: 'TII',
              regulator: RegulatorEnum.OIC,
              licenseRenewals: [
                {
                  key: LicenseRenewalEnum.NEW,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
                },
                {
                  key: LicenseRenewalEnum.RENEW1,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW1],
                },
                {
                  key: LicenseRenewalEnum.RENEW2,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW2],
                },
                {
                  key: LicenseRenewalEnum.RENEW3,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW3],
                },
                {
                  key: LicenseRenewalEnum.RENEW4,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW4],
                },
                {
                  key: LicenseRenewalEnum.UK,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.UK],
                },
                {
                  key: LicenseRenewalEnum.UL,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.UL],
                },
              ],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.AGENT,
                  name: ApplicantTypeTHEnum.AGENT,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                    LicenseRenewalEnum.UK,
                    LicenseRenewalEnum.UL,
                  ],
                },
                {
                  key: ApplicantTypeEnum.BROKER,
                  name: ApplicantTypeTHEnum.BROKER,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                    LicenseRenewalEnum.UK,
                    LicenseRenewalEnum.UL,
                  ],
                },
              ],
              licenseTypes: [
                {
                  key: LicenseTypeEnum.LIFE,
                  name: LicenseTypeTHEnum.LIFE,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                    LicenseRenewalEnum.UK,
                    LicenseRenewalEnum.UL,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
                {
                  key: LicenseTypeEnum.NONLIFE,
                  name: LicenseTypeTHEnum['NON-LIFE'],
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
              ],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          defaultLearningConfig: {
            idle: {
              isEnabled: true,
              timeoutMinute: 25,
              stayTimeoutMinute: 5,
            },
            rpc: {
              isEnabled: true,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: true,
            isLivenessEnabled: true,
          },
        },
      ];

      const data = organizationService.convertToObjectiveTypeConfig(input);
      expect(data).toEqual(expectedOutput);
    });

    it('when they are 2 objective and 2 regulator and 3 trainingCenter, expect correctly', () => {
      const input: InputOrganizationObjectiveParams[] = [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.TSI,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'SKILLLANE',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 10,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'SkillLane',
              trainingCenterCode: 'SKILLLANE',
              applicantType: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
            },
          ],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.OIC,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'TII',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              trainingCenterCode: 'TIPA',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW1
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW2
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW3
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TII',
              trainingCenterCode: 'TII',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            //
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW1
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW2
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW2,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW3
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW3,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            // RENEW4
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.BOTH,
            },
            {
              trainingCenterName: 'TIPA',
              trainingCenterCode: 'TIPA',
              licenseRenewal: LicenseRenewalEnum.RENEW4,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.BOTH,
            },
          ],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.REGULAR,
          regulator: RegulatorEnum.NONE,
          trainingCenterConfigs: [],
          list: [],
          learningConfig: {
            idle: { isEnabled: false, timeoutMinute: 25, stayTimeoutMinute: 5 },
            rpc: {
              isEnabled: false,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: false,
            isLivenessEnabled: false,
          },
        },
      ];

      const expectedOutput = [
        {
          objectiveType: CourseObjectiveTypeEnum.TRAINING,
          regulators: [
            {
              name: RegulatorEnum.TSI,
              trainingCenterKeys: ['SKILLLANE'],
            },
            {
              name: RegulatorEnum.OIC,
              trainingCenterKeys: ['TII', 'TIPA'],
            },
          ],
          trainingCenters: [
            {
              key: 'SKILLLANE',
              name: 'SkillLane',
              regulator: RegulatorEnum.TSI,
              licenseRenewals: [],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
                  name: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
                  licenseRenewalKeys: [],
                },
              ],
              licenseTypes: [],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 10,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              key: 'TII',
              name: 'TII',
              regulator: RegulatorEnum.OIC,
              licenseRenewals: [
                {
                  key: LicenseRenewalEnum.NEW,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
                },
                {
                  key: LicenseRenewalEnum.RENEW1,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW1],
                },
                {
                  key: LicenseRenewalEnum.RENEW2,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW2],
                },
                {
                  key: LicenseRenewalEnum.RENEW3,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW3],
                },
              ],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.AGENT,
                  name: ApplicantTypeTHEnum.AGENT,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                },
                {
                  key: ApplicantTypeEnum.BROKER,
                  name: ApplicantTypeTHEnum.BROKER,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                },
              ],
              licenseTypes: [
                {
                  key: LicenseTypeEnum.LIFE,
                  name: LicenseTypeTHEnum.LIFE,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
                {
                  key: LicenseTypeEnum.NONLIFE,
                  name: LicenseTypeTHEnum['NON-LIFE'],
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
              ],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              key: 'TIPA',
              name: 'TIPA',
              regulator: RegulatorEnum.OIC,
              licenseRenewals: [
                {
                  key: LicenseRenewalEnum.NEW,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
                },
                {
                  key: LicenseRenewalEnum.RENEW1,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW1],
                },
                {
                  key: LicenseRenewalEnum.RENEW2,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW2],
                },
                {
                  key: LicenseRenewalEnum.RENEW3,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW3],
                },
                {
                  key: LicenseRenewalEnum.RENEW4,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW4],
                },
              ],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.AGENT,
                  name: ApplicantTypeTHEnum.AGENT,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                  ],
                },
                {
                  key: ApplicantTypeEnum.BROKER,
                  name: ApplicantTypeTHEnum.BROKER,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                    LicenseRenewalEnum.RENEW4,
                  ],
                },
              ],
              licenseTypes: [
                {
                  key: LicenseTypeEnum.LIFE,
                  name: LicenseTypeTHEnum.LIFE,
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
                {
                  key: LicenseTypeEnum.NONLIFE,
                  name: LicenseTypeTHEnum['NON-LIFE'],
                  licenseRenewalKeys: [
                    LicenseRenewalEnum.NEW,
                    LicenseRenewalEnum.RENEW1,
                    LicenseRenewalEnum.RENEW2,
                    LicenseRenewalEnum.RENEW3,
                  ],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
                {
                  key: LicenseTypeEnum.BOTH,
                  name: LicenseTypeTHEnum.BOTH,
                  licenseRenewalKeys: [LicenseRenewalEnum.RENEW4],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                },
              ],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          defaultLearningConfig: {
            idle: {
              isEnabled: true,
              timeoutMinute: 25,
              stayTimeoutMinute: 5,
            },
            rpc: {
              isEnabled: true,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: true,
            isLivenessEnabled: true,
          },
        },
        {
          objectiveType: CourseObjectiveTypeEnum.REGULAR,
          regulators: [],
          trainingCenters: [],
          defaultLearningConfig: {
            idle: {
              isEnabled: false,
              timeoutMinute: 25,
              stayTimeoutMinute: 5,
            },
            rpc: {
              isEnabled: false,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: false,
            isLivenessEnabled: false,
          },
        },
      ];

      const data = organizationService.convertToObjectiveTypeConfig(input);
      expect(data).toEqual(expectedOutput);
    });

    it('when they are 2 objective and 2 regulator and 4 trainingCenter, expect correctly', () => {
      const input: InputOrganizationObjectiveParams[] = [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.OIC,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'AAA',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              trainingCenterCode: 'BBB',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'บริษัท A',
              trainingCenterCode: 'AAA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'บริษัท A',
              trainingCenterCode: 'AAA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'บริษัท A',
              trainingCenterCode: 'AAA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'บริษัท A',
              trainingCenterCode: 'AAA',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
            {
              trainingCenterName: 'บริษัท A',
              trainingCenterCode: 'AAA',
              licenseRenewal: LicenseRenewalEnum.RENEW1,
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
            },
            {
              trainingCenterName: 'บริษัท B',
              trainingCenterCode: 'BBB',
              licenseRenewal: LicenseRenewalEnum.NEW,
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
            },
          ],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.TSI,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'AAA',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 10,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'บริษัท A',
              trainingCenterCode: 'AAA',
              applicantType: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
            },
          ],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.NONE,
          trainingCenterConfigs: [
            {
              trainingCenterCode: 'DDD',
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          list: [
            {
              trainingCenterName: 'บริษัท D',
              trainingCenterCode: 'DDD',
            },
          ],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.REGULAR,
          regulator: RegulatorEnum.NONE,
          trainingCenterConfigs: [],
          list: [],
          learningConfig: {
            idle: { isEnabled: false, timeoutMinute: 25, stayTimeoutMinute: 5 },
            rpc: {
              isEnabled: false,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: false,
            isLivenessEnabled: false,
          },
        },
      ];

      const expectedOutput = [
        {
          objectiveType: CourseObjectiveTypeEnum.TRAINING,
          regulators: [
            {
              name: RegulatorEnum.OIC,
              trainingCenterKeys: ['AAA', 'BBB'],
            },
            {
              name: RegulatorEnum.TSI,
              trainingCenterKeys: ['AAA'],
            },
            {
              name: '',
              trainingCenterKeys: ['DDD'],
            },
          ],
          trainingCenters: [
            {
              key: 'AAA',
              name: 'บริษัท A',
              regulator: RegulatorEnum.OIC,
              licenseRenewals: [
                {
                  key: LicenseRenewalEnum.NEW,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
                },
                {
                  key: LicenseRenewalEnum.RENEW1,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW1],
                },
              ],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.BROKER,
                  name: ApplicantTypeTHEnum.BROKER,
                  licenseRenewalKeys: [LicenseRenewalEnum.NEW, LicenseRenewalEnum.RENEW1],
                },
                {
                  key: ApplicantTypeEnum.AGENT,
                  name: ApplicantTypeTHEnum.AGENT,
                  licenseRenewalKeys: [LicenseRenewalEnum.NEW],
                },
              ],
              licenseTypes: [
                {
                  key: LicenseTypeEnum.LIFE,
                  name: LicenseTypeTHEnum.LIFE,
                  licenseRenewalKeys: [LicenseRenewalEnum.NEW, LicenseRenewalEnum.RENEW1],
                  applicantTypeKeys: [ApplicantTypeEnum.BROKER, ApplicantTypeEnum.AGENT],
                },
                {
                  key: LicenseTypeEnum.NONLIFE,
                  name: LicenseTypeTHEnum['NON-LIFE'],
                  licenseRenewalKeys: [LicenseRenewalEnum.NEW],
                  applicantTypeKeys: [ApplicantTypeEnum.BROKER, ApplicantTypeEnum.AGENT],
                },
              ],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              key: 'BBB',
              name: 'บริษัท B',
              regulator: RegulatorEnum.OIC,
              licenseRenewals: [
                {
                  key: LicenseRenewalEnum.NEW,
                  name: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
                },
              ],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.AGENT,
                  name: ApplicantTypeTHEnum.AGENT,
                  licenseRenewalKeys: [LicenseRenewalEnum.NEW],
                },
              ],
              licenseTypes: [
                {
                  key: LicenseTypeEnum.NONLIFE,
                  name: LicenseTypeTHEnum['NON-LIFE'],
                  licenseRenewalKeys: [LicenseRenewalEnum.NEW],
                  applicantTypeKeys: [ApplicantTypeEnum.AGENT],
                },
              ],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              key: 'AAA',
              name: 'บริษัท A',
              regulator: RegulatorEnum.TSI,
              licenseRenewals: [],
              applicantTypes: [
                {
                  key: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
                  name: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
                  licenseRenewalKeys: [],
                },
              ],
              licenseTypes: [],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 10,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
            {
              key: 'DDD',
              name: 'บริษัท D',
              regulator: RegulatorEnum.NONE,
              licenseRenewals: [],
              applicantTypes: [],
              licenseTypes: [],
              learningConfig: {
                idle: {
                  isEnabled: true,
                  timeoutMinute: 25,
                  stayTimeoutMinute: 2,
                },
                rpc: {
                  isEnabled: true,
                  durationMinute: 60,
                  delayMinute: 20,
                  stayTimeoutMinute: 5,
                  gapIdleMinute: 10,
                  forwardDelayMinute: 10,
                },
                isIdentityVerificationEnabled: true,
                isLivenessEnabled: true,
              },
            },
          ],
          defaultLearningConfig: {
            idle: {
              isEnabled: true,
              timeoutMinute: 25,
              stayTimeoutMinute: 5,
            },
            rpc: {
              isEnabled: true,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: true,
            isLivenessEnabled: true,
          },
        },
        {
          objectiveType: CourseObjectiveTypeEnum.REGULAR,
          regulators: [],
          trainingCenters: [],
          defaultLearningConfig: {
            idle: {
              isEnabled: false,
              timeoutMinute: 25,
              stayTimeoutMinute: 5,
            },
            rpc: {
              isEnabled: false,
              durationMinute: 60,
              delayMinute: 20,
              stayTimeoutMinute: 5,
              gapIdleMinute: 10,
              forwardDelayMinute: 10,
            },
            isIdentityVerificationEnabled: false,
            isLivenessEnabled: false,
          },
        },
      ];

      const data = organizationService.convertToObjectiveTypeConfig(input);
      expect(data).toEqual(expectedOutput);
    });
  });

  describe('checkSaleOrderApproved', () => {
    it('found SO approved, expect: true', () => {
      const mockPlanPackages = [
        createMockPlan({ saleOrderStatus: SaleOrderStatusEnum.APPROVED }),
        createMockPlan(),
        createMockPlan(),
      ];
      const isSaleOrderApproved = organizationService.checkSaleOrderApproved(mockPlanPackages);
      expect(isSaleOrderApproved).toEqual(true);
    });

    it('not found SO approve, expect: false', () => {
      const mockPlanPackages = [createMockPlan(), createMockPlan(), createMockPlan()];
      const isSaleOrderApproved = organizationService.checkSaleOrderApproved(mockPlanPackages);
      expect(isSaleOrderApproved).toEqual(false);
    });

    it('empty array input, expect: false', () => {
      const mockPlanPackages = [];
      const isSaleOrderApproved = organizationService.checkSaleOrderApproved(mockPlanPackages);
      expect(isSaleOrderApproved).toEqual(false);
    });
  });
});
