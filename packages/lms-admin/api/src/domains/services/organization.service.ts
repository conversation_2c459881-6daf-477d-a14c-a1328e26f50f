import { LicenseRenewalMapping, ApplicantTypeMapping, LicenseTypeMapping } from '@domains/constants/types/course.type';
import {
  InputOrganizationObjectiveParams,
  InputOrganizationTrainingCenterParams,
  InputOrganizationTrainingCenterConfigParams,
} from '@domains/constants/types/organization.type';
import { IOrganizationService } from '@domains/interfaces/services/organization.service.interface';
import { arrayToHashMapByKey } from '@domains/utils/collection.util';
import { convertStringsToNumbers } from '@domains/utils/convertData.util';
import { DateFormat } from '@domains/utils/date.util';
import { CertificatePropertyTypeEnum } from '@iso/lms/enums/certificate.enum';
import {
  CourseObjectiveTypeEnum,
  RegulatorEnum,
  LicenseRenewalEnum,
  ApplicantTypeEnum,
  LicenseTypeEnum,
} from '@iso/lms/enums/course.enum';
import { SaleOrderStatusEnum } from '@iso/lms/enums/plan.enum';
import { OrganizationCertificateProperty } from '@iso/lms/models/organizationCertificate.model';
import {
  CertificateParams,
  CertificatePropertyParams,
  CertificatePropertyValueParams,
} from '@iso/lms/types/certificate.type';
import {
  OrganizationCourseObjectiveConfigParams,
  OrganizationTrainingCenterParams,
  OrganizationRegulatorParams,
  OrganizationLearningConfigParams,
  defaultTrainingLearningConfig,
} from '@iso/lms/types/organization.type';
import { OrganizationCertificatePropertyParams } from '@iso/lms/types/organizationCertificate.type';
import { PlanParams } from '@iso/lms/types/plan.type';
import { Injectable } from '@nestjs/common';
import { chain, isNil, isUndefined } from 'lodash';
import validator from 'validator';

@Injectable()
export class OrganizationService implements IOrganizationService {
  convertToObjectiveTypeConfig(params: InputOrganizationObjectiveParams[]): OrganizationCourseObjectiveConfigParams[] {
    const objectiveMap = new Map<
      CourseObjectiveTypeEnum,
      {
        regulators: OrganizationRegulatorParams[];
        trainingCenters: OrganizationTrainingCenterParams[];
        defaultLearningConfig: any;
      }
    >();

    for (const item of params) {
      const { list, trainingCenterConfigs } = item;
      const courseObjective = CourseObjectiveTypeEnum[item.courseObjective];
      const regulator = RegulatorEnum[item.regulator] ?? RegulatorEnum.NONE;

      if (!objectiveMap.has(courseObjective)) {
        objectiveMap.set(courseObjective, {
          regulators: [],
          trainingCenters: [],
          defaultLearningConfig: this.convertDefaultLearningConfig(item),
        });
      }

      const objective = objectiveMap.get(courseObjective);

      const { regulatorsMap, trainingCentersMap } = this.convertRegulatorTrainingCenterConfig(
        list,
        trainingCenterConfigs,
        regulator,
      );

      // Convert regulatorsMap to array
      for (const [name, trainingCenterKeysSet] of regulatorsMap.entries()) {
        objective.regulators.push({
          name,
          trainingCenterKeys: Array.from(trainingCenterKeysSet),
        });
      }

      const trainingCenters = Array.from(trainingCentersMap.values()).map((val) => {
        const newApplicantTypes = val.applicantTypes?.map((applicantType) => ({
          key: applicantType.key,
          name: applicantType.name,
          licenseRenewalKeys: [...new Set(applicantType.licenseRenewalKeys.filter(Boolean))], // Remove duplicates and falsy values
        }));
        const newLicenseTypes = val.licenseTypes?.map((licenseType) => ({
          key: licenseType.key,
          name: licenseType.name,
          licenseRenewalKeys: [...new Set(licenseType.licenseRenewalKeys.filter(Boolean))], // Remove duplicates and falsy values
          applicantTypeKeys: [...new Set(licenseType.applicantTypeKeys.filter(Boolean))], // Remove duplicates and falsy values
        }));

        return {
          ...val,
          applicantTypes: newApplicantTypes,
          licenseTypes: newLicenseTypes,
        };
      });

      // Convert trainingCentersMap to array
      objective.trainingCenters.push(...trainingCenters);
    }

    return Array.from(objectiveMap.entries()).map(
      ([objectiveType, { regulators, trainingCenters, defaultLearningConfig }]) => {
        return {
          objectiveType,
          regulators,
          trainingCenters,
          defaultLearningConfig,
        };
      },
    );
  }

  convertDefaultLearningConfig(params: InputOrganizationObjectiveParams): OrganizationLearningConfigParams {
    let result: OrganizationLearningConfigParams;

    if (params.courseObjective === CourseObjectiveTypeEnum.REGULAR) {
      result = convertStringsToNumbers(params.learningConfig);
    } else {
      result = defaultTrainingLearningConfig;
    }

    return result;
  }

  validateDomain(domain: string): boolean {
    if (domain) {
      const isFQDNValidate = validator.isFQDN(domain, { require_tld: false });
      const isCustomValidate = domain.length > 2 && !domain.includes('--') && /^[a-z0-9-]+$/.test(domain);
      return isFQDNValidate && isCustomValidate;
    }

    return false;
  }

  newOrganizationCertificatePropertiesByCertificate(
    certificate: CertificateParams,
  ): OrganizationCertificatePropertyParams[] {
    const properties = certificate.properties || [];
    const enabledCertificateProperties = properties.filter((_property) => _property.isEnabledSetting);
    const result = enabledCertificateProperties.map((_property) =>
      this.newDynamicOrganizationCertificateProperty(_property),
    );

    return result;
  }

  updateCertificatePropertyTypeWhenIsEnabledSetting(property: {
    type: CertificatePropertyTypeEnum;
    key: string;
    isEnabledSetting: boolean;
  }): CertificatePropertyTypeEnum {
    const { type, key, isEnabledSetting } = property;

    if (isEnabledSetting && type === CertificatePropertyTypeEnum.COLUMN_SETTING) {
      const keysNotChange = new Set([]); //todo implement keys not change type when extra condition ('zone', 'fai, etc.)

      if (!keysNotChange.has(key)) {
        return CertificatePropertyTypeEnum.TEXT;
      }
    }

    return type;
  }

  isValidCompareCertificateProperties(
    certificateProperties: CertificatePropertyParams[],
    organizationCertificateProperties: OrganizationCertificatePropertyParams[],
    dynamicFields: Record<string, { value: string; type: string }>,
  ): boolean {
    const organizationCertificatePropertiesMappingByKey = arrayToHashMapByKey<
      OrganizationCertificatePropertyParams,
      string
    >(organizationCertificateProperties, 'key');

    return certificateProperties
      .filter(({ isEnabledSetting }) => isEnabledSetting)
      .every((_property) => {
        if (_property.isEnabledSetting) {
          _property.type = this.updateCertificatePropertyTypeWhenIsEnabledSetting(_property);
        }

        if (_property.type === CertificatePropertyTypeEnum.COLUMN_SETTING) {
          const organizationCertificateProperty = organizationCertificatePropertiesMappingByKey.get(_property.key);

          if (!organizationCertificateProperty?.columnSettingKey) return false;

          return true;
        }

        const dynamicField = dynamicFields[_property.key];
        if (!dynamicField) return false;
        return _property.type === dynamicField.type;
      });
  }

  transformToOrganizationCertificateProperties(
    certificateProperties: CertificatePropertyParams[],
    organizationCertificateProperties: OrganizationCertificateProperty[],
    dynamicFields: Record<string, { value: string; type: string }>,
  ): OrganizationCertificateProperty[] {
    const enabledCertificateProperties = certificateProperties.filter((_property) => _property.isEnabledSetting);

    const organizationCertificatePropertiesMappingByKey = arrayToHashMapByKey<OrganizationCertificateProperty, string>(
      organizationCertificateProperties,
      'key',
    );

    const result = chain(enabledCertificateProperties)
      .map((_property) => {
        const dynamicField = dynamicFields[_property.key];
        const oldProperty = organizationCertificatePropertiesMappingByKey.get(_property.key);
        let { value } = _property;
        const organizationCertificatePropertyType = oldProperty
          ? oldProperty.type
          : this.updateCertificatePropertyTypeWhenIsEnabledSetting({
              type: _property.type,
              key: _property.key,
              isEnabledSetting: _property.isEnabledSetting,
            });

        const isValidUpdateByDynamicFieldsPropertyType = [
          CertificatePropertyTypeEnum.CURRENT_DATE,
          CertificatePropertyTypeEnum.IMAGE_URL,
          CertificatePropertyTypeEnum.TEXT,
        ].includes(organizationCertificatePropertyType);

        const isValidUpdateByAdminPropertyType = [CertificatePropertyTypeEnum.COLUMN_SETTING].includes(
          organizationCertificatePropertyType,
        );

        const isUndefinedDynamicField = isUndefined(dynamicField);

        if (isValidUpdateByAdminPropertyType && oldProperty) {
          return oldProperty;
        }

        if (isUndefinedDynamicField && isValidUpdateByDynamicFieldsPropertyType) {
          return null;
        }

        if (isValidUpdateByDynamicFieldsPropertyType) {
          value = this.getOrganizationCertificatePropertyDefaultValue(
            dynamicField?.value,
            organizationCertificatePropertyType,
          );
        }

        if (!isValidUpdateByDynamicFieldsPropertyType && !isValidUpdateByAdminPropertyType) {
          return null;
        }

        if (dynamicField.type !== organizationCertificatePropertyType) {
          return null;
        }

        const params: OrganizationCertificatePropertyParams = {
          key: _property.key,
          type: organizationCertificatePropertyType,
          columnSettingKey: null,
          value,
        };

        if (_property.type === CertificatePropertyTypeEnum.IMAGE_URL) {
          params.mediaId = value as string;
          params.value = null;
        }

        return new OrganizationCertificateProperty(params);
      })
      .compact()
      .value();

    return result;
  }

  checkSaleOrderApproved(plans: PlanParams[]) {
    return plans.some((item) => item.saleOrderStatus === SaleOrderStatusEnum.APPROVED);
  }

  private convertRegulatorTrainingCenterConfig(
    list: InputOrganizationTrainingCenterParams[],
    trainingCenterConfigs: InputOrganizationTrainingCenterConfigParams[],
    regulator: string,
  ): {
    regulatorsMap: Map<string, Set<string>>;
    trainingCentersMap: Map<string, OrganizationTrainingCenterParams>;
  } {
    const regulatorsMap = new Map<string, Set<string>>();
    const trainingCentersMap = new Map<string, OrganizationTrainingCenterParams>();

    for (const listItem of list) {
      const { trainingCenterName, trainingCenterCode } = listItem;

      const licenseRenewal = listItem.licenseRenewal as LicenseRenewalEnum;
      const applicantType = listItem.applicantType as ApplicantTypeEnum;
      const licenseType = listItem.licenseType as LicenseTypeEnum;

      // Add training center keys to regulator
      if (!regulatorsMap.has(regulator)) {
        regulatorsMap.set(regulator, new Set());
      }
      regulatorsMap.get(regulator).add(trainingCenterCode);

      // Add training center
      let trainingCenter = trainingCentersMap.get(trainingCenterCode);
      if (!trainingCenter) {
        const trainingCenterConfig = trainingCenterConfigs.find((val) => val.trainingCenterCode === trainingCenterCode);
        trainingCenter = {
          key: trainingCenterCode,
          name: trainingCenterName,
          regulator,
          licenseRenewals: [],
          applicantTypes: [],
          licenseTypes: [],
          learningConfig: convertStringsToNumbers(trainingCenterConfig.learningConfig),
        };
        trainingCentersMap.set(trainingCenterCode, trainingCenter);
      }

      this.addLicenseRenewalToTrainingCenter(trainingCenter, licenseRenewal);

      this.addApplicantTypeToTrainingCenter(
        trainingCenter,
        trainingCentersMap,
        trainingCenterCode,
        licenseRenewal,
        applicantType,
      );

      this.addLicenseTypeToTrainingCenter(
        trainingCenter,
        trainingCentersMap,
        trainingCenterCode,
        licenseRenewal,
        applicantType,
        licenseType,
      );
    }

    return {
      regulatorsMap,
      trainingCentersMap,
    };
  }

  private addLicenseRenewalToTrainingCenter(
    trainingCenter: OrganizationTrainingCenterParams,
    licenseRenewal: LicenseRenewalEnum,
  ) {
    if (!licenseRenewal) return;

    const findLicenseRenewal = trainingCenter.licenseRenewals.find((val) => val.key === licenseRenewal);
    if (!findLicenseRenewal) {
      trainingCenter.licenseRenewals.push({
        key: licenseRenewal,
        name: LicenseRenewalMapping[licenseRenewal],
      });
    }
  }

  private addApplicantTypeToTrainingCenter(
    trainingCenter: OrganizationTrainingCenterParams,
    trainingCentersMap: Map<string, OrganizationTrainingCenterParams>,
    trainingCenterCode: string,
    licenseRenewal: LicenseRenewalEnum,
    applicantType: ApplicantTypeEnum,
  ) {
    if (!applicantType) return;

    const findApplicantType = trainingCenter.applicantTypes.find((val) => val.key === applicantType);

    if (!findApplicantType) {
      trainingCentersMap.get(trainingCenterCode).applicantTypes.push({
        key: ApplicantTypeEnum[applicantType],
        name: ApplicantTypeMapping[applicantType],
        licenseRenewalKeys: [licenseRenewal],
      });
      return;
    }

    findApplicantType.licenseRenewalKeys.push(licenseRenewal);
  }

  private addLicenseTypeToTrainingCenter(
    trainingCenter: OrganizationTrainingCenterParams,
    trainingCentersMap: Map<string, OrganizationTrainingCenterParams>,
    trainingCenterCode: string,
    licenseRenewal: LicenseRenewalEnum,
    applicantType: ApplicantTypeEnum,
    licenseType: LicenseTypeEnum,
  ) {
    if (!licenseType) return;

    const findLicenseType = trainingCenter.licenseTypes.find((val) => val.key === licenseType);
    if (!findLicenseType) {
      trainingCentersMap.get(trainingCenterCode).licenseTypes.push({
        key: licenseType,
        name: LicenseTypeMapping[licenseType],
        licenseRenewalKeys: [licenseRenewal],
        applicantTypeKeys: [applicantType],
      });
      return;
    }
    findLicenseType.licenseRenewalKeys.push(licenseRenewal);
    findLicenseType.applicantTypeKeys.push(applicantType);
  }

  private newDynamicOrganizationCertificateProperty(
    certificateProperty: CertificatePropertyParams,
  ): OrganizationCertificatePropertyParams {
    const { isEnabledSetting, ...property } = certificateProperty;
    if (isEnabledSetting) {
      property.type = this.updateCertificatePropertyTypeWhenIsEnabledSetting(certificateProperty);
      property.value = this.getOrganizationCertificatePropertyDefaultValue(property.value, property.type);
      property.columnSettingKey = null;
    }
    return property as OrganizationCertificatePropertyParams;
  }

  private getOrganizationCertificatePropertyDefaultValue(
    dynamicFieldValue: CertificatePropertyValueParams,
    type: CertificatePropertyTypeEnum,
  ): CertificatePropertyValueParams {
    const isNilValue = isNil(dynamicFieldValue);

    if (type === CertificatePropertyTypeEnum.CURRENT_DATE) {
      return !isNilValue ? dynamicFieldValue : DateFormat.buddhistFullDateWithLocale;
    }

    return !isNilValue ? dynamicFieldValue : null;
  }
}
