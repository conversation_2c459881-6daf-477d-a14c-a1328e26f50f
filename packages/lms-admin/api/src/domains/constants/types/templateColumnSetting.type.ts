import { GenericID } from '@iso/constants/commonTypes';
import {
  ColumnSettingTemplateEnum,
  ColumnSettingKeyEnum,
  ColumnSettingModuleEnum,
} from '@iso/lms/enums/columnSetting.enum';

type PredefinedCreateTemplateColumnSettingParams = {
  id: GenericID;
  organizationId: GenericID;
  name: string;
  code: ColumnSettingTemplateEnum;
  module: ColumnSettingModuleEnum[];
  columnSetting: {
    key: ColumnSettingKeyEnum;
    isActive: boolean;
    isFilter: boolean;
    isRequired: boolean;
    isDefault: boolean;
  }[];
  mainFilter: string[];
};

const userManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'userManagement',
    code: ColumnSettingTemplateEnum.userManagement,
    module: [ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: false,
        isRequired: true,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_AVATAR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: true,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_USERNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: true,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: true,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_ACTIVE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DEPARTMENT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_SUPERVISOR,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO, //เลขที่ใบอนุญาติ oic
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO, //เลขที่ใบอนุญาติ oic
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO, //เลขที่ใบอนุญาติ tsi
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_PERMISSION_GROUP,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.USER_FULLNAME,
      ColumnSettingKeyEnum.USER_EMAIL,
      ColumnSettingKeyEnum.USER_CITIZEN_ID,
    ],
  },
];

const bulkActivateUserList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'bulkActivateUser',
    code: ColumnSettingTemplateEnum.bulkActivateUser,
    module: [ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
    ],
    mainFilter: [],
  },
];

const bulkEditUserList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'bulkEditUser',
    code: ColumnSettingTemplateEnum.bulkEditUser,
    module: [ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
    ],
    mainFilter: [],
  },
];

const courseManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'courseManagement',
    code: ColumnSettingTemplateEnum.courseManagement,
    module: [ColumnSettingModuleEnum.COURSE],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.COURSE_CODE,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.COURSE_IMAGE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.COURSE_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.COURSE_OBJECTIVE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.COURSE_IS_CERTIFICATE_ENABLED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.COURSE_SELF_ENROLL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.COURSE_IS_ENABLED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.COURSE_CREATED_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.COURSE_UPDATED_AT,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.COURSE_NAME,
      ColumnSettingKeyEnum.COURSE_CODE,
      ColumnSettingKeyEnum.COURSE_OBJECTIVE,
      ColumnSettingKeyEnum.COURSE_IS_ENABLED,
    ],
  },
];

const enrollmentLearningList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'enrollmentLearning',
    code: ColumnSettingTemplateEnum.enrollmentLearning,
    module: [ColumnSettingModuleEnum.ENROLLMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_COURSE_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ENROLL_TYPE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_STARTED_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_EXPIRED_AT,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_PERCENT_LEARNING_PROGRESS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_LEARNING_STATUS,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.ENROLLMENT_USER_FULLNAME,
      ColumnSettingKeyEnum.ENROLLMENT_USER_EMAIL,
      ColumnSettingKeyEnum.ENROLLMENT_COURSE_NAME,
      ColumnSettingKeyEnum.ENROLLMENT_STARTED_AT,
      ColumnSettingKeyEnum.ENROLLMENT_LEARNING_STATUS,
      ColumnSettingKeyEnum.ENROLLMENT_ENROLL_TYPE,
    ],
  },
];

const enrollmentApprovalList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'enrollmentApproval',
    code: ColumnSettingTemplateEnum.enrollmentApproval,
    module: [ColumnSettingModuleEnum.ENROLLMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_COURSE_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_REQUESTED_APPROVAL_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_APPROVAL_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_APPROVAL_STATUS,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.ENROLLMENT_USER_FULLNAME,
      ColumnSettingKeyEnum.ENROLLMENT_USER_EMAIL,
      ColumnSettingKeyEnum.ENROLLMENT_COURSE_NAME,
      ColumnSettingKeyEnum.ENROLLMENT_REQUESTED_APPROVAL_AT,
      ColumnSettingKeyEnum.ENROLLMENT_APPROVAL_STATUS,
    ],
  },
];

const regularEnrollmentList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'regularEnrollment',
    code: ColumnSettingTemplateEnum.regularEnrollment,
    module: [ColumnSettingModuleEnum.ENROLLMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_COURSE_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ENROLL_TYPE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_STARTED_AT,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_PERCENT_LEARNING_PROGRESS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_LEARNING_STATUS,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.ENROLLMENT_USER_FULLNAME,
      ColumnSettingKeyEnum.ENROLLMENT_USER_EMAIL,
      ColumnSettingKeyEnum.ENROLLMENT_COURSE_NAME,
      ColumnSettingKeyEnum.ENROLLMENT_STARTED_AT,
      ColumnSettingKeyEnum.ENROLLMENT_LEARNING_STATUS,
      ColumnSettingKeyEnum.ENROLLMENT_ENROLL_TYPE,
    ],
  },
];

const regularPreEnrollmentList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'regularPreEnrollment',
    code: ColumnSettingTemplateEnum.regularPreEnrollment,
    module: [ColumnSettingModuleEnum.PRE_ENROLLMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_FULL_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSES,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ROUND_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_STATUS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ENROLL_TYPE,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.PRE_ENROLLMENT_FULL_NAME,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_EMAIL,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSES,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSE_GROUP,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_ROUND_DATE,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_STATUS,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_ENROLL_TYPE,
    ],
  },
];

const preEnrollmentList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'preEnrollment',
    code: ColumnSettingTemplateEnum.preEnrollment,
    module: [ColumnSettingModuleEnum.PRE_ENROLLMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_FULL_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_NO, //เลขที่ใบอนุญาติ tsi
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_NO, //เลขที่ใบอนุญาติ oic
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_LIFE_NO, //เลขที่ใบอนุญาติ oic
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSES,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSE_CODE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ROUND_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_IS_CHECK_REGULATOR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_STATUS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ENROLL_TYPE,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.PRE_ENROLLMENT_FULL_NAME,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_EMAIL,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSES,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSE_GROUP,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_ROUND_DATE,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_STATUS,
      ColumnSettingKeyEnum.PRE_ENROLLMENT_ENROLL_TYPE,
    ],
  },
];

const additionalDocumentList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'additionalDocument',
    code: ColumnSettingTemplateEnum.additionalDocument,
    module: [ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_REQUEST_NO,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_COURSE_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_CREATED_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_EXPIRED_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_APPROVAL_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_DOC_STATUS,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_FULLNAME,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_EMAIL,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_COURSE_NAME,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_CREATED_AT,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_DOC_STATUS,
    ],
  },
];

const deductDocumentList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'deductDocument',
    code: ColumnSettingTemplateEnum.deductDocument,
    module: [ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_REQUEST_NO,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_COURSE_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_SENT_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_APPROVAL_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_STARTED_AT,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_LICENSE_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_LICENSE_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_DEDUCT_DOC_STATUS,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_FULLNAME,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_EMAIL,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_CITIZEN_ID,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_COURSE_NAME,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_STARTED_AT,
      ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_DEDUCT_DOC_STATUS,
    ],
  },
];

const memberManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'memberManagement',
    code: ColumnSettingTemplateEnum.memberManagement,
    module: [ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_AVATAR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DEPARTMENT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_SUPERVISOR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_ACTIVE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.USER_FULLNAME,
      ColumnSettingKeyEnum.USER_EMAIL,
      ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
    ],
  },
];

const subordinateManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'subordinateManagement',
    code: ColumnSettingTemplateEnum.subordinateManagement,
    module: [ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_AVATAR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DEPARTMENT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_SUPERVISOR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_ACTIVE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.USER_FULLNAME,
      ColumnSettingKeyEnum.USER_EMAIL,
      ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
    ],
  },
];

const userGroupConditionManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'userGroupConditionManagement',
    code: ColumnSettingTemplateEnum.userGroupConditionManagement,
    module: [
      ColumnSettingModuleEnum.USER,
      ColumnSettingModuleEnum.DEPARTMENT,
      ColumnSettingModuleEnum.LICENSE,
      ColumnSettingModuleEnum.USER_DIRECT_REPORT,
      ColumnSettingModuleEnum.COURSE,
    ],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_FIRSTNAME_LASTNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_USERNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_DEPARTMENT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_SUPERVISOR,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_COURSE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_ACTIVE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
      },
    ],
    mainFilter: [],
  },
];

const userMapUserGroupManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'userMapUserGroupManagement',
    code: ColumnSettingTemplateEnum.userMapUserGroupManagement,
    module: [ColumnSettingModuleEnum.USER, ColumnSettingModuleEnum.USER_GROUP],
    columnSetting: [
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_AVATAR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_USERNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_DEPARTMENT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_SUPERVISOR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GROUP_USER_GROUP_PARTICIPATION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_ACTIVE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.USER_FULLNAME,
      ColumnSettingKeyEnum.USER_CITIZEN_ID,
      ColumnSettingKeyEnum.USER_EMAIL,
      ColumnSettingKeyEnum.USER_GROUP_USER_GROUP_PARTICIPATION,
    ],
  },
];

const knowledgeContentItemList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'knowledgeContentItemManagement',
    code: ColumnSettingTemplateEnum.knowledgeContentItem,
    module: [ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CODE,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_THUMBNAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TITLE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TYPE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CATEGORY_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_INSTRUCTOR_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_STATUS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_PUBLISHED_START_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_PUBLISHED_END_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_UPDATED_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_IS_DOWNLOAD_ENABLED,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TITLE,
      ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CODE,
      ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TYPE,
      ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_STATUS,
    ],
  },
];

const dashboardKnowledgeContentManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'dashboardKnowledgeContentManagement',
    code: ColumnSettingTemplateEnum.dashboardKnowledgeContentManagement,
    module: [ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM, ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_DASHBOARD_TITLE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CODE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TYPE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CATEGORY_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_INSTRUCTOR_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_PUBLISHED_START_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_GENDER,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_DASHBOARD_TITLE,
      ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TYPE,
      ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CATEGORY_NAME,
    ],
  },
];

const learningPathManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'learningPathManagement',
    code: ColumnSettingTemplateEnum.learningPathManagement,
    module: [ColumnSettingModuleEnum.LEARNING_PATH],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_CODE,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_THUMBNAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_NAME,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_NUMBER_OF_CONTENT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_IS_CERTIFICATE_ENABLED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLL_TYPE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_EXPIRY_DAY,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_CREATED_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_UPDATED_AT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_IS_ENABLED,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.LEARNING_PATH_NAME,
      ColumnSettingKeyEnum.LEARNING_PATH_CODE,
      ColumnSettingKeyEnum.LEARNING_PATH_IS_ENABLED,
    ],
  },
];

const inProgressLearningPathEnrollmentManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'inProgressLearningPathEnrollmentManagement',
    code: ColumnSettingTemplateEnum.inProgressLearningPathEnrollmentManagement,
    module: [ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_FULL_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_LEARNING_PATH_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STARTED_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EXPIRED_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_CONTENT_PROGRESS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STATUS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_ENROLL_BY,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_FULL_NAME,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EMAIL,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_LEARNING_PATH_NAME,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STARTED_DATE,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EXPIRED_DATE,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STATUS,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_ENROLL_BY,
    ],
  },
];

const preAssignLearningPathEnrollmentManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'preAssignLearningPathEnrollmentManagement',
    code: ColumnSettingTemplateEnum.preAssignLearningPathEnrollmentManagement,
    module: [ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_FULL_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_LEARNING_PATH_NAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STARTED_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EXPIRED_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_CONTENT_PROGRESS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STATUS,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_ENROLL_BY,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_FULL_NAME,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EMAIL,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_LEARNING_PATH_NAME,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STARTED_DATE,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EXPIRED_DATE,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STATUS,
      ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_ENROLL_BY,
    ],
  },
];

const assignContentManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'assignContentManagement',
    code: ColumnSettingTemplateEnum.assignContentManagement,
    module: [ColumnSettingModuleEnum.JOB_TRANSACTION],
    columnSetting: [
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_STATUS,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_ERROR_MESSAGES,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_AVATAR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_GENDER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_DATE_OF_BIRTH,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_DEPARTMENT,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_SUPERVISOR,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_ACTIVE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_END_DATE,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.JOB_TRANSACTION_USER_FULLNAME,
      ColumnSettingKeyEnum.JOB_TRANSACTION_USER_EMAIL,
      ColumnSettingKeyEnum.JOB_TRANSACTION_USER_MOBILE_PHONE_NUMBER,
    ],
  },
];

const transferClassroomUserManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'transferClassroomUserManagement',
    code: ColumnSettingTemplateEnum.transferClassroomUserManagement,
    module: [ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT, ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.CLASSROOM_TRANSFER_ENROLLMENT_STATUS,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.CLASSROOM_ROUND_DATE,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.CLASSROOM_LOCATION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_ACTIVE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.USER_FULLNAME,
      ColumnSettingKeyEnum.USER_EMAIL,
      ColumnSettingKeyEnum.USER_CITIZEN_ID,
      ColumnSettingKeyEnum.CLASSROOM_ROUND_DATE,
      ColumnSettingKeyEnum.CLASSROOM_LOCATION,
      ColumnSettingKeyEnum.CLASSROOM_TRANSFER_ENROLLMENT_STATUS,
    ],
  },
];

const attendanceClassroomManagementList: PredefinedCreateTemplateColumnSettingParams[] = [
  {
    id: null,
    organizationId: null,
    name: 'attendanceClassroomManagement',
    code: ColumnSettingTemplateEnum.attendanceClassroomManagement,
    module: [ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT, ColumnSettingModuleEnum.USER],
    columnSetting: [
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_FULLNAME,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.CLASSROOM_ENROLLMENT_STATUS,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.CLASSROOM_TOTAL_ATTENDED,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.CLASSROOM_SCORE_HOMEWORK,
      },
      {
        isActive: true,
        isFilter: false,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.CLASSROOM_REMARK,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: true,
        key: ColumnSettingKeyEnum.USER_EMAIL,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_POSITION,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_ACTIVE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
      },
      {
        isActive: false,
        isFilter: false,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
      },
      {
        isActive: true,
        isFilter: true,
        isRequired: false,
        isDefault: false,
        key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
      },
    ],
    mainFilter: [
      ColumnSettingKeyEnum.USER_FULLNAME,
      ColumnSettingKeyEnum.USER_EMAIL,
      ColumnSettingKeyEnum.USER_CITIZEN_ID,
      ColumnSettingKeyEnum.CLASSROOM_ENROLLMENT_STATUS,
    ],
  },
];

export const PredefinedCreateTemplateColumnSettingList: PredefinedCreateTemplateColumnSettingParams[] = [
  ...userManagementList,
  ...bulkActivateUserList,
  ...bulkEditUserList,
  ...courseManagementList,
  ...enrollmentLearningList,
  ...enrollmentApprovalList,
  ...regularEnrollmentList,
  ...regularPreEnrollmentList,
  ...preEnrollmentList,
  ...additionalDocumentList,
  ...deductDocumentList,
  ...userGroupConditionManagementList,
  ...userMapUserGroupManagementList,
  ...knowledgeContentItemList,
  ...dashboardKnowledgeContentManagementList,
  ...learningPathManagementList,
  ...inProgressLearningPathEnrollmentManagementList,
  ...preAssignLearningPathEnrollmentManagementList,
  ...transferClassroomUserManagementList,
  ...attendanceClassroomManagementList,
  ...memberManagementList,
  ...subordinateManagementList,
  ...assignContentManagementList,
];
