import { Nullable } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';

export type TemplateParams = {
  name: string;
  enable: boolean;
  content?: any;
};

export type OrganizationParams = {
  domain: string;
  mailerConfig: any;
};

export type MailPayloadParams = {
  fullName: string;
  courseName: string;
};

export type MailPayloadEnrollmentCompleteAndCertificateParams = MailPayloadParams & {
  certificateUrl: string;
  certificatePDFUrl: string;
  certificateCode: string;
  isDeduct?: boolean;
  isDeductApproved?: boolean;
  refName?: string;
  operationExpiredDate?: string;
  objectiveType: Nullable<CourseObjectiveTypeEnum>;
  regulator: Nullable<RegulatorEnum>;
};

export type MailPayloadLearningPathEnrollmentCertificateParams = {
  learningPathName: string;
  fullName: string;
  certificateUrl: string;
  certificatePDFUrl: string;
  certificateCode: string;
  refName: string;
};

export type MailPayloadLearningPathEnrollmentCompleteAndCertificateParams =
  MailPayloadLearningPathEnrollmentCertificateParams;

export type MailPayloadLearningPathEnrollmentCompleteParams = {
  learningPathName: string;
  learningPathCode: string;
  fullName: string;
};

export type MailPayloadAchievementCompleteParams = {
  fullName: string;
  achievementName: string;
  courseName: string;
  certificateUrl: string;
  certificatePDFUrl: string;
  certificateCode: string;
};
