import { OrganizationSchedulerTypeEnum } from '@iso/lms/enums/organizationScheduler.enum';

export const PredefinedCreateOrganizationSchedulerList = [
  {
    organizationId: null,
    type: OrganizationSchedulerTypeEnum.SEND_EMAIL_CREATE_USER,
    cron: '0 9 * * *',
  },
  {
    organizationId: null,
    type: OrganizationSchedulerTypeEnum.SEND_EMAIL_CREATE_ENROLLMENT,
    cron: '10 9 * * *',
  },
  {
    organizationId: null,
    type: OrganizationSchedulerTypeEnum.SEND_EMAIL_ASSIGN_LEARNING_PATH_ENROLLMENT,
    cron: '0 9 * * *',
  },
  {
    organizationId: null,
    type: OrganizationSchedulerTypeEnum.SEND_EMAIL_REMIND_CLASSROOM_ROUND_START,
    cron: '0 8 * * *',
  },
  {
    organizationId: null,
    type: OrganizationSchedulerTypeEnum.SEND_NOTIFICATION_REMIND_EXPIRED_ENROLLMENT,
    cron: '30 9 * * *',
  },
  {
    organizationId: null,
    type: OrganizationSchedulerTypeEnum.SEND_NOTIFICATION_ENROLLMENT_ACHIEVEMENT_COMPLETED,
    cron: '30 9 * * *',
  },
  {
    organizationId: null,
    type: OrganizationSchedulerTypeEnum.SEND_NOTIFICATION_PLAN_PACKAGE_LICENSE_EXPIRED,
    cron: '0 0 * * *',
  },
];
