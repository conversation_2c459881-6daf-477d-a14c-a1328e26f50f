import {
  ApplicantTypeEnum,
  ApplicantTypeTHEnum,
  LicenseRenewalEnum,
  LicenseRenewalTHEnum,
  LicenseTypeEnum,
  LicenseTypeTHEnum,
} from '@iso/lms/enums/course.enum';

export const LicenseRenewalMapping = {
  [LicenseRenewalEnum.NONE]: LicenseRenewalTHEnum[LicenseRenewalEnum.NONE],
  [LicenseRenewalEnum.NEW]: LicenseRenewalTHEnum[LicenseRenewalEnum.NEW],
  [LicenseRenewalEnum.RENEW1]: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW1],
  [LicenseRenewalEnum.RENEW2]: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW2],
  [LicenseRenewalEnum.RENEW3]: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW3],
  [LicenseRenewalEnum.RENEW4]: LicenseRenewalTHEnum[LicenseRenewalEnum.RENEW4],
  [LicenseRenewalEnum.UL]: LicenseRenewalTHEnum[LicenseRenewalEnum.UL],
  [LicenseRenewalEnum.UK]: LicenseRenewalTHEnum[LicenseRenewalEnum.UK],
};

export const ApplicantTypeMapping = {
  [ApplicantTypeEnum.AGENT]: ApplicantTypeTHEnum.AGENT,
  [ApplicantTypeEnum.BROKER]: ApplicantTypeTHEnum.BROKER,
  [ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER]: ApplicantTypeTHEnum.ADVISOR_ANALYST_PLANNER,
  [ApplicantTypeEnum.ANALYST]: ApplicantTypeTHEnum.ANALYST,
  [ApplicantTypeEnum.ADVISOR]: ApplicantTypeTHEnum.ADVISOR,
  [ApplicantTypeEnum.PLANNER]: ApplicantTypeTHEnum.PLANNER,
  [ApplicantTypeEnum.ACCOUNTANT]: ApplicantTypeTHEnum.ACCOUNTANT,
};

export const LicenseTypeMapping = {
  [LicenseTypeEnum.LIFE]: LicenseTypeTHEnum.LIFE,
  [LicenseTypeEnum.NONLIFE]: LicenseTypeTHEnum['NON-LIFE'],
  [LicenseTypeEnum.BOTH]: LicenseTypeTHEnum.BOTH,
  [LicenseTypeEnum.INVESTMENT]: LicenseTypeTHEnum.INVESTMENT,
  [LicenseTypeEnum.UNIT_LINKED]: LicenseTypeTHEnum.UNIT_LINKED,
  [LicenseTypeEnum.CPA]: LicenseTypeTHEnum.CPA,
  [LicenseTypeEnum.RA]: LicenseTypeTHEnum.RA,
};
