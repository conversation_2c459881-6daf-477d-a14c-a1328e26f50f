import {
  CourseDIToken,
  EnrollmentDIToken,
  PreEnrollmentTransactionDIToken,
  OrganizationDIToken,
  PlanDIToken,
  ProductSKUDIToken,
} from '@applications/di/domain';
import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import {
  OutputUpdateOrganizationPlanParams,
  UpdateOrganizationPlanParams,
} from '@domains/constants/types/organization.type';
import { MainPackageParams, SubPackageParams } from '@domains/constants/types/package.type';
import { IPlanDataMapper } from '@domains/interfaces/infrastructures/dataMappers/plan.dataMapper.interface';
import { IPlanPackageDataMapper } from '@domains/interfaces/infrastructures/dataMappers/planPackage.dataMapper.interface';
import {
  ICourseMarketplaceRepository,
  ICourseRepository,
  ICourseVersionRepository,
  IEnrollmentRepository,
  IOrganizationRepository,
  IPackageRepository,
  IPlanPackageLicenseRepository,
  IPlanPackageRepository,
  IPlanRepository,
  IPreEnrollmentTransactionRepository,
  IProductSKUCourseRepository,
} from '@domains/interfaces/infrastructures/repositories';
import { ICourseService } from '@domains/interfaces/services/course.service.interface';
import { IUpdateOrganizationPlanUseCase } from '@domains/interfaces/usecases/organization.usecase.interface';
import { date, DateFormat } from '@domains/utils/date.util';
import { DbClientParams } from '@infrastructures/constants/types/database.type';
import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { ExternalContentTypeEnum } from '@iso/lms/enums/course.enum';
import { PackageContentModelTypeEnum, PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import { Course } from '@iso/lms/models/course.model';
import { CourseVersion } from '@iso/lms/models/courseVersion.model';
import { Organization } from '@iso/lms/models/organization.model';
import { Package } from '@iso/lms/models/package.model';
import { Plan } from '@iso/lms/models/plan.model';
import { PlanPackage } from '@iso/lms/models/planPackage.model';
import { PlanPackageLicense } from '@iso/lms/models/planPackageLicense.model';
import { CourseParams } from '@iso/lms/types/course.type';
import {
  PlanPackageContentParams,
  PlanPackageTypeContentParams,
  PlanPackageTypePlatformParams,
} from '@iso/lms/types/planPackage.type';
import { Inject, Injectable } from '@nestjs/common';
import { cloneDeep, uniq } from 'lodash';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

@Injectable()
export class UpdateOrganizationPlanUseCase implements IUpdateOrganizationPlanUseCase {
  constructor(
    @Inject(InfrastructureConfigDIToken.DbClient)
    private readonly mongoClient: DbClientParams,
    @Inject(ProductSKUDIToken.CourseMarketplaceRepository)
    private readonly courseMarketplaceRepository: ICourseMarketplaceRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @Inject(PlanDIToken.PlanPackageRepository)
    private readonly planPackageRepository: IPlanPackageRepository,
    @Inject(PlanDIToken.PackageRepository)
    private readonly packageRepository: IPackageRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(PreEnrollmentTransactionDIToken.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,
    @Inject(ProductSKUDIToken.ProductSKUCourseRepository)
    private readonly productSKUCourseRepository: IProductSKUCourseRepository,
    @Inject(PlanDIToken.PlanPackageLicenseRepository)
    private readonly planPackageLicenseRepository: IPlanPackageLicenseRepository,

    @Inject(PlanDIToken.PlanDataMapper)
    private readonly planDataMapper: IPlanDataMapper,
    @Inject(PlanDIToken.PlanPackageDataMapper)
    private readonly planPackageDataMapper: IPlanPackageDataMapper,

    // service
    @Inject(CourseDIToken.CourseService)
    private readonly courseService: ICourseService,
  ) {}

  async execute(params: UpdateOrganizationPlanParams): Promise<OutputUpdateOrganizationPlanParams> {
    const { organizationId, planId, planName, mainPackage, subPackages, deletePackageIds, createByAdminUserId } =
      params;

    this.validatePlanPackageData(mainPackage, subPackages);

    const subPackageIds = subPackages.map((val) => val.packageId);
    const packageIds = [...subPackageIds, mainPackage.packageId];

    const [organization, plan, packages] = await Promise.all([
      this.organizationRepository.findOne({ id: organizationId }),
      this.planRepository.findOne({ organizationId, id: planId }),
      this.packageRepository.find({ id: { $in: packageIds } }),
    ]);

    this.validateExistingData(organization, packages, packageIds, plan);

    const planPackages = await this.planPackageRepository.find({ planId });
    const mainPlanPackage = planPackages.find((val) => val.type === PackageTypeEnum.PLATFORM);
    const subPlanPackages = planPackages.filter((val) => val.type === PackageTypeEnum.CONTENT);
    const mainPackageDetail = packages.find((val) => val.id === mainPlanPackage.packageId);

    const planPackageIds = planPackages.map((val) => val.id);
    const planPackageLicenses = await this.planPackageLicenseRepository.find({
      planPackageId: { $in: planPackageIds },
      userId: { $ne: null },
    });
    const mainPlanPackageLicenses = planPackageLicenses.filter((val) => val.planPackageId === mainPlanPackage.id);

    // assign new value
    plan.name = planName;
    plan.startDate = date(mainPackage.startDate).toDate();
    plan.gracingDate = mainPackage.gracingDate ? date(mainPackage.gracingDate).toDate() : null;
    plan.endDate = date(mainPackage.endDate).toDate();

    mainPlanPackage.packageId = mainPackage.packageId;
    mainPlanPackage.content = this.preparePlanPackageContentDetail({
      packageDetail: mainPackageDetail,
      planPackageType: PackageTypeEnum.PLATFORM,
      updateData: mainPackage,
      existingPlanPackage: mainPlanPackage,
      existingPlanPackageLicenses: mainPlanPackageLicenses,
    });

    mainPlanPackage.totalUsageDay = mainPackage.totalUsageDay;
    mainPlanPackage.startDate = date(mainPackage.startDate).toDate();
    mainPlanPackage.gracingDate = mainPackage.gracingDate ? date(mainPackage.gracingDate).toDate() : null;
    mainPlanPackage.endDate = date(mainPackage.endDate).toDate();

    const updatedPlanPackages: PlanPackage[] = [];
    updatedPlanPackages.push(mainPlanPackage);

    for (const [index, subPackage] of subPackages.entries()) {
      const existingSubPlanPackage = cloneDeep(subPlanPackages.find((pkg) => pkg.id === subPackage.id));
      const existingSubPlanPackageLicenses = planPackageLicenses.filter((val) => val.planPackageId === subPackage.id);

      const packageDetail = packages.find((pkg) => pkg.id === subPackage.packageId);
      const updatedAt = date().add(index, 'millisecond').toDate();

      const isDeleteSubPackage = deletePackageIds.includes(subPackage.packageId);

      if (!existingSubPlanPackage || isDeleteSubPackage) {
        const newPlanPackage = await PlanPackage.new({
          planId: plan.id,
          packageId: subPackage.packageId,
          name: packageDetail.name,
          description: packageDetail.description,
          type: PackageTypeEnum.CONTENT,
          content: this.preparePlanPackageContentDetail({
            packageDetail,
            planPackageType: PackageTypeEnum.CONTENT,
            updateData: subPackage,
            existingPlanPackage: null,
            existingPlanPackageLicenses: [],
          }),
          totalUsageDay: subPackage.totalUsageDay,
          startDate: date(subPackage.startDate).toDate(),
          gracingDate: subPackage?.gracingDate ? date(subPackage.gracingDate).toDate() : null,
          endDate: date(subPackage.endDate).toDate(),
        });
        newPlanPackage.updatedAt = updatedAt;

        updatedPlanPackages.push(newPlanPackage);
      } else {
        const newContent = this.preparePlanPackageContentDetail({
          packageDetail,
          planPackageType: PackageTypeEnum.CONTENT,
          updateData: subPackage,
          existingPlanPackage: existingSubPlanPackage,
          existingPlanPackageLicenses: existingSubPlanPackageLicenses,
        });

        existingSubPlanPackage.packageId = subPackage.packageId;
        existingSubPlanPackage.name = packageDetail.name;
        existingSubPlanPackage.description = packageDetail.description;
        existingSubPlanPackage.content = newContent;
        existingSubPlanPackage.totalUsageDay = subPackage.totalUsageDay;
        existingSubPlanPackage.startDate = date(subPackage.startDate).toDate();
        existingSubPlanPackage.gracingDate = subPackage?.gracingDate ? date(subPackage.gracingDate).toDate() : null;
        existingSubPlanPackage.endDate = date(subPackage.endDate).toDate();
        existingSubPlanPackage.updatedAt = updatedAt;
        updatedPlanPackages.push(existingSubPlanPackage);
      }
    }

    const deleteSubPackageIds = subPlanPackages
      .filter((val) => deletePackageIds.includes(val.packageId))
      .map((val) => val.id);

    organization.createByAdminUserId = createByAdminUserId;

    const addCourse = await this.createCourse(plan, updatedPlanPackages);
    const deleteCourse = await this.deleteCourse(plan, subPlanPackages, updatedPlanPackages, deleteSubPackageIds);

    const session = this.mongoClient.startSession();
    session.startTransaction();
    const opts = { session };

    try {
      await this.planRepository.save(plan, opts);
      await this.planPackageRepository.saveMany(updatedPlanPackages, opts);
      await this.planPackageRepository.deleteMany({ id: { $in: deleteSubPackageIds } }, opts);
      await this.organizationRepository.save(organization, opts);
      if (deleteCourse.length > 0) {
        await this.courseRepository.deleteMany({ id: { $in: deleteCourse } }, { ...opts, hardDelete: true });
        await this.courseVersionRepository.deleteMany(
          { courseId: { $in: deleteCourse } },
          { ...opts, hardDelete: true },
        );
      }

      if (addCourse.newCourses.length > 0) {
        await this.courseRepository.saveMany(addCourse.newCourses, opts);
      }

      if (addCourse.newCourseVersions.length > 0) {
        await this.courseVersionRepository.saveMany(addCourse.newCourseVersions, opts);
      }

      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

    return {
      organizationId,
      plan: this.planDataMapper.toDTO(plan),
      mainPackage: this.planPackageDataMapper.toDTO(mainPlanPackage),
      subPackages: this.planPackageDataMapper.toDTOs(subPlanPackages),
    };
  }

  private validateExistingData = (
    organization: Organization,
    packages: Package[],
    packageIds: GenericID[],
    plan: Plan,
  ): void => {
    if (!organization) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Organization not found',
      });
    }

    const foundPackageIds = packages.map((val) => val.id);
    const missingPackageIds = packageIds.filter((id) => !foundPackageIds.includes(id));

    if (missingPackageIds.length > 0) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Some packages were not found',
        data: { packageIds: missingPackageIds },
      });
    }

    if (!plan) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Plan not found',
      });
    }
  };

  private validatePlanPackageData(mainPackage: MainPackageParams, subPackages: SubPackageParams[]): void {
    // Main Package
    this.validateLicense(mainPackage.totalLicense, mainPackage.totalTransferLicense);

    if (mainPackage.gracingDate && date(mainPackage.gracingDate).isBefore(mainPackage.startDate, 'day')) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'Gracing date cannot be before the start date.',
      });
    }

    if (
      date(mainPackage.endDate).isBefore(mainPackage.startDate, 'day') ||
      (mainPackage.gracingDate && date(mainPackage.endDate).isBefore(mainPackage.gracingDate, 'day'))
    ) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'End date cannot be before the start date or the gracing date.',
      });
    }

    // Sub Package
    for (const subPackage of subPackages) {
      this.validateLicense(subPackage.totalLicense, subPackage.totalTransferLicense);

      if (subPackage.totalLicense > mainPackage.totalLicense) {
        throw Exception.new({
          code: Code.VALIDATE_INPUT_ERROR,
          message: 'Sub package licenses cannot exceed main package licenses.',
        });
      }

      if (!subPackage.gracingDate) continue;

      const isSubPackageInRange =
        subPackage.gracingDate &&
        date(subPackage.gracingDate).isSameOrAfter(date(mainPackage.startDate), 'day') &&
        date(subPackage.endDate).isSameOrBefore(date(mainPackage.endDate), 'day');

      if (!isSubPackageInRange) {
        throw Exception.new({
          code: Code.VALIDATE_INPUT_ERROR,
          message: `Sub package period must be within the main package period (${date(mainPackage.startDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)} - ${date(mainPackage.endDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)}).`,
        });
      }
    }
  }

  private validateLicense = (totalLicense: number, totalTransferLicense: number): void => {
    if (!Number.isInteger(totalLicense) || totalLicense < 1) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'Total license must be an integer greater than 0.',
      });
    }

    if (!Number.isInteger(totalTransferLicense)) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'Total transfer license must be an integer.',
      });
    }
  };

  private calculateNewRemainLicense = (
    newTotalLicense: number = 0,
    oldTotalLicense: number = 0,
    oldRemainLicense: number = 0,
    planPackageLicenses: PlanPackageLicense[] = [],
  ): number => {
    if (newTotalLicense === oldTotalLicense) {
      return oldRemainLicense;
    }

    if (newTotalLicense < oldTotalLicense) {
      const newRemainLicense = newTotalLicense - planPackageLicenses.length;
      return newRemainLicense;
    }

    const increasedRemain = oldRemainLicense + (newTotalLicense - oldTotalLicense);
    return Math.min(increasedRemain, newTotalLicense);
  };

  private calculateNewRemainTransferLicense = (
    newTotalLicense: number = 0,
    oldTotalLicense: number = 0,
    oldRemainLicense: number = 0,
  ): number => {
    if (newTotalLicense === oldTotalLicense) {
      return oldRemainLicense;
    }

    if (newTotalLicense < oldTotalLicense) {
      return Math.min(oldRemainLicense, newTotalLicense);
    }

    const increasedRemain = oldRemainLicense + (newTotalLicense - oldTotalLicense);
    return Math.min(increasedRemain, newTotalLicense);
  };

  private preparePlanPackageContentDetail(params: {
    packageDetail: Package;
    planPackageType: PackageTypeEnum;
    updateData: Partial<SubPackageParams | MainPackageParams>;
    existingPlanPackage: Nullable<PlanPackage>;
    existingPlanPackageLicenses: PlanPackageLicense[];
  }): PlanPackageContentParams {
    const { packageDetail, planPackageType, updateData, existingPlanPackage, existingPlanPackageLicenses } = params;

    let planPackageTypeContentParams: PlanPackageContentParams;

    if (planPackageType === PackageTypeEnum.PLATFORM) {
      const mainPackage = updateData as MainPackageParams;
      const planPackageContent = existingPlanPackage?.content as PlanPackageTypePlatformParams;

      planPackageTypeContentParams = {
        totalLicense: mainPackage.totalLicense,
        totalTransferLicense: mainPackage.totalTransferLicense,
        remainLicense: this.calculateNewRemainLicense(
          mainPackage.totalLicense,
          planPackageContent?.totalLicense,
          planPackageContent?.remainLicense,
          existingPlanPackageLicenses,
        ),
        remainTransferLicense: this.calculateNewRemainTransferLicense(
          mainPackage.totalTransferLicense,
          planPackageContent?.totalTransferLicense,
          planPackageContent?.remainTransferLicense,
        ),
      };
    } else if (planPackageType === PackageTypeEnum.CONTENT) {
      const subPackageData = updateData as SubPackageParams;
      const packageContentModeType = packageDetail.contentModeType;
      const planPackageContent = existingPlanPackage?.content as PlanPackageTypeContentParams;
      const isSamePackage = subPackageData.packageId === existingPlanPackage?.packageId;

      let productSKUCourseIds: GenericID[] = [];

      if (packageContentModeType === PackageContentModelTypeEnum.CUSTOM) {
        const { addProductSKUCourseIds = [], removeProductSKUCourseIds = [] } = subPackageData;

        if (planPackageContent?.productSKUCourseIds && isSamePackage) {
          const existProductSKUCourseIds = planPackageContent.productSKUCourseIds;
          const filterExistProductSKUCourseIds = existProductSKUCourseIds.filter(
            (val) => !removeProductSKUCourseIds.includes(val) && !addProductSKUCourseIds.includes(val),
          );
          const updateProductSKUCourseIds = [...addProductSKUCourseIds, ...filterExistProductSKUCourseIds];
          productSKUCourseIds = updateProductSKUCourseIds;
        } else {
          productSKUCourseIds = addProductSKUCourseIds;
        }
      }

      planPackageTypeContentParams = {
        model: packageContentModeType,
        type: packageDetail.contentType,
        productSKUCourseIds: productSKUCourseIds ?? [],
        totalLicense: subPackageData.totalLicense,
        totalTransferLicense: subPackageData.totalTransferLicense,
        remainLicense: this.calculateNewRemainLicense(
          subPackageData.totalLicense,
          planPackageContent?.totalLicense,
          planPackageContent?.remainLicense,
          existingPlanPackageLicenses,
        ),
        remainTransferLicense: this.calculateNewRemainTransferLicense(
          subPackageData.totalTransferLicense,
          planPackageContent?.totalTransferLicense,
          planPackageContent?.remainTransferLicense,
        ),
      };
    }

    return planPackageTypeContentParams;
  }

  private async createCourse(plan: Plan, updatePlanPackages: PlanPackage[]) {
    const newCourses = [];
    const newCourseVersions = [];
    const { organizationId } = plan;
    const planPackagesContents = updatePlanPackages.filter((val) => val.type === PackageTypeEnum.CONTENT);
    if (planPackagesContents.length === 0) {
      return { newCourses, newCourseVersions };
    }

    //get productSKUCourseId plan package custom
    const planPackagesContentCustom = planPackagesContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.CUSTOM,
    );

    const productSKUCourseIds1 = planPackagesContentCustom.flatMap(
      (val) => (val.content as PlanPackageTypeContentParams).productSKUCourseIds,
    );

    //get productSKUCourseId plan package subscription
    const planPackagesContentSubscription = planPackagesContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.SUBSCRIPTION,
    );

    const packageSubScriptionIds = planPackagesContentSubscription.map((val) => val.packageId);
    const allPlanPackageSubScriptions = await this.packageRepository.find({ id: { $in: packageSubScriptionIds } });
    const productSKUCourseIds2 = allPlanPackageSubScriptions.flatMap((val) => val.productSKUCourseIds);

    const productSKUCourseIds = [...productSKUCourseIds1, ...productSKUCourseIds2];

    const productSKUCourses = await this.productSKUCourseRepository.find({ id: { $in: productSKUCourseIds } });
    const courseExistList = await this.findExistCourseList(organizationId, productSKUCourseIds);

    for (const productSKUCourse of productSKUCourses) {
      const courseExist = this.getExistCourse(courseExistList, organizationId, productSKUCourse.id);

      if (courseExist) {
        const { externalContentTypes = [] } = courseExist;
        externalContentTypes.push(ExternalContentTypeEnum.PLAN_PACKAGE);
        courseExist.externalContentTypes = uniq(externalContentTypes);
        newCourses.push(courseExist);
      } else {
        const courseData = this.courseService.buildCreateCourse(
          productSKUCourse,
          organizationId,
          ExternalContentTypeEnum.PLAN_PACKAGE,
        );
        const newCourse = await Course.new(courseData);
        const courseVersionData = this.courseService.buildCreateCourseVersion(productSKUCourse, newCourse.id);
        const newCourseVersion = await CourseVersion.new(courseVersionData);
        newCourses.push(newCourse);
        newCourseVersions.push(newCourseVersion);
      }
    }

    const output = { newCourses, newCourseVersions };

    return output;
  }

  private async deleteCourse(
    plan: Plan,
    oldPlanPackageContents: PlanPackage[],
    updatePlanPackages: PlanPackage[],
    deleteSubPackageIds: GenericID[],
  ) {
    // 1. check current plan product sku course id different in plan package content custom
    // 2. check packages id change in plan package content
    // 3. check delete plan package content
    // 4. get all prouduct sku course id in Another plan package content subscription
    // 5. get all prouduct sku course id in Another plan package content custom

    const output = [];
    const { id: planId, organizationId } = plan;
    const existProductSKUCourseIds = [];
    const allMissingProductSKUCourseIds = [];

    const oldPlanPackagesContentCustom = oldPlanPackageContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.CUSTOM,
    );
    const oldPlanPackagesContentSubscription = oldPlanPackageContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.SUBSCRIPTION,
    );

    const upatePlanPackagesContents = updatePlanPackages.filter((val) => val.type === PackageTypeEnum.CONTENT);
    const updatePlanPackagesContentCustoms = upatePlanPackagesContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.CUSTOM,
    );
    const updatePlanPackagesContentSubscriptions = upatePlanPackagesContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.SUBSCRIPTION,
    );
    const samePlanPackageContentSubscriptionPackageIds = updatePlanPackagesContentSubscriptions.map(
      (val) => val.packageId,
    );

    const oldProductSKUCourseIds = oldPlanPackagesContentCustom.flatMap(
      (val) => (val.content as PlanPackageTypeContentParams).productSKUCourseIds,
    );

    const currentProductSKUCourseIds = updatePlanPackagesContentCustoms.flatMap(
      (val) => (val.content as PlanPackageTypeContentParams).productSKUCourseIds,
    );

    if (currentProductSKUCourseIds.length) {
      existProductSKUCourseIds.push(...currentProductSKUCourseIds);
    }

    // check product sku course id different in plan package content custom
    const tempMissingProductSKUIdList1 = oldProductSKUCourseIds.filter(
      (item) => !currentProductSKUCourseIds.includes(item),
    );

    if (tempMissingProductSKUIdList1.length > 0) {
      allMissingProductSKUCourseIds.push(...tempMissingProductSKUIdList1);
    }

    // check old change plan package content Id
    const oldPlanPackageContentIdChangeList = oldPlanPackageContents.filter((newItem) => {
      const oldItem = updatePlanPackages.find((val) => val.id === newItem.id);
      return oldItem && oldItem.packageId !== newItem.packageId;
    });

    const oldPlanPackageContentIdCustomChangeList = oldPlanPackageContentIdChangeList.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.CUSTOM,
    );

    const oldPlanPackageContentIdSubscriptionChangeList = oldPlanPackageContentIdChangeList.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.SUBSCRIPTION,
    );

    const oldPlanPackageContentIdSubscriptionChangePackageIds = oldPlanPackageContentIdSubscriptionChangeList.map(
      (val) => val.packageId,
    );

    // check change plan package product skuId missing
    const tempMissingProductSKUIdList2 = oldPlanPackageContentIdCustomChangeList.flatMap(
      (val) => (val.content as PlanPackageTypeContentParams).productSKUCourseIds,
    );

    if (tempMissingProductSKUIdList2.length > 0) {
      allMissingProductSKUCourseIds.push(...tempMissingProductSKUIdList2);
    }

    if (oldPlanPackageContentIdSubscriptionChangePackageIds.length > 0) {
      const packageSubscriptionList = await this.packageRepository.aggregate<Record<string, any>>([
        {
          $match: { id: { $in: oldPlanPackageContentIdSubscriptionChangePackageIds } },
        },
        {
          $project: {
            id: 1,
            productSKUCourseIds: 1,
          },
        },
      ]);

      const tempMissingProductSKUIdList3 = packageSubscriptionList.flatMap((val) => val.productSKUCourseIds);
      allMissingProductSKUCourseIds.push(...tempMissingProductSKUIdList3);
    }

    // check delete plan package content
    const planPackagesContentCustomDelete = oldPlanPackagesContentCustom
      .filter((val) => deleteSubPackageIds.includes(val.id))
      .map((val) => val);

    const planPackagesContentSubscriptionDelete = oldPlanPackagesContentSubscription
      .filter((val) => deleteSubPackageIds.includes(val.id))
      .map((val) => val);

    const planPackagesContentSubscriptionDeletePackageIds = planPackagesContentSubscriptionDelete.map(
      (val) => val.packageId,
    );

    const tempMissingProductSKUIdList4 = planPackagesContentCustomDelete.flatMap(
      (val) => (val.content as PlanPackageTypeContentParams).productSKUCourseIds,
    );

    if (tempMissingProductSKUIdList4.length > 0) {
      allMissingProductSKUCourseIds.push(...tempMissingProductSKUIdList4);
    }

    if (planPackagesContentSubscriptionDeletePackageIds.length > 0) {
      const packageSubscriptionList = await this.packageRepository.aggregate<Record<string, any>>([
        {
          $match: { id: { $in: planPackagesContentSubscriptionDeletePackageIds } },
        },
        {
          $project: {
            id: 1,
            productSKUCourseIds: 1,
          },
        },
      ]);

      const tempMissingProductSKUIdList5 = packageSubscriptionList.flatMap((val) => val.productSKUCourseIds);
      allMissingProductSKUCourseIds.push(...tempMissingProductSKUIdList5);
    }

    // find all Another plan subscription in organization
    const allAnotherPlanSubscriptions = await this.findAnotherPlanPackageContents(
      planId,
      organizationId,
      PackageContentModelTypeEnum.SUBSCRIPTION,
    );

    // find all Another plan custom in organization
    const allAnotherPlanCustoms = await this.findAnotherPlanPackageContents(
      planId,
      organizationId,
      PackageContentModelTypeEnum.CUSTOM,
    );

    const anotherPlanPackagePackageIds = allAnotherPlanSubscriptions.map((val) => val.planPackage.packageId);
    const allPackageSubscriptionIds = uniq([
      ...samePlanPackageContentSubscriptionPackageIds,
      ...anotherPlanPackagePackageIds,
    ]);
    const anotherPlanPackageCustomProductSKUCourseIds = allAnotherPlanCustoms.flatMap(
      (val) => val.planPackage.content.productSKUCourseIds,
    );

    if (anotherPlanPackageCustomProductSKUCourseIds.length > 0) {
      // push product SKU CourseId
      existProductSKUCourseIds.push(...anotherPlanPackageCustomProductSKUCourseIds);
    }

    if (allPackageSubscriptionIds.length > 0) {
      const packageSubscriptionList = await this.packageRepository.aggregate<Record<string, any>>([
        {
          $match: { id: { $in: allPackageSubscriptionIds } },
        },
        {
          $project: {
            id: 1,
            productSKUCourseIds: 1,
          },
        },
      ]);

      // push product SKU CourseId
      const existProductSKUCourseId = packageSubscriptionList.flatMap((val) => val.productSKUCourseIds);
      existProductSKUCourseIds.push(...existProductSKUCourseId);
    }

    const productSKUCourseIds = uniq(
      allMissingProductSKUCourseIds.filter((item) => !existProductSKUCourseIds.includes(item)),
    );

    const productSKUCourses = await this.productSKUCourseRepository.aggregate<Record<string, any>>([
      {
        $match: { id: { $in: productSKUCourseIds } },
      },
      {
        $project: {
          id: 1,
        },
      },
    ]);
    const courseMarketplaceList = await this.courseMarketplaceRepository.find({
      organizationId,
      productSKUCourseId: { $in: productSKUCourseIds },
    });
    const courseExistList = await this.findExistCourseList(organizationId, productSKUCourseIds);
    const courseIds = courseExistList.map((val) => val.id);
    const courseCodes = courseExistList.map((val) => val.code);
    const enrollmentExistList = await this.findEnrollmentByCourseIds(courseIds);
    const preEnrollmentTransactionExistList = await this.findPreEnrollmentByCourseCodes(organizationId, courseCodes);

    for (const productSKUCourse of productSKUCourses) {
      // check in course marketplace
      const courseMarketplace = courseMarketplaceList.find((val) => val.productSKUCourseId === productSKUCourse.id);
      if (courseMarketplace) {
        continue;
      }

      // check enrollment and pre enrollment
      const courseExist = this.getExistCourse(courseExistList, organizationId, productSKUCourse.id);
      const isEnroll = this.checkEnroll(enrollmentExistList, preEnrollmentTransactionExistList, courseExist);

      if (!isEnroll) {
        output.push(courseExist.id);
      }
    }

    return output;
  }

  private async findAnotherPlanPackageContents(
    planId: GenericID,
    organizationId: GenericID,
    contentModel: PackageContentModelTypeEnum,
  ) {
    const aggregateParams = [
      {
        $match: { id: { $ne: planId }, organizationId },
      },
      {
        $lookup: {
          from: 'plan-packages',
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$planId', '$$id'] },
                    { $eq: ['$type', PackageTypeEnum.CONTENT] },
                    { $eq: ['$content.model', contentModel] },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      { $unwind: '$planPackage' },
    ];

    const output = await this.planRepository.aggregate<Record<string, any>>(aggregateParams);

    return output;
  }

  private async findExistCourseList(organizationId: GenericID, productSKUCourseIds: GenericID[]) {
    //get course by organizationIds and productSKUCourseIds
    const output = await this.courseRepository.aggregate<CourseParams>([
      {
        $match: {
          organizationId,
          productSKUCourseId: { $in: productSKUCourseIds },
        },
      },
    ]);

    return output;
  }

  private getExistCourse(courseExistList: CourseParams[], organizationId: GenericID, productSKUCourseId: GenericID) {
    const output = courseExistList.find(
      (val) => val.organizationId === organizationId && val.productSKUCourseId === productSKUCourseId,
    );

    return output;
  }

  private async findEnrollmentByCourseIds(courseIds: GenericID[]) {
    const output = await this.enrollmentRepository.aggregate<Record<string, any>>([
      {
        $match: {
          courseId: { $in: courseIds },
        },
      },
      {
        $project: {
          id: 1,
          courseId: 1,
        },
      },
      {
        $group: {
          _id: '$courseId',
          courseId: { $first: '$courseId' },
        },
      },
    ]);

    return output;
  }

  private async findPreEnrollmentByCourseCodes(organizationId: GenericID, courseCodes: string[]) {
    const output = await this.preEnrollmentTransactionRepository.aggregate<Record<string, any>>([
      {
        $match: {
          organizationId,
          'payload.courseCode': { $in: courseCodes },
        },
      },
      {
        $project: {
          id: 1,
          organizationId: 1,
          code: '$payload.courseCode',
        },
      },
      {
        $group: {
          _id: { code: '$code', organizationId: '$organizationId' },
          code: { $first: '$code' },
          organizationId: { $first: '$organizationId' },
        },
      },
    ]);

    return output;
  }

  private checkEnroll(
    enrollmentExistList: Record<string, any>[],
    preEnrollmentTransactionExistList: Record<string, any>[],
    courseExist: CourseParams,
  ) {
    //get one enrollment by courseId
    const enrollmentExist = enrollmentExistList.find((val) => val.courseId === courseExist.id);
    if (enrollmentExist) {
      return true;
    }

    //get one pre-enrollment by code
    const preEnrollmentTransactionExist = preEnrollmentTransactionExistList.find(
      (val) => val.code === courseExist.code && val.organizationId === courseExist.organizationId,
    );
    if (preEnrollmentTransactionExist) {
      return true;
    }

    return false;
  }
}
