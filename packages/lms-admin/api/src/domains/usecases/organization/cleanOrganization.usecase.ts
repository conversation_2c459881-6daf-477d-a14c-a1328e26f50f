import { ClientSession } from 'mongodb';

import { AdminUserDIToken, OrganizationDIToken, PlanDIToken } from '@applications/di/domain';
import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import { InfrastructureServiceDIToken } from '@applications/di/infrastructures/service';
import { SlackChannelEnum } from '@domains/constants/enums/infrastructures/slackNotification.enum';
import { DeleteCollectionResultEnum } from '@domains/constants/enums/organization.enum';
import { CleanOrganizationParams, DeleteCollectionResultParams } from '@domains/constants/types/organization.type';
import {
  IAdminUserRepository,
  IOrganizationRepository,
  IPlanRepository,
} from '@domains/interfaces/infrastructures/repositories';
import { IWebhookService } from '@domains/interfaces/infrastructures/services/webhookService.interface';
import { IOrganizationService } from '@domains/interfaces/services/organization.service.interface';
import { ICleanOrganizationUseCase } from '@domains/interfaces/usecases/organization.usecase.interface';
import { SlackNotificationService } from '@domains/services/slackNotification.service';
import { date } from '@domains/utils/date.util';
import { DbClientParams, DbInstanceParams } from '@infrastructures/constants/types/database.type';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { Injectable, Inject } from '@nestjs/common';
import { difference } from 'lodash';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

@Injectable()
export class CleanOrganizationUseCase implements ICleanOrganizationUseCase {
  constructor(
    @Inject(InfrastructureConfigDIToken.DbClient)
    private readonly mongoClient: DbClientParams,
    @Inject(InfrastructureConfigDIToken.DbInstance)
    private readonly db: DbInstanceParams,
    @Inject(AdminUserDIToken.AdminUserRepository)
    private readonly adminUserRepository: IAdminUserRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @Inject(OrganizationDIToken.OrganizationService)
    private readonly organizationService: IOrganizationService,
    @Inject(InfrastructureServiceDIToken.WebhookService)
    private readonly webhookService: IWebhookService,
    @Inject(InfrastructureServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: CleanOrganizationParams): Promise<void> {
    const { adminUserId, organizationId, ignoreUserIds } = params;

    const adminUser = await this.adminUserRepository.findOne({ id: adminUserId });
    if (!adminUser) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Admin user not found.',
        data: { id: adminUserId },
      });
    }

    const organization = await this.organizationRepository.findOne({ id: organizationId });
    if (!organization) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Organization not found.',
        data: { id: organizationId },
      });
    }

    const startOperationTime = date();
    const planList = await this.planRepository.find({ organizationId: organization.id });
    const isSaleOrderApproved = this.organizationService.checkSaleOrderApproved(planList);
    if (isSaleOrderApproved) {
      throw Exception.new({
        code: Code.CONFLICT,
        message: 'Sale order approved.',
        data: { id: organizationId },
      });
    }

    const [users, courses, enrollments, knowledgeContentItems] = await Promise.all([
      this.db.collection(DBCollectionEnum.USERS).find({ organizationId }).toArray(),
      this.db.collection(DBCollectionEnum.COURSES).find({ organizationId }).toArray(),
      this.db.collection(DBCollectionEnum.ENROLLMENTS).find({ organizationId }).toArray(),
      this.db.collection(DBCollectionEnum.KNOWLEDGE_CONTENT_ITEMS).find({ organizationId }).toArray(),
    ]);

    const userIds = users.map((item) => item.guid);
    const courseIds = courses.map((item) => item.id);
    const enrollmentIds = enrollments.map((item) => item.id);
    const knowledgeContentItemIds = knowledgeContentItems.map((item) => item.id);

    const [
      enrollmentAttachments,
      learningPathEnrollments,
      jobs,
      preEnrollmentTransactions,
      knowledgeContentInteractions,
      ignorePermissionGroups,
    ] = await Promise.all([
      this.db
        .collection(DBCollectionEnum.ENROLLMENT_ATTACHMENTS)
        .find({ enrollmentId: { $in: enrollmentIds } })
        .toArray(),
      this.db.collection(DBCollectionEnum.LEARNING_PATH_ENROLLMENTS).find({ organizationId }).toArray(),
      this.db.collection(DBCollectionEnum.JOBS).find({ organizationId }).toArray(),
      this.db.collection(DBCollectionEnum.PRE_ENROLLMENT_TRANSACTIONS).find({ organizationId }).toArray(),
      this.db
        .collection(DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTIONS)
        .find({ knowledgeContentItemId: { $in: knowledgeContentItemIds } })
        .toArray(),
      this.db
        .collection(DBCollectionEnum.PERMISSION_GROUPS)
        .find({ organizationId, permissionIds: { $in: ['*'] } })
        .toArray(),
    ]);

    const enrollmentAttachmentIds = enrollmentAttachments.map((item) => item.id);
    const learningPathEnrollmentIds = learningPathEnrollments.map((item) => item.id);
    const jobIds = jobs.map((item) => item.guid);
    const preEnrollmentTransactionIds = preEnrollmentTransactions.map((item) => item.id);
    const knowledgeContentInteractionIds = knowledgeContentInteractions.map((item) => item.id);
    const ignorePermissionGroupIds = ignorePermissionGroups.map((item) => item.id);
    const removeUserIds = difference(userIds, ignoreUserIds);

    let isDeleteSuccess = false;
    const deleteResult = this.buildPrepDeleteResult();

    const session = this.mongoClient.startSession();
    session.startTransaction();
    const opts = { session };
    try {
      await this.updateUserPermissionGroup(ignoreUserIds, ignorePermissionGroupIds, opts);

      await this.deleteDataCollection(
        DBCollectionEnum.USERS,
        ignoreUserIds.length > 0 ? { organizationId, guid: { $nin: ignoreUserIds } } : { organizationId },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.LICENSES,
        ignoreUserIds.length > 0 ? { organizationId, userId: { $nin: ignoreUserIds } } : { organizationId },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.USER_LOGINS,
        { userId: { $in: removeUserIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.USERS_AUTHENTICATIONS,
        { userId: { $in: removeUserIds } },
        opts,
        deleteResult,
      );

      // remove by organization
      await this.deleteDataCollection(DBCollectionEnum.ENROLLMENTS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.USER_GROUPS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(
        DBCollectionEnum.LEARNING_PATH_ENROLLMENTS,
        { organizationId },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(DBCollectionEnum.DEPARTMENTS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.ROUNDS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.CLASSROOM_ROUNDS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.COMMENTS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.ANNOUNCEMENTS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.PROMOTE_NOTIFICATIONS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.LOGS_ACTIONS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.JOBS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(
        DBCollectionEnum.PRE_ENROLLMENT_RESERVATIONS,
        { organizationId },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.PRE_ENROLLMENT_TRANSACTIONS,
        { organizationId },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(DBCollectionEnum.PRE_ASSIGN_CONTENTS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.REPORT_HISTORIES, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.USER_DIRECT_REPORTS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.USER_NOTIFICATIONS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(DBCollectionEnum.USER_ACCESS_LOGS, { organizationId }, opts, deleteResult);

      await this.deleteDataCollection(
        DBCollectionEnum.PERMISSION_GROUPS,
        { organizationId, permissionIds: { $nin: ['*'] } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_TYPES,
        { organizationId },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(DBCollectionEnum.USERS_LOGIN_FAILS, { organizationId }, opts, deleteResult);

      // remove by enrollment
      await this.deleteDataCollection(
        DBCollectionEnum.ENROLLMENT_CERTIFICATES,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.SURVEY_SUBMISSIONS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.QUIZ_ANSWERS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.CLASSROOM_LOCATION_ENROLLMENTS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.ENROLLMENT_ATTACHMENTS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.REGISTRATIONS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.COURSE_ITEM_PROGRESS_HISTORIES,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.VERIFY_ENROLLMENT,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.LOGS_ACTIVITY_DETECT,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.LOGS_FACE_COMPARISON,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.LOGS_SNAPSHOT_ENROLLMENTS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.LOG_RESET_ENROLLMENTS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.IDENTIFICATION_CARDS,
        { enrollmentId: { $in: enrollmentIds } },
        opts,
        deleteResult,
      );

      // remove by course
      await this.deleteDataCollection(
        DBCollectionEnum.SUMMARY_TSI_QUIZ_SCORES,
        { courseId: { $in: courseIds } },
        opts,
        deleteResult,
      );

      // remove by enrollment attachment
      await this.deleteDataCollection(
        DBCollectionEnum.VERIFY_ENROLLMENT_ATTACHMENTS,
        { enrollmentAttachmentId: { $in: enrollmentAttachmentIds } },
        opts,
        deleteResult,
      );

      // remove by knowledge content item
      await this.deleteDataCollection(
        DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTIONS,
        { knowledgeContentItemId: { $in: knowledgeContentItemIds } },
        opts,
        deleteResult,
      );

      // remove by knowledge content interaction
      await this.deleteDataCollection(
        DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTION_HISTORIES,
        { knowledgeContentInteractionId: { $in: knowledgeContentInteractionIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.KNOWLEDGE_CONTENT_DURATION_HISTORIES,
        { knowledgeContentInteractionId: { $in: knowledgeContentInteractionIds } },
        opts,
        deleteResult,
      );

      // remove by learning path enrollment
      await this.deleteDataCollection(
        DBCollectionEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATES,
        { learningPathEnrollmentId: { $in: learningPathEnrollmentIds } },
        opts,
        deleteResult,
      );

      // remove by job
      await this.deleteDataCollection(
        DBCollectionEnum.JOB_TRANSACTIONS,
        { jobId: { $in: jobIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.PRE_ASSIGN_CONTENT_JOBS,
        { jobId: { $in: jobIds } },
        opts,
        deleteResult,
      );

      // remove by pre-enrollment transaction
      await this.deleteDataCollection(
        DBCollectionEnum.PRE_ENROLLMENT_TRANSACTION_HISTORIES,
        { preEnrollmentTransactionId: { $in: preEnrollmentTransactionIds } },
        opts,
        deleteResult,
      );

      await this.deleteDataCollection(
        DBCollectionEnum.ENROLLMENT_REGULATOR_REPORTS,
        { preEnrollmentTransactionId: { $in: preEnrollmentTransactionIds } },
        opts,
        deleteResult,
      );

      const deleteErrorResult = Object.values(deleteResult).find(
        (item) => item.status === DeleteCollectionResultEnum.FAILED,
      );

      if (deleteErrorResult) {
        throw Exception.new({
          code: Code.INTERNAL_SERVER_ERROR,
          message: `(${deleteErrorResult.collection}) ${deleteErrorResult.message}`,
          data: { id: organizationId, collection: deleteErrorResult.collection },
        });
      }

      isDeleteSuccess = true;
      await session.commitTransaction();
    } catch (error) {
      isDeleteSuccess = false;
      this.logger.error(`Clean organization failed, organizationId: ${organizationId}, errorMessage: ${error.message}`);
      await session.abortTransaction();
      throw error;
    } finally {
      const endOperationTime = date();
      session.endSession();

      // send notification to slack
      const useOperationTime = endOperationTime.diff(startOperationTime, 'seconds');
      const emailAdminUser = adminUser.email;
      const deleteAllCount = Object.values(deleteResult).length;
      const deleteSuccessCount = Object.values(deleteResult).filter(
        (item) => item.status === DeleteCollectionResultEnum.SUCCESS,
      ).length;

      const slackNotificationPayload = SlackNotificationService.buildCleanOrganizationPayload({
        isDeleteSuccess,
        useOperationTime,
        emailAdminUser,
        organizationDomain: organization.domain,
        organizationId: organization.id,
        deleteResult,
        deleteSuccessCount,
        deleteAllCount,
      });

      await this.webhookService.sendSlackNotification(slackNotificationPayload, SlackChannelEnum.BOT);
    }
  }

  private buildPrepDeleteResult(): Record<string, DeleteCollectionResultParams> {
    const collections = [
      DBCollectionEnum.USERS,
      DBCollectionEnum.LICENSES,
      DBCollectionEnum.USER_LOGINS,
      DBCollectionEnum.USERS_AUTHENTICATIONS,
      DBCollectionEnum.ENROLLMENTS,
      DBCollectionEnum.USER_GROUPS,
      DBCollectionEnum.LEARNING_PATH_ENROLLMENTS,
      DBCollectionEnum.DEPARTMENTS,
      DBCollectionEnum.ROUNDS,
      DBCollectionEnum.CLASSROOM_ROUNDS,
      DBCollectionEnum.COMMENTS,
      DBCollectionEnum.ANNOUNCEMENTS,
      DBCollectionEnum.PROMOTE_NOTIFICATIONS,
      DBCollectionEnum.LOGS_ACTIONS,
      DBCollectionEnum.JOBS,
      DBCollectionEnum.PRE_ENROLLMENT_RESERVATIONS,
      DBCollectionEnum.PRE_ENROLLMENT_TRANSACTIONS,
      DBCollectionEnum.PRE_ASSIGN_CONTENTS,
      DBCollectionEnum.REPORT_HISTORIES,
      DBCollectionEnum.USER_DIRECT_REPORTS,
      DBCollectionEnum.USER_NOTIFICATIONS,
      DBCollectionEnum.USER_ACCESS_LOGS,
      DBCollectionEnum.PERMISSION_GROUPS,
      DBCollectionEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_TYPES,
      DBCollectionEnum.USERS_LOGIN_FAILS,
      DBCollectionEnum.ENROLLMENT_CERTIFICATES,
      DBCollectionEnum.SURVEY_SUBMISSIONS,
      DBCollectionEnum.QUIZ_ANSWERS,
      DBCollectionEnum.CLASSROOM_LOCATION_ENROLLMENTS,
      DBCollectionEnum.ENROLLMENT_ATTACHMENTS,
      DBCollectionEnum.REGISTRATIONS,
      DBCollectionEnum.COURSE_ITEM_PROGRESS_HISTORIES,
      DBCollectionEnum.VERIFY_ENROLLMENT,
      DBCollectionEnum.LOGS_ACTIVITY_DETECT,
      DBCollectionEnum.LOGS_FACE_COMPARISON,
      DBCollectionEnum.LOGS_SNAPSHOT_ENROLLMENTS,
      DBCollectionEnum.LOG_RESET_ENROLLMENTS,
      DBCollectionEnum.IDENTIFICATION_CARDS,
      DBCollectionEnum.SUMMARY_TSI_QUIZ_SCORES,
      DBCollectionEnum.VERIFY_ENROLLMENT_ATTACHMENTS,
      DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTIONS,
      DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTION_HISTORIES,
      DBCollectionEnum.KNOWLEDGE_CONTENT_DURATION_HISTORIES,
      DBCollectionEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATES,
      DBCollectionEnum.JOB_TRANSACTIONS,
      DBCollectionEnum.PRE_ASSIGN_CONTENT_JOBS,
      DBCollectionEnum.PRE_ENROLLMENT_TRANSACTION_HISTORIES,
      DBCollectionEnum.ENROLLMENT_REGULATOR_REPORTS,
    ];

    const result: Record<string, DeleteCollectionResultParams> = {};

    for (const collection of collections) {
      result[collection] = {
        collection,
        deleteCount: '-',
        status: DeleteCollectionResultEnum.PENDING,
        message: '',
      };
    }

    return result;
  }

  private updateDeleteResult(
    result: Record<string, DeleteCollectionResultParams>,
    collection: DBCollectionEnum,
    deletedCount: Nullable<number>,
    status: string,
    message?: string,
  ) {
    if (!result[collection]) return result;

    if (status === DeleteCollectionResultEnum.SUCCESS) {
      result[collection].deleteCount = String(deletedCount ?? 0);
    }

    result[collection].collection = collection;
    result[collection].status = status;
    result[collection].message = message || '';

    return result;
  }

  private async deleteDataCollection(
    collection: DBCollectionEnum,
    filterQuery: Record<string, any>,
    mongoOpts: { session: ClientSession },
    deleteResult: Record<string, DeleteCollectionResultParams>,
  ) {
    try {
      const result = await this.db.collection(collection).deleteMany(filterQuery, mongoOpts);
      this.updateDeleteResult(deleteResult, collection, result?.deletedCount, DeleteCollectionResultEnum.SUCCESS);
    } catch (err) {
      this.updateDeleteResult(deleteResult, collection, null, DeleteCollectionResultEnum.FAILED, err.message);
    }
  }

  private async updateUserPermissionGroup(
    userIds: GenericID[],
    ignorePermissionGroupIds: GenericID[],
    mongoOpts: { session: ClientSession },
  ) {
    await this.db.collection(DBCollectionEnum.USERS).updateMany(
      { guid: { $in: userIds } },
      [
        {
          $set: {
            permissionGroupIds: {
              $filter: {
                input: '$permissionGroupIds',
                as: 'item',
                cond: { $in: ['$$item', ignorePermissionGroupIds] },
              },
            },
          },
        },
      ],
      mongoOpts,
    );
  }
}
