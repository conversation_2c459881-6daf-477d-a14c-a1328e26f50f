import { LicenseRenewalMapping, ApplicantTypeMapping, LicenseTypeMapping } from '@domains/constants/types/course.type';
import { InputCreateOrganizationParams } from '@domains/constants/types/organization.type';
import { IAdminUserRepository } from '@domains/interfaces/infrastructures/repositories/adminUser.repository.interface';
import { ILoginProviderRepository } from '@domains/interfaces/infrastructures/repositories/loginProvider.repository.interface';
import { IOrganizationRepository } from '@domains/interfaces/infrastructures/repositories/organization.repository.interface';
import { IOrganizationLoginProviderRepository } from '@domains/interfaces/infrastructures/repositories/organizationLoginProvider.repository.interface';
import { IOrganizationReportRepository } from '@domains/interfaces/infrastructures/repositories/organizationReport.repository.interface';
import { IOrganizationSchedulerRepository } from '@domains/interfaces/infrastructures/repositories/organizationScheduler.repository.interface';
import { IOrganizationStorageRepository } from '@domains/interfaces/infrastructures/repositories/organizationStorage.repository.interface';
import { IPermissionGroupRepository } from '@domains/interfaces/infrastructures/repositories/permissionGroup.repository.interface';
import { ITemplateColumnSettingRepository } from '@domains/interfaces/infrastructures/repositories/templateColumnSetting.repository.interface';
import { ITermsAndConditionsRepository } from '@domains/interfaces/infrastructures/repositories/termsAndConditions.repository.interface';
import { IOrganizationService } from '@domains/interfaces/services/organization.service.interface';
import { DbClientParams } from '@infrastructures/constants/types/database.type';
import { OrganizationDataMapper } from '@infrastructures/dataMappers';
import {
  RegulatorEnum,
  CourseObjectiveTypeEnum,
  LicenseTypeEnum,
  ApplicantTypeEnum,
  LicenseRenewalEnum,
  LicenseTypeTHEnum,
} from '@iso/lms/enums/course.enum';
import { UserLicenseTypeCodeEnum } from '@iso/lms/enums/license.enum';
import { LoginProviderMethodEnum, LoginProviderTypeEnum } from '@iso/lms/enums/loginProvider.enum';
import { AdminUser } from '@iso/lms/models/adminUser.model';
import { LoginProvider } from '@iso/lms/models/loginProvider.model';
import { OrganizationParams } from '@iso/lms/types/organization.type';

import { CreateOrganizationUseCase } from '../createOrganization.usecase';

// Mock implementations
const mockMongoClient: jest.Mocked<DbClientParams> = {
  startSession: jest.fn().mockReturnValue({
    startTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    abortTransaction: jest.fn(),
    endSession: jest.fn(),
  }),
} as any;

const mockAdminUserRepository: jest.Mocked<IAdminUserRepository> = {
  findOne: jest.fn(),
  // Add other methods if needed during test execution
} as any;

const mockLoginProviderRepository: jest.Mocked<ILoginProviderRepository> = {
  findOne: jest.fn(),
} as any;

const mockOrganizationRepository: jest.Mocked<IOrganizationRepository> = {
  findOne: jest.fn(),
  save: jest.fn(),
} as any;

const mockOrgLoginProviderRepository: jest.Mocked<IOrganizationLoginProviderRepository> = {
  save: jest.fn(),
} as any;

const mockOrgReportRepository: jest.Mocked<IOrganizationReportRepository> = {
  saveMany: jest.fn(),
} as any;

const mockOrgSchedulerRepository: jest.Mocked<IOrganizationSchedulerRepository> = {
  saveMany: jest.fn(),
} as any;

const mockOrgStorageRepository: jest.Mocked<IOrganizationStorageRepository> = {
  saveMany: jest.fn(),
} as any;

const mockPermissionGroupRepository: jest.Mocked<IPermissionGroupRepository> = {
  save: jest.fn(),
} as any;

const mockTemplateColumnSettingRepository: jest.Mocked<ITemplateColumnSettingRepository> = {
  saveMany: jest.fn(),
} as any;

const mockTermsAndConditionsRepository: jest.Mocked<ITermsAndConditionsRepository> = {
  save: jest.fn(),
} as any;

const mockOrganizationService: jest.Mocked<IOrganizationService> = {
  validateDomain: jest.fn(),
  convertToObjectiveTypeConfig: jest.fn(),
} as any;

const mockOrganizationDataMapper = new OrganizationDataMapper();

describe('CreateOrganizationUseCase', () => {
  let useCase: CreateOrganizationUseCase;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    useCase = new CreateOrganizationUseCase(
      mockMongoClient,
      mockAdminUserRepository,
      mockLoginProviderRepository,
      mockOrganizationRepository,
      mockOrgLoginProviderRepository,
      mockOrgReportRepository,
      mockOrgSchedulerRepository,
      mockOrgStorageRepository,
      mockPermissionGroupRepository,
      mockTemplateColumnSettingRepository,
      mockTermsAndConditionsRepository,
      mockOrganizationService,
    );

    // Default mock implementations
    mockAdminUserRepository.findOne.mockResolvedValue(
      new AdminUser({ id: 'admin-id', name: 'Admin', email: '<EMAIL>', isActive: true }),
    );
    mockOrganizationRepository.findOne.mockResolvedValue(null); // No existing organization
    mockOrganizationService.validateDomain.mockReturnValue(true);
    mockLoginProviderRepository.findOne.mockResolvedValue(
      new LoginProvider({
        id: 'local-provider',
        type: LoginProviderTypeEnum.LOCAL,
        method: LoginProviderMethodEnum.LOCAL,
        name: 'Local',
        payload: {},
      }),
    );
  });

  it('should correctly set licensesConfiguration and courseObjectiveConfigs when TFAC objective is provided', async () => {
    // Arrange
    const params: InputCreateOrganizationParams = {
      domain: 'tfac-test.com',
      name: 'TFAC Test Org',
      nameEng: 'TFAC Test Org Eng',
      objectives: [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.TFAC,
          trainingCenterConfigs: [{ trainingCenterCode: 'TFAC_CENTER', learningConfig: {} as any }],
          list: [
            {
              trainingCenterCode: 'TFAC_CENTER',
              trainingCenterName: 'TFAC Training Center',
              applicantType: ApplicantTypeEnum.ACCOUNTANT,
              licenseType: LicenseTypeEnum.CPA,
              licenseRenewal: LicenseRenewalEnum.NONE,
            },
            {
              trainingCenterCode: 'TFAC_CENTER',
              trainingCenterName: 'TFAC Training Center',
              applicantType: ApplicantTypeEnum.ACCOUNTANT,
              licenseType: LicenseTypeEnum.RA,
              licenseRenewal: LicenseRenewalEnum.NONE,
            },
          ],
        },
      ],
      createByAdminUserId: 'admin-id',
    };

    const expectedCourseObjectiveConfig = [
      {
        objectiveType: CourseObjectiveTypeEnum.TRAINING,
        regulators: [
          {
            name: RegulatorEnum.TFAC,
            trainingCenterKeys: ['TFAC_CENTER'],
            config: { subscriptionKey: '' },
            isEnabled: false,
          },
        ],
        trainingCenters: [
          {
            key: 'TFAC_CENTER',
            name: 'TFAC Training Center',
            regulator: RegulatorEnum.TFAC,
            licenseTypes: [
              {
                key: LicenseTypeEnum.CPA,
                name: LicenseTypeMapping[LicenseTypeEnum.CPA],
                applicantTypeKeys: [ApplicantTypeEnum.ACCOUNTANT],
                licenseRenewalKeys: [],
              },
              {
                key: LicenseTypeEnum.RA,
                name: LicenseTypeMapping[LicenseTypeEnum.RA],
                applicantTypeKeys: [ApplicantTypeEnum.ACCOUNTANT],
                licenseRenewalKeys: [],
              },
            ],
            applicantTypes: [
              {
                key: ApplicantTypeEnum.ACCOUNTANT,
                name: ApplicantTypeMapping[ApplicantTypeEnum.ACCOUNTANT],
                licenseRenewalKeys: [],
              },
            ],
            licenseRenewals: [],
            learningConfig: {} as any,
          },
        ],
        defaultLearningConfig: {} as any,
      },
    ];
    mockOrganizationService.convertToObjectiveTypeConfig.mockReturnValue(expectedCourseObjectiveConfig);

    // Mock the save method to capture the Organization instance
    let capturedOrganization: OrganizationParams;
    mockOrganizationRepository.save.mockImplementationOnce(async (orgModel) => {
      capturedOrganization = mockOrganizationDataMapper.toDTO(orgModel);
    });

    // Act
    await useCase.execute(params);

    // Assert
    expect(mockOrganizationRepository.save).toHaveBeenCalledTimes(1);
    expect(capturedOrganization).toBeDefined();

    // Assert licensesConfiguration
    const licensesConfig = capturedOrganization?.licensesConfiguration;
    expect(licensesConfig).toBeDefined();

    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_CPA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_CPA,
      isDisplay: true,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_RA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_RA,
      isDisplay: true,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TSI)).toEqual({
      type: UserLicenseTypeCodeEnum.TSI,
      isDisplay: false,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_LIFE)).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_LIFE,
      isDisplay: false,
    });
    expect(
      licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_NON_LIFE),
    ).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_NON_LIFE,
      isDisplay: false,
    });

    // Assert courseObjectiveConfigs
    expect(capturedOrganization?.courseObjectiveConfigs).toEqual(expectedCourseObjectiveConfig);
  });

  it('should correctly set licensesConfiguration and courseObjectiveConfigs when TSI objective is provided', async () => {
    const params: InputCreateOrganizationParams = {
      domain: 'tsi-test.com',
      name: 'TSI Test Org',
      nameEng: 'TSI Test Org Eng',
      objectives: [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.TSI,
          trainingCenterConfigs: [{ trainingCenterCode: 'TSI_CENTER', learningConfig: {} as any }],
          list: [
            {
              trainingCenterCode: 'TSI_CENTER',
              trainingCenterName: 'TSI Training Center',
              applicantType: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
              licenseType: LicenseTypeEnum.INVESTMENT,
              licenseRenewal: LicenseRenewalEnum.NONE,
            },
          ],
        },
      ],
      createByAdminUserId: 'admin-id',
    };

    const expectedCourseObjectiveConfig = [
      {
        objectiveType: CourseObjectiveTypeEnum.TRAINING,
        regulators: [
          {
            name: RegulatorEnum.TSI,
            trainingCenterKeys: ['TSI_CENTER'],
            config: { subscriptionKey: '' },
            isEnabled: false,
          },
        ],
        trainingCenters: [
          {
            key: 'TSI_CENTER',
            name: 'TSI Training Center',
            regulator: RegulatorEnum.TSI,
            licenseTypes: [
              {
                key: LicenseTypeEnum.INVESTMENT,
                name: LicenseTypeMapping[LicenseTypeEnum.INVESTMENT],
                applicantTypeKeys: [ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER],
                licenseRenewalKeys: [LicenseRenewalEnum.NONE],
              },
            ],
            applicantTypes: [
              {
                key: ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER,
                name: ApplicantTypeMapping[ApplicantTypeEnum.ADVISOR_ANALYST_PLANNER],
                licenseRenewalKeys: [LicenseRenewalEnum.NONE],
              },
            ],
            licenseRenewals: [{ key: LicenseRenewalEnum.NONE, name: LicenseRenewalMapping[LicenseRenewalEnum.NONE] }],
            learningConfig: {} as any,
          },
        ],
        defaultLearningConfig: {} as any,
      },
    ];
    mockOrganizationService.convertToObjectiveTypeConfig.mockReturnValue(expectedCourseObjectiveConfig);

    let capturedOrganization: OrganizationParams;
    mockOrganizationRepository.save.mockImplementationOnce(async (orgModel) => {
      capturedOrganization = mockOrganizationDataMapper.toDTO(orgModel);
    });

    await useCase.execute(params);

    expect(mockOrganizationRepository.save).toHaveBeenCalledTimes(1);
    const licensesConfig = capturedOrganization?.licensesConfiguration;

    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TSI)).toEqual({
      type: UserLicenseTypeCodeEnum.TSI,
      isDisplay: true,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_LIFE)).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_LIFE,
      isDisplay: false,
    });
    expect(
      licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_NON_LIFE),
    ).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_NON_LIFE,
      isDisplay: false,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_CPA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_CPA,
      isDisplay: false,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_RA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_RA,
      isDisplay: false,
    });

    expect(capturedOrganization?.courseObjectiveConfigs).toEqual(expectedCourseObjectiveConfig);
  });

  it('should correctly set licensesConfiguration and courseObjectiveConfigs when OIC objective with specific applicant and renewal types is provided', async () => {
    const params: InputCreateOrganizationParams = {
      domain: 'oic-test.com',
      name: 'OIC Test Org',
      nameEng: 'OIC Test Org Eng',
      objectives: [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.OIC,
          trainingCenterConfigs: [{ trainingCenterCode: 'OIC_CENTER', learningConfig: {} as any }],
          list: [
            {
              trainingCenterCode: 'OIC_CENTER',
              trainingCenterName: 'OIC TC',
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.LIFE,
              licenseRenewal: LicenseRenewalEnum.RENEW1,
            },
            {
              trainingCenterCode: 'OIC_CENTER',
              trainingCenterName: 'OIC TC',
              applicantType: ApplicantTypeEnum.BROKER,
              licenseType: LicenseTypeEnum.LIFE,
              licenseRenewal: LicenseRenewalEnum.RENEW2,
            },
            {
              trainingCenterCode: 'OIC_CENTER',
              trainingCenterName: 'OIC TC',
              applicantType: ApplicantTypeEnum.AGENT,
              licenseType: LicenseTypeEnum.NONLIFE,
              licenseRenewal: LicenseRenewalEnum.RENEW1,
            },
          ],
        },
      ],
      createByAdminUserId: 'admin-id',
    };

    const expectedCourseObjectiveConfig = [
      {
        objectiveType: CourseObjectiveTypeEnum.TRAINING,
        regulators: [
          {
            name: RegulatorEnum.OIC,
            trainingCenterKeys: ['OIC_CENTER'],
            config: { subscriptionKey: '' },
            isEnabled: false,
          },
        ],
        trainingCenters: [
          {
            key: 'OIC_CENTER',
            name: 'OIC TC',
            regulator: RegulatorEnum.OIC,
            licenseTypes: [
              {
                key: LicenseTypeEnum.LIFE,
                name: LicenseTypeMapping[LicenseTypeEnum.LIFE],
                applicantTypeKeys: [ApplicantTypeEnum.AGENT, ApplicantTypeEnum.BROKER],
                licenseRenewalKeys: [LicenseRenewalEnum.RENEW1, LicenseRenewalEnum.RENEW2],
              },
              {
                key: LicenseTypeEnum.NONLIFE,
                name: LicenseTypeMapping[LicenseTypeEnum.NONLIFE],
                applicantTypeKeys: [ApplicantTypeEnum.AGENT],
                licenseRenewalKeys: [LicenseRenewalEnum.RENEW1],
              },
            ],
            applicantTypes: [
              {
                key: ApplicantTypeEnum.AGENT,
                name: ApplicantTypeMapping[ApplicantTypeEnum.AGENT],
                licenseRenewalKeys: [LicenseRenewalEnum.RENEW1],
              },
              {
                key: ApplicantTypeEnum.BROKER,
                name: ApplicantTypeMapping[ApplicantTypeEnum.BROKER],
                licenseRenewalKeys: [LicenseRenewalEnum.RENEW2],
              },
            ],
            licenseRenewals: [
              { key: LicenseRenewalEnum.RENEW1, name: LicenseRenewalMapping[LicenseRenewalEnum.RENEW1] },
              { key: LicenseRenewalEnum.RENEW2, name: LicenseRenewalMapping[LicenseRenewalEnum.RENEW2] },
            ],
            learningConfig: {} as any,
          },
        ],
        defaultLearningConfig: {} as any,
      },
    ];
    mockOrganizationService.convertToObjectiveTypeConfig.mockReturnValue(expectedCourseObjectiveConfig);

    let capturedOrganization: OrganizationParams;
    mockOrganizationRepository.save.mockImplementationOnce(async (orgModel) => {
      capturedOrganization = mockOrganizationDataMapper.toDTO(orgModel);
    });

    await useCase.execute(params);

    expect(mockOrganizationRepository.save).toHaveBeenCalledTimes(1);
    const licensesConfig = capturedOrganization?.licensesConfiguration;

    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_LIFE)).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_LIFE,
      isDisplay: true,
    });
    expect(
      licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_NON_LIFE),
    ).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_NON_LIFE,
      isDisplay: true,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_CPA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_CPA,
      isDisplay: false,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_RA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_RA,
      isDisplay: false,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TSI)).toEqual({
      type: UserLicenseTypeCodeEnum.TSI,
      isDisplay: false,
    });

    expect(capturedOrganization?.courseObjectiveConfigs).toEqual(expectedCourseObjectiveConfig);
  });

  it('should correctly set licensesConfiguration for multiple regulators including TFAC', async () => {
    const params: InputCreateOrganizationParams = {
      domain: 'multi-reg.com',
      name: 'Multi Reg Org',
      nameEng: 'Multi Reg Org Eng',
      objectives: [
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.TFAC,
          trainingCenterConfigs: [{ trainingCenterCode: 'TFAC_MULTI', learningConfig: {} as any }],
          list: [],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.TSI,
          trainingCenterConfigs: [{ trainingCenterCode: 'TSI_MULTI', learningConfig: {} as any }],
          list: [],
        },
        {
          courseObjective: CourseObjectiveTypeEnum.TRAINING,
          regulator: RegulatorEnum.OIC,
          trainingCenterConfigs: [],
          list: [
            {
              trainingCenterCode: 'TII',
              trainingCenterName: 'TII',
              licenseType: LicenseTypeEnum.LIFE,
              applicantType: 'AGENT',
              licenseRenewal: 'NEW',
            },
          ],
        },
      ],
      createByAdminUserId: 'admin-id',
    };

    const expectedMultiCourseObjectiveConfig = [
      {
        objectiveType: CourseObjectiveTypeEnum.TRAINING,
        regulators: [
          {
            name: RegulatorEnum.TFAC,
            trainingCenterKeys: ['TFAC_MULTI'],
            config: { subscriptionKey: '' },
            isEnabled: false,
          },
          {
            name: RegulatorEnum.TSI,
            trainingCenterKeys: ['TSI_MULTI'],
            config: { subscriptionKey: '' },
            isEnabled: false,
          },
          { name: RegulatorEnum.OIC, trainingCenterKeys: ['TII'], config: { subscriptionKey: '' }, isEnabled: false },
        ],
        trainingCenters: [
          {
            key: 'TFAC_MULTI',
            name: 'TFAC_MULTI',
            regulator: RegulatorEnum.TFAC,
            licenseTypes: [
              {
                key: LicenseTypeEnum.CPA,
                name: LicenseTypeTHEnum[LicenseTypeEnum.CPA],
                licenseRenewalKeys: [],
                applicantTypeKeys: [ApplicantTypeEnum.ACCOUNTANT],
              },
              {
                key: LicenseTypeEnum.RA,
                name: LicenseTypeTHEnum[LicenseTypeEnum.RA],
                licenseRenewalKeys: [],
                applicantTypeKeys: [ApplicantTypeEnum.ACCOUNTANT],
              },
            ],
            applicantTypes: [
              {
                key: ApplicantTypeEnum.ACCOUNTANT,
                name: ApplicantTypeEnum.ACCOUNTANT,
                licenseRenewalKeys: [],
              },
            ],
            licenseRenewals: [],
            learningConfig: {} as any,
          },
          {
            key: 'TSI_MULTI',
            name: 'TSI_MULTI',
            regulator: RegulatorEnum.TSI,
            licenseTypes: [],
            applicantTypes: [],
            licenseRenewals: [],
            learningConfig: {} as any,
          },
          {
            key: 'TII',
            name: 'TII',
            regulator: RegulatorEnum.OIC,
            licenseTypes: [
              {
                key: LicenseTypeEnum.LIFE,
                name: LicenseTypeMapping[LicenseTypeEnum.LIFE],
                applicantTypeKeys: ['AGENT'],
                licenseRenewalKeys: [LicenseRenewalEnum.NEW],
              },
            ],
            applicantTypes: [{ key: 'AGENT', name: 'Agent', licenseRenewalKeys: [LicenseRenewalEnum.NEW] }],
            licenseRenewals: [{ key: 'NEW', name: 'New' }],
            learningConfig: {} as any,
          },
        ],
        defaultLearningConfig: {} as any,
      },
    ];
    mockOrganizationService.convertToObjectiveTypeConfig.mockReturnValue(expectedMultiCourseObjectiveConfig);

    let capturedOrganization: OrganizationParams;
    mockOrganizationRepository.save.mockImplementationOnce(async (orgModel) => {
      capturedOrganization = mockOrganizationDataMapper.toDTO(orgModel);
    });

    await useCase.execute(params);

    expect(mockOrganizationRepository.save).toHaveBeenCalledTimes(1);
    const licensesConfig = capturedOrganization?.licensesConfiguration;

    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_CPA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_CPA,
      isDisplay: true,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TFAC_RA)).toEqual({
      type: UserLicenseTypeCodeEnum.TFAC_RA,
      isDisplay: true,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.TSI)).toEqual({
      type: UserLicenseTypeCodeEnum.TSI,
      isDisplay: true,
    });
    expect(licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_LIFE)).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_LIFE,
      isDisplay: true,
    });
    expect(
      licensesConfig?.availableLicensesTypes.find((lt) => lt.type === UserLicenseTypeCodeEnum.OIC_NON_LIFE),
    ).toEqual({
      type: UserLicenseTypeCodeEnum.OIC_NON_LIFE,
      isDisplay: false,
    });

    expect(capturedOrganization?.courseObjectiveConfigs).toEqual(expectedMultiCourseObjectiveConfig);
  });
});
