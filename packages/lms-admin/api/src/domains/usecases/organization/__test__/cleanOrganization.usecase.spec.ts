import { MongoClient } from 'mongodb';

import {
  IAdminUserRepository,
  IOrganizationRepository,
  IPlanRepository,
} from '@domains/interfaces/infrastructures/repositories';
import { IWebhookService } from '@domains/interfaces/infrastructures/services/webhookService.interface';
import { IOrganizationService } from '@domains/interfaces/services/organization.service.interface';
import { ICleanOrganizationUseCase } from '@domains/interfaces/usecases/organization.usecase.interface';
import { createMockAdminUser } from '@domains/models/__test__/mockData/adminUser.spec';
import { createMockAnnouncement } from '@domains/models/__test__/mockData/announcement.spec';
import { createMockClassroomLocationEnrollment } from '@domains/models/__test__/mockData/classroomLocationEnrollment.spec';
import { createMockClassroomRound } from '@domains/models/__test__/mockData/classroomRound.spec';
import { createMockComment } from '@domains/models/__test__/mockData/comment.spec';
import { createMockCourse } from '@domains/models/__test__/mockData/course.spec';
import { createMockCourseItemProgressHistory } from '@domains/models/__test__/mockData/courseItemProgressHistory.spec';
import { createMockDepartment } from '@domains/models/__test__/mockData/department.spec';
import { createMockEnrollment } from '@domains/models/__test__/mockData/enrollment.spec';
import { createMockEnrollmentAttachment } from '@domains/models/__test__/mockData/enrollmentAttachment.spec';
import { createMockEnrollmentAttachmentAdditionalType } from '@domains/models/__test__/mockData/enrollmentAttachmentAdditionalType.spec';
import { createMockEnrollmentCertificate } from '@domains/models/__test__/mockData/enrollmentCertificate.spec';
import { createMockEnrollmentRegulatorReport } from '@domains/models/__test__/mockData/enrollmentRegulatorReport.spec';
import { createMockIdentificationCard } from '@domains/models/__test__/mockData/identificationCard.spec';
import { createMockJob } from '@domains/models/__test__/mockData/job.spec';
import { createMockJobTransaction } from '@domains/models/__test__/mockData/jobTransaction.spec';
import { createMockKnowledgeContentDurationHistory } from '@domains/models/__test__/mockData/knowledgeContentDurationHistory.spec';
import { createMockKnowledgeContentInteraction } from '@domains/models/__test__/mockData/knowledgeContentInteraction.spec';
import { createMockKnowledgeContentInteractionHistory } from '@domains/models/__test__/mockData/knowledgeContentInteractionHistory.spec';
import { createMockKnowledgeContentItem } from '@domains/models/__test__/mockData/knowledgeContentItem.spec';
import { createMockLearningPathEnrollment } from '@domains/models/__test__/mockData/learningPathEnrollment.spec';
import { createMockLearningPathEnrollmentCertificate } from '@domains/models/__test__/mockData/learningPathEnrollmentCertificate.spec';
import { createMockLicense } from '@domains/models/__test__/mockData/license.spec';
import { createMockLogAction } from '@domains/models/__test__/mockData/logAction.spec';
import { createMockLogActivityDetect } from '@domains/models/__test__/mockData/logActivityDetect.spec';
import { createMockLogFaceComparison } from '@domains/models/__test__/mockData/logFaceComparison.spec';
import { createMockLogResetEnrollment } from '@domains/models/__test__/mockData/logResetEnrollment.spec';
import { createMockLogSnapshotEnrollment } from '@domains/models/__test__/mockData/logSnapshotEnrollment.spec';
import { createMockOrganization } from '@domains/models/__test__/mockData/organization.spec';
import { createMockPermissionGroup } from '@domains/models/__test__/mockData/permissionGroup.spec';
import { createMockPreAssignContent } from '@domains/models/__test__/mockData/preAssignContent.spec';
import { createMockPreAssignContentJob } from '@domains/models/__test__/mockData/preAssignContentJob.spec';
import { createMockPreEnrollmentReservation } from '@domains/models/__test__/mockData/preEnrollmentReservation.spec';
import { createMockPreEnrollmentTransactions } from '@domains/models/__test__/mockData/preEnrollmentTransaction.spec';
import { createMockPreEnrollmentTransactionHistory } from '@domains/models/__test__/mockData/preEnrollmentTransactionHistory.spec';
import { createMockPromoteNotification } from '@domains/models/__test__/mockData/promoteNotification.spec';
import { createMockQuizAnswer } from '@domains/models/__test__/mockData/quizAnswer.spec';
import { createMockRegistration } from '@domains/models/__test__/mockData/registration.spec';
import { createMockReportHistory } from '@domains/models/__test__/mockData/reportHistory.spec';
import { createMockRound } from '@domains/models/__test__/mockData/round.spec';
import { createMockSummaryTsiQuizScore } from '@domains/models/__test__/mockData/summaryTsiQuizScore.spec';
import { createMockSurveySubmission } from '@domains/models/__test__/mockData/surveySubmission.spec';
import { createMockUser } from '@domains/models/__test__/mockData/user.spec';
import { createMockUserAccessLog } from '@domains/models/__test__/mockData/userAccessLog.spec';
import { createMockUsersAuthentication } from '@domains/models/__test__/mockData/userAuthentication.spec';
import { createMockUserDirectReport } from '@domains/models/__test__/mockData/userDirectReport.spec';
import { createMockUserGroup } from '@domains/models/__test__/mockData/userGroup.spec';
import { createMockUserLogin } from '@domains/models/__test__/mockData/userLogin.spec';
import { createMockUserLoginFail } from '@domains/models/__test__/mockData/userLoginFail.spec';
import { createMockUserNotification } from '@domains/models/__test__/mockData/userNotification.spec';
import { createMockVerifyEnrollment } from '@domains/models/__test__/mockData/verifyEnrollment.spec';
import { createMockVerifyEnrollmentAttachment } from '@domains/models/__test__/mockData/verifyEnrollmentAttachment.spec';
import { CleanOrganizationUseCase } from '@domains/usecases/organization/cleanOrganization.usecase';
import { DbInstanceParams } from '@infrastructures/constants/types/database.type';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { instanceToPlain } from 'class-transformer';
import { mock, MockProxy } from 'jest-mock-extended';
import { isArray } from 'lodash';

const toDAL = (entity: any) => instanceToPlain(entity, { excludePrefixes: ['_'] });

describe('Test Use Case: CleanOrganizationUseCase', () => {
  let mockUseCase: ICleanOrganizationUseCase;

  let mockMongoClient: MockProxy<MongoClient>;
  let mockDB: MockProxy<DbInstanceParams>;
  let mockAdminUserRepository: MockProxy<IAdminUserRepository>;
  let mockOrganizationRepository: MockProxy<IOrganizationRepository>;
  let mockPlanRepository: MockProxy<IPlanRepository>;
  let mockOrganizationService: MockProxy<IOrganizationService>;
  let mockWebhookService: MockProxy<IWebhookService>;
  let mockLogger: MockProxy<ILogger>;

  const mockAdminUser = createMockAdminUser();
  const mockOrganization = createMockOrganization({ id: 'mock_organization1' });
  const mockUser = createMockUser({ guid: 'mock_user1', organizationId: mockOrganization.id });
  const mockEnrollment = createMockEnrollment({ organizationId: mockOrganization.id });
  const mockKnowledgeContentItem = createMockKnowledgeContentItem({ organizationId: mockOrganization.id });
  const mockKnowledgeContentInteraction = createMockKnowledgeContentInteraction({
    knowledgeContentItemId: mockKnowledgeContentItem.id,
  });
  const mockCourse = createMockCourse({ organizationId: mockOrganization.id });
  const mockEnrollmentAttachment = createMockEnrollmentAttachment({ enrollmentId: mockEnrollment.id });
  const mockPreEnrollmentReservation = createMockPreEnrollmentReservation({ organizationId: mockOrganization.id });
  const mockPreEnrollmentTransaction = createMockPreEnrollmentTransactions({ organizationId: mockOrganization.id });
  const mockLearningPathEnrollment = createMockLearningPathEnrollment({ organizationId: mockOrganization.id });
  const mockJob = createMockJob({ organizationId: mockOrganization.id });

  const defaultMockDatabase = Object.freeze({
    [DBCollectionEnum.USERS]: [toDAL(mockUser), toDAL(createMockUser())],
    [DBCollectionEnum.LICENSES]: [
      toDAL(createMockLicense({ organizationId: mockUser.organizationId, userId: mockUser.guid })),
      toDAL(createMockLicense()),
    ],
    [DBCollectionEnum.USER_LOGINS]: [
      toDAL(createMockUserLogin({ userId: mockUser.guid })),
      toDAL(createMockUserLogin()),
    ],
    [DBCollectionEnum.COURSES]: [toDAL(mockCourse), toDAL(createMockCourse())],
    [DBCollectionEnum.ENROLLMENTS]: [toDAL(mockEnrollment), toDAL(createMockEnrollment())],
    [DBCollectionEnum.PRE_ENROLLMENT_RESERVATIONS]: [
      toDAL(mockPreEnrollmentReservation),
      toDAL(createMockPreEnrollmentReservation()),
    ],
    [DBCollectionEnum.ENROLLMENT_ATTACHMENTS]: [
      toDAL(mockEnrollmentAttachment),
      toDAL(createMockEnrollmentAttachment()),
    ],
    [DBCollectionEnum.KNOWLEDGE_CONTENT_ITEMS]: [
      toDAL(mockKnowledgeContentItem),
      toDAL(createMockKnowledgeContentItem()),
    ],
    [DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTIONS]: [
      toDAL(mockKnowledgeContentInteraction),
      toDAL(createMockKnowledgeContentInteraction()),
    ],
    [DBCollectionEnum.LEARNING_PATH_ENROLLMENTS]: [
      toDAL(mockLearningPathEnrollment),
      toDAL(createMockLearningPathEnrollment()),
    ],
    [DBCollectionEnum.JOBS]: [toDAL(mockJob), toDAL(createMockJob())],
    [DBCollectionEnum.PRE_ENROLLMENT_TRANSACTIONS]: [
      toDAL(mockPreEnrollmentTransaction),
      toDAL(createMockPreEnrollmentTransactions()),
    ],
    [DBCollectionEnum.USER_GROUPS]: [
      toDAL(createMockUserGroup({ organizationId: mockOrganization.id })),
      toDAL(createMockUserGroup()),
    ],
    [DBCollectionEnum.DEPARTMENTS]: [
      toDAL(createMockDepartment({ organizationId: mockOrganization.id })),
      toDAL(createMockDepartment()),
    ],
    [DBCollectionEnum.ROUNDS]: [
      toDAL(createMockRound({ organizationId: mockOrganization.id })),
      toDAL(createMockRound()),
    ],
    [DBCollectionEnum.CLASSROOM_ROUNDS]: [
      toDAL(createMockClassroomRound({ organizationId: mockOrganization.id })),
      toDAL(createMockClassroomRound()),
    ],
    [DBCollectionEnum.COMMENTS]: [
      toDAL(createMockComment({ organizationId: mockOrganization.id })),
      toDAL(createMockComment()),
    ],
    [DBCollectionEnum.ANNOUNCEMENTS]: [
      toDAL(createMockAnnouncement({ organizationId: mockOrganization.id })),
      toDAL(createMockAnnouncement()),
    ],
    [DBCollectionEnum.PROMOTE_NOTIFICATIONS]: [
      toDAL(createMockPromoteNotification({ organizationId: mockOrganization.id })),
      toDAL(createMockPromoteNotification()),
    ],
    [DBCollectionEnum.LOGS_ACTIONS]: [
      toDAL(createMockLogAction({ organizationId: mockOrganization.id })),
      toDAL(createMockLogAction()),
    ],
    [DBCollectionEnum.REPORT_HISTORIES]: [
      toDAL(createMockReportHistory({ organizationId: mockOrganization.id })),
      toDAL(createMockReportHistory()),
    ],
    [DBCollectionEnum.USER_DIRECT_REPORTS]: [
      toDAL(createMockUserDirectReport({ organizationId: mockOrganization.id })),
      toDAL(createMockUserDirectReport()),
    ],
    [DBCollectionEnum.USER_NOTIFICATIONS]: [
      toDAL(createMockUserNotification({ organizationId: mockOrganization.id })),
      toDAL(createMockUserNotification()),
    ],
    [DBCollectionEnum.USER_ACCESS_LOGS]: [
      toDAL(createMockUserAccessLog({ organizationId: mockOrganization.id })),
      toDAL(createMockUserAccessLog()),
    ],
    [DBCollectionEnum.PERMISSION_GROUPS]: [
      toDAL(createMockPermissionGroup({ organizationId: mockOrganization.id, permissionIds: ['*'] })),
      toDAL(
        createMockPermissionGroup({
          organizationId: mockOrganization.id,
          permissionIds: ['mock_permission1', 'mock_permission2'],
        }),
      ),
      toDAL(createMockPermissionGroup()),
    ],
    [DBCollectionEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_TYPES]: [
      toDAL(createMockEnrollmentAttachmentAdditionalType({ organizationId: mockOrganization.id })),
      toDAL(createMockEnrollmentAttachmentAdditionalType()),
    ],
    [DBCollectionEnum.USERS_LOGIN_FAILS]: [
      toDAL(createMockUserLoginFail({ organizationId: mockOrganization.id })),
      toDAL(createMockUserLoginFail()),
    ],
    [DBCollectionEnum.ENROLLMENT_CERTIFICATES]: [
      toDAL(createMockEnrollmentCertificate({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockEnrollmentCertificate()),
    ],
    [DBCollectionEnum.SURVEY_SUBMISSIONS]: [
      toDAL(createMockSurveySubmission({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockSurveySubmission()),
    ],
    [DBCollectionEnum.QUIZ_ANSWERS]: [
      toDAL(createMockQuizAnswer({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockQuizAnswer()),
    ],
    [DBCollectionEnum.CLASSROOM_LOCATION_ENROLLMENTS]: [
      toDAL(createMockClassroomLocationEnrollment({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockClassroomLocationEnrollment()),
    ],
    [DBCollectionEnum.REGISTRATIONS]: [
      toDAL(createMockRegistration({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockRegistration()),
    ],
    [DBCollectionEnum.COURSE_ITEM_PROGRESS_HISTORIES]: [
      toDAL(createMockCourseItemProgressHistory({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockCourseItemProgressHistory()),
    ],
    [DBCollectionEnum.VERIFY_ENROLLMENT]: [
      toDAL(createMockVerifyEnrollment({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockVerifyEnrollment()),
    ],
    [DBCollectionEnum.LOGS_ACTIVITY_DETECT]: [
      toDAL(createMockLogActivityDetect({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockLogActivityDetect()),
    ],
    [DBCollectionEnum.LOGS_FACE_COMPARISON]: [
      toDAL(createMockLogFaceComparison({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockLogFaceComparison()),
    ],
    [DBCollectionEnum.LOGS_SNAPSHOT_ENROLLMENTS]: [
      toDAL(createMockLogSnapshotEnrollment({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockLogSnapshotEnrollment()),
    ],
    [DBCollectionEnum.LOG_RESET_ENROLLMENTS]: [
      toDAL(createMockLogResetEnrollment({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockLogResetEnrollment()),
    ],
    [DBCollectionEnum.IDENTIFICATION_CARDS]: [
      toDAL(createMockIdentificationCard({ enrollmentId: mockEnrollment.id })),
      toDAL(createMockIdentificationCard()),
    ],
    [DBCollectionEnum.SUMMARY_TSI_QUIZ_SCORES]: [
      toDAL(createMockSummaryTsiQuizScore({ courseId: mockCourse.id })),
      toDAL(createMockSummaryTsiQuizScore()),
    ],
    [DBCollectionEnum.VERIFY_ENROLLMENT_ATTACHMENTS]: [
      toDAL(createMockVerifyEnrollmentAttachment({ enrollmentAttachmentId: mockEnrollmentAttachment.id })),
      toDAL(createMockVerifyEnrollmentAttachment()),
    ],
    [DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTION_HISTORIES]: [
      toDAL(
        createMockKnowledgeContentInteractionHistory({
          knowledgeContentInteractionId: mockKnowledgeContentInteraction.id,
        }),
      ),
      toDAL(createMockKnowledgeContentInteractionHistory()),
    ],
    [DBCollectionEnum.KNOWLEDGE_CONTENT_DURATION_HISTORIES]: [
      toDAL(
        createMockKnowledgeContentDurationHistory({
          knowledgeContentInteractionId: mockKnowledgeContentInteraction.id,
        }),
      ),
      toDAL(createMockKnowledgeContentDurationHistory()),
    ],
    [DBCollectionEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATES]: [
      toDAL(createMockLearningPathEnrollmentCertificate({ learningPathEnrollmentId: mockLearningPathEnrollment.id })),
      toDAL(createMockLearningPathEnrollmentCertificate()),
    ],
    [DBCollectionEnum.JOB_TRANSACTIONS]: [
      toDAL(createMockJobTransaction({ jobId: mockJob.guid })),
      toDAL(createMockJobTransaction()),
    ],
    [DBCollectionEnum.PRE_ASSIGN_CONTENT_JOBS]: [
      toDAL(createMockPreAssignContentJob({ jobId: mockJob.guid })),
      toDAL(createMockPreAssignContentJob()),
    ],
    [DBCollectionEnum.PRE_ASSIGN_CONTENTS]: [
      toDAL(createMockPreAssignContent({ organizationId: mockOrganization.id })),
      toDAL(createMockPreAssignContent()),
    ],
    [DBCollectionEnum.PRE_ENROLLMENT_TRANSACTION_HISTORIES]: [
      toDAL(createMockPreEnrollmentTransactionHistory({ preEnrollmentTransactionId: mockPreEnrollmentTransaction.id })),
      toDAL(createMockPreEnrollmentTransactionHistory()),
    ],
    [DBCollectionEnum.ENROLLMENT_REGULATOR_REPORTS]: [
      toDAL(createMockEnrollmentRegulatorReport({ preEnrollmentTransactionId: mockPreEnrollmentTransaction.id })),
      toDAL(createMockEnrollmentRegulatorReport()),
    ],
    [DBCollectionEnum.USERS_AUTHENTICATIONS]: [
      createMockUsersAuthentication({ userId: mockUser.guid }),
      createMockUsersAuthentication(),
    ],
  });

  const mockDatabase = { ...defaultMockDatabase };

  beforeEach(() => {
    (global as any).mockDatabase = JSON.parse(JSON.stringify(defaultMockDatabase));

    mockMongoClient = mock<MongoClient>({
      startSession: jest.fn().mockReturnValue({
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        abortTransaction: jest.fn(),
        endSession: jest.fn(),
      }),
    });

    mockDB = mock<DbInstanceParams>({
      collection: jest.fn().mockImplementation((collectionName: string) => {
        return {
          deleteMany: jest.fn().mockImplementation((filter: Record<string, any>, _options: any) => {
            let deletedCount = 0;
            if (Object.keys(filter).length === 0) {
              mockDatabase[collectionName] = [];
              return {
                deletedCount: 0,
              };
            } else {
              mockDatabase[collectionName] = mockDatabase[collectionName].filter((item) => {
                for (const key in filter) {
                  const filterValue = filter[key];
                  const itemValue = item[key];

                  if (filterValue && typeof filterValue === 'object') {
                    if (filterValue.$in && !filterValue.$in.includes(itemValue)) {
                      return true;
                    } else if (filterValue.$nin) {
                      if (isArray(itemValue)) {
                        for (const value of itemValue) {
                          if (filterValue.$nin.includes(value)) {
                            return true;
                          }
                        }
                      } else if (filterValue.$nin.includes(itemValue)) {
                        return true;
                      }
                    } else if (filterValue.$ne && filterValue.$ne === itemValue) {
                      return true;
                    }
                  } else if (filterValue !== itemValue) {
                    return true;
                  }
                }

                deletedCount++;
                return false;
              });

              return {
                deletedCount,
              };
            }
          }),
          find: jest.fn().mockImplementation((filter: Record<string, any>, _options: any) => {
            let result = mockDatabase[collectionName];
            if (result && filter) {
              result = result.filter((item) => {
                for (const key in filter) {
                  const filterValue = filter[key];
                  const itemValue = item[key];
                  if (filterValue && typeof filterValue === 'object') {
                    if (filterValue.$in && !filterValue.$in.includes(itemValue)) {
                      return false;
                    } else if (filterValue.$nin && filterValue.$nin.includes(itemValue)) {
                      return false;
                    } else if (filterValue.$ne && filterValue.$ne === itemValue) {
                      return false;
                    }
                  } else if (filterValue !== itemValue) {
                    return false;
                  }
                }
                return true;
              });
            }

            return { toArray: jest.fn().mockResolvedValue(result) };
          }),
          updateMany: jest.fn(),
        };
      }),
    });

    mockAdminUserRepository = mock<IAdminUserRepository>();
    mockOrganizationRepository = mock<IOrganizationRepository>();
    mockPlanRepository = mock<IPlanRepository>();
    mockOrganizationService = mock<IOrganizationService>();
    mockWebhookService = mock<IWebhookService>();
    mockLogger = mock<ILogger>();

    mockUseCase = new CleanOrganizationUseCase(
      mockMongoClient,
      mockDB,
      mockAdminUserRepository,
      mockOrganizationRepository,
      mockPlanRepository,
      mockOrganizationService,
      mockWebhookService,
      mockLogger,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
    jest.restoreAllMocks();

    Object.assign(mockDatabase, JSON.parse(JSON.stringify(defaultMockDatabase)));
  });

  it('when organization not found, expect: throw error', async () => {
    const params = {
      adminUserId: 'mock_adminUser',
      organizationId: 'mock_organization1',
      ignoreUserIds: [],
    };

    mockAdminUserRepository.findOne.mockResolvedValue(mockAdminUser);
    mockOrganizationRepository.findOne.mockResolvedValue(null);

    expect(() => mockUseCase.execute(params)).rejects.toThrow('Organization not found.');
  });

  it('when organization sale order approved!, expect: throw error', async () => {
    const params = {
      adminUserId: 'mock_adminUser',
      organizationId: 'mock_organization1',
      ignoreUserIds: [],
    };

    mockAdminUserRepository.findOne.mockResolvedValue(mockAdminUser);
    mockOrganizationRepository.findOne.mockResolvedValue(mockOrganization);
    mockOrganizationService.checkSaleOrderApproved.mockReturnValue(true);

    expect(() => mockUseCase.execute(params)).rejects.toThrow('Sale order approved.');
  });

  it('when deleted data all users in organization, expect reduce data each collections', async () => {
    const params = {
      adminUserId: 'mock_adminUser',
      organizationId: 'mock_organization1',
      ignoreUserIds: [],
    };

    mockAdminUserRepository.findOne.mockResolvedValue(mockAdminUser);
    mockOrganizationRepository.findOne.mockResolvedValue(mockOrganization);
    mockOrganizationService.checkSaleOrderApproved.mockReturnValue(false);

    await mockUseCase.execute(params);

    expect(mockDatabase[DBCollectionEnum.USERS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_LOGINS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LICENSES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USERS_AUTHENTICATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_GROUPS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LEARNING_PATH_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.DEPARTMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ROUNDS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.CLASSROOM_ROUNDS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.COMMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ANNOUNCEMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PROMOTE_NOTIFICATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_ACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.JOBS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ENROLLMENT_RESERVATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.REPORT_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_DIRECT_REPORTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_NOTIFICATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_ACCESS_LOGS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PERMISSION_GROUPS].length).toBe(2);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_TYPES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USERS_LOGIN_FAILS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_CERTIFICATES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.SURVEY_SUBMISSIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.QUIZ_ANSWERS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.CLASSROOM_LOCATION_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_ATTACHMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.REGISTRATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.COURSE_ITEM_PROGRESS_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.VERIFY_ENROLLMENT].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_ACTIVITY_DETECT].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_FACE_COMPARISON].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_SNAPSHOT_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOG_RESET_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.IDENTIFICATION_CARDS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.SUMMARY_TSI_QUIZ_SCORES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.VERIFY_ENROLLMENT_ATTACHMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTION_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.KNOWLEDGE_CONTENT_DURATION_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.JOB_TRANSACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ASSIGN_CONTENT_JOBS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ENROLLMENT_TRANSACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ASSIGN_CONTENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ENROLLMENT_TRANSACTION_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_REGULATOR_REPORTS].length).toBe(1);
  });

  it('when have ignore user, expect reduce data each collections', async () => {
    const params = {
      adminUserId: 'mock_adminUser',
      organizationId: 'mock_organization1',
      ignoreUserIds: ['mock_user1'],
    };

    mockAdminUserRepository.findOne.mockResolvedValue(mockAdminUser);
    mockOrganizationRepository.findOne.mockResolvedValue(mockOrganization);
    mockOrganizationService.checkSaleOrderApproved.mockReturnValue(false);

    await mockUseCase.execute(params);

    expect(mockDatabase[DBCollectionEnum.USERS].length).toBe(2);
    expect(mockDatabase[DBCollectionEnum.USER_LOGINS].length).toBe(2);
    expect(mockDatabase[DBCollectionEnum.LICENSES].length).toBe(2);
    expect(mockDatabase[DBCollectionEnum.USERS_AUTHENTICATIONS].length).toBe(2);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_GROUPS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LEARNING_PATH_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.DEPARTMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ROUNDS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.CLASSROOM_ROUNDS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.COMMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ANNOUNCEMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PROMOTE_NOTIFICATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_ACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.JOBS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ENROLLMENT_RESERVATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.REPORT_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_DIRECT_REPORTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_NOTIFICATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USER_ACCESS_LOGS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PERMISSION_GROUPS].length).toBe(2);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_TYPES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.USERS_LOGIN_FAILS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_CERTIFICATES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.SURVEY_SUBMISSIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.QUIZ_ANSWERS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.CLASSROOM_LOCATION_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_ATTACHMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.REGISTRATIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.COURSE_ITEM_PROGRESS_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.VERIFY_ENROLLMENT].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_ACTIVITY_DETECT].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_FACE_COMPARISON].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOGS_SNAPSHOT_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LOG_RESET_ENROLLMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.IDENTIFICATION_CARDS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.SUMMARY_TSI_QUIZ_SCORES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.VERIFY_ENROLLMENT_ATTACHMENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.KNOWLEDGE_CONTENT_INTERACTION_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.KNOWLEDGE_CONTENT_DURATION_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.JOB_TRANSACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ASSIGN_CONTENT_JOBS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ENROLLMENT_TRANSACTIONS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ASSIGN_CONTENTS].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.PRE_ENROLLMENT_TRANSACTION_HISTORIES].length).toBe(1);
    expect(mockDatabase[DBCollectionEnum.ENROLLMENT_REGULATOR_REPORTS].length).toBe(1);
  });

  it('when end process then send notification slack', async () => {
    const params = {
      adminUserId: 'mock_adminUser',
      organizationId: 'mock_organization1',
      ignoreUserIds: ['mock_user1'],
    };

    mockAdminUserRepository.findOne.mockResolvedValue(mockAdminUser);
    mockOrganizationRepository.findOne.mockResolvedValue(mockOrganization);
    mockOrganizationService.checkSaleOrderApproved.mockReturnValue(false);

    await mockUseCase.execute(params);

    expect(mockWebhookService.sendSlackNotification).toHaveBeenCalled();
  });
});
