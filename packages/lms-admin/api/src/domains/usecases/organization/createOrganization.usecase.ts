import {
  Admin<PERSON>ser<PERSON>Token,
  ColumnSettingDIToken,
  OrganizationDIToken,
  OrganizationLoginProviderDIToken,
  OrganizationReportDIToken,
  OrganizationSchedulerDIToken,
  OrganizationStorageDIToken,
  LoginProviderDIToken,
  PermissionGroupDIToken,
  TermsAndConditionsDIToken,
} from '@applications/di/domain';
import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import {
  PredefinedCreateOrganization,
  InputCreateOrganizationParams,
} from '@domains/constants/types/organization.type';
import { PredefinedCreateOrganizationReportList } from '@domains/constants/types/organizationReport.type';
import { PredefinedCreateOrganizationSchedulerList } from '@domains/constants/types/organizationScheduler.type';
import { PredefinedCreateOrganizationStorageList } from '@domains/constants/types/organizationStorage.type';
import { PredefinedCreateTemplateColumnSettingList } from '@domains/constants/types/templateColumnSetting.type';
import {
  IAdminUserRepository,
  ILoginProviderRepository,
  IOrganizationLoginProviderRepository,
  IOrganizationReportRepository,
  IOrganizationRepository,
  IOrganizationSchedulerRepository,
  IOrganizationStorageRepository,
  IPermissionGroupRepository,
  ITemplateColumnSettingRepository,
  ITermsAndConditionsRepository,
} from '@domains/interfaces/infrastructures/repositories';
import { IOrganizationService } from '@domains/interfaces/services/organization.service.interface';
import { ICreateOrganizationUseCase } from '@domains/interfaces/usecases/organization.usecase.interface';
import { DbClientParams } from '@infrastructures/constants/types/database.type';
import { GenericID } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum, LicenseTypeEnum, LicenseTypeTHEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { UserLicenseTypeCodeEnum } from '@iso/lms/enums/license.enum';
import { LoginProviderMethodEnum } from '@iso/lms/enums/loginProvider.enum';
import { OrganizationReportMenuKeyEnum } from '@iso/lms/enums/organizationReport.enum';
import { Organization } from '@iso/lms/models/organization.model';
import { OrganizationLoginProvider } from '@iso/lms/models/organizationLoginProvider.model';
import { OrganizationReport } from '@iso/lms/models/organizationReport.model';
import { OrganizationScheduler } from '@iso/lms/models/organizationScheduler.model';
import { OrganizationStorage } from '@iso/lms/models/organizationStorage.model';
import { PermissionGroup } from '@iso/lms/models/permissionGroup.model';
import { TemplateColumnSetting } from '@iso/lms/models/templateColumnSetting.model';
import { TermsAndConditions } from '@iso/lms/models/termsAndConditions.model';
import { CreateOrganizationParams, OrganizationLicensesConfigurationParams } from '@iso/lms/types/organization.type';
import { CreateOrganizationReportParams } from '@iso/lms/types/organizationReport.type';
import { CreateOrganizationSchedulerParams } from '@iso/lms/types/organizationScheduler.type';
import { CreateOrganizationStorageParams } from '@iso/lms/types/organizationStorage.type';
import { CreateTemplateColumnSettingParams } from '@iso/lms/types/templateColumnSetting.type';
import { Injectable, Inject } from '@nestjs/common';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

@Injectable()
export class CreateOrganizationUseCase implements ICreateOrganizationUseCase {
  constructor(
    @Inject(InfrastructureConfigDIToken.DbClient)
    private readonly mongoClient: DbClientParams,
    @Inject(AdminUserDIToken.AdminUserRepository)
    private readonly adminUserRepository: IAdminUserRepository,
    @Inject(LoginProviderDIToken.LoginProviderRepository)
    private readonly loginProviderRepository: ILoginProviderRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationLoginProviderDIToken.OrganizationLoginProviderRepository)
    private readonly organizationLoginProviderRepository: IOrganizationLoginProviderRepository,
    @Inject(OrganizationReportDIToken.OrganizationReportRepository)
    private readonly organizationReportRepository: IOrganizationReportRepository,
    @Inject(OrganizationSchedulerDIToken.OrganizationSchedulerRepository)
    private readonly organizationSchedulerRepository: IOrganizationSchedulerRepository,
    @Inject(OrganizationStorageDIToken.OrganizationStorageRepository)
    private readonly organizationStorageRepository: IOrganizationStorageRepository,
    @Inject(PermissionGroupDIToken.PermissionGroupRepository)
    private readonly permissionGroupRepository: IPermissionGroupRepository,
    @Inject(ColumnSettingDIToken.TemplateColumnSettingRepository)
    private readonly templateColumnSettingRepository: ITemplateColumnSettingRepository,
    @Inject(TermsAndConditionsDIToken.TermsAndConditionsRepository)
    private readonly termsAndConditionsRepository: ITermsAndConditionsRepository,
    @Inject(OrganizationDIToken.OrganizationService)
    private readonly organizationService: IOrganizationService,
  ) {}

  async execute(params: InputCreateOrganizationParams): Promise<{ id: GenericID }> {
    const { domain, name, nameEng, objectives, createByAdminUserId } = params;

    const newDomain = domain.toLowerCase();

    const oldOrganization = await this.organizationRepository.findOne({ domain: newDomain });
    if (oldOrganization) {
      throw Exception.new({
        code: Code.CONFLICT,
        message: 'Organization is exists.',
      });
    }

    const adminUser = await this.adminUserRepository.findOne({ id: createByAdminUserId });
    if (!adminUser) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Admin user not found.',
        data: { id: createByAdminUserId },
      });
    }

    const isValidDomain = this.organizationService.validateDomain(newDomain);
    if (!isValidDomain) {
      throw Exception.new({
        code: Code.BAD_REQUEST,
        message: 'Domain is invalid.',
      });
    }

    const availableCourseObjectiveTypes = Array.from(
      new Set(objectives.map((val) => CourseObjectiveTypeEnum[val.courseObjective])),
    );
    const courseObjectiveConfigs = this.organizationService.convertToObjectiveTypeConfig(objectives);

    const objectiveTypeTraining = courseObjectiveConfigs.find(
      (val) => val.objectiveType === CourseObjectiveTypeEnum.TRAINING,
    );
    const objectiveTypeOIC = objectiveTypeTraining?.regulators?.find((val) => val.name === RegulatorEnum.OIC);
    const objectiveTypeTSI = objectiveTypeTraining?.regulators?.find((val) => val.name === RegulatorEnum.TSI);
    const objectiveTypeTFAC = objectiveTypeTraining?.regulators?.find((val) => val.name === RegulatorEnum.TFAC);
    const isObjectiveTypeOIC = !!objectiveTypeOIC;
    const isObjectiveTypeTSI = !!objectiveTypeTSI;
    const isObjectiveTypeTFAC = !!objectiveTypeTFAC;

    const trainingCenterRegulatorOIC = objectiveTypeTraining?.trainingCenters?.find(
      (val) => val.regulator === RegulatorEnum.OIC,
    );
    const isOICLife = !!trainingCenterRegulatorOIC?.licenseTypes.some((val) => val.key === LicenseTypeEnum.LIFE);
    const isOICNonLife = !!trainingCenterRegulatorOIC?.licenseTypes.some((val) => val.key === LicenseTypeEnum.NONLIFE);

    const licensesConfiguration: OrganizationLicensesConfigurationParams = {
      availableLicensesTypes: [
        {
          type: UserLicenseTypeCodeEnum.TSI,
          isDisplay: isObjectiveTypeTSI,
        },
        {
          type: UserLicenseTypeCodeEnum.OIC_NON_LIFE,
          isDisplay: isOICNonLife,
        },
        {
          type: UserLicenseTypeCodeEnum.OIC_LIFE,
          isDisplay: isOICLife,
        },
        {
          type: UserLicenseTypeCodeEnum.TFAC_CPA,
          isDisplay: isObjectiveTypeTFAC,
        },
        {
          type: UserLicenseTypeCodeEnum.TFAC_RA,
          isDisplay: isObjectiveTypeTFAC,
        },
      ],
    };

    const certificateConfig = {
      logoImageUrl: '',
      textDynamicCertificate: '',
      certificateTypes: [],
    };

    if (isOICLife) {
      certificateConfig.certificateTypes.push({
        label: LicenseTypeTHEnum.LIFE,
        value: LicenseTypeEnum.LIFE,
      });
    }

    if (isOICNonLife) {
      certificateConfig.certificateTypes.push({
        label: LicenseTypeTHEnum['NON-LIFE'],
        value: LicenseTypeEnum.NONLIFE,
      });
    }

    const newOrg: CreateOrganizationParams = {
      ...PredefinedCreateOrganization,
      domain: newDomain,
      name,
      nameEng,
      availableCourseObjectiveTypes,
      courseObjectiveConfigs,
      certificateConfig,
      licensesConfiguration,
      createByAdminUserId,
    };

    const organizationModel = await Organization.new(newOrg);
    const organizationId = organizationModel.id;

    const loginProvider = await this.loginProviderRepository.findOne({ method: LoginProviderMethodEnum.LOCAL });

    const organizationLoginProviderModel = await OrganizationLoginProvider.new({
      organizationId,
      loginProviderId: loginProvider.id,
      isEnabled: true,
    });

    const termsAndConditionsModel = await TermsAndConditions.new({
      organizationId,
    });

    const permissionGroupModel = await PermissionGroup.new({
      organizationId,
      name: 'Super Admin',
      permissionIds: ['*'],
    });

    const reportList: CreateOrganizationReportParams[] = PredefinedCreateOrganizationReportList;
    const organizationReportModels: OrganizationReport[] = [];
    for (const item of reportList) {
      if (
        (item.menuKey === OrganizationReportMenuKeyEnum.OICReport && isObjectiveTypeOIC) ||
        (item.menuKey === OrganizationReportMenuKeyEnum.TSIReport && isObjectiveTypeTSI)
      ) {
        const organizationReportModel = await OrganizationReport.new({ ...item, organizationId, isEnabled: true });
        organizationReportModels.push(organizationReportModel);
      }

      if (item.menuKey === OrganizationReportMenuKeyEnum.EnrollmentReport) {
        const organizationReportModel = await OrganizationReport.new({ ...item, organizationId, isEnabled: true });
        organizationReportModels.push(organizationReportModel);
      }
    }

    const schedulerList: CreateOrganizationSchedulerParams[] = PredefinedCreateOrganizationSchedulerList;
    const organizationSchedulerModels: OrganizationScheduler[] = [];
    for (const item of schedulerList) {
      const organizationSchedulerModel = await OrganizationScheduler.new({ ...item, organizationId });
      organizationSchedulerModels.push(organizationSchedulerModel);
    }

    const organizationStorageList: CreateOrganizationStorageParams[] = PredefinedCreateOrganizationStorageList;
    const organizationStorageModels: OrganizationStorage[] = [];
    for (const item of organizationStorageList) {
      const organizationStorageModel = await OrganizationStorage.new({ ...item, organizationId });
      organizationStorageModels.push(organizationStorageModel);
    }

    const templateColumnSettingList = PredefinedCreateTemplateColumnSettingList;
    const templateColumnSettingModels: TemplateColumnSetting[] = [];
    for (const item of templateColumnSettingList) {
      let index = 0;
      const template: CreateTemplateColumnSettingParams = {
        organizationId,
        name: item.name,
        code: item.code,
        columnSetting: [],
        module: item.module,
        mainFilter: item.mainFilter,
      };

      for (const col of item.columnSetting) {
        template.columnSetting[index] = {
          ...col,
          order: index,
        };
        index++;
      }

      if (template.columnSetting.length > 0) {
        const templateColumnSettingModel = await TemplateColumnSetting.new(template);
        templateColumnSettingModels.push(templateColumnSettingModel);
      }
    }

    const session = this.mongoClient.startSession();
    session.startTransaction();
    const opts = { session };

    try {
      await this.organizationRepository.save(organizationModel, opts);
      await this.organizationLoginProviderRepository.save(organizationLoginProviderModel, opts);
      await this.permissionGroupRepository.save(permissionGroupModel, opts);
      await this.termsAndConditionsRepository.save(termsAndConditionsModel, opts);

      await this.organizationReportRepository.saveMany(organizationReportModels, opts);
      await this.organizationSchedulerRepository.saveMany(organizationSchedulerModels, opts);
      await this.organizationStorageRepository.saveMany(organizationStorageModels, opts);
      await this.templateColumnSettingRepository.saveMany(templateColumnSettingModels, opts);
      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

    return { id: organizationModel.id };
  }
}
