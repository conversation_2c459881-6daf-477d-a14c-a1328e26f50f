import { Course<PERSON>Token, OrganizationDIToken, PlanDIToken, ProductSKUDIToken } from '@applications/di/domain';
import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import {
  CreateOrganizationPlanParams,
  OutputCreateOrganizationPlanParams,
} from '@domains/constants/types/organization.type';
import { MainPackageParams, SubPackageParams } from '@domains/constants/types/package.type';
import { IPlanDataMapper, IPlanPackageDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import {
  ICourseRepository,
  ICourseVersionRepository,
  IOrganizationRepository,
  IPackageRepository,
  IPlanPackageRepository,
  IPlanRepository,
  IProductSKUCourseRepository,
} from '@domains/interfaces/infrastructures/repositories';
import { ICourseService } from '@domains/interfaces/services/course.service.interface';
import { ICreateOrganizationPlanUseCase } from '@domains/interfaces/usecases/organization.usecase.interface';
import { date, DateFormat } from '@domains/utils/date.util';
import { DbClientParams } from '@infrastructures/constants/types/database.type';
import { GenericID } from '@iso/constants/commonTypes';
import { ExternalContentTypeEnum } from '@iso/lms/enums/course.enum';
import { PackageContentModelTypeEnum, PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import { SaleOrderStatusEnum } from '@iso/lms/enums/plan.enum';
import { Course } from '@iso/lms/models/course.model';
import { CourseVersion } from '@iso/lms/models/courseVersion.model';
import { Package } from '@iso/lms/models/package.model';
import { Plan } from '@iso/lms/models/plan.model';
import { PlanPackage } from '@iso/lms/models/planPackage.model';
import { CourseParams } from '@iso/lms/types/course.type';
import { PlanPackageTypeContentParams, PlanPackageContentParams } from '@iso/lms/types/planPackage.type';
import { Inject, Injectable } from '@nestjs/common';
import { uniq } from 'lodash';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

@Injectable()
export class CreateOrganizationPlanUseCase implements ICreateOrganizationPlanUseCase {
  constructor(
    @Inject(InfrastructureConfigDIToken.DbClient)
    private readonly mongoClient: DbClientParams,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @Inject(PlanDIToken.PlanPackageRepository)
    private readonly planPackageRepository: IPlanPackageRepository,
    @Inject(PlanDIToken.PackageRepository)
    private readonly packageRepository: IPackageRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(ProductSKUDIToken.ProductSKUCourseRepository)
    private readonly productSKUCourseRepository: IProductSKUCourseRepository,

    @Inject(PlanDIToken.PlanDataMapper)
    private readonly planDataMapper: IPlanDataMapper,
    @Inject(PlanDIToken.PlanPackageDataMapper)
    private readonly planPackageDataMapper: IPlanPackageDataMapper,

    // service
    @Inject(CourseDIToken.CourseService)
    private readonly courseService: ICourseService,
  ) {}

  async execute(params: CreateOrganizationPlanParams): Promise<OutputCreateOrganizationPlanParams> {
    const { organizationId, planName, mainPackage, subPackages } = params;

    this.validatePlanPackageData(mainPackage, subPackages);

    const organization = await this.organizationRepository.findOne({ id: organizationId });

    if (!organization) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Organization not found',
      });
    }

    const subPackageIds = subPackages.map((val) => val.packageId);
    const packageIds = [...subPackageIds, mainPackage.packageId];

    const packages = await this.packageRepository.find({ id: { $in: packageIds } });

    const foundPackageIds = packages.map((val) => val.id);
    const missingPackageIds = packageIds.filter((id) => !foundPackageIds.includes(id));

    if (missingPackageIds.length > 0) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Some packages were not found',
        data: { packageIds: missingPackageIds },
      });
    }

    const planPackages: PlanPackage[] = [];
    // Plan
    const plan = await Plan.new({
      organizationId,
      name: planName,
      gracingDate: mainPackage?.gracingDate ? date(mainPackage.gracingDate).toDate() : null,
      startDate: date(mainPackage.startDate).toDate(),
      endDate: date(mainPackage.endDate).toDate(),
      saleOrderStatus: SaleOrderStatusEnum.PENDING,
    });

    // Main Package
    const mainPackagesDetail = packages.find((val) => val.id === mainPackage.packageId);
    const mainPlanPackage = await PlanPackage.new({
      planId: plan.id,
      packageId: mainPackage.packageId,
      name: mainPackagesDetail.name,
      description: mainPackagesDetail.description,
      type: PackageTypeEnum.PLATFORM,
      content: this.preparePlanPackageContentDetail(mainPackagesDetail, PackageTypeEnum.PLATFORM, mainPackage),
      totalUsageDay: mainPackage.totalUsageDay,
      startDate: date(mainPackage.startDate).toDate(),
      gracingDate: mainPackage?.gracingDate ? date(mainPackage.gracingDate).toDate() : null,
      endDate: date(mainPackage.endDate).toDate(),
    });

    // Sub Packages
    const subPlanPackages: PlanPackage[] = [];

    for (const [index, subPackage] of subPackages.entries()) {
      const subPackageDetail = packages.find((val) => val.id === subPackage.packageId);
      const updatedAt = date().add(index, 'millisecond').toDate();
      const planPackage = await PlanPackage.new({
        planId: plan.id,
        packageId: subPackage.packageId,
        name: subPackageDetail.name,
        description: subPackageDetail.description,
        type: PackageTypeEnum.CONTENT,
        content: this.preparePlanPackageContentDetail(subPackageDetail, PackageTypeEnum.CONTENT, subPackage),
        totalUsageDay: subPackage.totalUsageDay,
        startDate: date(subPackage.startDate).toDate(),
        gracingDate: subPackage?.gracingDate ? date(subPackage.gracingDate).toDate() : null,
        endDate: date(subPackage.endDate).toDate(),
      });
      planPackage.updatedAt = updatedAt;

      subPlanPackages.push(planPackage);
    }

    planPackages.push(mainPlanPackage, ...subPlanPackages);

    const addCourse = await this.createCourse(plan, planPackages);

    const session = this.mongoClient.startSession();
    session.startTransaction();
    const opts = { session };

    try {
      await this.planRepository.save(plan, opts);
      await this.planPackageRepository.saveMany(planPackages, opts);
      if (addCourse.newCourses.length > 0) {
        await this.courseRepository.saveMany(addCourse.newCourses, opts);
      }

      if (addCourse.newCourseVersions.length > 0) {
        await this.courseVersionRepository.saveMany(addCourse.newCourseVersions, opts);
      }

      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }

    return {
      organizationId,
      plan: this.planDataMapper.toDTO(plan),
      mainPackage: this.planPackageDataMapper.toDTO(mainPlanPackage),
      subPackages: this.planPackageDataMapper.toDTOs(subPlanPackages),
    };
  }

  private validatePlanPackageData(mainPackage: MainPackageParams, subPackages: SubPackageParams[]): void {
    const currentDate = date().startOf('day');

    // Main Package
    this.validateLicense(mainPackage.totalLicense, mainPackage.totalTransferLicense);

    if (date(mainPackage.startDate).isBefore(currentDate)) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'Start date must not be in the past.',
      });
    }

    if (mainPackage.gracingDate && date(mainPackage.gracingDate).isBefore(date(mainPackage.startDate), 'day')) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'Gracing date cannot be later than the start date.',
      });
    }

    // Sub Package
    for (const subPackage of subPackages) {
      this.validateLicense(subPackage.totalLicense, subPackage.totalTransferLicense);

      if (subPackage.totalLicense > mainPackage.totalLicense) {
        throw Exception.new({
          code: Code.VALIDATE_INPUT_ERROR,
          message: 'Sub package licenses cannot exceed main package licenses.',
        });
      }

      if (!subPackage?.gracingDate) continue;

      const isSubPackageInRange =
        date(subPackage?.gracingDate).isSameOrAfter(date(mainPackage.startDate), 'day') &&
        date(subPackage.endDate).isSameOrBefore(date(mainPackage.endDate), 'day');

      if (!isSubPackageInRange) {
        throw Exception.new({
          code: Code.VALIDATE_INPUT_ERROR,
          message: `Sub package gracing date must be within the main package period (${date(mainPackage.startDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)} - ${date(mainPackage.endDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)}).`,
        });
      }
    }
  }

  private validateLicense = (totalLicense: number, totalTransferLicense: number): void => {
    if (!Number.isInteger(totalLicense) || totalLicense < 1) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'Total license must be an integer greater than 0.',
      });
    }

    if (!Number.isInteger(totalTransferLicense)) {
      throw Exception.new({
        code: Code.VALIDATE_INPUT_ERROR,
        message: 'Total transfer license must be an integer.',
      });
    }
  };

  private preparePlanPackageContentDetail(
    packageDetail: Package,
    planPackageType: PackageTypeEnum,
    data: Partial<SubPackageParams | MainPackageParams>,
  ): PlanPackageContentParams {
    let planPackageTypeContentParams: PlanPackageContentParams;

    if (planPackageType === PackageTypeEnum.PLATFORM) {
      const mainPackage = data as MainPackageParams;

      planPackageTypeContentParams = {
        totalLicense: mainPackage.totalLicense,
        totalTransferLicense: mainPackage.totalTransferLicense,
        remainLicense: mainPackage.totalLicense,
        remainTransferLicense: mainPackage.totalTransferLicense,
      };
    } else if (planPackageType === PackageTypeEnum.CONTENT) {
      const subPackageData = data as SubPackageParams;

      const packageContentModeType = packageDetail.contentModeType;
      let productSKUCourseIds: GenericID[] = [];

      if (packageContentModeType === PackageContentModelTypeEnum.CUSTOM) {
        productSKUCourseIds = subPackageData.productSKUCourseIds;
      }

      planPackageTypeContentParams = {
        model: packageContentModeType,
        type: packageDetail.contentType,
        productSKUCourseIds: productSKUCourseIds ?? [],
        totalLicense: subPackageData.totalLicense,
        totalTransferLicense: subPackageData.totalTransferLicense,
        remainLicense: subPackageData.totalLicense,
        remainTransferLicense: subPackageData.totalTransferLicense,
      };
    }

    return planPackageTypeContentParams;
  }

  private async createCourse(plan: Plan, planPackages: PlanPackage[]) {
    const newCourses = [];
    const newCourseVersions = [];
    const { organizationId } = plan;
    const planPackagesContents = planPackages.filter((val) => val.type === PackageTypeEnum.CONTENT);
    if (planPackagesContents.length === 0) {
      return { newCourses, newCourseVersions };
    }

    //get productSKUCourseId plan package custom
    const planPackagesContentCustom = planPackagesContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.CUSTOM,
    );

    const productSKUCourseIds1 = planPackagesContentCustom.flatMap(
      (val) => (val.content as PlanPackageTypeContentParams).productSKUCourseIds,
    );

    //get productSKUCourseId plan package subscription
    const planPackagesContentSubscription = planPackagesContents.filter(
      (val) => (val.content as PlanPackageTypeContentParams).model === PackageContentModelTypeEnum.SUBSCRIPTION,
    );

    const packageSubScriptionIds = planPackagesContentSubscription.map((val) => val.packageId);
    const allPlanPackageSubScriptions = await this.packageRepository.find({ id: { $in: packageSubScriptionIds } });
    const productSKUCourseIds2 = allPlanPackageSubScriptions.flatMap((val) => val.productSKUCourseIds);

    const productSKUCourseIds = [...productSKUCourseIds1, ...productSKUCourseIds2];

    const productSKUCourses = await this.productSKUCourseRepository.find({ id: { $in: productSKUCourseIds } });
    const courseExistList = await this.findExistCourseList(organizationId, productSKUCourseIds);

    for (const productSKUCourse of productSKUCourses) {
      const courseExist = this.getExistCourse(courseExistList, organizationId, productSKUCourse.id);

      if (courseExist) {
        const { externalContentTypes = [] } = courseExist;
        externalContentTypes.push(ExternalContentTypeEnum.PLAN_PACKAGE);
        courseExist.externalContentTypes = uniq(externalContentTypes);
        newCourses.push(courseExist);
      } else {
        const courseData = this.courseService.buildCreateCourse(
          productSKUCourse,
          organizationId,
          ExternalContentTypeEnum.PLAN_PACKAGE,
        );
        const newCourse = await Course.new(courseData);
        const courseVersionData = this.courseService.buildCreateCourseVersion(productSKUCourse, newCourse.id);
        const newCourseVersion = await CourseVersion.new(courseVersionData);
        newCourses.push(newCourse);
        newCourseVersions.push(newCourseVersion);
      }
    }

    const output = { newCourses, newCourseVersions };

    return output;
  }

  private async findExistCourseList(organizationId: GenericID, productSKUCourseIds: GenericID[]) {
    //get course by organizationIds and productSKUCourseIds
    const output = await this.courseRepository.aggregate<CourseParams>([
      {
        $match: {
          organizationId,
          productSKUCourseId: { $in: productSKUCourseIds },
        },
      },
    ]);

    return output;
  }

  private getExistCourse(courseExistList: CourseParams[], organizationId: GenericID, productSKUCourseId: GenericID) {
    const output = courseExistList.find(
      (val) => val.organizationId === organizationId && val.productSKUCourseId === productSKUCourseId,
    );

    return output;
  }
}
