import { AdminUser<PERSON>Token, OrganizationDIToken, PlanDIToken } from '@applications/di/domain';
import { OutputGetOrganizationParams } from '@domains/constants/types/organization.type';
import { IOrganizationDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import {
  IAdminUserRepository,
  IOrganizationRepository,
  IPlanRepository,
} from '@domains/interfaces/infrastructures/repositories';
import { IOrganizationService } from '@domains/interfaces/services/organization.service.interface';
import { IGetOrganizationUseCase } from '@domains/interfaces/usecases/organization.usecase.interface';
import { Inject, Injectable } from '@nestjs/common';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

@Injectable()
export class GetOrganizationUseCase implements IGetOrganizationUseCase {
  constructor(
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationDataMapper)
    private readonly organizationDataMapper: IOrganizationDataMapper,
    @Inject(AdminUserDIToken.AdminUserRepository)
    private readonly adminUserRepository: IAdminUserRepository,
    @Inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @Inject(OrganizationDIToken.OrganizationService)
    private readonly organizationService: IOrganizationService,
  ) {}

  async execute(organizationId: string): Promise<OutputGetOrganizationParams> {
    const organization = await this.organizationRepository.findOneById(organizationId);
    if (!organization) {
      throw Exception.new({
        code: Code.NOT_FOUND,
        message: 'Organization does not exist.',
      });
    }

    const organizationData = this.organizationDataMapper.toDTO(organization);

    const adminUser = await this.adminUserRepository.findOneById(organization.createByAdminUserId);
    const planList = await this.planRepository.find({ organizationId: organizationData.id });
    const isSaleOrderApproved = this.organizationService.checkSaleOrderApproved(planList);

    const result: OutputGetOrganizationParams = {
      id: organizationData.id,
      domain: organizationData.domain,
      name: organizationData.name,
      nameEng: organizationData.nameEng,
      fqdn: organizationData.fqdn,
      isEnabled: organizationData.isEnabled,
      isSaleOrderApproved,
      courseObjectiveConfigs: organizationData.courseObjectiveConfigs,
      licensesConfiguration: organizationData.licensesConfiguration,
      adminUser: adminUser
        ? {
            id: adminUser.id,
            profile: adminUser.profile,
          }
        : null,
      createdAt: organizationData.createdAt,
      updatedAt: organizationData.updatedAt,
    };
    return result;
  }
}
