import { IEnrollmentPlanPackageLicenseDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import { IEnrollmentPlanPackageLicenseRepository } from '@domains/interfaces/infrastructures/repositories';
import { DbInstanceParams } from '@infrastructures/constants/types/database.type';
import { BaseRepository } from '@infrastructures/persistence/repositories/base.repository';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { EnrollmentPlanPackageLicense } from '@iso/lms/models/enrollmentPlanPackageLicense.model';
import { EnrollmentPlanPackageLicenseParams } from '@iso/lms/types/enrollmentPlanPackageLicense.type';

const TABLE_NAME = DBCollectionEnum.ENROLLMENT_PLAN_PACKAGE_LICENSES;

export class EnrollmentPlanPackageLicenseRepository
  extends BaseRepository<EnrollmentPlanPackageLicense, EnrollmentPlanPackageLicenseParams>
  implements IEnrollmentPlanPackageLicenseRepository
{
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: IEnrollmentPlanPackageLicenseDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }
}
