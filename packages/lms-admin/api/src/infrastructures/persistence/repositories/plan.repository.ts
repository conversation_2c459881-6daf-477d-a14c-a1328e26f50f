import { IPlanDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import { IPlanRepository } from '@domains/interfaces/infrastructures/repositories';
import { DbInstanceParams } from '@infrastructures/constants/types/database.type';
import { BaseRepository } from '@infrastructures/persistence/repositories/base.repository';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { Plan } from '@iso/lms/models/plan.model';
import { PlanParams } from '@iso/lms/types/plan.type';
import { Injectable } from '@nestjs/common';

const TABLE_NAME = DBCollectionEnum.PLANS;

@Injectable()
export class PlanRepository extends BaseRepository<Plan, PlanParams> implements IPlanRepository {
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: IPlanDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }
}
