import { IUserDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import { User } from '@iso/lms/models/user.model';
import { UserParams } from '@iso/lms/types/user.type';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UserDataMapper implements IUserDataMapper {
  toDomain(entity: UserParams): User {
    return new User(entity);
  }

  toDomains(daoEntities: UserParams[]): User[] {
    return daoEntities.map((val) => this.toDomain(val));
  }

  toDAL(entity: User): UserParams {
    const {
      guid,
      username,
      email,
      passwordHash,
      verified,
      citizenId,
      last4DigitCitizenId,
      organizationId,
      customerCodes,
      profile,
      active,
      isTest,
      permissions,
      isTwoFactorEnable,
      isTwoFactorRegister,
      isTwoFactorLogin,
      twoFactorSecret,
      regulatorProfile,
      lostToken,
      firstPasswordToken,
      additionalField,
      permissionGroupIds,
      permissionRegulator,
      setPasswordDate,
      firstActiveDate,
      isEnableLocalLogin,
      suspendedUntil,
      returnUrl,
      isPassedUlSaleQualify,
      isTerminated,
      role,
      employeeId,
      position,
      createdAt,
      updatedAt,
      deletedAt,
    } = entity;

    return {
      guid,
      username,
      email,
      passwordHash,
      verified,
      citizenId,
      last4DigitCitizenId,
      organizationId,
      customerCodes,
      profile,
      active,
      isTest,
      permissions,
      isTwoFactorEnable,
      isTwoFactorRegister,
      isTwoFactorLogin,
      twoFactorSecret,
      regulatorProfile,
      lostToken,
      firstPasswordToken,
      additionalField,
      permissionGroupIds,
      permissionRegulator,
      setPasswordDate,
      firstActiveDate,
      isEnableLocalLogin,
      suspendedUntil,
      returnUrl,
      isPassedUlSaleQualify,
      isTerminated,
      role,
      employeeId,
      position,
      createdAt,
      updatedAt,
      deletedAt,
    };
  }

  toDTO(entity: User): UserParams {
    const {
      guid,
      username,
      email,
      verified,
      citizenId,
      last4DigitCitizenId,
      organizationId,
      customerCodes,
      profile,
      active,
      isTest,
      permissions,
      isTwoFactorEnable,
      isTwoFactorRegister,
      isTwoFactorLogin,
      twoFactorSecret,
      regulatorProfile,
      lostToken,
      firstPasswordToken,
      additionalField,
      permissionGroupIds,
      permissionRegulator,
      setPasswordDate,
      firstActiveDate,
      isEnableLocalLogin,
      suspendedUntil,
      returnUrl,
      isPassedUlSaleQualify,
      isTerminated,
      role,
      employeeId,
      position,
      createdAt,
      updatedAt,
      deletedAt,
    } = entity;

    return {
      guid,
      username,
      email,
      passwordHash: '',
      verified,
      citizenId,
      last4DigitCitizenId,
      organizationId,
      customerCodes,
      profile,
      active,
      isTest,
      permissions,
      isTwoFactorEnable,
      isTwoFactorRegister,
      isTwoFactorLogin,
      twoFactorSecret,
      regulatorProfile,
      lostToken,
      firstPasswordToken,
      additionalField,
      permissionGroupIds,
      permissionRegulator,
      setPasswordDate,
      firstActiveDate,
      isEnableLocalLogin,
      suspendedUntil,
      returnUrl,
      isPassedUlSaleQualify,
      isTerminated,
      role,
      employeeId,
      position,
      createdAt,
      updatedAt,
      deletedAt,
    };
  }

  toDTOs(entities: User[]): UserParams[] {
    return entities.map((val) => this.toDTO(val));
  }
}
