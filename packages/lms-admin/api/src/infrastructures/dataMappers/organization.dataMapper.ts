import { IOrganizationDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import { Organization } from '@iso/lms/models/organization.model';
import { OrganizationParams } from '@iso/lms/types/organization.type';
import { Injectable } from '@nestjs/common';
import { instanceToPlain } from 'class-transformer';

@Injectable()
export class OrganizationDataMapper implements IOrganizationDataMapper {
  toDomain(daoEntity: OrganizationParams): Organization {
    return new Organization(daoEntity);
  }

  toDomains(daoEntities: OrganizationParams[]): Organization[] {
    return daoEntities.map((val) => this.toDomain(val));
  }

  toDAL(domainEntity: Organization): OrganizationParams {
    const payload = instanceToPlain(domainEntity, { excludePrefixes: ['_'] }) as OrganizationParams;
    const {
      id,
      domain,
      fqdn,
      name,
      nameEng,
      availableCourseObjectiveTypes,
      courseObjectiveConfigs,
      videoAuthenInstruction,
      organizationContact,
      userValidateTemplate,
      certificateConfig,
      themeConfig,
      isEnableLocalLogin,
      validationPasswordConfig,
      tokenConfig,
      autoApprovalEnrollmentConfig,
      featureConfig,
      notificationConfig,
      transactionCounter,
      licensesConfiguration,
      passwordPolicyConfig,
      isLivenessEnabled,
      livenessMode,
      isEnabled,
      createByAdminUserId,
      createdAt,
      updatedAt,
      deletedAt,
    } = payload;

    return {
      id,
      domain,
      fqdn,
      name,
      nameEng,
      availableCourseObjectiveTypes,
      courseObjectiveConfigs,
      videoAuthenInstruction,
      organizationContact,
      userValidateTemplate,
      certificateConfig,
      themeConfig,
      isEnableLocalLogin,
      validationPasswordConfig,
      tokenConfig,
      autoApprovalEnrollmentConfig,
      featureConfig,
      notificationConfig,
      transactionCounter,
      licensesConfiguration,
      passwordPolicyConfig,
      isLivenessEnabled,
      livenessMode,
      isEnabled,
      createByAdminUserId,
      createdAt,
      updatedAt,
      deletedAt,
    };
  }

  toDTO(domainEntity: Organization): OrganizationParams {
    const payload = instanceToPlain(domainEntity, { excludePrefixes: ['_'] }) as OrganizationParams;
    const {
      id,
      domain,
      fqdn,
      name,
      nameEng,
      availableCourseObjectiveTypes,
      courseObjectiveConfigs,
      videoAuthenInstruction,
      organizationContact,
      userValidateTemplate,
      certificateConfig,
      themeConfig,
      isEnableLocalLogin,
      validationPasswordConfig,
      tokenConfig,
      autoApprovalEnrollmentConfig,
      featureConfig,
      notificationConfig,
      transactionCounter,
      licensesConfiguration,
      passwordPolicyConfig,
      isLivenessEnabled,
      livenessMode,
      isEnabled,
      createByAdminUserId,
      createdAt,
      updatedAt,
    } = payload;

    return {
      id,
      domain,
      fqdn,
      name,
      nameEng,
      availableCourseObjectiveTypes,
      courseObjectiveConfigs,
      videoAuthenInstruction,
      organizationContact,
      userValidateTemplate,
      certificateConfig,
      themeConfig,
      isEnableLocalLogin,
      validationPasswordConfig,
      tokenConfig,
      autoApprovalEnrollmentConfig,
      featureConfig,
      notificationConfig,
      transactionCounter,
      licensesConfiguration,
      passwordPolicyConfig,
      isLivenessEnabled,
      livenessMode,
      isEnabled,
      createByAdminUserId,
      createdAt,
      updatedAt,
    };
  }

  toDTOs(entities: Organization[]): OrganizationParams[] {
    return entities.map((val) => this.toDTO(val));
  }
}
