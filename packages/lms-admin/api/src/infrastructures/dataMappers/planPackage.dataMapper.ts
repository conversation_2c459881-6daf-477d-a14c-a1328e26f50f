import { IPlanPackageDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import { PlanPackage } from '@iso/lms/models/planPackage.model';
import { PlanPackageParams } from '@iso/lms/types/planPackage.type';
import { Injectable } from '@nestjs/common';

@Injectable()
export class PlanPackageDataMapper implements IPlanPackageDataMapper {
  toDomain(entity: PlanPackageParams): PlanPackage {
    return new PlanPackage(entity);
  }

  toDomains(daoEntities: PlanPackageParams[]): PlanPackage[] {
    return daoEntities.map((val) => this.toDomain(val));
  }

  toDAL(entity: PlanPackage): PlanPackageParams {
    const {
      id,
      planId,
      packageId,
      name,
      description,
      type,
      content,
      totalUsageDay,
      startDate,
      gracingDate,
      endDate,
      createdAt,
      updatedAt,
      deletedAt,
    } = entity;

    return {
      id,
      planId,
      packageId,
      name,
      description,
      type,
      content,
      totalUsageDay,
      startDate,
      gracingDate,
      endDate,
      createdAt,
      updatedAt,
      deletedAt,
    };
  }

  toDTO(entity: PlanPackage): PlanPackageParams {
    return this.toDAL(entity);
  }

  toDTOs(entities: PlanPackage[]): PlanPackageParams[] {
    return entities.map((val) => this.toDTO(val));
  }
}
