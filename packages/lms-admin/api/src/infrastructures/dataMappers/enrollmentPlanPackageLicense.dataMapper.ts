import { IEnrollmentPlanPackageLicenseDataMapper } from '@domains/interfaces/infrastructures/dataMappers';
import { EnrollmentPlanPackageLicense } from '@iso/lms/models/enrollmentPlanPackageLicense.model';
import { EnrollmentPlanPackageLicenseParams } from '@iso/lms/types/enrollmentPlanPackageLicense.type';
import { Injectable } from '@nestjs/common';
import { instanceToPlain } from 'class-transformer';

@Injectable()
export class EnrollmentPlanPackageLicenseDataMapper implements IEnrollmentPlanPackageLicenseDataMapper {
  toDomain(daoEntity: EnrollmentPlanPackageLicenseParams): EnrollmentPlanPackageLicense {
    return new EnrollmentPlanPackageLicense(daoEntity);
  }

  toDomains(daoEntities: EnrollmentPlanPackageLicenseParams[]): EnrollmentPlanPackageLicense[] {
    return daoEntities.map((val) => this.toDomain(val));
  }

  toDAL(domainEntity: EnrollmentPlanPackageLicense): EnrollmentPlanPackageLicenseParams {
    const payload = instanceToPlain(domainEntity, { excludePrefixes: ['_'] });
    const { id, userId, enrollmentId, planPackageLicenseId, packageType, createdAt, updatedAt, deletedAt } = payload;

    return {
      id,
      userId,
      enrollmentId,
      planPackageLicenseId,
      packageType,
      createdAt,
      updatedAt,
      deletedAt,
    };
  }

  toDTO(entity: EnrollmentPlanPackageLicense): EnrollmentPlanPackageLicenseParams {
    return this.toDAL(entity);
  }

  toDTOs(entities: EnrollmentPlanPackageLicense[]): EnrollmentPlanPackageLicenseParams[] {
    return entities.map((val) => this.toDTO(val));
  }
}
