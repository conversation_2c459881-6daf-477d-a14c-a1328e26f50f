import { LoggerLevelColors, LoggerLevels } from '@infrastructures/services/logger/constants';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { LoggerTypeEnum } from '@infrastructures/services/logger/types';
import { ConsoleLogger, Injectable } from '@nestjs/common';
import winston, { createLogger, format, transports } from 'winston';

const customLevels = {
  levels: LoggerLevels,
  colors: LoggerLevelColors,
};
winston.addColors(customLevels.colors);

@Injectable()
export default class Logger extends ConsoleLogger implements ILogger {
  private logger: winston.Logger;

  constructor(appName?: string) {
    super(appName);
    const isDev = process.env.NODE_ENV === 'dev';
    let consoleFormat;
    if (!isDev) {
      consoleFormat = format.combine(format.timestamp(), format.ms(), format.json());
    } else {
      consoleFormat = format.combine(
        format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
        format.ms(),
        format.colorize({ all: true }),
        format.printf((info) => {
          const timestamp = typeof info.timestamp === 'string' ? info.timestamp : JSON.stringify(info.timestamp);
          const message = typeof info.message === 'string' ? info.message : JSON.stringify(info.message);
          let msg = `${timestamp} [${this.context}] | ${info.level}: ${message}`;
          if (info.data) {
            msg += `: ${JSON.stringify(info.data, null, 2)}`;
          }
          return msg;
        }),
      );
    }

    this.logger = createLogger({
      levels: customLevels.levels,
      level: isDev ? 'debug' : 'info',
      format: consoleFormat,
      transports: [
        new transports.Console({
          format: consoleFormat,
        }),
      ],
    });
  }

  log(msg: string, data?: any): void {
    this.emittedLogMessage(LoggerTypeEnum.info, msg, data);
  }

  debug(msg: string, data?: any): void {
    this.emittedLogMessage(LoggerTypeEnum.debug, msg, data);
  }

  info(msg: string, data?: any): void {
    this.emittedLogMessage(LoggerTypeEnum.info, msg, data);
  }

  warn(msg: string, data?: any): void {
    this.emittedLogMessage(LoggerTypeEnum.warn, msg, data);
  }

  error(msg: string, data?: any): void {
    this.emittedLogMessage(LoggerTypeEnum.error, msg, data);
  }

  http(msg: string): void {
    this.emittedLogMessage(LoggerTypeEnum.http, msg);
  }

  private emittedLogMessage(logType: LoggerTypeEnum, message: string, data?: any): void {
    if (data) {
      this.logger[logType](message, { data });
      return;
    }

    this.logger[logType](message);
  }
}
