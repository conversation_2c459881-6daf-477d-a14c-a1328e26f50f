import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import { InfrastructureServiceDIToken } from '@applications/di/infrastructures/service';
import { IEnvironment } from '@domains/interfaces/infrastructures/configs/environment.interface';
import fastifyCookie from '@fastify/cookie';
import fastifyCsrf from '@fastify/csrf-protection';
import helmet from '@fastify/helmet';
import fastifyMultipart from '@fastify/multipart';
import fastifySession from '@fastify/session';
import { MemoryCachedInstanceParams } from '@infrastructures/constants/types/memoryCache.type';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import Logger from '@infrastructures/services/logger/logger';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import RedisStore from 'connect-redis';
import ms from 'ms';

import { AppModule } from '../app.module';

export class ServerApplication {
  private static instance: ServerApplication;
  private logger = new Logger(ServerApplication.name);
  static new(): ServerApplication {
    if (!ServerApplication.instance) {
      ServerApplication.instance = new ServerApplication();
    }

    return ServerApplication.instance;
  }

  async run(): Promise<void> {
    const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter(), {
      logger: new Logger('LMS-Admin'),
    });
    const environment = app.get<symbol, IEnvironment>(InfrastructureConfigDIToken.Environment);
    const redis = app.get<symbol, MemoryCachedInstanceParams>(InfrastructureConfigDIToken.MemoryCachedInstance);

    const redisStore = new RedisStore({
      client: redis,
      prefix: 'lms-admin:session:',
    });

    await app.register(fastifyCookie, {
      secret: 'secretjzlnrgsxlcwtjdunygjbiwwurm',
    });

    await app.register(fastifySession, {
      secret: 'secretjzlnrgsxlcwtjdunygjbiwwurm',
      saveUninitialized: false,
      cookie: { maxAge: ms('15m'), secure: environment.envName !== 'dev' },
      store: redisStore,
    });

    await app.register(fastifyMultipart, {
      attachFieldsToBody: true,
    });

    this.initialApplication(app);

    await this.initialSecurity(app);

    const { port } = environment;

    await app.listen(port, '0.0.0.0');

    this.logger.log(`Server is running on ${await app.getUrl()}`);
  }

  private initialApplication(app: NestFastifyApplication): void {
    const LoggerApplication = app.get<symbol, ILogger>(InfrastructureServiceDIToken.LoggerApplication);
    LoggerApplication.setContext('LMS-Admin');
    app.useLogger(LoggerApplication);
    app.useGlobalPipes(new ValidationPipe());
  }

  private async initialSecurity(app: NestFastifyApplication) {
    app.enableCors({ origin: '*' });

    await app.register(helmet);
    await app.register(fastifyCsrf);
  }
}
