export class EnrollmentDIToken {
  // infrastructure
  static readonly EnrollmentDataMapper: unique symbol = Symbol('EnrollmentDataMapper');
  static readonly EnrollmentRepository: unique symbol = Symbol('EnrollmentRepository');

  static readonly EnrollmentPlanPackageLicenseDataMapper: unique symbol = Symbol(
    'EnrollmentPlanPackageLicenseDataMapper',
  );
  static readonly EnrollmentPlanPackageLicenseRepository: unique symbol = Symbol(
    'EnrollmentPlanPackageLicenseRepository',
  );

  // service

  // usecase
}
