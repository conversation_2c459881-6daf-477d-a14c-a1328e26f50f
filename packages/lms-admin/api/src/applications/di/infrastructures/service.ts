export class InfrastructureServiceDIToken {
  static readonly HttpService: symbol = Symbol('HttpService');
  static readonly OAuthService: unique symbol = Symbol('OAuthService');
  static readonly JwtService: symbol = Symbol('JwtService');
  static readonly TwoFactorService: symbol = Symbol('TwoFactorService');
  static readonly CoreCertificateService: symbol = Symbol('CoreCertificateService');
  static readonly StorageFactoryService: symbol = Symbol('StorageFactoryService');
  static readonly MessageBrokerService: symbol = Symbol('MessageBrokerService');
  static readonly NotificationMessageBrokerService: symbol = Symbol('NotificationMessageBrokerService');
  static readonly NotificationService: symbol = Symbol('NotificationService');
  static readonly ImageService: symbol = Symbol('ImageService');
  static readonly GoogleCloudFileStorageService: symbol = Symbol('GoogleCloudFileStorageService');
  static readonly AWSFileStorageService: symbol = Symbol('AWSFileStorageService');
  static readonly AESCipherService: symbol = Symbol('AESCipherService');
  static readonly LmsApiService: symbol = Symbol('LmsApiService');
  static readonly WebhookService: symbol = Symbol('WebhookService');
  static readonly LoggerApplication: unique symbol = Symbol('LoggerApplication');
}
