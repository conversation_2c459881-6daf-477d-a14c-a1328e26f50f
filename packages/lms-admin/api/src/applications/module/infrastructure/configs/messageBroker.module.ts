import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import { InfrastructureServiceDIToken } from '@applications/di/infrastructures/service';
import { EnvironmentModule } from '@applications/module/infrastructure/configs/environment.module';
import { LoggerServiceModule } from '@applications/module/infrastructure/services/logger.service.module';
import { MessageBrokerEventNotificationEnum } from '@domains/constants/enums/infrastructures/messageBroker.enum';
import { IEnvironment } from '@domains/interfaces/infrastructures/configs/environment.interface';
import { IMessageBrokerEventEmitter } from '@domains/interfaces/infrastructures/services/messageBroker.interface';
import { MessageBrokerEventEmitter } from '@infrastructures/configs/messageBroker/messageBroker.event';
import { MessageBrokerInstance } from '@infrastructures/configs/messageBroker/messageBroker.instance';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { Module } from '@nestjs/common';

@Module({
  imports: [EnvironmentModule, LoggerServiceModule],
  providers: [
    {
      provide: InfrastructureConfigDIToken.MessageNotificationBrokerInstance,
      inject: [
        InfrastructureConfigDIToken.Environment,
        InfrastructureConfigDIToken.MessageBrokerEventEmitter,
        InfrastructureServiceDIToken.LoggerApplication,
      ],
      useFactory: (environment: IEnvironment, event: IMessageBrokerEventEmitter, logger: ILogger) =>
        new MessageBrokerInstance(
          environment.amqpNotificationURI,
          MessageBrokerEventNotificationEnum.Connected,
          event,
          logger,
        ),
    },
    {
      provide: InfrastructureConfigDIToken.MessageBrokerEventEmitter,
      useClass: MessageBrokerEventEmitter,
    },
  ],
  exports: [InfrastructureConfigDIToken.MessageNotificationBrokerInstance],
})
export class MessageBrokerModule {}
