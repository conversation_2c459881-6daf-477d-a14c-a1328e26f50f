import { InfrastructureServiceDIToken } from '@applications/di/infrastructures/service';
import Logger from '@infrastructures/services/logger/logger';
import { Module } from '@nestjs/common';

@Module({
  providers: [
    {
      provide: InfrastructureServiceDIToken.LoggerApplication,
      useClass: Logger,
    },
  ],
  exports: [InfrastructureServiceDIToken.LoggerApplication],
})
export class LoggerServiceModule {}
