import {
  Admin<PERSON>ser<PERSON>Token,
  CertificateDIToken,
  ColumnSettingDIToken,
  CourseDIToken,
  EnrollmentDIToken,
  LoginProviderDIToken,
  MediaDIToken,
  OrganizationCertificateDIToken,
  OrganizationDIToken,
  OrganizationLoginProviderDIToken,
  OrganizationReportDIToken,
  OrganizationSchedulerDIToken,
  OrganizationStorageDIToken,
  PermissionGroupDIToken,
  PlanDIToken,
  PreEnrollmentTransactionDIToken,
  ProductSKUDIToken,
  TermsAndConditionsDIToken,
  UserDIToken,
} from '@applications/di/domain';
import {
  AdminUserAccessOrganizationLogDataMapper,
  AdminUserDataMapper,
  AdminUserOrganizationAccountDataMapper,
  CertificateDataMapper,
  ColumnSettingDataMapper,
  CourseDataMapper,
  CourseVersionCertificateDataMapper,
  CourseVersionDataMapper,
  EnrollmentDataMapper,
  LoginProviderDataMapper,
  MediaDataMapper,
  OrganizationCertificateDataMapper,
  OrganizationColumnSettingDataMapper,
  OrganizationDataMapper,
  OrganizationLoginProviderDataMapper,
  OrganizationReportDataMapper,
  OrganizationSchedulerDataMapper,
  OrganizationStorageDataMapper,
  PackageDataMapper,
  PermissionGroupDataMapper,
  PlanDataMapper,
  PlanPackageDataMapper,
  PreEnrollmentTransactionDataMapper,
  ProductSKUBundleDataMapper,
  ProductSKUCourseDataMapper,
  ProductSKUDataMapper,
  TemplateColumnSettingDataMapper,
  TermsAndConditionsDataMapper,
  UserDataMapper,
  CourseMarketplaceDataMapper,
  PlanPackageLicenseDataMapper,
  EnrollmentPlanPackageLicenseDataMapper,
} from '@infrastructures/dataMappers';
import { Module, Provider } from '@nestjs/common';

const adaptersProvider: Provider[] = [
  {
    provide: AdminUserDIToken.AdminUserAccessOrganizationLogDataMapper,
    useClass: AdminUserAccessOrganizationLogDataMapper,
  },
  {
    provide: AdminUserDIToken.AdminUserDataMapper,
    useClass: AdminUserDataMapper,
  },
  {
    provide: AdminUserDIToken.AdminUserOrganizationAccountDataMapper,
    useClass: AdminUserOrganizationAccountDataMapper,
  },
  {
    provide: CertificateDIToken.CertificateDataMapper,
    useClass: CertificateDataMapper,
  },
  {
    provide: ColumnSettingDIToken.ColumnSettingDataMapper,
    useClass: ColumnSettingDataMapper,
  },
  {
    provide: ColumnSettingDIToken.OrganizationColumnSettingDataMapper,
    useClass: OrganizationColumnSettingDataMapper,
  },
  {
    provide: ColumnSettingDIToken.TemplateColumnSettingDataMapper,
    useClass: TemplateColumnSettingDataMapper,
  },
  {
    provide: CourseDIToken.CourseVersionCertificateDataMapper,
    useClass: CourseVersionCertificateDataMapper,
  },
  {
    provide: LoginProviderDIToken.LoginProviderDataMapper,
    useClass: LoginProviderDataMapper,
  },
  {
    provide: MediaDIToken.MediaDataMapper,
    useClass: MediaDataMapper,
  },
  {
    provide: OrganizationCertificateDIToken.OrganizationCertificateDataMapper,
    useClass: OrganizationCertificateDataMapper,
  },
  {
    provide: OrganizationDIToken.OrganizationDataMapper,
    useClass: OrganizationDataMapper,
  },
  {
    provide: OrganizationLoginProviderDIToken.OrganizationLoginProviderDataMapper,
    useClass: OrganizationLoginProviderDataMapper,
  },
  {
    provide: OrganizationReportDIToken.OrganizationReportDataMapper,
    useClass: OrganizationReportDataMapper,
  },
  {
    provide: OrganizationSchedulerDIToken.OrganizationSchedulerDataMapper,
    useClass: OrganizationSchedulerDataMapper,
  },
  {
    provide: OrganizationStorageDIToken.OrganizationStorageDataMapper,
    useClass: OrganizationStorageDataMapper,
  },
  {
    provide: PlanDIToken.PackageDataMapper,
    useClass: PackageDataMapper,
  },
  {
    provide: PermissionGroupDIToken.PermissionGroupDataMapper,
    useClass: PermissionGroupDataMapper,
  },
  {
    provide: TermsAndConditionsDIToken.TermsAndConditionsDataMapper,
    useClass: TermsAndConditionsDataMapper,
  },
  {
    provide: ColumnSettingDIToken.TemplateColumnSettingDataMapper,
    useClass: TemplateColumnSettingDataMapper,
  },
  {
    provide: MediaDIToken.MediaDataMapper,
    useClass: MediaDataMapper,
  },
  {
    provide: ProductSKUDIToken.ProductSKUDataMapper,
    useClass: ProductSKUDataMapper,
  },
  {
    provide: ProductSKUDIToken.ProductSKUBundleDataMapper,
    useClass: ProductSKUBundleDataMapper,
  },
  {
    provide: ProductSKUDIToken.ProductSKUCourseDataMapper,
    useClass: ProductSKUCourseDataMapper,
  },
  {
    provide: UserDIToken.UserDataMapper,
    useClass: UserDataMapper,
  },
  {
    provide: PlanDIToken.PlanDataMapper,
    useClass: PlanDataMapper,
  },
  {
    provide: PlanDIToken.PlanPackageDataMapper,
    useClass: PlanPackageDataMapper,
  },
  {
    provide: PlanDIToken.PlanPackageLicenseDataMapper,
    useClass: PlanPackageLicenseDataMapper,
  },
  {
    provide: CourseDIToken.CourseDataMapper,
    useClass: CourseDataMapper,
  },
  {
    provide: CourseDIToken.CourseVersionDataMapper,
    useClass: CourseVersionDataMapper,
  },
  {
    provide: CourseDIToken.CourseVersionDataMapper,
    useClass: CourseVersionDataMapper,
  },
  {
    provide: EnrollmentDIToken.EnrollmentDataMapper,
    useClass: EnrollmentDataMapper,
  },
  {
    provide: PreEnrollmentTransactionDIToken.PreEnrollmentTransactionDataMapper,
    useClass: PreEnrollmentTransactionDataMapper,
  },
  {
    provide: ProductSKUDIToken.CourseMarketplaceDataMapper,
    useClass: CourseMarketplaceDataMapper,
  },
  {
    provide: EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper,
    useClass: EnrollmentPlanPackageLicenseDataMapper,
  },
];

@Module({
  providers: [...adaptersProvider],
  exports: [
    AdminUserDIToken.AdminUserAccessOrganizationLogDataMapper,
    AdminUserDIToken.AdminUserDataMapper,
    AdminUserDIToken.AdminUserOrganizationAccountDataMapper,
    CertificateDIToken.CertificateDataMapper,
    ColumnSettingDIToken.ColumnSettingDataMapper,
    ColumnSettingDIToken.OrganizationColumnSettingDataMapper,
    ColumnSettingDIToken.TemplateColumnSettingDataMapper,
    CourseDIToken.CourseDataMapper,
    CourseDIToken.CourseVersionCertificateDataMapper,
    CourseDIToken.CourseVersionDataMapper,
    EnrollmentDIToken.EnrollmentDataMapper,
    EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper,
    LoginProviderDIToken.LoginProviderDataMapper,
    MediaDIToken.MediaDataMapper,
    OrganizationCertificateDIToken.OrganizationCertificateDataMapper,
    OrganizationDIToken.OrganizationDataMapper,
    OrganizationLoginProviderDIToken.OrganizationLoginProviderDataMapper,
    OrganizationReportDIToken.OrganizationReportDataMapper,
    OrganizationSchedulerDIToken.OrganizationSchedulerDataMapper,
    OrganizationStorageDIToken.OrganizationStorageDataMapper,
    PermissionGroupDIToken.PermissionGroupDataMapper,
    PlanDIToken.PackageDataMapper,
    PlanDIToken.PlanDataMapper,
    PlanDIToken.PlanPackageDataMapper,
    PlanDIToken.PlanPackageLicenseDataMapper,
    PreEnrollmentTransactionDIToken.PreEnrollmentTransactionDataMapper,
    ProductSKUDIToken.ProductSKUBundleDataMapper,
    ProductSKUDIToken.ProductSKUCourseDataMapper,
    ProductSKUDIToken.ProductSKUDataMapper,
    ProductSKUDIToken.CourseMarketplaceDataMapper,
    TermsAndConditionsDIToken.TermsAndConditionsDataMapper,
    UserDIToken.UserDataMapper,
  ],
})
export class DataMapperModule {}
