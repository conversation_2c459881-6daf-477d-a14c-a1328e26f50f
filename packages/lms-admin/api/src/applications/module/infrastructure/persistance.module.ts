import {
  Admin<PERSON>ser<PERSON>Token,
  AuthenticationDIToken,
  CertificateDIToken,
  ColumnSettingDIToken,
  CourseDIToken,
  EnrollmentDIToken,
  LoginProviderDIToken,
  MediaDIToken,
  OrganizationCertificateDIToken,
  OrganizationDIToken,
  OrganizationLoginProviderDIToken,
  OrganizationReportDIToken,
  OrganizationSchedulerDIToken,
  OrganizationStorageDIToken,
  PermissionGroupDIToken,
  PlanDIToken,
  PreEnrollmentTransactionDIToken,
  ProductSKUDIToken,
  TermsAndConditionsDIToken,
  UserDIToken,
} from '@applications/di/domain';
import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import { InfrastructureConfigsModule } from '@applications/module/infrastructure/config.module';
import { DataMapperModule } from '@applications/module/infrastructure/dataMapper.module';
import {
  IAdminUserAccessOrganizationLogDataMapper,
  IAdminUserDataMapper,
  IAdminUserOrganizationAccountDataMapper,
  ICertificateDataMapper,
  IColumnSettingDataMapper,
  ICourseDataMapper,
  ICourseVersionCertificateDataMapper,
  ICourseVersionDataMapper,
  IEnrollmentDataMapper,
  IEnrollmentPlanPackageLicenseDataMapper,
  ILoginProviderDataMapper,
  IMediaDataMapper,
  IOrganizationCertificateDataMapper,
  IOrganizationColumnSettingDataMapper,
  IOrganizationDataMapper,
  IOrganizationLoginProviderDataMapper,
  IOrganizationReportDataMapper,
  IOrganizationSchedulerDataMapper,
  IOrganizationStorageDataMapper,
  IPackageDataMapper,
  IPermissionGroupDataMapper,
  IPlanDataMapper,
  IPlanPackageDataMapper,
  IPlanPackageLicenseDataMapper,
  IPreEnrollmentTransactionDataMapper,
  IProductSKUBundleDataMapper,
  IProductSKUCourseDataMapper,
  IProductSKUDataMapper,
  ITemplateColumnSettingDataMapper,
  ITermsAndConditionsDataMapper,
  IUserDataMapper,
} from '@domains/interfaces/infrastructures/dataMappers';
import { ICourseMarketplaceDataMapper } from '@domains/interfaces/infrastructures/dataMappers/courseMarketplace.dataMapper.interface';
import { DbInstanceParams } from '@infrastructures/constants/types/database.type';
import { MemoryCachedInstanceParams } from '@infrastructures/constants/types/memoryCache.type';
import {
  AdminUserAccessOrganizationLogRepository,
  AdminUserOrganizationAccountRepository,
  AdminUserRepository,
  AuthenticationMemoryCachedRepository,
  CertificateRepository,
  ColumnSettingRepository,
  CourseRepository,
  CourseVersionCertificateRepository,
  CourseVersionRepository,
  EnrollmentRepository,
  LoginProviderRepository,
  MediaRepository,
  OrganizationCertificateRepository,
  OrganizationColumnSettingRepository,
  OrganizationLoginProviderRepository,
  OrganizationReportRepository,
  OrganizationRepository,
  OrganizationSchedulerRepository,
  OrganizationStorageRepository,
  PackageRepository,
  PermissionGroupRepository,
  PlanPackageRepository,
  PlanPackageLicenseRepository,
  PlanRepository,
  PreEnrollmentTransactionRepository,
  ProductSKUBundleRepository,
  ProductSKUCourseRepository,
  ProductSKURepository,
  TemplateColumnSettingRepository,
  TermsAndConditionsRepository,
  UserRepository,
  CourseMarketplaceRepository,
  EnrollmentPlanPackageLicenseRepository,
} from '@infrastructures/persistence/repositories';
import { Module, Provider } from '@nestjs/common';

const persistenceProviders: Provider[] = [
  {
    provide: AdminUserDIToken.AdminUserRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, AdminUserDIToken.AdminUserDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IAdminUserDataMapper) => new AdminUserRepository(db, dataMapper),
  },
  {
    provide: AdminUserDIToken.AdminUserAccessOrganizationLogRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, AdminUserDIToken.AdminUserAccessOrganizationLogDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IAdminUserAccessOrganizationLogDataMapper) =>
      new AdminUserAccessOrganizationLogRepository(db, dataMapper),
  },
  {
    provide: AdminUserDIToken.AdminUserOrganizationAccountRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, AdminUserDIToken.AdminUserOrganizationAccountDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IAdminUserOrganizationAccountDataMapper) =>
      new AdminUserOrganizationAccountRepository(db, dataMapper),
  },
  {
    provide: AuthenticationDIToken.AuthenticationMemoryCachedRepository,
    inject: [InfrastructureConfigDIToken.MemoryCachedInstance],
    useFactory: (db: MemoryCachedInstanceParams) => new AuthenticationMemoryCachedRepository(db),
  },
  {
    provide: CertificateDIToken.CertificateRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, CertificateDIToken.CertificateDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ICertificateDataMapper) => new CertificateRepository(db, dataMapper),
  },
  {
    provide: ColumnSettingDIToken.ColumnSettingRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ColumnSettingDIToken.ColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IColumnSettingDataMapper) =>
      new ColumnSettingRepository(db, dataMapper),
  },
  {
    provide: ColumnSettingDIToken.OrganizationColumnSettingRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ColumnSettingDIToken.OrganizationColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationColumnSettingDataMapper) =>
      new OrganizationColumnSettingRepository(db, dataMapper),
  },
  {
    provide: ColumnSettingDIToken.TemplateColumnSettingRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ColumnSettingDIToken.TemplateColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ITemplateColumnSettingDataMapper) =>
      new TemplateColumnSettingRepository(db, dataMapper),
  },
  {
    provide: CourseDIToken.CourseVersionCertificateRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, CourseDIToken.CourseVersionCertificateDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ICourseVersionCertificateDataMapper) =>
      new CourseVersionCertificateRepository(db, dataMapper),
  },
  {
    provide: LoginProviderDIToken.LoginProviderRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, LoginProviderDIToken.LoginProviderDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ILoginProviderDataMapper) =>
      new LoginProviderRepository(db, dataMapper),
  },
  {
    provide: MediaDIToken.MediaRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, MediaDIToken.MediaDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IMediaDataMapper) => new MediaRepository(db, dataMapper),
  },
  {
    provide: OrganizationCertificateDIToken.OrganizationCertificateRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, OrganizationCertificateDIToken.OrganizationCertificateDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationCertificateDataMapper) =>
      new OrganizationCertificateRepository(db, dataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, OrganizationDIToken.OrganizationDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationDataMapper) =>
      new OrganizationRepository(db, dataMapper),
  },
  {
    provide: OrganizationLoginProviderDIToken.OrganizationLoginProviderRepository,
    inject: [
      InfrastructureConfigDIToken.DbInstance,
      OrganizationLoginProviderDIToken.OrganizationLoginProviderDataMapper,
    ],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationLoginProviderDataMapper) =>
      new OrganizationLoginProviderRepository(db, dataMapper),
  },
  {
    provide: OrganizationReportDIToken.OrganizationReportRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, OrganizationReportDIToken.OrganizationReportDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationReportDataMapper) =>
      new OrganizationReportRepository(db, dataMapper),
  },
  {
    provide: OrganizationSchedulerDIToken.OrganizationSchedulerRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, OrganizationSchedulerDIToken.OrganizationSchedulerDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationSchedulerDataMapper) =>
      new OrganizationSchedulerRepository(db, dataMapper),
  },
  {
    provide: OrganizationStorageDIToken.OrganizationStorageRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, OrganizationStorageDIToken.OrganizationStorageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationStorageDataMapper) =>
      new OrganizationStorageRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PackageRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, PlanDIToken.PackageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPackageDataMapper) => new PackageRepository(db, dataMapper),
  },
  {
    provide: PermissionGroupDIToken.PermissionGroupRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, PermissionGroupDIToken.PermissionGroupDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPermissionGroupDataMapper) =>
      new PermissionGroupRepository(db, dataMapper),
  },
  {
    provide: TermsAndConditionsDIToken.TermsAndConditionsRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, TermsAndConditionsDIToken.TermsAndConditionsDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ITermsAndConditionsDataMapper) =>
      new TermsAndConditionsRepository(db, dataMapper),
  },
  {
    provide: ColumnSettingDIToken.TemplateColumnSettingRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ColumnSettingDIToken.TemplateColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ITemplateColumnSettingDataMapper) =>
      new TemplateColumnSettingRepository(db, dataMapper),
  },
  {
    provide: MediaDIToken.MediaRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, MediaDIToken.MediaDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IMediaDataMapper) => new MediaRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PackageRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, PlanDIToken.PackageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPackageDataMapper) => new PackageRepository(db, dataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKURepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IProductSKUDataMapper) => new ProductSKURepository(db, dataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKUBundleRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUBundleDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IProductSKUBundleDataMapper) =>
      new ProductSKUBundleRepository(db, dataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKUCourseRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUCourseDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IProductSKUCourseDataMapper) =>
      new ProductSKUCourseRepository(db, dataMapper),
  },
  {
    provide: UserDIToken.UserRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, UserDIToken.UserDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IUserDataMapper) => new UserRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, PlanDIToken.PlanDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanDataMapper) => new PlanRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, PlanDIToken.PlanPackageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageDataMapper) => new PlanPackageRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageLicenseRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, PlanDIToken.PlanPackageLicenseDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageLicenseDataMapper) =>
      new PlanPackageLicenseRepository(db, dataMapper),
  },
  {
    provide: CourseDIToken.CourseRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, CourseDIToken.CourseDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ICourseDataMapper) => new CourseRepository(db, dataMapper),
  },
  {
    provide: CourseDIToken.CourseVersionRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, CourseDIToken.CourseVersionDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ICourseVersionDataMapper) =>
      new CourseVersionRepository(db, dataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IEnrollmentDataMapper) => new EnrollmentRepository(db, dataMapper),
  },
  {
    provide: PreEnrollmentTransactionDIToken.PreEnrollmentTransactionRepository,
    inject: [
      InfrastructureConfigDIToken.DbInstance,
      PreEnrollmentTransactionDIToken.PreEnrollmentTransactionDataMapper,
    ],
    useFactory: (db: DbInstanceParams, dataMapper: IPreEnrollmentTransactionDataMapper) =>
      new PreEnrollmentTransactionRepository(db, dataMapper),
  },
  {
    provide: ProductSKUDIToken.CourseMarketplaceRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, ProductSKUDIToken.CourseMarketplaceDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ICourseMarketplaceDataMapper) =>
      new CourseMarketplaceRepository(db, dataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentPlanPackageLicenseRepository,
    inject: [InfrastructureConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IEnrollmentPlanPackageLicenseDataMapper) =>
      new EnrollmentPlanPackageLicenseRepository(db, dataMapper),
  },
];

@Module({
  imports: [DataMapperModule, InfrastructureConfigsModule],
  providers: [...persistenceProviders],
  exports: [
    AdminUserDIToken.AdminUserAccessOrganizationLogRepository,
    AdminUserDIToken.AdminUserOrganizationAccountRepository,
    AdminUserDIToken.AdminUserRepository,
    AuthenticationDIToken.AuthenticationMemoryCachedRepository,
    CertificateDIToken.CertificateRepository,
    ColumnSettingDIToken.ColumnSettingRepository,
    ColumnSettingDIToken.OrganizationColumnSettingRepository,
    ColumnSettingDIToken.TemplateColumnSettingRepository,
    CourseDIToken.CourseRepository,
    CourseDIToken.CourseVersionCertificateRepository,
    CourseDIToken.CourseVersionRepository,
    EnrollmentDIToken.EnrollmentRepository,
    EnrollmentDIToken.EnrollmentPlanPackageLicenseRepository,
    LoginProviderDIToken.LoginProviderRepository,
    MediaDIToken.MediaRepository,
    OrganizationCertificateDIToken.OrganizationCertificateRepository,
    OrganizationDIToken.OrganizationRepository,
    OrganizationLoginProviderDIToken.OrganizationLoginProviderRepository,
    OrganizationReportDIToken.OrganizationReportRepository,
    OrganizationSchedulerDIToken.OrganizationSchedulerRepository,
    OrganizationStorageDIToken.OrganizationStorageRepository,
    PermissionGroupDIToken.PermissionGroupRepository,
    PlanDIToken.PackageRepository,
    PlanDIToken.PlanRepository,
    PlanDIToken.PlanPackageRepository,
    PlanDIToken.PlanPackageLicenseRepository,
    PreEnrollmentTransactionDIToken.PreEnrollmentTransactionRepository,
    ProductSKUDIToken.ProductSKUBundleRepository,
    ProductSKUDIToken.ProductSKUCourseRepository,
    ProductSKUDIToken.ProductSKURepository,
    TermsAndConditionsDIToken.TermsAndConditionsRepository,
    UserDIToken.UserRepository,
    ProductSKUDIToken.CourseMarketplaceRepository,
  ],
})
export class PersistanceModule {}
