import { InfrastructureConfigDIToken } from '@applications/di/infrastructures/config';
import { InfrastructureServiceDIToken } from '@applications/di/infrastructures/service';
import { EnvironmentModule } from '@applications/module/infrastructure/configs/environment.module';
import { MessageBrokerModule } from '@applications/module/infrastructure/configs/messageBroker.module';
import { DataMapperModule } from '@applications/module/infrastructure/dataMapper.module';
import { PersistanceModule } from '@applications/module/infrastructure/persistance.module';
import { LoggerServiceModule } from '@applications/module/infrastructure/services/logger.service.module';
import { IMessageBrokerInstance } from '@domains/interfaces/infrastructures/services/messageBroker.interface';
import { AESCipherService } from '@infrastructures/services/aes.service';
import { AWSFileStorageService } from '@infrastructures/services/awsFileStorage.service';
import { CoreCertificateService } from '@infrastructures/services/coreCertificate.service';
import { GoogleCloudFileStorageService } from '@infrastructures/services/googleCloudFileStorage.service';
import { HttpService } from '@infrastructures/services/http.service';
import { ImageService } from '@infrastructures/services/image.service';
import { JwtService } from '@infrastructures/services/jwt.service';
import { LmsApiService } from '@infrastructures/services/lmsApi.service';
import { MessageBrokerService } from '@infrastructures/services/messageBroker.service';
import { NotificationService } from '@infrastructures/services/notification.service';
import { OAuthService } from '@infrastructures/services/oAuth.service';
import { StorageFactoryService } from '@infrastructures/services/storage.service';
import { TwoFactorService } from '@infrastructures/services/twoFactor.service';
import { WebhookService } from '@infrastructures/services/webhook.service';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [
    EnvironmentModule,
    PersistanceModule,
    MessageBrokerModule,
    EventEmitterModule.forRoot(),
    DataMapperModule,
    LoggerServiceModule,
  ],
  providers: [
    {
      provide: InfrastructureServiceDIToken.HttpService,
      useClass: HttpService,
    },
    {
      provide: InfrastructureServiceDIToken.OAuthService,
      useClass: OAuthService,
    },
    {
      provide: InfrastructureServiceDIToken.JwtService,
      useClass: JwtService,
    },
    {
      provide: InfrastructureServiceDIToken.TwoFactorService,
      useClass: TwoFactorService,
    },
    {
      provide: InfrastructureServiceDIToken.CoreCertificateService,
      useClass: CoreCertificateService,
    },
    {
      provide: InfrastructureServiceDIToken.StorageFactoryService,
      useClass: StorageFactoryService,
    },
    {
      provide: InfrastructureServiceDIToken.NotificationMessageBrokerService,
      inject: [InfrastructureConfigDIToken.MessageNotificationBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) => new MessageBrokerService(brokerMessageInstance),
    },
    {
      provide: InfrastructureServiceDIToken.NotificationService,
      useClass: NotificationService,
    },
    {
      provide: InfrastructureServiceDIToken.ImageService,
      useClass: ImageService,
    },
    {
      provide: InfrastructureServiceDIToken.GoogleCloudFileStorageService,
      useClass: GoogleCloudFileStorageService,
    },
    {
      provide: InfrastructureServiceDIToken.AWSFileStorageService,
      useClass: AWSFileStorageService,
    },
    {
      provide: InfrastructureServiceDIToken.AESCipherService,
      useClass: AESCipherService,
    },
    {
      provide: InfrastructureServiceDIToken.LmsApiService,
      useClass: LmsApiService,
    },
    {
      provide: InfrastructureServiceDIToken.WebhookService,
      useClass: WebhookService,
    },
  ],
  exports: [
    InfrastructureServiceDIToken.HttpService,
    InfrastructureServiceDIToken.OAuthService,
    InfrastructureServiceDIToken.JwtService,
    InfrastructureServiceDIToken.TwoFactorService,
    InfrastructureServiceDIToken.CoreCertificateService,
    InfrastructureServiceDIToken.StorageFactoryService,
    InfrastructureServiceDIToken.NotificationMessageBrokerService,
    InfrastructureServiceDIToken.NotificationService,
    InfrastructureServiceDIToken.ImageService,
    InfrastructureServiceDIToken.GoogleCloudFileStorageService,
    InfrastructureServiceDIToken.AWSFileStorageService,
    InfrastructureServiceDIToken.AESCipherService,
    InfrastructureServiceDIToken.LmsApiService,
    InfrastructureServiceDIToken.WebhookService,
    PersistanceModule,
    DataMapperModule,
    LoggerServiceModule,
  ],
})
export class InfrastructureServiceModule {}
