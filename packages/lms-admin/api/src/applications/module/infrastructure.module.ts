import { InfrastructureConfigsModule } from '@applications/module/infrastructure/config.module';
import { InfrastructureServiceModule } from '@applications/module/infrastructure/service.module';
import { Module, Provider } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { HttpExceptionFilter } from '@presenters/api/middleware/exceptions/httpExceptionFilter';

const providers: Provider[] = [
  {
    provide: APP_FILTER,
    useClass: HttpExceptionFilter,
  },
];

@Module({
  imports: [InfrastructureConfigsModule, InfrastructureServiceModule],
  exports: [InfrastructureConfigsModule, InfrastructureServiceModule],
  providers,
})
export class InfrastructureModule {}
