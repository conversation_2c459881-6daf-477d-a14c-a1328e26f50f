import { <PERSON><PERSON><PERSON>oken, OrganizationDIToken, PlanDIToken } from '@applications/di/domain';
import { InfrastructureModule } from '@applications/module/infrastructure.module';
import { CourseService } from '@domains/services/course.service';
import { OrganizationService } from '@domains/services/organization.service';
import { PackageService } from '@domains/services/package.service';
import { CheckDuplicatedDomainUseCase } from '@domains/usecases/organization/checkDuplicatedDomain.usecase';
import { CheckOrganizationExistProductSKUCourseUseCase } from '@domains/usecases/organization/checkOrganizationExistProductSKUCourse.usecase';
import { CleanOrganizationUseCase } from '@domains/usecases/organization/cleanOrganization.usecase';
import { CreateOrganizationUseCase } from '@domains/usecases/organization/createOrganization.usecase';
import { CreateOrganizationCertificatesUseCase } from '@domains/usecases/organization/createOrganizationCertificate.usecase';
import { CreateOrganizationPlanUseCase } from '@domains/usecases/organization/createOrganizationPlan.usecase';
import { GetAdditionalColumnSettingUseCase } from '@domains/usecases/organization/getAdditionalColumnSetting.usecase';
import { GetOrganizationUseCase } from '@domains/usecases/organization/getOrganization.usecase';
import { GetOrganizationCertificateUseCase } from '@domains/usecases/organization/getOrganizationCertificate.usecase';
import { GetOrganizationCertificateListUseCase } from '@domains/usecases/organization/getOrganizationCertificateList.usecase';
import { GetOrganizationCertificateThumbnailUrlUseCase } from '@domains/usecases/organization/getOrganizationCertificateThumbnailUrl.usecase';
import { GetOrganizationCourseMarketplaceDropdownUseCase } from '@domains/usecases/organization/getOrganizationCourseMarketplaceDropdown.usecase';
import { GetOrganizationCourseMarketplaceListUseCase } from '@domains/usecases/organization/getOrganizationCourseMarketplaceList.usecase';
import { GetOrganizationListUseCase } from '@domains/usecases/organization/getOrganizationList.usecase';
import { GetOrganizationPlanDetailUseCase } from '@domains/usecases/organization/getOrganizationPlanDetail.usecase';
import { GetOrganizationPlanListUseCase } from '@domains/usecases/organization/getOrganizationPlanList.usecase';
import { GetOrganizationReportListUseCase } from '@domains/usecases/organization/getOrganizationReportList.usecase';
import { GetOrganizationReportOrderUseCase } from '@domains/usecases/organization/getOrganizationReportOrder.usecase';
import { GetRequestAccessOrganizationUseCase } from '@domains/usecases/organization/getRequestAccessOrganizationUseCase.usecase';
import { GetTemplateColumnSettingDetailUseCase } from '@domains/usecases/organization/getTemplateColumnSettingDetail.usecase';
import { GetTemplateColumnSettingDropdownUseCase } from '@domains/usecases/organization/getTemplateColumnSettingDropdown.usecase';
import { OrganizationUserDropdownUseCase } from '@domains/usecases/organization/organizationUserDropdown.usecase';
import { UpdateOrganizationReportUseCase } from '@domains/usecases/organization/updateOrganisationReport.usecase';
import { UpdateOrganizationCertificatesUseCase } from '@domains/usecases/organization/updateOrganizationCertificate.usecase';
import { UpdateOrganizationCourseMarketplaceUseCase } from '@domains/usecases/organization/updateOrganizationCourseMarketplace.usecase';
import { UpdateOrganizationGeneralUseCase } from '@domains/usecases/organization/updateOrganizationGeneral.usecase';
import { UpdateOrganizationObjectiveUseCase } from '@domains/usecases/organization/updateOrganizationObjective.usecase';
import { UpdateOrganizationPlanUseCase } from '@domains/usecases/organization/updateOrganizationPlan.usecase';
import { UpdateOrganizationReportOrderUseCase } from '@domains/usecases/organization/updateOrganizationReportOrder.usecase';
import { UpdateTemplateColumnSettingUseCase } from '@domains/usecases/organization/updateTemplateColumnSetting.usecase';
import { Module, Provider } from '@nestjs/common';
import { OrganizationController } from '@presenters/api/controllers/organization/organization.controller';

const usecaseProviders: Provider[] = [
  {
    provide: OrganizationDIToken.GetOrganizationUseCase,
    useClass: GetOrganizationUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationListUseCase,
    useClass: GetOrganizationListUseCase,
  },
  {
    provide: OrganizationDIToken.CreateOrganizationUseCase,
    useClass: CreateOrganizationUseCase,
  },
  {
    provide: OrganizationDIToken.UpdateOrganizationGeneralUseCase,
    useClass: UpdateOrganizationGeneralUseCase,
  },
  {
    provide: OrganizationDIToken.CheckDuplicatedDomainUseCase,
    useClass: CheckDuplicatedDomainUseCase,
  },
  {
    provide: OrganizationDIToken.UpdateOrganizationObjectiveUseCase,
    useClass: UpdateOrganizationObjectiveUseCase,
  },
  {
    provide: OrganizationDIToken.OrganizationService,
    useClass: OrganizationService,
  },
  {
    provide: OrganizationDIToken.GetOrganizationReportListUseCase,
    useClass: GetOrganizationReportListUseCase,
  },
  {
    provide: OrganizationDIToken.UpdateOrganizationReportUseCase,
    useClass: UpdateOrganizationReportUseCase,
  },
  {
    provide: OrganizationDIToken.GetTemplateColumnSettingDropdownUseCase,
    useClass: GetTemplateColumnSettingDropdownUseCase,
  },
  {
    provide: OrganizationDIToken.GetTemplateColumnSettingDetailUseCase,
    useClass: GetTemplateColumnSettingDetailUseCase,
  },
  {
    provide: OrganizationDIToken.GetAdditionalColumnSettingUseCase,
    useClass: GetAdditionalColumnSettingUseCase,
  },
  {
    provide: OrganizationDIToken.UpdateTemplateColumnSettingUseCase,
    useClass: UpdateTemplateColumnSettingUseCase,
  },

  { provide: OrganizationDIToken.GetOrganizationReportOrderUseCase, useClass: GetOrganizationReportOrderUseCase },
  {
    provide: OrganizationDIToken.UpdateOrganizationReportOrderUseCase,
    useClass: UpdateOrganizationReportOrderUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationCertificateListUseCase,
    useClass: GetOrganizationCertificateListUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationCertificateThumbnailUrlUseCase,
    useClass: GetOrganizationCertificateThumbnailUrlUseCase,
  },
  {
    provide: OrganizationDIToken.CreateOrganizationCertificateUseCase,
    useClass: CreateOrganizationCertificatesUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationCertificateUseCase,
    useClass: GetOrganizationCertificateUseCase,
  },
  {
    provide: OrganizationDIToken.UpdateOrganizationCertificateUseCase,
    useClass: UpdateOrganizationCertificatesUseCase,
  },
  {
    provide: OrganizationDIToken.GetRequestAccessOrganizationUseCase,
    useClass: GetRequestAccessOrganizationUseCase,
  },
  {
    provide: OrganizationDIToken.OrganizationUserDropdownUseCase,
    useClass: OrganizationUserDropdownUseCase,
  },
  {
    provide: OrganizationDIToken.CleanOrganizationUseCase,
    useClass: CleanOrganizationUseCase,
  },
  {
    provide: OrganizationDIToken.CreateOrganizationPlanUseCase,
    useClass: CreateOrganizationPlanUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationPlanDetailUseCase,
    useClass: GetOrganizationPlanDetailUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationPlanListUseCase,
    useClass: GetOrganizationPlanListUseCase,
  },
  {
    provide: OrganizationDIToken.UpdateOrganizationPlanUseCase,
    useClass: UpdateOrganizationPlanUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationCourseMarketplaceDropdownUseCase,
    useClass: GetOrganizationCourseMarketplaceDropdownUseCase,
  },
  {
    provide: OrganizationDIToken.GetOrganizationCourseMarketplaceListUseCase,
    useClass: GetOrganizationCourseMarketplaceListUseCase,
  },
  {
    provide: OrganizationDIToken.CheckOrganizationExistProductSKUCourseUseCase,
    useClass: CheckOrganizationExistProductSKUCourseUseCase,
  },
  {
    provide: OrganizationDIToken.UpdateOrganizationCourseMarketplaceUseCase,
    useClass: UpdateOrganizationCourseMarketplaceUseCase,
  },
];

const serviceProviders: Provider[] = [
  {
    provide: PlanDIToken.PackageService,
    useClass: PackageService,
  },
  {
    provide: CourseDIToken.CourseService,
    useClass: CourseService,
  },
];

@Module({
  imports: [InfrastructureModule],
  controllers: [OrganizationController],
  providers: [...usecaseProviders, ...serviceProviders],
  exports: [],
})
export class OrganizationModule {}
