{"name": "lms-admin-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "packageManager": "pnpm@9.12.0", "scripts": {"clean": "rimraf node_modules", "clean:build": "<PERSON><PERSON><PERSON> dist", "build-check": "rm -rf dist && tsc --diagnostics --project tsconfig.app.json --noEmit", "build": "nest build swc", "dev": "NODE_ENV=dev nest start -b swc --watch", "release": "NODE_ENV=release nest start -b swc --watch", "master": "NODE_ENV=master nest start -b swc --watch", "format": "prettier --write --config ./node_modules/@lms/eslint-config/.prettierrc.json \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prebuild": "<PERSON><PERSON><PERSON> dist", "start:prod": "node dist/main", "start": "nest start -b swc", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:watch": "jest --watch", "test": "jest", "watch-dev": "nest start -b swc --debug --watch"}, "dependencies": {"@aws-sdk/client-s3": "3.758.0", "@aws-sdk/s3-request-presigner": "3.758.0", "@fastify/cookie": "^9.4.0", "@fastify/csrf-protection": "^6.4.1", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "7.3.0", "@fastify/session": "^10.9.0", "@google-cloud/storage": "6.10.1", "@iso/constants": "workspace:*", "@iso/email": "workspace:*", "@iso/helpers": "workspace:*", "@iso/lms": "workspace:*", "@nestjs/common": "9.2.1", "@nestjs/config": "^3.2.2", "@nestjs/core": "9.2.1", "@nestjs/event-emitter": "^2.1.1", "@nestjs/platform-fastify": "9.2.1", "@slack/webhook": "6.0.0", "@types/jsonwebtoken": "8.3.5", "@types/speakeasy": "^2.0.5", "amqplib": "0.10.4", "axios": "1.7.7", "axios-retry": "3.2.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "connect-redis": "^7.1.1", "dayjs": "1.11.10", "dd-trace": "5.24.0", "express-session": "^1.17.3", "fastify": "4.12.0", "form-data": "4.0.0", "http-status-codes": "^1.4.0", "ioredis": "5.4.1", "jsonwebtoken": "^8.5.1", "mongodb": "3.7.3", "ms": "2.1.3", "nest-winston": "1.10.2", "qs": "6.9.4", "randomstring": "1.3.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sharp": "0.32.6", "simple-oauth2": "^5.1.0", "speakeasy": "^2.0.0", "timestring": "^7.0.0", "uuid": "11.0.2", "validator": "^13.7.0", "winston": "3.3.3"}, "devDependencies": {"@lms/eslint-config": "workspace:*", "@lms/typescript-config": "workspace:*", "@nestjs/cli": "10.4.7", "@nestjs/schematics": "10.2.3", "@nestjs/testing": "9.2.1", "@types/amqplib": "0.10.5", "@types/mongodb": "3.5.18", "@types/ms": "0.7.34", "@types/node": "20.3.1", "@types/qs": "6.9.18", "@types/randomstring": "1.1.6", "@types/supertest": "2.0.12", "@types/timestring": "7.0.0", "@types/uuid": "10.0.0", "jest": "29.5.0", "jest-mock-extended": "3.0.7", "supertest": "6.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "./", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s?$": ["@swc/jest"]}, "collectCoverageFrom": ["**/*.(t|j)s"], "modulePathIgnorePatterns": ["__mocks__"], "testPathIgnorePatterns": ["/mockData/"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"@applications/(.*)$": ["<rootDir>/src/applications/$1"], "@core/(.*)$": ["<rootDir>/src/core/$1"], "@core/instances/(.*)$": ["<rootDir>/src/core/instances/$1"], "@domains/(.*)$": ["<rootDir>/src/domains/$1"], "@infrastructures/(.*)$": ["<rootDir>/src/infrastructures/$1"], "@usecases/(.*)$": ["<rootDir>/src/usecases/$1"], "@presenters/(.*)$": ["<rootDir>/src/presenters/$1"]}}}