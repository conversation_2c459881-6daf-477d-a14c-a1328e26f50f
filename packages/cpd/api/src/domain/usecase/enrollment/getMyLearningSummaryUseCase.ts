import { GenericID } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum, ExternalContentTypeEnum } from '@iso/lms/enums/course.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { PackageContentModelTypeEnum } from '@iso/lms/enums/packages.enum';
import { PreEnrollmentReservationStatusEnum } from '@iso/lms/enums/preEnrollmentReservation.enum';
import {
  PreEnrollmentTransactionEnrollByEnum,
  PreEnrollmentTransactionStatusEnum,
} from '@iso/lms/enums/preEnrollmentTransaction.enum';
import {
  checkHavePlanPackageLicenseAvailableToday,
  getAvailablePeriodLicenseByUser,
} from '@iso/lms/services/planPackageLicense.service';
import { PlanPackageLicenseParams } from '@iso/lms/types/planPackageLicense.type';
import { inject, injectable } from 'inversify';
import { map as _map } from 'lodash';

import { CourseDIToken, LearningPathEnrollmentDIToken } from '@entities/constants/di';
import {
  OutputMyLearningSummaryParams,
  MyLearningSummaryParams,
  MyEnrollmentEligibleLearningFilterEnum
} from '@entities/constants/enrollment';
import {
  LearningPathContentProgressRegisterFromEnum,
  LearningPathEnrollmentStatusEnum,
} from '@entities/constants/learningPathEnrollment';
import { TYPES } from '@entities/constants/types';
import { IGetMyLearningSummaryUseCase } from '@entities/interface/enrollment.interface';
import {
  EnrollmentRepositoryInterface,
  ICourseRepository,
  ILearningPathEnrollmentRepository,
  IPreEnrollmentTransactionRepository,
} from '@entities/interface/repository';
import { date } from '@entities/services/dateUtils';

@injectable()
export class GetMyLearningSummaryUseCase implements IGetMyLearningSummaryUseCase {
  constructor(
    @inject(TYPES.EnrollmentRepository)
    private readonly enrollmentRepository: EnrollmentRepositoryInterface,
    @inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @inject(TYPES.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,
    @inject(LearningPathEnrollmentDIToken.LearningPathEnrollmentRepository)
    private readonly learningPathEnrollmentRepository: ILearningPathEnrollmentRepository,
  ) {}

  async execute(params: MyLearningSummaryParams): Promise<OutputMyLearningSummaryParams> {
    const { userId, organizationId, citizenId } = params;

    const myLearningPathsSummaryPromises = this.learningPathEnrollmentRepository.aggregate(
      await this.getMyLearningPathsSummaryQuery(organizationId, userId),
    );

    const myEnrollmentSummaryPromises = this.enrollmentRepository.aggregate(
      await this.getMyEnrollmentSummaryQuery(organizationId, userId),
    );

    const myPreEnrollmentSummaryPromises = this.preEnrollmentTransactionRepository.aggregate(
      await this.getMyPreEnrollmentSummaryQuery(organizationId, citizenId, userId),
    );

    const myHistoryLearningPathsSummaryPromises = this.learningPathEnrollmentRepository.aggregate([
      {
        $match: {
          userId,
          organizationId,
          status: {
            $in: [
              LearningPathEnrollmentStatusEnum.CANCELED,
              LearningPathEnrollmentStatusEnum.COMPLETED,
              LearningPathEnrollmentStatusEnum.EXPIRED,
            ],
          },
        },
      },
    ]);

    const myHistoryEnrollmentSummaryPromises = this.enrollmentRepository.aggregate(
      await this.getMyHistorySummaryQuery(organizationId, userId),
    );

    const myHistoryPreEnrollmentSummaryPromises = this.preEnrollmentTransactionRepository.aggregate([
      {
        $match: {
          status: PreEnrollmentTransactionStatusEnum.REJECTED,
          organizationId,
        },
      },
      {
        $lookup: {
          from: 'users',
          let: { citizenId: '$payload.citizenId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$guid', userId],
                    },
                    {
                      $eq: ['$citizenId', '$$citizenId'],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                guid: '$guid',
                citizenId: '$citizenId',
              },
            },
          ],
          as: 'user',
        },
      },
      { $unwind: '$user' },
      {
        $lookup: {
          from: 'enrollment-regulator-reports',
          localField: 'id',
          foreignField: 'preEnrollmentTransactionId',
          as: 'enrollmentRegulatorReport',
        },
      },
      {
        $unwind: '$enrollmentRegulatorReport',
      },
      {
        $lookup: {
          from: 'courses',
          let: { courseId: '$enrollmentRegulatorReport.courseId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$id', '$$courseId'] }, { $eq: ['$isEnabled', true] }],
                },
              },
            },
            {
              $project: {
                id: 1,
                url: 1,
                objectiveType: 1,
                productSKUCourseId: 1,
              },
            },
          ],
          as: 'course',
        },
      },
      { $unwind: '$course' },

      // Join Plan Package License to match GetMyHistoryEnrollmentUseCase behavior
      ...this.buildFindPlanPackageLicenseQuery(organizationId, userId),
    ]);

    const enrollmentIdsRegisterFromLearningPathPromises = this.getEnrollmentIdsRegisterFromLearningPath({
      organizationId,
      userId,
    });

    const [
      myLearningPaths,
      myLearningEnrollment,
      myLearningPreEnrollments,
      myHistoryLearningPaths,
      myHistoryEnrollment,
      myHistoryPreEnrollments,
      enrollmentIdInLearningPathList,
    ] = await Promise.all([
      myLearningPathsSummaryPromises,
      myEnrollmentSummaryPromises,
      myPreEnrollmentSummaryPromises,
      myHistoryLearningPathsSummaryPromises,
      myHistoryEnrollmentSummaryPromises,
      myHistoryPreEnrollmentSummaryPromises,
      enrollmentIdsRegisterFromLearningPathPromises,
    ]);

    const myLearningCourseList = [...myLearningEnrollment, ...myLearningPreEnrollments].filter(
      (item) => !enrollmentIdInLearningPathList.includes(item.id),
    );

    // Apply the same filtering logic as GetMyHistoryEnrollmentUseCase
    const historyEnrollmentItems = myHistoryEnrollment.map((data) => {
      const { planPackageLicenses, ...remainData } = data;

      const isActivePlanPackageLicense = this.getIsActivePlanPackageLicense({
        userId,
        enrollmentExternalContentType: data.externalContentType,
        planPackageLicenses,
      });

      return {
        ...remainData,
        isActivePlanPackageLicense,
      };
    });

    const historyPreEnrollmentItems = myHistoryPreEnrollments.map((data) => {
      const { planPackageLicenses, ...remainData } = data;

      const isActivePlanPackageLicense = this.getIsActivePlanPackageLicense({
        userId,
        enrollmentExternalContentType: data.externalContentType,
        planPackageLicenses,
      });

      return {
        ...remainData,
        isActivePlanPackageLicense,
      };
    });

    // Apply filtering with default eligibleLearning as 'ALL' to match GetMyHistoryEnrollmentUseCase behavior
    const filteredHistoryEnrollment = this.filterEnrollmentByEligibilityLearning(
      MyEnrollmentEligibleLearningFilterEnum.ALL,
      historyEnrollmentItems,
    );

    const filteredHistoryPreEnrollment = this.filterEnrollmentByEligibilityLearning(
      MyEnrollmentEligibleLearningFilterEnum.ALL,
      historyPreEnrollmentItems,
    );

    const myHistoryCourseList = [...filteredHistoryEnrollment, ...filteredHistoryPreEnrollment].filter(
      (item) => !enrollmentIdInLearningPathList.includes(item.id),
    );

    return {
      totalLearning: myLearningPaths.length + myLearningCourseList.length,
      totalHistories: myHistoryLearningPaths.length + myHistoryCourseList.length,
    };
  }

  private async getMyLearningPathsSummaryQuery(organizationId: GenericID, userId: GenericID) {
    const aggregateParam = [
      {
        $match: {
          userId,
          organizationId,
          status: {
            $in: [
              LearningPathEnrollmentStatusEnum.PRE_ASSIGN,
              LearningPathEnrollmentStatusEnum.ASSIGNED,
              LearningPathEnrollmentStatusEnum.IN_PROGRESS,
            ],
          },
          $or: [{ expiredAt: { $gte: date().toDate() } }, { expiredAt: { $eq: null } }],
        },
      },
    ];
    return aggregateParam;
  }

  private async getMyEnrollmentSummaryQuery(organizationId: GenericID, userId: GenericID) {
    const courseRegularAggregate = await this.getCourseRegularListQuery(organizationId);
    const courseRegularList = await this.courseRepository.aggregate(courseRegularAggregate);

    const courseNonRegularAggregate = await this.getCourseNonRegularListQuery(organizationId);
    const courseNonRegularList = await this.courseRepository.aggregate(courseNonRegularAggregate);

    const courseRegularIds = courseRegularList.map((val: Record<string, string>) => val.id);
    const courseNonRegularIds = courseNonRegularList.map((val: Record<string, string>) => val.id);

    const aggregateParam = [
      {
        $match: {
          userId,
          $or: [
            {
              $and: [
                {
                  courseId: {
                    $in: courseNonRegularIds,
                  },
                },
                {
                  $or: [
                    {
                      $and: [
                        {
                          status: {
                            $in: [
                              EnrollmentStatusEnum.NOT_STARTED,
                              EnrollmentStatusEnum.IN_PROGRESS,
                              EnrollmentStatusEnum.PASSED,
                            ],
                          },
                        },
                        {
                          $or: [{ expiredAt: { $gte: date().toDate() } }, { expiredAt: { $eq: null } }],
                        },
                      ],
                    },
                    {
                      $and: [
                        {
                          status: {
                            $in: [
                              EnrollmentStatusEnum.PENDING_RESULT,
                              EnrollmentStatusEnum.PASSED,
                              EnrollmentStatusEnum.VERIFIED,
                              EnrollmentStatusEnum.PENDING_APPROVAL,
                            ],
                          },
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              $and: [
                {
                  courseId: {
                    $in: courseRegularIds,
                  },
                },
                {
                  $or: [
                    {
                      $and: [
                        {
                          status: {
                            $in: [
                              EnrollmentStatusEnum.NOT_STARTED,
                              EnrollmentStatusEnum.IN_PROGRESS,
                              EnrollmentStatusEnum.PASSED,
                            ],
                          },
                        },
                        {
                          $or: [{ expiredAt: { $gte: date().toDate() } }, { expiredAt: { $eq: null } }],
                        },
                      ],
                    },
                    {
                      status: {
                        $in: [EnrollmentStatusEnum.PENDING_RESULT],
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
    ];

    return aggregateParam;
  }

  private async getMyPreEnrollmentSummaryQuery(organizationId: GenericID, citizenId: string, userId: GenericID) {
    const aggregateParam = [
      {
        $match: {
          'payload.citizenId': citizenId,
          status: {
            $in: [
              PreEnrollmentTransactionStatusEnum.PASSED,
              PreEnrollmentTransactionStatusEnum.EDITED_AFTER_VERIFY,
              PreEnrollmentTransactionStatusEnum.WAITING_VERIFY,
            ],
          },
          organizationId,
        },
      },
      {
        $facet: {
          admin: [
            {
              $match: {
                enrollBy: PreEnrollmentTransactionEnrollByEnum.ADMIN,
              },
            },
            {
              $lookup: {
                from: 'pre-enrollment-reservations',
                let: { preEnrollmentReservationId: '$preEnrollmentReservationId' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          {
                            $eq: ['$id', '$$preEnrollmentReservationId'],
                          },
                          {
                            $eq: ['$status', PreEnrollmentReservationStatusEnum.PASSED],
                          },
                        ],
                      },
                    },
                  },
                ],
                as: 'preEnrollmentReservation',
              },
            },
            {
              $unwind: '$preEnrollmentReservation',
            },
          ],
          self: [
            {
              $match: {
                enrollBy: PreEnrollmentTransactionEnrollByEnum.SELF,
              },
            },
          ],
        },
      },
      {
        $project: {
          transactions: { $concatArrays: ['$admin', '$self'] },
        },
      },
      { $unwind: { path: '$transactions', preserveNullAndEmptyArrays: true } },
      { $replaceRoot: { newRoot: { $ifNull: ['$transactions', {}] } } },
      {
        $lookup: {
          from: 'users',
          let: { citizenId: '$payload.citizenId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$guid', userId],
                    },
                    {
                      $eq: ['$citizenId', '$$citizenId'],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                guid: '$guid',
                citizenId: '$citizenId',
              },
            },
          ],
          as: 'user',
        },
      },
      { $unwind: '$user' },
      {
        $lookup: {
          from: 'enrollment-regulator-reports',
          localField: 'id',
          foreignField: 'preEnrollmentTransactionId',
          as: 'enrollmentRegulatorReport',
        },
      },
      {
        $unwind: '$enrollmentRegulatorReport',
      },
      {
        $lookup: {
          from: 'courses',
          let: { courseId: '$enrollmentRegulatorReport.courseId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$id', '$$courseId'],
                    },
                    {
                      $in: ['$objectiveType', [CourseObjectiveTypeEnum.TRAINING, CourseObjectiveTypeEnum.REGULAR]],
                    },
                    { $eq: ['$isEnabled', true] },
                  ],
                },
              },
            },
          ],
          as: 'course',
        },
      },
      { $unwind: '$course' },
    ];
    return aggregateParam;
  }

  private async getMyHistorySummaryQuery(organizationId: GenericID, userId: GenericID) {
    const courseRegularAggregate = await this.getCourseRegularListQuery(organizationId);
    const courseRegularList = await this.courseRepository.aggregate(courseRegularAggregate);

    const courseRegularIds = courseRegularList.map((val: Record<string, string>) => val.id);

    const statusApprovedQuery = [{ status: EnrollmentStatusEnum.APPROVED }];
    const statusRejectedQuery = [{ status: EnrollmentStatusEnum.REJECTED }];
    const statusExpiredQuery = [
      {
        $and: [
          {
            $or: [{ status: EnrollmentStatusEnum.EXPIRED }, { status: EnrollmentStatusEnum.IN_PROGRESS }],
          },
          { expiredAt: { $lt: date().toDate() } },
        ],
      },
    ];
    const statusPassedQuery = [
      {
        $and: [
          { status: EnrollmentStatusEnum.PASSED },
          { courseId: { $in: courseRegularIds } },
          { expiredAt: { $lt: date().toDate() } },
        ],
      },
    ];
    const statusCompletedQuery = [{ status: EnrollmentStatusEnum.COMPLETED }];
    const statusCanceledQuery = [{ status: EnrollmentStatusEnum.CANCELED }];

    const aggregateParam = [
      {
        $match: {
          userId,
          $or: [
            ...statusExpiredQuery,
            ...statusApprovedQuery,
            ...statusRejectedQuery,
            ...statusCanceledQuery,
            ...statusPassedQuery,
            ...statusCompletedQuery,
          ],
        },
      },
      {
        $addFields: {
          requestedApprovalAt: {
            $ifNull: ['$requestedApprovalAt', '$expiredAt'],
          },
        },
      },
      {
        $project: {
          id: 1,
          courseId: 1,
          courseVersionId: 1,
          userId: 1,
          status: 1,
          createdAt: 1,
          startedAt: 1,
          updatedAt: 1,
          passedAt: 1,
          requestedApprovalAt: 1,
          expiredAt: 1,
          approvalAt: 1,
          certificateUrl: 1,
          externalContentType: 1,
        },
      },
      {
        $lookup: {
          from: 'courses',
          let: { courseId: '$courseId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$id', '$$courseId'],
                    },
                    {
                      $in: ['$objectiveType', [CourseObjectiveTypeEnum.TRAINING, CourseObjectiveTypeEnum.REGULAR]],
                    },
                  ],
                },
              },
            },
            { $project: { id: 1, url: 1, objectiveType: 1, productSKUCourseId: 1 } },
          ],
          as: 'course',
        },
      },
      { $unwind: '$course' },
      {
        $lookup: {
          from: 'course-versions',
          let: {
            courseVersionId: '$courseVersionId',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$id', '$$courseVersionId'],
                    },
                  ],
                },
              },
            },
            { $project: { id: 1, courseId: 1 } },
          ],
          as: 'courseVersion',
        },
      },
      { $unwind: '$courseVersion' },

      // Join Plan Package License to match GetMyHistoryEnrollmentUseCase behavior
      ...this.buildFindPlanPackageLicenseQuery(organizationId, userId),
    ];

    return aggregateParam;
  }

  private async getCourseRegularListQuery(organizationId: GenericID) {
    const aggregateParam = [
      {
        $match: {
          objectiveType: CourseObjectiveTypeEnum.REGULAR,
          organizationId,
          isEnabled: true,
        },
      },
      {
        $project: {
          id: 1,
        },
      },
    ];

    return aggregateParam;
  }

  private async getCourseNonRegularListQuery(organizationId: GenericID) {
    const aggregateParam = [
      {
        $match: {
          objectiveType: {
            $in: [CourseObjectiveTypeEnum.TRAINING],
          },
          organizationId,
          isEnabled: true,
        },
      },
      {
        $project: {
          id: 1,
        },
      },
    ];

    return aggregateParam;
  }

  private async getEnrollmentIdsRegisterFromLearningPath(params: { userId: GenericID; organizationId: GenericID }) {
    const result = await this.learningPathEnrollmentRepository.aggregate([
      {
        $match: {
          userId: params.userId,
          organizationId: params.organizationId,
        },
      },
      {
        $unwind: {
          path: `$contentProgress`,
        },
      },
      {
        $match: {
          'contentProgress.registerFrom': LearningPathContentProgressRegisterFromEnum.LEARNING_PATH,
        },
      },
      {
        $project: {
          _id: 0,
          enrollmentId: {
            $ifNull: ['$contentProgress.enrollmentId', '$contentProgress.preEnrollmentId'],
          },
          registerFrom: '$contentProgress.registerFrom',
        },
      },
    ]);

    return result.length ? _map(result, 'enrollmentId') : [];
  }

  private buildFindPlanPackageLicenseQuery(organizationId: GenericID, userId: GenericID) {
    const currentDate = date().toDate();

    return [
      {
        $lookup: {
          from: 'plans',
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$organizationId', organizationId] }, { $gte: ['$endDate', currentDate] }],
                },
              },
            },
          ],
          as: 'plans',
        },
      },
      {
        $lookup: {
          from: 'plan-packages',
          let: {
            productSKUCourseId: '$course.productSKUCourseId',
            planIds: {
              $map: {
                input: '$plans',
                as: 'plan',
                in: '$$plan.id',
              },
            },
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $in: ['$planId', '$$planIds'] }, { $gte: ['$endDate', currentDate] }],
                },
              },
            },
            {
              $lookup: {
                from: 'packages',
                let: { packageId: '$packageId' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$id', '$$packageId'] },
                    },
                  },
                ],
                as: 'package',
              },
            },
            { $unwind: '$package' },
            {
              $match: {
                $expr: {
                  $or: [
                    {
                      $and: [
                        { $eq: ['$content.model', PackageContentModelTypeEnum.CUSTOM] },
                        { $in: ['$$productSKUCourseId', '$content.productSKUCourseIds'] },
                      ],
                    },
                    {
                      $and: [
                        { $eq: ['$content.model', PackageContentModelTypeEnum.SUBSCRIPTION] },
                        { $in: ['$$productSKUCourseId', '$package.productSKUCourseIds'] },
                      ],
                    },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: 'plan-package-licenses',
                let: {
                  planPackageId: '$id',
                  planPackageStartDate: '$startDate',
                  planPackageEndDate: '$endDate',
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [{ $eq: ['$planPackageId', '$$planPackageId'] }, { $eq: ['$userId', userId] }],
                      },
                    },
                  },
                  {
                    $addFields: {
                      startedAt: {
                        $ifNull: ['$startedAt', '$$planPackageStartDate'],
                      },
                      expiredAt: {
                        $ifNull: ['$expiredAt', '$$planPackageEndDate'],
                      },
                    },
                  },
                  {
                    $match: {
                      $expr: {
                        $gte: ['$expiredAt', currentDate],
                      },
                    },
                  },
                ],
                as: 'planPackageLicense',
              },
            },
            { $unwind: '$planPackageLicense' },
            {
              $replaceRoot: {
                newRoot: '$planPackageLicense',
              },
            },
          ],
          as: 'planPackageLicenses',
        },
      },
      {
        $project: {
          plans: 0,
        },
      },
    ];
  }

  private getIsActivePlanPackageLicense(params: {
    userId: GenericID;
    enrollmentExternalContentType: ExternalContentTypeEnum;
    planPackageLicenses: PlanPackageLicenseParams[];
  }): boolean {
    const { userId, enrollmentExternalContentType, planPackageLicenses } = params;
    if (enrollmentExternalContentType !== ExternalContentTypeEnum.PLAN_PACKAGE) return false;

    const availableLicenses = getAvailablePeriodLicenseByUser(planPackageLicenses, userId);
    const isActivePlanPackageLicense = checkHavePlanPackageLicenseAvailableToday(availableLicenses);

    return isActivePlanPackageLicense;
  }

  private filterEnrollmentByEligibilityLearning(
    eligibleLearning: string,
    dataList: Record<string, any>[],
  ) {
    if (
      eligibleLearning === MyEnrollmentEligibleLearningFilterEnum.LEARNABLE ||
      eligibleLearning === MyEnrollmentEligibleLearningFilterEnum.EXPIRED
    ) {
      const isEligibleLearning = eligibleLearning === MyEnrollmentEligibleLearningFilterEnum.LEARNABLE;

      const newDataList = dataList.filter((val) => {
        const isPlanPackage = val.externalContentType === ExternalContentTypeEnum.PLAN_PACKAGE;
        if (!isPlanPackage) return isEligibleLearning;
        return val.isActivePlanPackageLicense === isEligibleLearning;
      });

      return newDataList;
    }

    return dataList;
  }
}
