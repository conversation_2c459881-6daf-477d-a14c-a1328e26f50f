export class InfrastructureServicesDIToken {
  static readonly MessageBrokerService: unique symbol = Symbol('MessageBrokerService');
  static readonly NotificationService: unique symbol = Symbol('NotificationService');
  static readonly ScheduleService: unique symbol = Symbol('ScheduleService');
  static readonly WebHookNotificationService: unique symbol = Symbol('WebHookNotificationService');
  static readonly LoggerJobMessageService: unique symbol = Symbol('LoggerJobMessageService');
  static readonly LoggerApplication: unique symbol = Symbol('LoggerApplication');
  static readonly HttpService: unique symbol = Symbol('HttpService');
  static readonly CertificateService: unique symbol = Symbol('CertificateService');
  static readonly OAuthService: unique symbol = Symbol('OAuthService');
  static readonly JwtService: unique symbol = Symbol('JwtService');
  static readonly RetailService: unique symbol = Symbol('RetailService');
}
