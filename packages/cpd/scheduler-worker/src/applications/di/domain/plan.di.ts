export class PlanDIToken {
  // Data Mapper
  static readonly PlanDataMapper: unique symbol = Symbol('PlanDataMapper');
  static readonly PackageDataMapper: unique symbol = Symbol('PackageDataMapper');
  static readonly PlanPackageDataMapper: unique symbol = Symbol('PlanPackageDataMapper');
  static readonly PlanPackageLicenseDataMapper: unique symbol = Symbol('PlanPackageLicenseDataMapper');
  static readonly PlanPackageLicenseHistoryDataMapper: unique symbol = Symbol('PlanPackageLicenseHistoryDataMapper');

  // Repositories
  static readonly PlanRepository: unique symbol = Symbol('PlanRepository');
  static readonly PackageRepository: unique symbol = Symbol('PackageRepository');
  static readonly PlanPackageRepository: unique symbol = Symbol('PlanPackageRepository');
  static readonly PlanPackageLicenseRepository: unique symbol = Symbol('PlanPackageLicenseRepository');
  static readonly PlanPackageLicenseHistoryRepository: unique symbol = Symbol('PlanPackageLicenseHistoryRepository');

  // Presenter
  static readonly PlanScheduler: unique symbol = Symbol('PlanScheduler');

  // Service
  static readonly PlanMessageBrokerService: unique symbol = Symbol('PlanMessageBrokerService');

  // Use Case
  static readonly PlanPackageLicenseExpiredUseCase: unique symbol = Symbol('PlanPackageLicenseExpiredUseCase');
}
