export class EnrollmentDIToken {
  // Infrastructure
  static readonly EnrollmentDataMapper: unique symbol = Symbol('EnrollmentDataMapper');
  static readonly EnrollmentRepository: unique symbol = Symbol('EnrollmentRepository');
  static readonly EnrollmentMessageBrokerService: unique symbol = Symbol('EnrollmentMessageBrokerService');

  static readonly EnrollmentCertificateRepository: unique symbol = Symbol('EnrollmentCertificateRepository');
  static readonly EnrollmentCertificateDataMapper: unique symbol = Symbol('EnrollmentCertificateDataMapper');

  static readonly CourseItemProgressHistoryDataMapper: unique symbol = Symbol('CourseItemProgressHistoryDataMapper');
  static readonly CourseItemProgressHistoryRepository: unique symbol = Symbol('CourseItemProgressHistoryRepository');

  static readonly SummaryTSIQuizScoreDataMapper: unique symbol = Symbol('SummaryTSIQuizScoreDataMapper');
  static readonly SummaryTSIQuizScoreRepository: unique symbol = Symbol('SummaryTSIQuizScoreRepository');

  static readonly EnrollmentPlanPackageLicenseRepository: unique symbol = Symbol(
    'EnrollmentPlanPackageLicenseRepository',
  );
  static readonly EnrollmentPlanPackageLicenseDataMapper: unique symbol = Symbol(
    'EnrollmentPlanPackageLicenseDataMapper',
  );

  // Presenter
  static readonly EnrollmentScheduler: unique symbol = Symbol('EnrollmentScheduler');

  // Use Case
  static readonly ApproveEnrollmentUseCase: unique symbol = Symbol('ApproveEnrollmentUseCase');
  static readonly RemoveCourseItemProgressHistoryUseCase: unique symbol = Symbol(
    'RemoveCourseItemProgressHistoryUseCase',
  );
  static readonly EnrollmentExpiredUseCase: unique symbol = Symbol('EnrollmentExpiredUseCase');
  static readonly CalculateCumulativeTSIQuizScoreUseCase: unique symbol = Symbol(
    'CalculateCumulativeTSIQuizScoreUseCase',
  );
}
