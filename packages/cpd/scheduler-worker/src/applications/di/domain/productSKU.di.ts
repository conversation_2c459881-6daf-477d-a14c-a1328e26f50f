export class ProductSKUDIToken {
  // Infrastructure
  static readonly ProductSKUDataMapper: unique symbol = Symbol('ProductSKUDataMapper');
  static readonly ProductSKUBundleDataMapper: unique symbol = Symbol('ProductSKUBundleDataMapper');
  static readonly ProductSKUCourseDataMapper: unique symbol = Symbol('ProductSKUCourseDataMapper');
  static readonly CourseMarketplaceDataMapper: unique symbol = Symbol('CourseMarketplaceDataMapper');

  static readonly ProductSKURepository: unique symbol = Symbol('ProductSKURepository');
  static readonly ProductSKUBundleRepository: unique symbol = Symbol('ProductSKUBundleRepository');
  static readonly ProductSKUCourseRepository: unique symbol = Symbol('ProductSKUCourseRepository');
  static readonly CourseMarketplaceRepository: unique symbol = Symbol('CourseMarketplaceRepository');

  // Presenters

  // UseCase
}
