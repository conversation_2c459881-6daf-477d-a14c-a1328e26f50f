import { Module, Provider } from '@nestjs/common';

import {
  AchievementRepository,
  BadgeRepository,
  ClassroomLocationEnrollmentRepository,
  ClassroomLocationRepository,
  ClassroomRoundRepository,
  CourseItemProgressHistoryRepository,
  CourseMarketplaceRepository,
  CourseRepository,
  CourseVersionRepository,
  CustomerPartnerRepository,
  CustomerRepository,
  EnrollmentCertificateRepository,
  EnrollmentPlanPackageLicenseRepository,
  EnrollmentRepository,
  EnrollmentAchievementRepository,
  EnrollmentBadgeRepository,
  JobRepository,
  JobTransactionRepository,
  KnowledgeContentDurationHistoryRepository,
  KnowledgeContentInteractionHistoryRepository,
  KnowledgeContentInteractionRepository,
  KnowledgeContentItemRepository,
  LearningPathEnrollmentRepository,
  LearningPathRepository,
  LearningPathVersionRepository,
  MaterialMediaRepository,
  OrganizationRepository,
  OrganizationSchedulerRepository,
  PackageRepository,
  PlanPackageLicenseHistoryRepository,
  PlanPackageLicenseRepository,
  PlanPackageRepository,
  PlanRepository,
  PreEnrollmentReservationRepository,
  PreEnrollmentTransactionRepository,
  ProductSKUBundleRepository,
  ProductSKUCourseRepository,
  ProductSKURepository,
  PromoteNotificationRepository,
  SummaryTSIQuizScoreRepository,
  UserGroupRepository,
  UserNotificationRepository,
  UserRepository,
} from '@infrastructures/persistence/repositories/database';

import {
  AchievementDIToken,
  BadgeDIToken,
  ClassroomDIToken,
  CourseDIToken,
  CreditDIToken,
  EnrollmentDIToken,
  JobDIToken,
  KnowledgeContentDIToken,
  LearningPathDIToken,
  LearningPathEnrollmentDIToken,
  MaterialMediaDIToken,
  NotificationDIToken,
  OrganizationDIToken,
  PlanDIToken,
  PreEnrollmentDIToken,
  ProductSKUDIToken,
  UserDIToken,
  UserGroupDIToken,
} from '@applications/di/domain';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { DbModule } from '@applications/module/infrastructure/configs/db.module';
import { DataMapperModule } from '@applications/module/infrastructure/dataMapper.module';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';

import {
  IAchievementDataMapper,
  IBadgeDataMapper,
  IClassroomLocationDataMapper,
  IClassroomLocationEnrollmentDataMapper,
  IClassroomRoundDataMapper,
  ICourseDataMapper,
  ICourseItemProgressHistoryDataMapper,
  ICourseMarketplaceDataMapper,
  ICourseVersionDataMapper,
  ICustomerDataMapper,
  ICustomerPartnerDataMapper,
  IEnrollmentCertificateDataMapper,
  IEnrollmentDataMapper,
  IEnrollmentPlanPackageLicenseDataMapper,
  IEnrollmentAchievementDataMapper,
  IEnrollmentBadgeDataMapper,
  IJobDataMapper,
  IJobTransactionDataMapper,
  IKnowledgeContentDurationHistoryDataMapper,
  IKnowledgeContentInteractionDataMapper,
  IKnowledgeContentInteractionHistoryDataMapper,
  IKnowledgeContentItemDataMapper,
  ILearningPathDataMapper,
  ILearningPathEnrollmentDataMapper,
  ILearningPathVersionDataMapper,
  IMaterialMediaDataMapper,
  IOrganizationDataMapper,
  IOrganizationSchedulerDataMapper,
  IPackageDataMapper,
  IPlanDataMapper,
  IPlanPackageDataMapper,
  IPlanPackageLicenseDataMapper,
  IPlanPackageLicenseHistoryDataMapper,
  IPreEnrollmentReservationDataMapper,
  IPreEnrollmentTransactionDataMapper,
  IProductSKUBundleDataMapper,
  IProductSKUCourseDataMapper,
  IProductSKUDataMapper,
  IPromoteNotificationDataMapper,
  ISummaryTSIQuizScoreDataMapper,
  IUserDataMapper,
  IUserGroupDataMapper,
  IUserNotificationDataMapper,
} from '@interfaces/dataMapper';

const databaseRepositoriesProvider: Provider[] = [
  {
    provide: AchievementDIToken.AchievementRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, AchievementDIToken.AchievementDataMapper],
    useFactory: (db: DbInstanceParams, achievementDataMapper: IAchievementDataMapper) =>
      new AchievementRepository(db, achievementDataMapper),
  },
  {
    provide: AchievementDIToken.EnrollmentAchievementRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, AchievementDIToken.EnrollmentAchievementDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentAchievementDataMapper: IEnrollmentAchievementDataMapper) =>
      new EnrollmentAchievementRepository(db, enrollmentAchievementDataMapper),
  },
  {
    provide: BadgeDIToken.BadgeRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, BadgeDIToken.BadgeDataMapper],
    useFactory: (db: DbInstanceParams, badgeDataMapper: IBadgeDataMapper) => new BadgeRepository(db, badgeDataMapper),
  },
  {
    provide: BadgeDIToken.EnrollmentBadgeRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, BadgeDIToken.EnrollmentBadgeDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentBadgeDataMapper: IEnrollmentBadgeDataMapper) =>
      new EnrollmentBadgeRepository(db, enrollmentBadgeDataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationDataMapper],
    useFactory: (db: DbInstanceParams, organizationDataMapper: IOrganizationDataMapper) =>
      new OrganizationRepository(db, organizationDataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationSchedulerRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationSchedulerDataMapper],
    useFactory: (db: DbInstanceParams, organizationSchedulerDataMapper: IOrganizationSchedulerDataMapper) =>
      new OrganizationSchedulerRepository(db, organizationSchedulerDataMapper),
  },
  {
    provide: CourseDIToken.CourseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.CourseDataMapper],
    useFactory: (db: DbInstanceParams, courseDataMapper: ICourseDataMapper) =>
      new CourseRepository(db, courseDataMapper),
  },
  {
    provide: CourseDIToken.CourseVersionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.CourseVersionDataMapper],
    useFactory: (db: DbInstanceParams, courseVersionDataMapper: ICourseVersionDataMapper) =>
      new CourseVersionRepository(db, courseVersionDataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentDataMapper: IEnrollmentDataMapper) =>
      new EnrollmentRepository(db, enrollmentDataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentCertificateRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentCertificateDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentCertificateDataMapper: IEnrollmentCertificateDataMapper) =>
      new EnrollmentCertificateRepository(db, enrollmentCertificateDataMapper),
  },
  {
    provide: EnrollmentDIToken.CourseItemProgressHistoryRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.CourseItemProgressHistoryDataMapper],
    useFactory: (db: DbInstanceParams, courseItemProgressHistoryDataMapper: ICourseItemProgressHistoryDataMapper) =>
      new CourseItemProgressHistoryRepository(db, courseItemProgressHistoryDataMapper),
  },
  {
    provide: EnrollmentDIToken.SummaryTSIQuizScoreRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.SummaryTSIQuizScoreDataMapper],
    useFactory: (db: DbInstanceParams, summaryTSIQuizScoreDataMapper: ISummaryTSIQuizScoreDataMapper) =>
      new SummaryTSIQuizScoreRepository(db, summaryTSIQuizScoreDataMapper),
  },
  {
    provide: LearningPathDIToken.LearningPathRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LearningPathDIToken.LearningPathDataMapper],
    useFactory: (db: DbInstanceParams, learningPathDataMapper: ILearningPathDataMapper) =>
      new LearningPathRepository(db, learningPathDataMapper),
  },
  {
    provide: LearningPathDIToken.LearningPathVersionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LearningPathDIToken.LearningPathVersionDataMapper],
    useFactory: (db: DbInstanceParams, learningPathVersionDataMapper: ILearningPathVersionDataMapper) =>
      new LearningPathVersionRepository(db, learningPathVersionDataMapper),
  },
  {
    provide: LearningPathEnrollmentDIToken.LearningPathEnrollmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LearningPathEnrollmentDIToken.LearningPathEnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, learningPathEnrollmentDataMapper: ILearningPathEnrollmentDataMapper) =>
      new LearningPathEnrollmentRepository(db, learningPathEnrollmentDataMapper),
  },
  {
    provide: KnowledgeContentDIToken.KnowledgeContentItemRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, KnowledgeContentDIToken.KnowledgeContentItemDataMapper],
    useFactory: (db: DbInstanceParams, knowledgeContentItemDataMapper: IKnowledgeContentItemDataMapper) =>
      new KnowledgeContentItemRepository(db, knowledgeContentItemDataMapper),
  },
  {
    provide: KnowledgeContentDIToken.KnowledgeContentInteractionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, KnowledgeContentDIToken.KnowledgeContentInteractionDataMapper],
    useFactory: (db: DbInstanceParams, knowledgeContentInteractionDataMapper: IKnowledgeContentInteractionDataMapper) =>
      new KnowledgeContentInteractionRepository(db, knowledgeContentInteractionDataMapper),
  },
  {
    provide: KnowledgeContentDIToken.KnowledgeContentDurationHistoryRepository,
    inject: [
      InfrastructuresConfigDIToken.DbInstance,
      KnowledgeContentDIToken.KnowledgeContentDurationHistoryDataMapper,
    ],
    useFactory: (
      db: DbInstanceParams,
      knowledgeContentDurationHistoryDataMapper: IKnowledgeContentDurationHistoryDataMapper,
    ) => new KnowledgeContentDurationHistoryRepository(db, knowledgeContentDurationHistoryDataMapper),
  },
  {
    provide: KnowledgeContentDIToken.KnowledgeContentInteractionHistoryRepository,
    inject: [
      InfrastructuresConfigDIToken.DbInstance,
      KnowledgeContentDIToken.KnowledgeContentInteractionHistoryDataMapper,
    ],
    useFactory: (
      db: DbInstanceParams,
      knowledgeContentInteractionHistoryDataMapper: IKnowledgeContentInteractionHistoryDataMapper,
    ) => new KnowledgeContentInteractionHistoryRepository(db, knowledgeContentInteractionHistoryDataMapper),
  },
  {
    provide: ClassroomDIToken.ClassroomRoundRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ClassroomDIToken.ClassroomRoundDataMapper],
    useFactory: (db: DbInstanceParams, classroomRoundDataMapper: IClassroomRoundDataMapper) =>
      new ClassroomRoundRepository(db, classroomRoundDataMapper),
  },
  {
    provide: ClassroomDIToken.ClassroomLocationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ClassroomDIToken.ClassroomLocationDataMapper],
    useFactory: (db: DbInstanceParams, classroomLocationDataMapper: IClassroomLocationDataMapper) =>
      new ClassroomLocationRepository(db, classroomLocationDataMapper),
  },
  {
    provide: ClassroomDIToken.ClassroomLocationEnrollmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ClassroomDIToken.ClassroomLocationEnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, classroomLocationEnrollmentDataMapper: IClassroomLocationEnrollmentDataMapper) =>
      new ClassroomLocationEnrollmentRepository(db, classroomLocationEnrollmentDataMapper),
  },
  {
    provide: MaterialMediaDIToken.MaterialMediaRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.MaterialMediaDataMapper],
    useFactory: (db: DbInstanceParams, materialMediaDataMapper: IMaterialMediaDataMapper) =>
      new MaterialMediaRepository(db, materialMediaDataMapper),
  },
  {
    provide: UserDIToken.UserRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserDIToken.UserDataMapper],
    useFactory: (db: DbInstanceParams, userDataMapper: IUserDataMapper) => new UserRepository(db, userDataMapper),
  },
  {
    provide: UserGroupDIToken.UserGroupRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserGroupDIToken.UserGroupDataMapper],
    useFactory: (db: DbInstanceParams, userGroupDataMapper: IUserGroupDataMapper) =>
      new UserGroupRepository(db, userGroupDataMapper),
  },
  {
    provide: NotificationDIToken.UserNotificationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, NotificationDIToken.UserNotificationDataMapper],
    useFactory: (db: DbInstanceParams, userNotificationDataMapper: IUserNotificationDataMapper) =>
      new UserNotificationRepository(db, userNotificationDataMapper),
  },
  {
    provide: NotificationDIToken.PromoteNotificationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, NotificationDIToken.PromoteNotificationDataMapper],
    useFactory: (db: DbInstanceParams, promoteNotificationDataMapper: IPromoteNotificationDataMapper) =>
      new PromoteNotificationRepository(db, promoteNotificationDataMapper),
  },
  {
    provide: PlanDIToken.PackageRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PackageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPackageDataMapper) => new PackageRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageLicenseHistoryRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanPackageLicenseHistoryDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageLicenseHistoryDataMapper) =>
      new PlanPackageLicenseHistoryRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageLicenseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanPackageLicenseDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageLicenseDataMapper) =>
      new PlanPackageLicenseRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanPackageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageDataMapper) => new PlanPackageRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanDataMapper) => new PlanRepository(db, dataMapper),
  },
  {
    provide: PreEnrollmentDIToken.PreEnrollmentReservationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PreEnrollmentDIToken.PreEnrollmentReservationDataMapper],
    useFactory: (db: DbInstanceParams, preEnrollmentReservationDataMapper: IPreEnrollmentReservationDataMapper) =>
      new PreEnrollmentReservationRepository(db, preEnrollmentReservationDataMapper),
  },
  {
    provide: PreEnrollmentDIToken.PreEnrollmentTransactionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PreEnrollmentDIToken.PreEnrollmentTransactionDataMapper],
    useFactory: (db: DbInstanceParams, preEnrollmentTransactionDataMapper: IPreEnrollmentTransactionDataMapper) =>
      new PreEnrollmentTransactionRepository(db, preEnrollmentTransactionDataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKURepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUDataMapper],
    useFactory: (db: DbInstanceParams, productSKUDataMapper: IProductSKUDataMapper) =>
      new ProductSKURepository(db, productSKUDataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKUBundleRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUBundleDataMapper],
    useFactory: (db: DbInstanceParams, productSKUBundleDataMapper: IProductSKUBundleDataMapper) =>
      new ProductSKUBundleRepository(db, productSKUBundleDataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKUCourseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUCourseDataMapper],
    useFactory: (db: DbInstanceParams, productSKUCourseDataMapper: IProductSKUCourseDataMapper) =>
      new ProductSKUCourseRepository(db, productSKUCourseDataMapper),
  },
  {
    provide: ProductSKUDIToken.CourseMarketplaceRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.CourseMarketplaceDataMapper],
    useFactory: (db: DbInstanceParams, courseMarketplaceDataMapper: ICourseMarketplaceDataMapper) =>
      new CourseMarketplaceRepository(db, courseMarketplaceDataMapper),
  },
  {
    provide: CreditDIToken.CustomerRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CreditDIToken.CustomerDataMapper],
    useFactory: (db: DbInstanceParams, customerDataMapper: ICustomerDataMapper) =>
      new CustomerRepository(db, customerDataMapper),
  },
  {
    provide: CreditDIToken.CustomerPartnerRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CreditDIToken.CustomerPartnerDataMapper],
    useFactory: (db: DbInstanceParams, customerPartnerDataMapper: ICustomerPartnerDataMapper) =>
      new CustomerPartnerRepository(db, customerPartnerDataMapper),
  },
  {
    provide: JobDIToken.JobRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, JobDIToken.JobDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IJobDataMapper) => new JobRepository(db, dataMapper),
  },
  {
    provide: JobDIToken.JobTransactionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, JobDIToken.JobTransactionDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IJobTransactionDataMapper) =>
      new JobTransactionRepository(db, dataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentPlanPackageLicenseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IEnrollmentPlanPackageLicenseDataMapper) =>
      new EnrollmentPlanPackageLicenseRepository(db, dataMapper),
  },
];
@Module({
  imports: [DbModule, DataMapperModule],
  providers: databaseRepositoriesProvider,
  exports: [
    AchievementDIToken.AchievementRepository,
    AchievementDIToken.EnrollmentAchievementRepository,
    BadgeDIToken.BadgeRepository,
    BadgeDIToken.EnrollmentBadgeRepository,
    ClassroomDIToken.ClassroomLocationEnrollmentRepository,
    ClassroomDIToken.ClassroomLocationRepository,
    ClassroomDIToken.ClassroomRoundRepository,
    CourseDIToken.CourseRepository,
    CourseDIToken.CourseVersionRepository,
    CreditDIToken.CustomerPartnerRepository,
    CreditDIToken.CustomerRepository,
    EnrollmentDIToken.CourseItemProgressHistoryRepository,
    EnrollmentDIToken.EnrollmentCertificateRepository,
    EnrollmentDIToken.EnrollmentRepository,
    EnrollmentDIToken.SummaryTSIQuizScoreRepository,
    EnrollmentDIToken.EnrollmentPlanPackageLicenseRepository,
    KnowledgeContentDIToken.KnowledgeContentDurationHistoryRepository,
    KnowledgeContentDIToken.KnowledgeContentInteractionHistoryRepository,
    KnowledgeContentDIToken.KnowledgeContentInteractionRepository,
    KnowledgeContentDIToken.KnowledgeContentItemRepository,
    LearningPathDIToken.LearningPathRepository,
    LearningPathDIToken.LearningPathVersionRepository,
    LearningPathEnrollmentDIToken.LearningPathEnrollmentRepository,
    MaterialMediaDIToken.MaterialMediaRepository,
    NotificationDIToken.PromoteNotificationRepository,
    NotificationDIToken.UserNotificationRepository,
    OrganizationDIToken.OrganizationRepository,
    OrganizationDIToken.OrganizationSchedulerRepository,
    PlanDIToken.PackageRepository,
    PlanDIToken.PlanPackageLicenseHistoryRepository,
    PlanDIToken.PlanPackageLicenseRepository,
    PlanDIToken.PlanPackageRepository,
    PlanDIToken.PlanRepository,
    PreEnrollmentDIToken.PreEnrollmentReservationRepository,
    PreEnrollmentDIToken.PreEnrollmentTransactionRepository,
    ProductSKUDIToken.CourseMarketplaceRepository,
    ProductSKUDIToken.ProductSKUBundleRepository,
    ProductSKUDIToken.ProductSKUCourseRepository,
    ProductSKUDIToken.ProductSKURepository,
    UserDIToken.UserRepository,
    UserGroupDIToken.UserGroupRepository,
    JobDIToken.JobRepository,
    JobDIToken.JobTransactionRepository,
  ],
})
export class DatabaseRepositoryModule {}
