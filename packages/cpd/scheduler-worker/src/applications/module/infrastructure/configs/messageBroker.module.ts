import { Module } from '@nestjs/common';

import { MessageBrokerEventEmitter } from '@infrastructures/configs/messageBroker/messageBroker.event';
import { MessageBrokerInstance } from '@infrastructures/configs/messageBroker/messageBroker.instance';
import { ILogger } from '@infrastructures/services/logger/interfaces';

import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { LoggerServiceModule } from '@applications/module/infrastructure/services/logger.service.module';

import {
  MessageBrokerEventEnum,
  MessageBrokerEventNotificationEnum,
} from '@constants/enums/infrastructures/messageBroker.enum';

import { IEnvironments } from '@interfaces/configs/environment.interface';
import { IMessageBrokerEventEmitter } from '@interfaces/configs/messageBroker.interface';
@Module({
  imports: [EnvironmentsModule, LoggerServiceModule],
  providers: [
    {
      provide: InfrastructuresConfigDIToken.MessageBrokerEventEmitter,
      useClass: MessageBrokerEventEmitter,
    },
    {
      provide: InfrastructuresConfigDIToken.MessageBrokerInstance,
      inject: [
        InfrastructuresConfigDIToken.Environment,
        InfrastructuresConfigDIToken.MessageBrokerEventEmitter,
        InfrastructureServicesDIToken.LoggerApplication,
      ],
      useFactory: (environment: IEnvironments, event: IMessageBrokerEventEmitter, logger: ILogger) =>
        new MessageBrokerInstance(environment.amqpURI, MessageBrokerEventEnum.Connected, event, logger),
    },
    {
      provide: InfrastructuresConfigDIToken.MessageBrokerNotificationInstance,
      inject: [
        InfrastructuresConfigDIToken.Environment,
        InfrastructuresConfigDIToken.MessageBrokerEventEmitter,
        InfrastructureServicesDIToken.LoggerApplication,
      ],
      useFactory: (environment: IEnvironments, event: IMessageBrokerEventEmitter, logger: ILogger) =>
        new MessageBrokerInstance(
          environment.amqpNotificationURI,
          MessageBrokerEventNotificationEnum.Connected,
          event,
          logger,
        ),
    },
  ],
  exports: [
    InfrastructuresConfigDIToken.MessageBrokerInstance,
    InfrastructuresConfigDIToken.MessageBrokerNotificationInstance,
  ],
})
export class MessageBrokerModule {}
