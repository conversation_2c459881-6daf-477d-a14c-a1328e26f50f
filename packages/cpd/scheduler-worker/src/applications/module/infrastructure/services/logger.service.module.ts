import { Module } from '@nestjs/common';

import Logger from '@infrastructures/services/logger/logger';
import { LoggerJobMessageService } from '@infrastructures/services/logger/loggerJobMessage.service';

import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';

@Module({
  providers: [
    {
      provide: InfrastructureServicesDIToken.LoggerJobMessageService,
      useClass: LoggerJobMessageService,
    },
    {
      provide: InfrastructureServicesDIToken.LoggerApplication,
      useClass: Logger,
    },
  ],
  exports: [InfrastructureServicesDIToken.LoggerJobMessageService, InfrastructureServicesDIToken.LoggerApplication],
})
export class LoggerServiceModule {}
