import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { ScheduleService } from '@infrastructures/services/scheduler/scheduler.service';

import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';
import { LoggerServiceModule } from '@applications/module/infrastructure/services/logger.service.module';

@Module({
  imports: [
    ScheduleModule.forRoot({
      cronJobs: true,
      intervals: false,
      timeouts: false,
    }),
    LoggerServiceModule,
  ],
  providers: [
    {
      provide: InfrastructureServicesDIToken.ScheduleService,
      useClass: ScheduleService,
    },
  ],
  exports: [InfrastructureServicesDIToken.ScheduleService],
})
export class SchedulerServiceModule {}
