import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { EnrollmentAchievementMessageBrokerService } from '@infrastructures/services/messageBroker/enrollmentAchievementMessageBroker.service';
import { EnrollmentMessageBrokerService } from '@infrastructures/services/messageBroker/enrollmentMessageBroker.service';
import { MessageBrokerService } from '@infrastructures/services/messageBroker/messageBroker.service';
import { NotificationMessageBrokerService } from '@infrastructures/services/messageBroker/notificationMessageBroker.service';
import { PlanMessageBrokerService } from '@infrastructures/services/messageBroker/planMessageBroker.service';
import { UserGroupMessageBrokerService } from '@infrastructures/services/messageBroker/userGroupMessageBroker.service';

import { AchievementDIToken, PlanDIToken } from '@applications/di/domain';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { UserGroupDIToken } from '@applications/di/domain/userGroup.di';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';
import { MessageBrokerModule } from '@applications/module/infrastructure/configs/messageBroker.module';

import { IMessageBrokerInstance } from '@interfaces/infrastructures/configs/messageBroker.interface';

@Module({
  imports: [MessageBrokerModule, EventEmitterModule.forRoot()],
  providers: [
    {
      provide: InfrastructureServicesDIToken.MessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) => new MessageBrokerService(brokerMessageInstance),
    },
    {
      provide: UserGroupDIToken.UserGroupMessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) =>
        new UserGroupMessageBrokerService(brokerMessageInstance),
    },
    {
      provide: EnrollmentDIToken.EnrollmentMessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) =>
        new EnrollmentMessageBrokerService(brokerMessageInstance),
    },
    {
      provide: AchievementDIToken.EnrollmentAchievementMessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) =>
        new EnrollmentAchievementMessageBrokerService(brokerMessageInstance),
    },
    {
      provide: NotificationDIToken.NotificationMessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) =>
        new NotificationMessageBrokerService(brokerMessageInstance),
    },
    {
      provide: PlanDIToken.PlanMessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) =>
        new PlanMessageBrokerService(brokerMessageInstance),
    },
  ],
  exports: [
    InfrastructureServicesDIToken.MessageBrokerService,
    UserGroupDIToken.UserGroupMessageBrokerService,
    EnrollmentDIToken.EnrollmentMessageBrokerService,
    AchievementDIToken.EnrollmentAchievementMessageBrokerService,
    NotificationDIToken.NotificationMessageBrokerService,
    PlanDIToken.PlanMessageBrokerService,
  ],
})
export class MessageBrokerAdaptorModule {}
