import { Module } from '@nestjs/common';

import {
  AchievementDataMapper,
  BadgeDataMapper,
  ClassroomLocationDataMapper,
  ClassroomLocationEnrollmentDataMapper,
  ClassroomRoundDataMapper,
  CourseDataMapper,
  CourseItemProgressHistoryDataMapper,
  CourseMarketplaceDataMapper,
  CourseVersionDataMapper,
  CustomerDataMapper,
  CustomerPartnerDataMapper,
  EnrollmentCertificateDataMapper,
  EnrollmentDataMapper,
  EnrollmentPlanPackageLicenseDataMapper,
  EnrollmentAchievementDataMapper,
  EnrollmentBadgeDataMapper,
  JobDataMapper,
  JobTransactionDataMapper,
  KnowledgeContentDurationHistoryDataMapper,
  KnowledgeContentInteractionDataMapper,
  KnowledgeContentInteractionHistoryDataMapper,
  KnowledgeContentItemDataMapper,
  LearningPathDataMapper,
  LearningPathEnrollmentDataMapper,
  LearningPathVersionDataMapper,
  MaterialMediaDataMapper,
  OrganizationDataMapper,
  OrganizationSchedulerDataMapper,
  PackageDataMapper,
  PlanDataMapper,
  PlanPackageDataMapper,
  PlanPackageLicenseDataMapper,
  PlanPackageLicenseHistoryDataMapper,
  PreEnrollmentReservationDataMapper,
  PreEnrollmentTransactionDataMapper,
  ProductSKUBundleDataMapper,
  ProductSKUCourseDataMapper,
  ProductSKUDataMapper,
  PromoteNotificationDataMapper,
  SummaryTSIQuizScoreDataMapper,
  TaskOperationDataMapper,
  UserDataMapper,
  UserGroupDataMapper,
  UserNotificationDataMapper,
} from '@infrastructures/dataMappers';

import {
  AchievementDIToken,
  BadgeDIToken,
  ClassroomDIToken,
  CourseDIToken,
  CreditDIToken,
  EnrollmentDIToken,
  JobDIToken,
  KnowledgeContentDIToken,
  LearningPathDIToken,
  LearningPathEnrollmentDIToken,
  MaterialMediaDIToken,
  NotificationDIToken,
  OrganizationDIToken,
  PlanDIToken,
  PreEnrollmentDIToken,
  ProductSKUDIToken,
  UserDIToken,
  UserGroupDIToken,
  TaskOperationDIToken,
} from '@applications/di/domain';

@Module({
  providers: [
    {
      provide: AchievementDIToken.AchievementDataMapper,
      useClass: AchievementDataMapper,
    },
    {
      provide: AchievementDIToken.EnrollmentAchievementDataMapper,
      useClass: EnrollmentAchievementDataMapper,
    },
    {
      provide: BadgeDIToken.BadgeDataMapper,
      useClass: BadgeDataMapper,
    },
    {
      provide: BadgeDIToken.EnrollmentBadgeDataMapper,
      useClass: EnrollmentBadgeDataMapper,
    },
    {
      provide: ClassroomDIToken.ClassroomLocationEnrollmentDataMapper,
      useClass: ClassroomLocationEnrollmentDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationDataMapper,
      useClass: OrganizationDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationSchedulerDataMapper,
      useClass: OrganizationSchedulerDataMapper,
    },
    {
      provide: UserDIToken.UserDataMapper,
      useClass: UserDataMapper,
    },
    {
      provide: UserGroupDIToken.UserGroupDataMapper,
      useClass: UserGroupDataMapper,
    },
    {
      provide: CourseDIToken.CourseDataMapper,
      useClass: CourseDataMapper,
    },
    {
      provide: CourseDIToken.CourseVersionDataMapper,
      useClass: CourseVersionDataMapper,
    },
    {
      provide: EnrollmentDIToken.EnrollmentDataMapper,
      useClass: EnrollmentDataMapper,
    },
    {
      provide: EnrollmentDIToken.EnrollmentCertificateDataMapper,
      useClass: EnrollmentCertificateDataMapper,
    },
    {
      provide: EnrollmentDIToken.CourseItemProgressHistoryDataMapper,
      useClass: CourseItemProgressHistoryDataMapper,
    },
    {
      provide: EnrollmentDIToken.SummaryTSIQuizScoreDataMapper,
      useClass: SummaryTSIQuizScoreDataMapper,
    },
    {
      provide: LearningPathDIToken.LearningPathDataMapper,
      useClass: LearningPathDataMapper,
    },
    {
      provide: LearningPathDIToken.LearningPathVersionDataMapper,
      useClass: LearningPathVersionDataMapper,
    },
    {
      provide: LearningPathEnrollmentDIToken.LearningPathEnrollmentDataMapper,
      useClass: LearningPathEnrollmentDataMapper,
    },
    {
      provide: KnowledgeContentDIToken.KnowledgeContentItemDataMapper,
      useClass: KnowledgeContentItemDataMapper,
    },
    {
      provide: KnowledgeContentDIToken.KnowledgeContentInteractionDataMapper,
      useClass: KnowledgeContentInteractionDataMapper,
    },
    {
      provide: KnowledgeContentDIToken.KnowledgeContentDurationHistoryDataMapper,
      useClass: KnowledgeContentDurationHistoryDataMapper,
    },
    {
      provide: KnowledgeContentDIToken.KnowledgeContentInteractionHistoryDataMapper,
      useClass: KnowledgeContentInteractionHistoryDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationDataMapper,
      useClass: OrganizationDataMapper,
    },
    {
      provide: ClassroomDIToken.ClassroomRoundDataMapper,
      useClass: ClassroomRoundDataMapper,
    },
    {
      provide: ClassroomDIToken.ClassroomLocationDataMapper,
      useClass: ClassroomLocationDataMapper,
    },
    {
      provide: ClassroomDIToken.ClassroomLocationEnrollmentDataMapper,
      useClass: ClassroomLocationEnrollmentDataMapper,
    },
    {
      provide: MaterialMediaDIToken.MaterialMediaDataMapper,
      useClass: MaterialMediaDataMapper,
    },
    {
      provide: NotificationDIToken.UserNotificationDataMapper,
      useClass: UserNotificationDataMapper,
    },
    {
      provide: NotificationDIToken.PromoteNotificationDataMapper,
      useClass: PromoteNotificationDataMapper,
    },
    {
      provide: PlanDIToken.PackageDataMapper,
      useClass: PackageDataMapper,
    },
    {
      provide: PlanDIToken.PlanDataMapper,
      useClass: PlanDataMapper,
    },
    {
      provide: PlanDIToken.PlanPackageDataMapper,
      useClass: PlanPackageDataMapper,
    },
    {
      provide: PlanDIToken.PlanPackageLicenseDataMapper,
      useClass: PlanPackageLicenseDataMapper,
    },
    {
      provide: PlanDIToken.PlanPackageLicenseHistoryDataMapper,
      useClass: PlanPackageLicenseHistoryDataMapper,
    },
    {
      provide: PreEnrollmentDIToken.PreEnrollmentReservationDataMapper,
      useClass: PreEnrollmentReservationDataMapper,
    },
    {
      provide: PreEnrollmentDIToken.PreEnrollmentTransactionDataMapper,
      useClass: PreEnrollmentTransactionDataMapper,
    },
    {
      provide: ProductSKUDIToken.ProductSKUDataMapper,
      useClass: ProductSKUDataMapper,
    },
    {
      provide: ProductSKUDIToken.ProductSKUBundleDataMapper,
      useClass: ProductSKUBundleDataMapper,
    },
    {
      provide: ProductSKUDIToken.ProductSKUCourseDataMapper,
      useClass: ProductSKUCourseDataMapper,
    },
    {
      provide: ProductSKUDIToken.CourseMarketplaceDataMapper,
      useClass: CourseMarketplaceDataMapper,
    },
    {
      provide: CreditDIToken.CustomerDataMapper,
      useClass: CustomerDataMapper,
    },
    {
      provide: CreditDIToken.CustomerPartnerDataMapper,
      useClass: CustomerPartnerDataMapper,
    },
    {
      provide: JobDIToken.JobDataMapper,
      useClass: JobDataMapper,
    },
    {
      provide: JobDIToken.JobTransactionDataMapper,
      useClass: JobTransactionDataMapper,
    },
    {
      provide: TaskOperationDIToken.TaskOperationDataMapper,
      useClass: TaskOperationDataMapper,
    },
    {
      provide: EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper,
      useClass: EnrollmentPlanPackageLicenseDataMapper,
    },
  ],
  exports: [
    AchievementDIToken.AchievementDataMapper,
    BadgeDIToken.BadgeDataMapper,
    ClassroomDIToken.ClassroomLocationDataMapper,
    ClassroomDIToken.ClassroomLocationEnrollmentDataMapper,
    ClassroomDIToken.ClassroomRoundDataMapper,
    CourseDIToken.CourseDataMapper,
    CourseDIToken.CourseVersionDataMapper,
    CreditDIToken.CustomerDataMapper,
    CreditDIToken.CustomerPartnerDataMapper,
    EnrollmentDIToken.CourseItemProgressHistoryDataMapper,
    EnrollmentDIToken.EnrollmentCertificateDataMapper,
    EnrollmentDIToken.EnrollmentDataMapper,
    EnrollmentDIToken.SummaryTSIQuizScoreDataMapper,
    EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper,
    AchievementDIToken.EnrollmentAchievementDataMapper,
    BadgeDIToken.EnrollmentBadgeDataMapper,
    JobDIToken.JobDataMapper,
    JobDIToken.JobTransactionDataMapper,
    KnowledgeContentDIToken.KnowledgeContentDurationHistoryDataMapper,
    KnowledgeContentDIToken.KnowledgeContentInteractionDataMapper,
    KnowledgeContentDIToken.KnowledgeContentInteractionHistoryDataMapper,
    KnowledgeContentDIToken.KnowledgeContentItemDataMapper,
    LearningPathDIToken.LearningPathDataMapper,
    LearningPathDIToken.LearningPathVersionDataMapper,
    LearningPathEnrollmentDIToken.LearningPathEnrollmentDataMapper,
    MaterialMediaDIToken.MaterialMediaDataMapper,
    NotificationDIToken.PromoteNotificationDataMapper,
    NotificationDIToken.UserNotificationDataMapper,
    OrganizationDIToken.OrganizationDataMapper,
    OrganizationDIToken.OrganizationSchedulerDataMapper,
    PlanDIToken.PackageDataMapper,
    PlanDIToken.PlanDataMapper,
    PlanDIToken.PlanPackageDataMapper,
    PlanDIToken.PlanPackageLicenseDataMapper,
    PlanDIToken.PlanPackageLicenseHistoryDataMapper,
    PreEnrollmentDIToken.PreEnrollmentReservationDataMapper,
    PreEnrollmentDIToken.PreEnrollmentTransactionDataMapper,
    ProductSKUDIToken.CourseMarketplaceDataMapper,
    ProductSKUDIToken.ProductSKUBundleDataMapper,
    ProductSKUDIToken.ProductSKUCourseDataMapper,
    ProductSKUDIToken.ProductSKUDataMapper,
    TaskOperationDIToken.TaskOperationDataMapper,
    UserDIToken.UserDataMapper,
    UserGroupDIToken.UserGroupDataMapper,
  ],
})
export class DataMapperModule {}
