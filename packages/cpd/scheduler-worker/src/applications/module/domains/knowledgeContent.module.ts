import { Module, Provider } from '@nestjs/common';
import { UpdateKnowledgeContentInteractionHistoryUsecase } from '@usecases/knowledgeContentInteraction/updateKnowledgeContentInteractionHistory.usecase';

import { KnowledgeContentDIToken } from '@applications/di/domain/knowledgeContent.di';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { MemoryCacheRepositoryModule } from '@applications/module/infrastructure/persistence/repository/memoryCache.repository.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

import { KnowledgeContentInteractionService } from '@domains/services/knowledgeContentInteraction.service';

import { KnowledgeContentScheduler } from '@presenters/schedulers/knowledgeContent.scheduler';

const presenterProviders: Provider[] = [
  {
    provide: KnowledgeContentDIToken.KnowledgeContentScheduler,
    useClass: KnowledgeContentScheduler,
  },
];

const serviceProviders: Provider[] = [
  {
    provide: KnowledgeContentDIToken.KnowledgeContentInteractionService,
    useClass: KnowledgeContentInteractionService,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: KnowledgeContentDIToken.UpdateKnowledgeContentInteractionHistoryUsecase,
    useClass: UpdateKnowledgeContentInteractionHistoryUsecase,
  },
];

@Module({
  imports: [EnvironmentsModule, SchedulerServiceModule, InfrastructureModule, MemoryCacheRepositoryModule],
  providers: [...usecaseProviders, ...presenterProviders, ...serviceProviders],
})
export class KnowledgeContentModule {}
