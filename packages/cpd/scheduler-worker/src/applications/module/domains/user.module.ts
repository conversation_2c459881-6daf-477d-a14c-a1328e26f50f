import { Module, Provider } from '@nestjs/common';

import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { MessageBrokerAdaptorModule } from '@applications/module/infrastructure/services/messageBrokerService.module';
import { NotificationServiceModule } from '@applications/module/infrastructure/services/notification.service.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

const presenterProviders: Provider[] = [];

const usecaseProviders: Provider[] = [];

@Module({
  imports: [
    EnvironmentsModule,
    SchedulerServiceModule,
    MessageBrokerAdaptorModule,
    InfrastructureModule,
    NotificationServiceModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders],
})
export class UserModule {}
