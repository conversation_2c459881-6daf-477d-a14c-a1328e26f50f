import { Module, Provider } from '@nestjs/common';
import { BulkUpdateRetailOrderUseCase } from '@usecases/preEnrollment/bulkUpdateRetailOrder.usecase';
import { PrepareAutoBulkUseCase } from '@usecases/preEnrollment/prepareAutoBulk.usecase';

import { PreEnrollmentDIToken } from '@applications/di/domain/preEnrollment.di';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { MessageBrokerAdaptorModule } from '@applications/module/infrastructure/services/messageBrokerService.module';
import { NotificationServiceModule } from '@applications/module/infrastructure/services/notification.service.module';
import { RetailServiceModule } from '@applications/module/infrastructure/services/retail.service.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

import { PrepareDataAutoBulkService } from '@domains/services/prepareDataAutoBulk.service';

import { PreEnrollmentScheduler } from '@presenters/schedulers/preEnrollment.scheduler';

const presenterProviders: Provider[] = [
  {
    provide: PreEnrollmentDIToken.PreEnrollmentScheduler,
    useClass: PreEnrollmentScheduler,
  },
];

const serviceProviders: Provider[] = [
  {
    provide: PreEnrollmentDIToken.PrepareDataAutoBulkService,
    useClass: PrepareDataAutoBulkService,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: PreEnrollmentDIToken.BulkUpdateRetailOrderUseCase,
    useClass: BulkUpdateRetailOrderUseCase,
  },
  {
    provide: PreEnrollmentDIToken.PrepareAutoBulkUseCase,
    useClass: PrepareAutoBulkUseCase,
  },
];

@Module({
  imports: [
    EnvironmentsModule,
    SchedulerServiceModule,
    MessageBrokerAdaptorModule,
    InfrastructureModule,
    NotificationServiceModule,
    RetailServiceModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders, ...serviceProviders],
})
export class PreEnrollmentModule {}
