import { Module, Provider } from '@nestjs/common';
import { LearningPathEnrollmentExpireUseCase } from '@usecases/learningPathEnrollment/learningPathEnrollmentExpire.usecase';

import { LearningPathEnrollmentDIToken } from '@applications/di/domain/learningPathEnrollment.di';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { MessageBrokerAdaptorModule } from '@applications/module/infrastructure/services/messageBrokerService.module';
import { NotificationServiceModule } from '@applications/module/infrastructure/services/notification.service.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

import { LearningPathEnrollmentScheduler } from '@presenters/schedulers/learningPathEnrollment.scheduler';

const presenterProviders: Provider[] = [
  {
    provide: LearningPathEnrollmentDIToken.LearningPathEnrollmentScheduler,
    useClass: LearningPathEnrollmentScheduler,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: LearningPathEnrollmentDIToken.LearningPathEnrollmentExpireUseCase,
    useClass: LearningPathEnrollmentExpireUseCase,
  },
];

@Module({
  imports: [
    EnvironmentsModule,
    SchedulerServiceModule,
    MessageBrokerAdaptorModule,
    InfrastructureModule,
    NotificationServiceModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders],
})
export class LearningPathEnrollmentModule {}
