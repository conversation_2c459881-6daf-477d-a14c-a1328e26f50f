import { Module, Provider } from '@nestjs/common';
import { ApproveEnrollmentUseCase } from '@usecases/enrollment/approveEnrollment.usecase';
import { EnrollmentExpiredUseCase } from '@usecases/enrollment/enrollmentExpired.usecase';
import { RemoveCourseItemProgressUseCase } from '@usecases/enrollment/removeCourseItenProgressHsitory.usecase';
import { CalculateCumulativeTSIQuizScoreUseCase } from '@usecases/summaryTSIQuizScore/summaryTSIQuizScore.usecase';

import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { CertificateServiceModule } from '@applications/module/infrastructure/services/certificate.service.module';
import { MessageBrokerAdaptorModule } from '@applications/module/infrastructure/services/messageBrokerService.module';
import { NotificationServiceModule } from '@applications/module/infrastructure/services/notification.service.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

import { EnrollmentScheduler } from '@presenters/schedulers/enrollment.scheduler';

const presenterProviders: Provider[] = [
  {
    provide: EnrollmentDIToken.EnrollmentScheduler,
    useClass: EnrollmentScheduler,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: EnrollmentDIToken.ApproveEnrollmentUseCase,
    useClass: ApproveEnrollmentUseCase,
  },
  {
    provide: EnrollmentDIToken.RemoveCourseItemProgressHistoryUseCase,
    useClass: RemoveCourseItemProgressUseCase,
  },
  {
    provide: EnrollmentDIToken.EnrollmentExpiredUseCase,
    useClass: EnrollmentExpiredUseCase,
  },
  {
    provide: EnrollmentDIToken.CalculateCumulativeTSIQuizScoreUseCase,
    useClass: CalculateCumulativeTSIQuizScoreUseCase,
  },
];

@Module({
  imports: [
    EnvironmentsModule,
    SchedulerServiceModule,
    MessageBrokerAdaptorModule,
    InfrastructureModule,
    NotificationServiceModule,
    CertificateServiceModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders],
})
export class EnrollmentModule {}
