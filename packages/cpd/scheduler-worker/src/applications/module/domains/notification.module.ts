import { Module, Provider } from '@nestjs/common';
import { PublishPromoteNotificationUsecase } from '@usecases/notification/publishPromoteNotification.usecase';

import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { MessageBrokerAdaptorModule } from '@applications/module/infrastructure/services/messageBrokerService.module';
import { NotificationServiceModule } from '@applications/module/infrastructure/services/notification.service.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

import { NotificationScheduler } from '@presenters/schedulers/notification.scheduler';

const presenterProviders: Provider[] = [
  {
    provide: NotificationDIToken.NotificationScheduler,
    useClass: NotificationScheduler,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: NotificationDIToken.PublishPromoteNotificationUsecase,
    useClass: PublishPromoteNotificationUsecase,
  },
];

@Module({
  imports: [
    EnvironmentsModule,
    SchedulerServiceModule,
    MessageBrokerAdaptorModule,
    InfrastructureModule,
    NotificationServiceModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders],
})
export class NotificationModule {}
