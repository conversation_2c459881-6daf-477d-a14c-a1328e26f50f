import { Module, Provider } from '@nestjs/common';
import { UpdateUsersInUserGroupByConditionUseCase } from '@usecases/userGroup/updateUsersInUseGroupCondition.usecase';

import { UserGroupDIToken } from '@applications/di/domain/userGroup.di';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { MessageBrokerAdaptorModule } from '@applications/module/infrastructure/services/messageBrokerService.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

import { UserGroupScheduler } from '@presenters/schedulers/userGroup.scheduler';

const adaptersProviders: Provider[] = [];

const presenterProviders: Provider[] = [
  {
    provide: UserGroupDIToken.UserGroupScheduler,
    useClass: UserGroupScheduler,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: UserGroupDIToken.UpdateUsersInUserGroupByConditionUseCase,
    useClass: UpdateUsersInUserGroupByConditionUseCase,
  },
];

@Module({
  imports: [EnvironmentsModule, SchedulerServiceModule, MessageBrokerAdaptorModule, InfrastructureModule],
  providers: [...adaptersProviders, ...usecaseProviders, ...presenterProviders],
})
export class UserGroupModule {}
