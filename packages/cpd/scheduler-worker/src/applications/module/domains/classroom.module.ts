import { Module, Provider } from '@nestjs/common';
import { CancelEmptyClassroomRoundUseCase } from '@usecases/classroom/cancelEmptyClassroomRound.usecase';
import { CancelWaitingClassroomUseCase } from '@usecases/classroom/cancelWaitingClassroom.usecase';
import { PrepareRemindClassroomStartUseCase } from '@usecases/classroom/prepareRemindClassroomStart.usecase';
import { StartClassroomLocationEnrollmentUseCase } from '@usecases/classroom/startClassroomLocationEnrollment.usecase';
import { StartClassroomRoundUseCase } from '@usecases/classroom/startClassroomRound.usecase';

import { ClassroomDIToken } from '@applications/di/domain/classroom.di';
import { EnvironmentsModule } from '@applications/module/infrastructure/configs/environments.module';
import { MessageBrokerAdaptorModule } from '@applications/module/infrastructure/services/messageBrokerService.module';
import { NotificationServiceModule } from '@applications/module/infrastructure/services/notification.service.module';
import { SchedulerServiceModule } from '@applications/module/infrastructure/services/scheduler.service.module';
import { InfrastructureModule } from '@applications/module/infrastructure.module';

import { ClassroomScheduler } from '@presenters/schedulers/classroom.scheduler';

const presenterProviders: Provider[] = [
  {
    provide: ClassroomDIToken.ClassroomScheduler,
    useClass: ClassroomScheduler,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: ClassroomDIToken.StartClassroomRoundUseCase,
    useClass: StartClassroomRoundUseCase,
  },
  {
    provide: ClassroomDIToken.CancelEmptyClassroomRoundUseCase,
    useClass: CancelEmptyClassroomRoundUseCase,
  },
  {
    provide: ClassroomDIToken.StartClassroomLocationEnrollmentUseCase,
    useClass: StartClassroomLocationEnrollmentUseCase,
  },
  {
    provide: ClassroomDIToken.CancelWaitingClassroomUseCase,
    useClass: CancelWaitingClassroomUseCase,
  },
  {
    provide: ClassroomDIToken.PrepareRemindClassroomStartUseCase,
    useClass: PrepareRemindClassroomStartUseCase,
  },
];

@Module({
  imports: [
    EnvironmentsModule,
    SchedulerServiceModule,
    MessageBrokerAdaptorModule,
    InfrastructureModule,
    NotificationServiceModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders],
})
export class ClassroomModule {}
