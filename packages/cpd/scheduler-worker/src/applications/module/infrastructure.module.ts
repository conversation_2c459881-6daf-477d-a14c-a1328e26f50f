import { Module } from '@nestjs/common';

import { ConfigsModule } from '@applications/module/infrastructure/configs.module';
import { DataMapperModule } from '@applications/module/infrastructure/dataMapper.module';
import { PersistenceModule } from '@applications/module/infrastructure/persistence.module';
import { InfrastructureServiceModule } from '@applications/module/infrastructure/service.module';

@Module({
  imports: [ConfigsModule, InfrastructureServiceModule, PersistenceModule, DataMapperModule],
  exports: [ConfigsModule, InfrastructureServiceModule, PersistenceModule, DataMapperModule],
})
export class InfrastructureModule {}
