import { Module } from '@nestjs/common';

import { AchievementModule } from '@applications/module/domains/achievement.module';
import { ClassroomModule } from '@applications/module/domains/classroom.module';
import { CourseModule } from '@applications/module/domains/course.module';
import { EnrollmentModule } from '@applications/module/domains/enrollment.module';
import { HealthModule } from '@applications/module/domains/health.module';
import { KnowledgeContentModule } from '@applications/module/domains/knowledgeContent.module';
import { LearningPathEnrollmentModule } from '@applications/module/domains/learningPathEnrollment.module';
import { NotificationModule } from '@applications/module/domains/notification.module';
import { PlanModule } from '@applications/module/domains/plan.module';
import { PreEnrollmentModule } from '@applications/module/domains/preEnrollment.module';
import { TaskOperationModule } from '@applications/module/domains/taskOperation.module';
import { UserModule } from '@applications/module/domains/user.module';
import { UserGroupModule } from '@applications/module/domains/userGroup.module';

@Module({
  imports: [
    ClassroomModule,
    CourseModule,
    EnrollmentModule,
    AchievementModule,
    HealthModule,
    KnowledgeContentModule,
    LearningPathEnrollmentModule,
    NotificationModule,
    UserGroupModule,
    UserModule,
    PreEnrollmentModule,
    PlanModule,
    TaskOperationModule,
  ],
})
export class DomainModule {}
