import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { getPublishDateTimeOfNotification } from '@iso/helpers/dateUtils';
import {
  buildCertificateNotificationMessage,
  buildEnrollmentRejectOrExpireNotificationMessage,
  buildRemindEnrollmentExpiredNotificationMessage,
} from '@iso/helpers/userNotification';
import { ClassroomLocationEnrollmentStatusEnum } from '@iso/lms/enums/classroomLocationEnrollment.enum';
import { CourseItemStatusCodeEnum } from '@iso/lms/enums/courseItemProgress.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { MaterialMediaTypeEnum } from '@iso/lms/enums/materialMedia.enum';
import { OrganizationSchedulerTypeEnum } from '@iso/lms/enums/organizationScheduler.enum';
import { Enrollment } from '@iso/lms/models/enrollment.model';
import { UserNotification } from '@iso/lms/models/userNotification.model';
import { Inject, Injectable } from '@nestjs/common';
import { keyBy, partition } from 'lodash';

import { ClassroomDIToken } from '@applications/di/domain/classroom.di';
import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { UserDIToken } from '@applications/di/domain/user.di';
import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';

import {
  EnrollmentExpiredStatusApprovedParams,
  EnrollmentExpiredStatusInProgressParams,
  EnrollmentRemindExpiredParams,
} from '@constants/types/enrollment.type';

import { IClassroomLocationEnrollmentRepository } from '@interfaces/repositories/classroomLocationEnrollment.repository.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IEnrollmentCertificateRepository } from '@interfaces/repositories/enrollmentCertificate.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IOrganizationSchedulerRepository } from '@interfaces/repositories/organizationScheduler.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { IUserNotificationRepository } from '@interfaces/repositories/userNotification.repository.interface';
import { ICertificateService } from '@interfaces/services/certificate.service.interface';
import { ILoggerJobMessageService } from '@interfaces/services/logger.service.interface';
import { INotificationService, IWebHookNotificationService } from '@interfaces/services/notification.service.interface';
import { IEnrollmentExpiredUseCase } from '@interfaces/usecases/enrollment.interface';

import { date, formatDateInThaiLocale } from '@domains/utils/date.util';

const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

@Injectable()
export class EnrollmentExpiredUseCase implements IEnrollmentExpiredUseCase {
  private readonly numberOfDaysToRemindExpire = [1, 3, 5, 8, 15, 22];

  constructor(
    @Inject(InfrastructureServicesDIToken.LoggerJobMessageService)
    private readonly loggerJobMessage: ILoggerJobMessageService,
    @Inject(InfrastructureServicesDIToken.NotificationService)
    private readonly notificationService: INotificationService,
    @Inject(InfrastructureServicesDIToken.CertificateService)
    private readonly certificateService: ICertificateService,
    @Inject(NotificationDIToken.UserNotificationRepository)
    private readonly userNotificationRepository: IUserNotificationRepository,
    @Inject(InfrastructureServicesDIToken.WebHookNotificationService)
    private readonly webHookNotificationService: IWebHookNotificationService,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationSchedulerRepository)
    private readonly organizationSchedulerRepository: IOrganizationSchedulerRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository) private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(EnrollmentDIToken.EnrollmentCertificateRepository)
    private readonly enrollmentCertificateRepository: IEnrollmentCertificateRepository,
    @Inject(CourseDIToken.CourseRepository) private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(ClassroomDIToken.ClassroomLocationEnrollmentRepository)
    private readonly classroomLocationEnrollmentRepository: IClassroomLocationEnrollmentRepository,
  ) {
    this.loggerJobMessage.setLogContext(EnrollmentExpiredUseCase.name);
  }

  async execute(): Promise<void> {
    const currentDate = date().toDate();
    const yesterday = date(currentDate).subtract(1, 'day').toDate();

    this.loggerJobMessage.info(`${currentDate}`);

    const courseRegularIds = await this.courseRepository.findRegularIds();

    const daysToRemindExpireAggregate = this.numberOfDaysToRemindExpire.map((val) => {
      return {
        $and: [
          {
            courseId: { $nin: courseRegularIds },
          },
          { expiredAt: { $gte: date(currentDate).add(val, 'day').startOf('day').toDate() } },
          { expiredAt: { $lte: date(currentDate).add(val, 'day').endOf('day').toDate() } },
        ],
      };
    });

    const enrollmentAggregateParams = [
      {
        $match: {
          $or: [
            {
              $and: [
                { status: EnrollmentStatusEnum.IN_PROGRESS },
                {
                  $or: [
                    {
                      expiredAt: {
                        $lt: currentDate,
                      },
                    },
                    ...daysToRemindExpireAggregate,
                  ],
                },
              ],
            },
            {
              $and: [
                {
                  status: EnrollmentStatusEnum.PASSED,
                  courseId: { $in: courseRegularIds },
                },
                {
                  expiredAt: {
                    $lt: currentDate,
                  },
                },
              ],
            },
            {
              $and: [
                {
                  status: EnrollmentStatusEnum.APPROVED,
                },
                {
                  expiredAt: {
                    $gte: yesterday,
                  },
                },
                {
                  expiredAt: {
                    $lt: currentDate,
                  },
                },
              ],
            },
          ],
        },
      },
    ];

    const enrollments = await this.enrollmentRepository.aggregate<Enrollment>(enrollmentAggregateParams);

    this.loggerJobMessage.info(`Found total enrollment, total ${enrollments.length} rows`);

    const organizationIds = enrollments.map((val) => val.organizationId);

    const enrollmentInProgressList = enrollments.filter((val) => val.status === EnrollmentStatusEnum.IN_PROGRESS);
    const enrollmentPassedList = enrollments.filter((val) => val.status === EnrollmentStatusEnum.PASSED);
    const enrollmentApprovedList = enrollments.filter((val) => val.status === EnrollmentStatusEnum.APPROVED);

    if (enrollments.length > 0) {
      const userIds = enrollments.map((val) => val.userId);
      const courseIds = enrollments.map((val) => val.courseId);
      const courseVersionIds = enrollments.map((val) => val.courseVersionId);

      const users = await this.userRepository.find({
        guid: {
          $in: userIds,
        },
      });

      const courses = await this.courseRepository.find({
        id: {
          $in: courseIds,
        },
      });

      const courseVersions = await this.courseVersionRepository.find({
        id: {
          $in: courseVersionIds,
        },
      });

      const orgIds = Array.from(new Set(users.map((user) => user.organizationId)));
      const organizations = await this.organizationRepository.find({ id: { $in: orgIds } });

      if (enrollmentInProgressList.length) {
        await this.enrollmentStatusInprogress({
          enrollments: enrollmentInProgressList,
          courseVersions,
          users,
          courses,
          organizationIds,
          currentDate,
          numberOfDaysToRemindExpire: this.numberOfDaysToRemindExpire,
          organizations,
        });
      }

      if (enrollmentPassedList.length) {
        await this.enrollmentStatusPassed(enrollmentPassedList);
      }

      let sentCertificateEmailSuccessList = [];
      let sentCertificateEmailFailedList = [];

      if (enrollmentApprovedList.length) {
        const resultSentCertificateEmail = await this.enrollmentStatusApproved({
          enrollments: enrollmentApprovedList,
          users,
          organizations,
          courses,
          courseVersions,
        });

        sentCertificateEmailSuccessList = resultSentCertificateEmail.successList;
        sentCertificateEmailFailedList = resultSentCertificateEmail.failedList;
      }

      const enrollmentCertificateList = await this.enrollmentCertificateRepository.find({
        enrollmentId: {
          $in: sentCertificateEmailSuccessList,
        },
      });

      for (const enrollmentCertificate of enrollmentCertificateList) {
        enrollmentCertificate.isSentEmail = true;
      }

      const totalSentEmail = enrollmentCertificateList.length + sentCertificateEmailFailedList.length;
      const totalSentEmailSuccess = enrollmentCertificateList.length;

      if (totalSentEmailSuccess > 0) {
        await this.enrollmentCertificateRepository.saveMany(enrollmentCertificateList);
      }

      this.webHookNotificationService.sendCertificateEmail(totalSentEmail, totalSentEmailSuccess);
    } else {
      this.webHookNotificationService.sendNotChangeEnrollmentExpired();
      this.loggerJobMessage.info('Nothing change.');
    }
  }

  async enrollmentStatusInprogress({
    enrollments,
    courseVersions,
    users,
    courses,
    organizationIds,
    currentDate,
    numberOfDaysToRemindExpire,
    organizations,
  }: EnrollmentExpiredStatusInProgressParams): Promise<void> {
    const enrollmentExpired = enrollments.filter((val) => date(val.expiredAt).isBefore(currentDate));
    const enrollmentRemindExpired = enrollments.filter(
      (val) =>
        !date(val.expiredAt).isBefore(currentDate) &&
        numberOfDaysToRemindExpire.includes(date(val.expiredAt).diff(currentDate, 'day')),
    );

    if (enrollmentExpired.length) {
      this.loggerJobMessage.info(`Found enrollment inprogress is expired, total ${enrollmentExpired.length} rows.`);

      const enrollmentClassroomList = enrollmentExpired.filter((val) =>
        val.learningProgress.some(
          (item) =>
            item.type === MaterialMediaTypeEnum.CLASSROOM && item.statusCode === CourseItemStatusCodeEnum.IN_PROGRESS,
        ),
      );

      const learningProgressList = enrollmentClassroomList.flatMap((val) => val.learningProgress);
      const classroomLocationEnrollmentIds = learningProgressList
        .filter(
          (val) =>
            val.type === MaterialMediaTypeEnum.CLASSROOM && val.statusCode === CourseItemStatusCodeEnum.IN_PROGRESS,
        )
        .map((val) => val.classroomLocationEnrollmentId);

      const classroomLocationEnrollmentInProgressList = await this.classroomLocationEnrollmentRepository.aggregate([
        {
          $match: {
            id: { $in: classroomLocationEnrollmentIds },
            status: ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS,
          },
        },
      ]);

      const [enrollmentPendingResultList, enrollmentExpiredList] = partition(enrollmentExpired, (item) => {
        const courseVersion = courseVersions.find((val) => val.id === item.courseVersionId);
        if (!courseVersion) return false;

        const totalCourseItemNoClassroom = courseVersion.totalCourseItems - courseVersion.totalClassrooms;
        const learningProgressCompleteNoClassrooms = item.learningProgress.filter(
          (val) => val.type !== MaterialMediaTypeEnum.CLASSROOM && val.statusCode === CourseItemStatusCodeEnum.COMPLETE,
        );

        const isCompleteNoClassroom = learningProgressCompleteNoClassrooms.length === totalCourseItemNoClassroom;
        if (!isCompleteNoClassroom) return false;

        const learningProgressClassrooms = item.learningProgress.filter(
          (_learningProgress) => _learningProgress.type === MaterialMediaTypeEnum.CLASSROOM,
        );

        const isEqualLearningProgressClassroom = learningProgressClassrooms.length === courseVersion.totalClassrooms;
        if (!isEqualLearningProgressClassroom) return false;

        const learningProgressInProgressClassrooms = item.learningProgress.filter(
          (val) =>
            val.type === MaterialMediaTypeEnum.CLASSROOM && val.statusCode === CourseItemStatusCodeEnum.IN_PROGRESS,
        );

        if (!learningProgressInProgressClassrooms.length) return false;

        let isPendingMarkResult = false;
        for (const learningProgressClassroom of learningProgressInProgressClassrooms) {
          const classroomLocationEnrollment = classroomLocationEnrollmentInProgressList.find(
            (val) => val.id === learningProgressClassroom.classroomLocationEnrollmentId,
          );

          if (classroomLocationEnrollment) {
            isPendingMarkResult = true;
            break;
          }
        }

        return isPendingMarkResult;
      });

      const updateEnrollmentPendingResultList = enrollmentPendingResultList.map((item) => {
        item.status = EnrollmentStatusEnum.PENDING_RESULT;
        return item;
      });

      const updateEnrollmentExpiredList = enrollmentExpiredList.map((item) => {
        item.status = EnrollmentStatusEnum.EXPIRED;
        return item;
      });

      await this.enrollmentRepository.saveMany([...updateEnrollmentPendingResultList, ...updateEnrollmentExpiredList]);

      const modifiedCount = enrollmentExpiredList.length;

      const userNotificationExpiredList = [];

      for (const enrollment of enrollmentExpiredList) {
        const { userId, courseId, courseVersionId } = enrollment;
        const user = users.find((val) => val.guid === userId);

        const {
          email,
          profile: { firstname, lastname },
          organizationId,
        } = user;

        const fullName = `${firstname} ${lastname}`;
        const organization = organizations.find((val) => val.id === organizationId);

        const course = courses.find((val) => val.id === courseId);
        const courseVersion = courseVersions.find((val) => val.id === courseVersionId);
        const userNotification = await UserNotification.new({
          userId,
          organizationId,
          type: UserNotificationTypeEnum.ENROLLMENT_EXPIRED,
          payload: {
            mediaId: course.thumbnailMediaId,
            thumbnailUrl: course.thumbnailUrl,
            message: buildEnrollmentRejectOrExpireNotificationMessage(courseVersion.name),
            url: {
              code: course.url,
              enrollmentId: enrollment.id,
            },
          },
        });
        userNotificationExpiredList.push(userNotification);

        await this.notificationService.notifyEnrollmentReject(
          email,
          { fullName, courseName: courseVersion.name },
          organization,
        );
        await delay(100);
      }

      if (userNotificationExpiredList.length) {
        await this.userNotificationRepository.saveMany(userNotificationExpiredList);

        for (const userNotifyData of userNotificationExpiredList) {
          this.notificationService.sendInApplicationNotification(
            userNotifyData.payload.message,
            userNotifyData.organizationId,
          );
          await delay(100);
        }
      }

      this.webHookNotificationService.sendEnrollmentExpired(modifiedCount);
      this.loggerJobMessage.info(`Enrollment changed status to expired, total ${modifiedCount} rows.`);
    }

    if (enrollmentRemindExpired.length) {
      await this.enrollmentRemindExpired({
        enrollments: enrollmentRemindExpired,
        courseVersions,
        users,
        courses,
        organizationIds,
        currentDate,
        organizations,
      });
    }
  }

  async enrollmentRemindExpired({
    enrollments,
    courseVersions,
    users,
    courses,
    organizationIds,
    currentDate,
    organizations,
  }: EnrollmentRemindExpiredParams): Promise<void> {
    this.loggerJobMessage.info(`Found enrollment nearly expired, total ${enrollments.length} rows.`);

    const organizationSchedulers = await this.organizationSchedulerRepository.find({
      type: OrganizationSchedulerTypeEnum.SEND_NOTIFICATION_REMIND_EXPIRED_ENROLLMENT,
      organizationId: { $in: organizationIds },
    });
    const organizationSchedulerKeyByOrganizationId = keyBy(organizationSchedulers, 'organizationId');

    const userNotificationRemindExpiredList = [];

    for (const enrollment of enrollments) {
      const { userId, courseId, courseVersionId } = enrollment;

      const dayUntilExpired = date(enrollment.expiredAt).diff(currentDate, 'day');

      const course = courses.find((val) => val.id === courseId);
      const courseVersion = courseVersions.find((val) => val.id === courseVersionId);
      const user = users.find((val) => val.guid === userId);

      const {
        email,
        profile: { firstname, lastname },
        organizationId,
      } = user;

      const fullName = `${firstname} ${lastname}`;
      const organization = organizations.find((val) => val.id === organizationId);
      const organizationScheduler = organizationSchedulerKeyByOrganizationId[organizationId];
      const sendTimeRemindEnrollmentExpired = getPublishDateTimeOfNotification(organizationScheduler?.cron);

      const userNotification = await UserNotification.new({
        userId,
        organizationId,
        type: UserNotificationTypeEnum.REMIND_ENROLLMENT_EXPIRED,
        payload: {
          mediaId: course.thumbnailMediaId,
          thumbnailUrl: course.thumbnailUrl,
          message: buildRemindEnrollmentExpiredNotificationMessage({
            courseName: courseVersion.name,
            remindDay: dayUntilExpired,
          }),
          url: {
            code: course.url,
            enrollmentId: enrollment.id,
          },
        },
        publishedAt: sendTimeRemindEnrollmentExpired,
      });

      userNotificationRemindExpiredList.push(userNotification);

      const emailData = {
        fullName,
        contentName: courseVersion.name,
        expiredAt: enrollment.expiredAt,
        thumbnailUrl: course.thumbnailUrl,
        url: course.url,
      };

      await this.notificationService.notifyRemindEnrollmentExpired(
        email,
        emailData,
        organization,
        sendTimeRemindEnrollmentExpired,
      );
      await delay(100);
    }

    if (userNotificationRemindExpiredList.length) {
      await this.userNotificationRepository.saveMany(userNotificationRemindExpiredList);

      for (const userNotifyData of userNotificationRemindExpiredList) {
        this.notificationService.sendInApplicationNotification(
          userNotifyData.payload.message,
          userNotifyData.organizationId,
          userNotifyData.publishedAt,
        );
        await delay(100);
      }
    }
  }

  async enrollmentStatusPassed(enrollments: Enrollment[]): Promise<void> {
    this.loggerJobMessage.info(`Found enrollment passed is expired, Total ${enrollments.length} rows.`);

    const updateEnrollmentPassedToCompleted = enrollments.map((val) => {
      val.status = EnrollmentStatusEnum.COMPLETED;
      return val;
    });

    await this.enrollmentRepository.saveMany(updateEnrollmentPassedToCompleted);

    const modifiedCount = updateEnrollmentPassedToCompleted.length;
    this.loggerJobMessage.info(`Enrollment changed status to completed, total ${modifiedCount} rows.`);
  }

  async enrollmentStatusApproved({
    enrollments,
    organizations,
    users,
    courses,
    courseVersions,
  }: EnrollmentExpiredStatusApprovedParams): Promise<{ successList: string[]; failedList: string[] }> {
    this.loggerJobMessage.info(`Found enrollment status is approved, total ${enrollments.length} rows.`);

    const sentCertificateEmailSuccessList = [];
    const sentCertificateEmailFailedList = [];

    const enrollmentApprovedIds = enrollments.map((enrollment) => enrollment.id);

    const enrollmentCertificates =
      await this.enrollmentCertificateRepository.findWithOrganizationCertificateDetail(enrollmentApprovedIds);

    for (const enrollment of enrollments) {
      const { userId, courseId, courseVersionId } = enrollment;

      const user = users.find((val) => val.guid === userId);

      const {
        email,
        profile: { firstname, lastname },
        organizationId,
      } = user;

      const organization = organizations.find((val) => val.id === organizationId);
      const course = courses.find((val) => val.id === courseId);
      const courseVersion = courseVersions.find((val) => val.id === courseVersionId);
      const fullName = `${firstname} ${lastname}`;
      const enrollmentCertificateList = enrollmentCertificates.filter(
        (enrollmentCertificate) =>
          enrollmentCertificate.enrollmentId === enrollment.id &&
          enrollmentCertificate?.isSentEmailOnExpiredDate &&
          !enrollmentCertificate?.isSentEmail,
      );
      const userNotificationCertificateList = [];

      for (const enrollmentCertificate of enrollmentCertificateList) {
        const { courseVersionCertificate } = enrollmentCertificate;
        const operationExpiredDate = date(enrollment.expiredAt).add(6, 'day').toDate();

        await delay(100);
        try {
          const { certificatePDFUrl } = await this.certificateService.getCertificateUrl(
            enrollmentCertificate.certificateCode,
          );
          const emailData = {
            certificateUrl: enrollmentCertificate.certificateUrl,
            certificatePDFUrl,
            certificateCode: enrollmentCertificate.certificateCode,
            operationExpiredDate: formatDateInThaiLocale(operationExpiredDate),
            refName: courseVersionCertificate.refName,
            fullName,
            courseName: courseVersion.name,
            objectiveType: course.objectiveType,
            regulator: course.regulatorInfo.regulator,
          };

          await this.notificationService.notifyEnrollmentCertificate(email, emailData, organization);

          const userNotification = await UserNotification.new({
            userId,
            organizationId,
            type: UserNotificationTypeEnum.ENROLLMENT_APPROVED_WITH_CERTIFICATE,
            payload: {
              mediaId: course.thumbnailMediaId,
              thumbnailUrl: course.thumbnailUrl,
              message: buildCertificateNotificationMessage(courseVersion.name),
              url: {
                certificateUrl: enrollmentCertificate.certificateUrl,
                enrollmentId: enrollment.id,
              },
            },
          });

          userNotificationCertificateList.push(userNotification);
          sentCertificateEmailSuccessList.push(enrollment.id);
        } catch (error) {
          this.loggerJobMessage.jobError(`Send certificate error ${error.message}`);
          sentCertificateEmailFailedList.push(enrollment.id);
        }
      }

      if (userNotificationCertificateList.length) {
        await this.userNotificationRepository.saveMany(userNotificationCertificateList);

        for (const userNotifyData of userNotificationCertificateList) {
          this.notificationService.sendInApplicationNotification(
            userNotifyData.payload.message,
            userNotifyData.organizationId,
          );
          await delay(100);
        }
      }
    }

    return { successList: sentCertificateEmailSuccessList, failedList: sentCertificateEmailFailedList };
  }
}
