import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { getPublishDateTimeOfNotification } from '@iso/helpers/dateUtils';
import { buildRemindClassroomRoundStartNotificationMessage } from '@iso/helpers/userNotification';
import { ClassroomLocationEnrollmentStatusEnum } from '@iso/lms/enums/classroomLocationEnrollment.enum';
import { OrganizationSchedulerTypeEnum } from '@iso/lms/enums/organizationScheduler.enum';
import { ClassroomLocationEnrollment } from '@iso/lms/models/classroomLocationEnrollment.model';
import { UserNotification } from '@iso/lms/models/userNotification.model';
import { MaterialMediaParams } from '@iso/lms/types/materialMedia.type';
import { OrganizationParams } from '@iso/lms/types/organization.type';
import { OrganizationSchedulerParams } from '@iso/lms/types/organizationScheduler.type';
import { UserParams } from '@iso/lms/types/user.type';
import { Inject, Injectable } from '@nestjs/common';
import { keyBy } from 'lodash';

import { ClassroomDIToken } from '@applications/di/domain/classroom.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { MaterialMediaDIToken } from '@applications/di/domain/materialMedia.di';
import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { UserDIToken } from '@applications/di/domain/user.di';
import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';

import { MailPayloadParams, MailPayloadRemindClassroomStartParams } from '@constants/types/mailer.type';

import { IClassroomLocationRepository } from '@interfaces/repositories/classroomLocation.repository.interface';
import { IClassroomLocationEnrollmentRepository } from '@interfaces/repositories/classroomLocationEnrollment.repository.interface';
import { IClassroomRoundRepository } from '@interfaces/repositories/classroomRound.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IMaterialMediaRepository } from '@interfaces/repositories/materialMedia.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IOrganizationSchedulerRepository } from '@interfaces/repositories/organizationScheduler.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { IUserNotificationRepository } from '@interfaces/repositories/userNotification.repository.interface';
import { ILoggerJobMessageService } from '@interfaces/services/logger.service.interface';
import { INotificationService, IWebHookNotificationService } from '@interfaces/services/notification.service.interface';
import { IPrepareRemindClassroomStartUseCase } from '@interfaces/usecases/classroom.interface';

import { date } from '@domains/utils/date.util';

@Injectable()
export class PrepareRemindClassroomStartUseCase implements IPrepareRemindClassroomStartUseCase {
  constructor(
    // Infrastructure
    @Inject(InfrastructureServicesDIToken.LoggerJobMessageService)
    private readonly loggerJobMessageService: ILoggerJobMessageService,

    // Repository
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationSchedulerRepository)
    private readonly organizationSchedulerRepository: IOrganizationSchedulerRepository,
    @Inject(ClassroomDIToken.ClassroomRoundRepository)
    private readonly classroomRoundRepository: IClassroomRoundRepository,
    @Inject(ClassroomDIToken.ClassroomLocationRepository)
    private readonly classroomLocationRepository: IClassroomLocationRepository,
    @Inject(ClassroomDIToken.ClassroomLocationEnrollmentRepository)
    private readonly classroomLocationEnrollmentRepository: IClassroomLocationEnrollmentRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(MaterialMediaDIToken.MaterialMediaRepository)
    private readonly materialMediaRepository: IMaterialMediaRepository,
    @Inject(NotificationDIToken.UserNotificationRepository)
    private readonly userNotificationRepository: IUserNotificationRepository,

    // Service
    @Inject(InfrastructureServicesDIToken.WebHookNotificationService)
    private readonly webHookNotificationService: IWebHookNotificationService,
    @Inject(InfrastructureServicesDIToken.NotificationService)
    private readonly notificationService: INotificationService,
  ) {}

  async execute(): Promise<void> {
    const nextRoundDate = date().add(1, 'day').startOf('day').toDate();
    const nextClassroomRounds = await this.classroomRoundRepository.findClassroomRoundByDate(nextRoundDate);

    const nextClassroomRoundIds = nextClassroomRounds.map((item) => item.id);

    const nextClassroomRoundEnrollments = await this.classroomLocationEnrollmentRepository.find({
      classroomRoundId: { $in: nextClassroomRoundIds },
      status: { $ne: ClassroomLocationEnrollmentStatusEnum.CANCELED },
    });

    if (!nextClassroomRoundEnrollments.length) return;

    // prepare data
    const organizationIds = nextClassroomRounds.map((item) => item.organizationId);
    const materialMediaIds = nextClassroomRounds.map((item) => item.materialMediaId);

    const userIds = nextClassroomRoundEnrollments.map((item) => item.userId);
    const enrollmentIds = nextClassroomRoundEnrollments.map((item) => item.enrollmentId);
    const classroomLocationIds = nextClassroomRoundEnrollments.map((item) => item.classroomLocationId);

    const [users, organizations, organizationSchedulers, materialMedias, enrollments, classroomLocations] =
      await Promise.all([
        this.userRepository.aggregate<UserParams>([{ $match: { guid: { $in: userIds } } }]),
        this.organizationRepository.aggregate<OrganizationParams>([{ $match: { id: { $in: organizationIds } } }]),
        this.findOrganizationSchedulerRemindClassroomStart(organizationIds),
        this.materialMediaRepository.aggregate<MaterialMediaParams>([{ $match: { id: { $in: materialMediaIds } } }]),
        this.enrollmentRepository.findEnrollmentWithCourse(enrollmentIds),
        this.classroomLocationRepository.findClassroomLocationWithInstructorName(classroomLocationIds),
      ]);

    const userMap = keyBy(users, 'guid');
    const enrollmentMap = keyBy(enrollments, 'id');
    const materialMediaMap = keyBy(materialMedias, 'id');
    const classroomRoundMap = keyBy(nextClassroomRounds, 'id');
    const classroomLocationMap = keyBy(classroomLocations, 'id');
    const organizationMap = keyBy(organizations, 'id');
    const organizationSchedulerMap = keyBy(organizationSchedulers, 'organizationId');

    const inAppUserNotificationList: UserNotification[] = [];
    const emailPayloadNotificationList: MailPayloadParams[] = [];

    const waitingClassroomEnrollmentList = nextClassroomRoundEnrollments.filter(
      (item) => item.status === ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL,
    );

    for (const classroomLocationEnrollment of nextClassroomRoundEnrollments) {
      const user = userMap[classroomLocationEnrollment.userId];
      const enrollment = enrollmentMap[classroomLocationEnrollment.enrollmentId];
      const classroomRound = classroomRoundMap[classroomLocationEnrollment.classroomRoundId];
      const materialMedia = materialMediaMap[classroomRound?.materialMediaId];
      const classroomLocation = classroomLocationMap[classroomLocationEnrollment.classroomLocationId];

      const organization = organizationMap[user?.organizationId];
      const organizationScheduler = organizationSchedulerMap[user?.organizationId];
      const publishDateNotification = getPublishDateTimeOfNotification(organizationScheduler?.cron);

      // user notify data
      const userNotification = await UserNotification.new({
        userId: user?.guid,
        organizationId: organization?.id,
        type: UserNotificationTypeEnum.REMIND_CLASSROOM_ROUND_START,
        payload: {
          mediaId: enrollment?.course.thumbnailMediaId,
          message: buildRemindClassroomRoundStartNotificationMessage({
            classroomName: materialMedia?.name || '',
          }),
          url: {
            code: enrollment?.course?.url,
          },
        },
        publishedAt: publishDateNotification,
      });

      inAppUserNotificationList.push(userNotification);

      const instructorNames =
        classroomLocation?.instructors.map((instructor) => {
          const firstname = instructor.firstname || '';
          const lastname = instructor.lastname || '';
          return `${firstname} ${lastname}`.trim();
        }) ?? [];

      const waitingNo = this.getWaitingNumber(
        waitingClassroomEnrollmentList,
        classroomLocationEnrollment.id,
        classroomLocationEnrollment.status,
      );

      // email payload data
      const emailData: MailPayloadRemindClassroomStartParams = {
        firstName: user?.profile?.firstname,
        lastName: user?.profile?.lastname,
        classroomName: materialMedia?.name,
        courseName: enrollment?.courseVersion.name,
        courseURL: enrollment?.course.url,
        thumbnailURL: enrollment?.course.thumbnailUrl,
        classroomType: classroomLocation?.type,
        classroomLocation: classroomLocation?.location,
        instructors: instructorNames,
        startedAt: classroomRound?.startedAt,
        expiredAt: classroomRound?.expiredAt,
        waitingNo,
      };

      const mailPayload = await this.notificationService.remindClassroomRoundStart(user.email, emailData, organization);
      mailPayload.sendAt = publishDateNotification;
      emailPayloadNotificationList.push(mailPayload);
    }

    // Send email
    for (const mailPayload of emailPayloadNotificationList) {
      this.notificationService.sendEmail(mailPayload);
    }

    // Send in app user notification
    if (inAppUserNotificationList.length) {
      await this.userNotificationRepository.saveMany(inAppUserNotificationList);

      for (const userNotifyData of inAppUserNotificationList) {
        this.notificationService.sendInApplicationNotification(
          userNotifyData.payload.message,
          userNotifyData.organizationId,
          userNotifyData.publishedAt,
        );
      }
    }

    // Send slack
    const headerText = 'ระบบได้ทำการเตรียมข้อมูลสำหรับแจ้งเตือนใกล้กำหนดการเรียนห้องเรียน';
    const total = nextClassroomRoundEnrollments.length;
    const totalUpdated = emailPayloadNotificationList.length;
    const totalError = total - totalUpdated;
    this.webHookNotificationService.sendUpdateStatusClassroom(headerText, total, totalError);

    this.loggerJobMessageService.info('Prepare remind classroom start data');
    this.loggerJobMessageService.info(`Updated ${totalUpdated} of ${total} rows.`);
  }

  private getWaitingNumber(
    waitingClassroomEnrollmentList: ClassroomLocationEnrollment[],
    classroomLocationEnrollmentId: GenericID,
    status: ClassroomLocationEnrollmentStatusEnum,
  ): Nullable<number> {
    let waitingNo: Nullable<number>;
    if (waitingClassroomEnrollmentList.length && status === ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL) {
      const waitingItemIndex = waitingClassroomEnrollmentList.findIndex(
        (item) => item.id === classroomLocationEnrollmentId,
      );
      if (waitingItemIndex !== -1) {
        waitingNo = waitingItemIndex + 1;
      }
    }
    return waitingNo;
  }

  private async findOrganizationSchedulerRemindClassroomStart(
    organizationIds: GenericID[],
  ): Promise<OrganizationSchedulerParams[]> {
    const result = await this.organizationSchedulerRepository.aggregate<OrganizationSchedulerParams>([
      {
        $match: {
          organizationId: { $in: organizationIds },
          type: OrganizationSchedulerTypeEnum.SEND_EMAIL_REMIND_CLASSROOM_ROUND_START,
        },
      },
    ]);

    return result;
  }
}
