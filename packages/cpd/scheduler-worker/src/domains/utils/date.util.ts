import dayjs from 'dayjs';
import 'dayjs/locale/th';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import isSameOrAfterPlugin from 'dayjs/plugin/isSameOrAfter';
import isSameOrBeforePlugin from 'dayjs/plugin/isSameOrBefore';
import timezone from 'dayjs/plugin/timezone'; // dependent on utc plugin
import utc from 'dayjs/plugin/utc';

dayjs.extend(buddhistEra);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfterPlugin);
dayjs.extend(isSameOrBeforePlugin);
dayjs.extend(duration);
dayjs.tz.setDefault('UTC');

export const TimeZoneEnum = {
  Bangkok: 'Asia/Bangkok',
  UTC: 'UTC',
};

export const DateFormat = {
  iso: 'YYYY-MM-DDTHH:mm:ss.SSS[Z]',
  yearMonthDayDash: 'YYYY-MM-DD',
  yearMonthDay: 'YYYYMMDD',
  yearMonth: 'YYYYMM',
  monthDayYearWithLeadingZero: 'MM/DD/YYYY',
  yearMonthDayHourMinuteSecond: 'YYYYMMDD-HHmmss',
  yearMonthDayHourMinuteWithLeadingZero: 'YYYY/MM/DD HH:mm',
  yearMonthDayWithLeadingZero: 'YYYY/MM/DD',
  dayMonthYearHourMinuteWithLeadingZero: 'DD/MM/YYYY HH:mm',
  dayMonthYearWithLeadingZero: 'DD/MM/YYYY',
  buddhistShortDate: 'DD MMM BBBB',
  buddhistDayMonthYearWithLeadingZero: 'DD/MM/BBBB',
  buddhistDayMonthYearHourMinuteWithLeadingZero: 'DD/MM/BBBB HH:mm',
  buddhistFullDateWithLocale: 'D เดือน MMMM พ.ศ. BBBB',
  hourMinute: 'HH:mm',
};

/**

 * @param val The input of date text to parsing.
 * @param timeZone The input time zone e.g. UTC, Asia/Bangkok. The default value is UTC
 * @param formatter The format of input date string. default value is YYYY-MM-DDTHH:mm:ss:SSS[Z]
 * @returns the UTC date
 */
export const parseDate = (
  val: Date | string,
  timeZone: string = TimeZoneEnum.UTC,
  formatter: string = DateFormat.iso,
): Date => dayjs.tz(val, formatter, timeZone).toDate();

/**
 * @param val The input of date text
 * @param formatter The format of input date string
 * @returns Return true when the input match with the format otherwise false
 */
export const isValidDateFormat = (val: string, formatter: string): boolean => dayjs(val, formatter, true).isValid();

export const buddhistOffset = 543;
/**
 * @description Convert buddhist date to christian date by subtract 543 years
 * @param date The input date
 * @returns The christian date
 */
export const toChristianDate = (date: Date): Date => dayjs(date).subtract(buddhistOffset, 'year').toDate();

export const getChristianYear = (year: string): number => {
  return parseInt(year) - buddhistOffset;
};

export const strToISO = (date: string): string => dayjs(date).toISOString();

export const date = (
  val?: string | number | dayjs.Dayjs | Date | null | undefined,
  format?: dayjs.OptionType | undefined,
): dayjs.Dayjs => dayjs(val ?? undefined, format).tz('Asia/Bangkok');

export const addDuration = (
  val?: string | number | dayjs.Dayjs | Date | null | undefined,
  format?: dayjs.OptionType | undefined,
): dayjs.Dayjs => dayjs(val ?? undefined, format).tz('Asia/Bangkok');

export const formatDate = (val: Date, format = 'DD/MM/YYYY', locale = 'th'): string => {
  return dayjs.tz(val, TimeZoneEnum.Bangkok).locale(locale).format(format);
};

/**
 * The function converts seconds to milliseconds.
 * @param {number} seconds - The parameter `seconds` is a number representing a duration in seconds.
 * @returns This function takes in a number of seconds and returns the equivalent number of milliseconds.
 */
export const secondsToMilliseconds = (seconds: number): number => {
  return seconds * 1000;
};

/**
 * @description format date of target time zone in the Thai locale according to the template passed in
 * @param date The input of date to formatting.
 * @param targetTimeZone The target time zone e.g. UTC, Asia/Bangkok. The default value is UTC
 * @param format The format to formatting the date to. The default value is Buddhist short date(DD MMM BBBB)
 * @returns the formatted date according to the format passed in. e.g. "26 ม.ค. 2557"
 */
export const formatDateInThaiLocale = (
  val: Date,
  targetTimeZone = TimeZoneEnum.UTC,
  format = DateFormat.buddhistShortDate,
): string => dayjs.tz(val).tz(targetTimeZone).locale('th').format(format);
