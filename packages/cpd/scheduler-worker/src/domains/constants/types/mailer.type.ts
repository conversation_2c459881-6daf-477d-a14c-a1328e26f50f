import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { ClassroomLocationTypeEnum } from '@iso/lms/enums/classroomLocation.enum';
import { CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { OrganizationNotificationConfigParams } from '@iso/lms/types/organization.type';

import { MailAttachmentParams } from '@constants/types/infrastructures/notification.type';

export type TemplateParams = {
  name: string;
  enable: boolean;
  content?: any;
};

export type OrganizationParams = {
  domain: string;
  mailerConfig: any;
};

export type MailRenderedTemplateParams = Nullable<{
  notificationConfig: OrganizationNotificationConfigParams;
  renderedTemplate: string;
  targetEmail: string | string[];
  subject: string;
  sendAt?: Date;
}>;

export type MailPayloadParams = {
  notificationConfig: OrganizationNotificationConfigParams;
  renderedTemplate: string;
  targetEmail: string | string[];
  subject: string;
  attachments?: MailAttachmentParams[];
  sendAt?: Date;
};

export type MailPayloadLearningPathEnrollmentExpiredParams = {
  fullName: string;
  learningPathName: string;
  learningPathCode: string;
  learningPathEnrollmentId: GenericID;
};

export type MailPayloadEnrollmentExpiredParams = {
  fullName: string;
  contentName: string;
  expiredAt: Date;
  thumbnailUrl: string;
  url: string;
};

export type MailPayloadCertificateParams = {
  fullName: string;
  courseName: string;
  certificateUrl: string;
  certificateCode: string;
  certificatePDFUrl: string;
  operationExpiredDate: string;
  refName: string;
  objectiveType: CourseObjectiveTypeEnum;
  regulator: RegulatorEnum;
};

export type MailPayloadEnrollmentRejectParams = {
  fullName: string;
  courseName: string;
};

export type MailPayloadCancelClassroomWaitingParams = {
  firstName: string;
  lastName: string;
  classroomName: string;
  courseName: string;
  courseURL: string;
  thumbnailURL: string;
  classroomType: ClassroomLocationTypeEnum;
  classroomLocation: string;
  instructors: string[];
  startedAt: Date;
  expiredAt: Date;
};

export type MailPayloadRemindClassroomStartParams = {
  firstName: string;
  lastName: string;
  classroomName: string;
  courseName: string;
  courseURL: string;
  thumbnailURL: string;
  classroomType: ClassroomLocationTypeEnum;
  classroomLocation: string;
  instructors: string[];
  startedAt: Date;
  expiredAt: Date;
  waitingNo: number;
};
