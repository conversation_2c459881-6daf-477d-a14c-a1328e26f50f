import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Organization } from '@iso/lms/models/organization.model';
import { TaskOperation } from '@iso/lms/models/taskOperation.model';
import { OrganizationParams } from '@iso/lms/types/organization.type';

import { WebhookNotificationChannelEnum } from '@constants/enums/infrastructures/notification.enum';
import { WebHookPayloadParams } from '@constants/types/infrastructures/notification.type';
import {
  MailPayloadCancelClassroomWaitingParams,
  MailPayloadLearningPathEnrollmentExpiredParams,
  MailPayloadParams,
  MailPayloadRemindClassroomStartParams,
  MailRenderedTemplateParams,
  MailPayloadCertificateParams,
  MailPayloadEnrollmentExpiredParams,
  MailPayloadEnrollmentRejectParams,
} from '@constants/types/mailer.type';

export interface INotificationService {
  sendInApplicationNotification(message: string, organizationId: GenericID, sendAt?: Nullable<Date>): void;
  sendEmail(mailPayload: MailPayloadParams): void;
  learningPathEnrollmentExpired(
    email: string,
    payload: MailPayloadLearningPathEnrollmentExpiredParams,
    organization: OrganizationParams,
  ): Promise<MailRenderedTemplateParams>;
  notifyEnrollmentReject(
    email: string,
    payload: MailPayloadEnrollmentRejectParams,
    organization: Organization,
  ): Promise<void>;
  notifyRemindEnrollmentExpired(
    email: string,
    payload: MailPayloadEnrollmentExpiredParams,
    organization: Organization,
    sendAt: Date,
  ): Promise<void>;
  notifyEnrollmentCertificate(
    email: string,
    payload: MailPayloadCertificateParams,
    organization: Organization,
  ): Promise<void>;
  cancelWaitingClassroom(
    email: string,
    payload: MailPayloadCancelClassroomWaitingParams,
    organization: OrganizationParams,
  ): Promise<MailRenderedTemplateParams>;
  remindClassroomRoundStart(
    email: string,
    payload: MailPayloadRemindClassroomStartParams,
    organization: OrganizationParams,
  ): Promise<MailRenderedTemplateParams>;
}

export interface IWebHookNotificationService {
  sendNotification(payload: WebHookPayloadParams, channel: WebhookNotificationChannelEnum): Promise<void>;
  sendNotChangeRemoveCourseItemProgressHistory(): Promise<void>;
  sendUpdateLearningPathEnrollmentExpired(total: number): Promise<void>;
  sendUpdateStatusClassroom(headerText: string, total: number, totalError: number): Promise<void>;
  sendEnrollmentExpired(total: number): Promise<void>;
  sendCertificateEmail(total: number, totalSuccess: number): Promise<void>;
  sendNotChangeEnrollmentExpired(): Promise<void>;
  sendNotChangeBulkPreEnrollmentTransactionUpdateRetailOrder(): Promise<void>;
  sendBulkPreEnrollmentTransactionUpdateRetailOrder(total: number, totalSuccess: number): Promise<void>;
  sendOperationExecute(
    domain: string,
    operationType: string,
    total: number,
    totalError: number,
    userId: string,
    urlAction: string,
  ): Promise<void>;
  sendAutoOperationEmptyExecute(domain: string): Promise<void>;
  sendNotifyTaskOperation(domain: string, topic: string, task: TaskOperation): Promise<void>;
}
