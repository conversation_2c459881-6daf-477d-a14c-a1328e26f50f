export interface IEnvironments {
  appName: string;
  port: number;

  // Database Env
  dbURI: string;
  dbName: string;

  // Message Broker Env
  amqpURI: string;
  amqpNotificationURI: string;

  // Memory Cache
  redisURL: string;

  // File Storage
  awsAccessKeyId: string;
  awsSecretAccessKey: string;
  awsRegion: string;
  awsUploadBucketName: string;
  certificateEndpoint: string;
  lmsApiEndpoint: string;

  // Cron Job
  cronAutoApproveEnrollment: string;
  cronEnrollmentExpired: string;
  cronBulkRemoveCourseItemProgressHistory: string;
  cronAutoEnrollment: string;
  cronBulkRemovePreEnrollmentTransactionStatusError: string;
  cronBulkUpdateRetailOrder: string;
  cronSftpImportUsers: string;
  cronUpdateUserInUserGroupByCondition: string;
  cronKnowledgeContentInteractionHistory: string;
  cronSummaryTsiQuizScore: string;
  cronUpdateStatusClassroom: string;
  cronPromoteNotification: string;
  cronAutoEnrollmentAchievement: string;
  cronPlanPackageLicenseExpired: string;

  // Slack
  webhookBotNotificationUrl: string;
  webhookAutoNotificationUrl: string;

  clientProtocol: string;

  s3BucketUrl: string;

  asURI: string;
  asClientId: string;
  asSecret: string;
  asScope: string;
  advanceTokenRequestTime: number;
  renewTokenSecondsBeforeExpires: number;

  basicAuthenticationUsername: string;
  basicAuthenticationPassword: string;
  retailApi: string;
}
