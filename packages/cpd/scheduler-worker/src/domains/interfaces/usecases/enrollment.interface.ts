import { IdentificationCard } from '@iso/lms/models/identificationCard.model';

import { IBaseUseCase } from '@interfaces/usecases/base.interface';

export interface IApproveEnrollmentUseCase extends IBaseUseCase<IdentificationCard, void> {}
export interface IRemoveCourseItemProgressHistoryUseCase extends IBase<PERSON>seCase<null, void> {}
export interface IEnrollmentExpiredUseCase extends IBaseUseCase<null, void> {}
export interface IPlanPackageLicenseExpiredUseCase extends IBaseUseCase<null, void> {}
