import { GenericID } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum, LicenseTypeEnum } from '@iso/lms/enums/course.enum';
import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { ProductTypeEnum } from '@iso/lms/enums/productSKU.enum';
import { Inject, Injectable } from '@nestjs/common';
import { union } from 'lodash';

import { CourseDIToken, CreditDIToken, PlanDIToken, ProductSKUDIToken, UserDIToken } from '@applications/di/domain';

import { AutoBulkPlanParams, AutoBulkTransactionParams } from '@constants/types/preEnrollment.type';

import {
  ICourseRepository,
  ICustomerPartnerRepository,
  IPlanPackageLicenseRepository,
  IPlanRepository,
  IProductSKUCourseRepository,
  IProductSKURepository,
  IUserRepository,
} from '@interfaces/repositories';
import { IPrepareDataAutoBulkService } from '@interfaces/services/prepareDataAutoBulk.service.interface';

@Injectable()
export class PrepareDataAutoBulkService implements IPrepareDataAutoBulkService {
  constructor(
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CreditDIToken.CustomerPartnerRepository)
    private readonly customerPartnerRepository: ICustomerPartnerRepository,
    @Inject(PlanDIToken.PlanPackageLicenseRepository)
    private readonly planPackageLicenseRepository: IPlanPackageLicenseRepository,
    @Inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @Inject(ProductSKUDIToken.ProductSKURepository)
    private readonly productSKURepository: IProductSKURepository,
    @Inject(ProductSKUDIToken.ProductSKUCourseRepository)
    private readonly productSKUCourseRepository: IProductSKUCourseRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
  ) {}

  async getAvailableActiveUserByTransactionList(
    originalTransactions: AutoBulkTransactionParams[],
    organizationId: GenericID,
  ): Promise<AutoBulkTransactionParams[]> {
    const transactions = originalTransactions.filter((val) => val.autoBulkOparation.includes(BulkOpTypeEnum.ACTIVATE));
    const result = await this.getNewUsersFromTransactions(transactions, organizationId);
    return result;
  }

  async getAvailableEditUserByTransactionList(
    originalTransactions: AutoBulkTransactionParams[],
    organizationId: GenericID,
  ): Promise<AutoBulkTransactionParams[]> {
    const transactions = originalTransactions.filter((val) => val.autoBulkOparation.includes(BulkOpTypeEnum.EDIT_USER));
    const result = await this.getOldUserFromTransactions(transactions, organizationId);
    return result;
  }

  async getAvailableEnrollCourseByTransactionList(
    originalTransactions: AutoBulkTransactionParams[],
    organizationId: GenericID,
  ): Promise<AutoBulkTransactionParams[]> {
    const transactions = originalTransactions.filter((val) =>
      val.autoBulkOparation.includes(BulkOpTypeEnum.ENROLLMENT),
    );

    const citizenIdList = union(transactions.map((val) => val.payload?.citizenId));
    const userIdList = union(transactions.map((val) => val.userId));

    // get course has enroll & status in progress
    const inProgressEnrollments = await this.userRepository.findEnrollmentStatusInProgressByCitizenIdOrUserIdList(
      organizationId,
      citizenIdList,
      userIdList,
    );

    // check course enable
    const availableCourses = await this.courseRepository.findAvailableCoursesByOrganizationId(organizationId);
    const availableCourseCodes = availableCourses.map((val) => val.code);

    let validTransactions = transactions.filter((val) => availableCourseCodes.includes(val.payload?.courseCode));

    if (validTransactions.length === 0) return [];

    // check course has enroll & status in progress
    validTransactions = validTransactions.filter((transaction) => {
      const { userId } = transaction.payload;
      const { citizenId, courseCode } = transaction.payload;

      const isExistEnrollment = inProgressEnrollments.find(
        (val) => val.courseCode === courseCode && (val.citizenId === citizenId || val.userId === userId),
      );

      return !isExistEnrollment;
    });

    if (validTransactions.length === 0) return [];

    // get course with certificate by course code
    const preEnrollCourseCodes = union(validTransactions.map((val) => val.payload?.courseCode));
    const courseAndCertificates = await this.courseRepository.findCourseWithRefCodeCertificateByCourseCode(
      organizationId,
      preEnrollCourseCodes,
    );

    const result = validTransactions.map((transaction) => {
      const { oicExtendYearType } = transaction.payload;
      const filterCourse = courseAndCertificates.find((val) => val.courseCode === transaction.payload.courseCode);
      let refCodes = [];

      if (filterCourse.isMultipleCertificate) {
        if (filterCourse?.objectiveType === CourseObjectiveTypeEnum.REGULAR) {
          refCodes = filterCourse.certificates.map((val) => val.refCode);
        } else {
          const certificates = this.getFilterCertificatesByOICLicenseType(filterCourse.certificates, oicExtendYearType);
          const _refCodes = certificates.map((val) => val.refCode);
          refCodes = this.getRefCodesByOICLicenseType(_refCodes, oicExtendYearType);
        }
      }

      transaction.courseId = filterCourse?.id;
      transaction.refCodes = refCodes;

      return transaction;
    });

    return result;
  }

  async getAvailableEnrollBundleByTransactionList(
    originalTransactions: AutoBulkTransactionParams[],
    organizationId: GenericID,
  ): Promise<AutoBulkTransactionParams[]> {
    const transactions = originalTransactions.filter((val) =>
      val.autoBulkOparation.includes(BulkOpTypeEnum.ENROLLMENT_BUNDLE),
    );

    const citizenIdList = union(transactions.map((val) => val.payload?.citizenId));
    const userIdList = union(transactions.map((val) => val.userId));

    // check bundle has enroll & status in progress
    const inProgressEnrollments = await this.userRepository.findEnrollmentStatusInProgressByCitizenIdOrUserIdList(
      organizationId,
      citizenIdList,
      userIdList,
    );

    // check bundle enable
    const enabledBundles = await this.productSKURepository.find({
      isEnabled: true,
      type: ProductTypeEnum.BUNDLE,
    });

    const availableBundleCodes = enabledBundles.map((val) => val.code);

    let validTransactions = transactions.filter((val) => availableBundleCodes.includes(val.payload?.productSKUCode));
    if (validTransactions.length === 0) return [];

    const preEnrollBundleCodes = union(validTransactions.map((val) => val.payload?.productSKUCode));

    // get bundle with certificate by product sku code
    const bundleWithCertificateList = await this.productSKURepository.findBundleWithRefCodeCertificateByProductSKUCode(
      organizationId,
      preEnrollBundleCodes,
    );

    // find product sku code by course id
    const courseIdsInProgress = inProgressEnrollments.map((val) => val.courseId);

    const aggregateCourseInProgressEnrollment = [
      {
        $match: {
          courseId: {
            $in: courseIdsInProgress,
          },
        },
      },
      {
        $lookup: {
          from: 'product-sku-bundles',
          let: { productSKUId: '$productSKUId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ['$$productSKUId', '$productSKUIds'],
                },
              },
            },
            {
              $lookup: {
                from: 'product-skus',
                let: { productSKUId: '$productSKUId' },
                pipeline: [
                  {
                    $match: {
                      $and: [
                        {
                          $expr: {
                            $eq: ['$id', '$$productSKUId'],
                          },
                        },
                        {
                          $expr: {
                            $eq: ['$isEnabled', true],
                          },
                        },
                      ],
                    },
                  },
                  {
                    $project: {
                      id: 1,
                      code: 1,
                    },
                  },
                ],
                as: 'productSKU',
              },
            },
            {
              $unwind: '$productSKU',
            },
          ],
          as: 'bundle',
        },
      },
      {
        $project: {
          id: 1,
          productSKUId: 1,
          courseId: 1,
          bundle: 1,
        },
      },
    ];

    const courseInProgressEnrollments: Record<string, any> = await this.productSKUCourseRepository.aggregate(
      aggregateCourseInProgressEnrollment,
    );

    validTransactions = validTransactions.filter((transaction) => {
      const { userId } = transaction;
      const { citizenId, productSKUCode } = transaction.payload;

      let isExistEnrollment = false;

      const userEnrollmentList = inProgressEnrollments.filter(
        (val) => val.citizenId === citizenId || val.userId === userId,
      );
      for (const userEnrollment of userEnrollmentList) {
        const checkCourse = courseInProgressEnrollments.find((val) => val.courseId === userEnrollment.courseId);
        if (checkCourse) {
          const checkProductCode = checkCourse.bundle.find((val) => val.productSKU.code === productSKUCode);
          if (checkProductCode) {
            isExistEnrollment = true;
            break;
          }
        }
      }

      return !isExistEnrollment;
    });

    const customerPartnerIdList = union(transactions.map((val) => val.customerPartnerId));

    const customerPartnerData = await this.customerPartnerRepository.find({ id: { $in: customerPartnerIdList } });

    const result = validTransactions.map((transaction) => {
      const { oicExtendYearType, productSKUCode } = transaction.payload;

      let refCodes = [];

      const filterBundle = bundleWithCertificateList.find((val) => val.productSKUCode === productSKUCode);
      for (const data of filterBundle.courses) {
        if (data.course.isMultipleCertificate) {
          const certificates = this.getFilterCertificatesByOICLicenseType(data.certificates, oicExtendYearType);
          let _refCodes = certificates.map((val) => val.refCode);
          _refCodes = this.getRefCodesByOICLicenseType(_refCodes, oicExtendYearType);
          refCodes = [...refCodes, ..._refCodes];
        }
      }

      const customerPartner = customerPartnerData.find((val) => val.id === transaction.customerPartnerId);

      transaction.productSKUId = filterBundle?.id;
      transaction.customerPartnerCode = customerPartner?.code;
      transaction.refCodes = refCodes;

      return transaction;
    });

    return result;
  }

  async getAvailableAssignPlanPackageLicenseByTransactionList(
    originalTransactions: AutoBulkTransactionParams[],
    organizationId: GenericID,
  ): Promise<AutoBulkTransactionParams[]> {
    const transactions = originalTransactions.filter((val) =>
      val.autoBulkOparation.includes(BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE),
    );

    // startDate <= Date Now and endDate >= Date Now
    const planPackagePlatformAvailableList = await this.planRepository.findPlanPackagePlatformAvailable(organizationId);
    if (planPackagePlatformAvailableList.length === 0) {
      return [];
    }

    const planPackagePlatformIds = planPackagePlatformAvailableList.map((val) => val.planPackage.id);
    const planDataList = this.transformPlanData(planPackagePlatformAvailableList);

    // get data new user
    const validNewUserList = await this.getNewUsersFromTransactions(transactions, organizationId);

    // get data old user map Id null
    const validOldMapUserIdList = await this.getOldUserMapUserIdFromTransactions(transactions, organizationId);

    // get data old user
    const validOldUserList = await this.getOldUserFromTransactions(transactions, organizationId);

    const userOldIds = union([
      ...validOldMapUserIdList.map((val) => val.userId),
      ...validOldUserList.map((val) => val.userId),
    ]);

    // expired Date >= Date Now
    const planPackagePlatformLicenseSOPendingActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOPendingActive(
        userOldIds,
        planPackagePlatformIds,
      );

    // expired Date > Date Now
    const planPackagePlatformLicenseSOApprovedActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOApprovedActive(
        userOldIds,
        planPackagePlatformIds,
      );

    const planPackagePlatformLicenseActive = planPackagePlatformLicenseSOPendingActive.concat(
      planPackagePlatformLicenseSOApprovedActive,
    );

    const newUserTransactionsWithPlans = validNewUserList.map((transaction) => ({
      ...transaction,
      plans: planDataList,
    }));

    const oldUserTransactionsWithPlans = [];
    for (const userId of userOldIds) {
      const userInPlanPackagePlatformLicense = planPackagePlatformLicenseActive.find((val) => val.userId === userId);
      if (!userInPlanPackagePlatformLicense) {
        const data = validOldUserList.find((val) => val.userId === userId);
        oldUserTransactionsWithPlans.push({ ...data, plans: planDataList });
      }
    }

    const result = [...newUserTransactionsWithPlans, ...oldUserTransactionsWithPlans];

    return result;
  }

  private async getNewUsersFromTransactions(
    transactions: AutoBulkTransactionParams[],
    organizationId: GenericID,
  ): Promise<AutoBulkTransactionParams[]> {
    if (transactions.length === 0) {
      return [];
    }
    // get user duplicate from pre enroll transaction list
    const emailList = union(transactions.map((val) => val.payload.email));
    const citizenIdList = union(transactions.map((val) => val.payload.citizenId));

    const existingUsers = await this.userRepository.find({
      organizationId,
      $or: [{ email: { $in: emailList } }, { citizenId: { $in: citizenIdList } }],
    });

    // get data for users that don't exist
    const result = transactions.filter((transaction) => {
      const userExists = existingUsers.find(
        (existingUser) =>
          existingUser.citizenId === transaction.payload.citizenId || existingUser.email === transaction.payload.email,
      );
      return !userExists;
    });

    return result;
  }

  private async getOldUserMapUserIdFromTransactions(
    transactions: AutoBulkTransactionParams[],
    organizationId: GenericID,
  ): Promise<AutoBulkTransactionParams[]> {
    if (transactions.length === 0) {
      return [];
    }

    const newTransactionList = transactions.filter((val) => !val.userId);

    const citizenIdList = union(newTransactionList.map((val) => val.payload.citizenId));

    const availableUserList = await this.userRepository.find({
      organizationId,
      citizenId: { $in: citizenIdList },
    });

    const result = newTransactionList.filter((transaction) => {
      const user = availableUserList.find((val) => val.citizenId === transaction.payload.citizenId);

      if (user) {
        transaction.userId = user.guid;
        transaction.payload.username = user.username;
      }

      return !!user;
    });

    return result;
  }

  private async getOldUserFromTransactions(transactions: AutoBulkTransactionParams[], organizationId: GenericID) {
    if (transactions.length === 0) {
      return [];
    }
    const userIdList = union(transactions.map((val) => val.userId));
    const availableUserList = await this.userRepository.find({
      organizationId,
      guid: {
        $in: userIdList,
      },
    });

    const result = transactions.filter((transaction) => {
      const user = availableUserList.find((val) => val.guid === transaction.userId);

      if (user) {
        transaction.payload.username = user.username;
      }

      return !!user;
    });

    return result;
  }

  private getFilterCertificatesByOICLicenseType(certificates: Record<string, any>[], licenseType: LicenseTypeEnum) {
    const result = certificates.filter((val) => {
      if (!licenseType) {
        return false;
      }

      if (licenseType === LicenseTypeEnum.BOTH) {
        return [LicenseTypeEnum.LIFE, LicenseTypeEnum.NONLIFE].includes(val.type);
      }

      return licenseType === val.type;
    });

    return result;
  }

  private getRefCodesByOICLicenseType(refCodes: string[], licenseType: LicenseTypeEnum) {
    if (licenseType === LicenseTypeEnum.BOTH) {
      return refCodes.length < 2 ? [...refCodes, '-'] : refCodes;
    }

    return refCodes;
  }

  private transformPlanData(planPackages: Record<string, any>[]): AutoBulkPlanParams[] {
    const planMap = new Map<
      GenericID,
      { id: GenericID; name: string; planPackages: { id: GenericID; name: string; type: string }[] }
    >();

    for (const item of planPackages) {
      const planId = item.id;
      const planName = item.name;

      if (!planMap.has(planId)) {
        planMap.set(planId, {
          id: planId,
          name: planName,
          planPackages: [],
        });
      }

      const planEntry = planMap.get(planId);
      if (item.planPackage) {
        planEntry.planPackages.push({
          id: item.planPackage.id,
          name: item.planPackage.name,
          type: item.planPackage.type,
        });
      }
    }
    return Array.from(planMap.values());
  }
}
