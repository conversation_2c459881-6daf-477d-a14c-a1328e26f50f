import { Inject, Injectable, OnModuleInit } from '@nestjs/common';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { UserGroupDIToken } from '@applications/di/domain/userGroup.di';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';

import { IEnvironments } from '@interfaces/infrastructures/configs/environment.interface';
import { IScheduleService } from '@interfaces/infrastructures/services/schedule.service.interface';
import { IUpdateUsersInUserGroupByConditionUseCase } from '@interfaces/usecases/userGroup.interface';

@Injectable()
export class UserGroupScheduler implements OnModuleInit {
  private readonly cronUpdateUserInUserGroupByConditionName = 'cronUpdateUserInUserGroupByCondition';

  constructor(
    @Inject(InfrastructuresConfigDIToken.Environment)
    private readonly environment: IEnvironments,
    @Inject(InfrastructureServicesDIToken.ScheduleService)
    private readonly scheduleService: IScheduleService,
    @Inject(UserGroupDIToken.UpdateUsersInUserGroupByConditionUseCase)
    private readonly updateUserGroupByConditionUseCase: IUpdateUsersInUserGroupByConditionUseCase,
    @Inject(InfrastructureServicesDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  onModuleInit() {
    this.scheduleService.addCronJob(
      this.cronUpdateUserInUserGroupByConditionName,
      this.environment.cronUpdateUserInUserGroupByCondition,
      this.handleUpdateUserInUserGroupByCondition.bind(this),
    );
  }

  private async handleUpdateUserInUserGroupByCondition(): Promise<void> {
    this.logger.log('start handle update user in userGroup by condition');
    await this.updateUserGroupByConditionUseCase.execute();
    this.logger.log('end handle update user in userGroup by condition');
  }
}
