import { Nullable } from '@iso/constants/commonTypes';

import { BaseMemoryCachedRepository } from '@infrastructures/persistence/repositories/memoryCache/baseMemoryCached.repository';

import { MemoryCachedInstanceParams } from '@constants/types/infrastructures/memoryCache.type';

import { IAuthenticationMemoryCachedRepository } from '@interfaces/memoryCache/authenticationMemoryCached.repository.interface';

export class AuthenticationMemoryCachedRepository
  extends BaseMemoryCachedRepository
  implements IAuthenticationMemoryCachedRepository
{
  constructor(readonly db: MemoryCachedInstanceParams) {
    super(db);
  }

  async getWithoutPrefix(key: string): Promise<Nullable<string>> {
    return this.db.get(key);
  }

  async setWithoutPrefix(key: string, value: string): Promise<void> {
    await this.db.set(key, value);
  }
}
