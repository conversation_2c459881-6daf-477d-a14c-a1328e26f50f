import { Optional } from '@iso/constants/commonTypes';
import { Inject, Injectable } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { isUndefined } from 'lodash';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';

import { IScheduleService } from '@interfaces/infrastructures/services/schedule.service.interface';

@Injectable()
export class ScheduleService implements IScheduleService {
  private readonly timezone = 'Asia/Bangkok';

  constructor(
    private schedulerRegistry: SchedulerRegistry,
    @Inject(InfrastructureServicesDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  addCronJob(name: string, cron: Optional<string>, subscribe: () => void): void {
    if (isUndefined(cron)) return;

    this.logger.log(`subscribe cron name: ${name}, cron time: ${cron}`);

    const job = new CronJob(cron, subscribe, null, false, this.timezone);

    this.schedulerRegistry.addCronJob(name, job);

    job.start();
  }
}
