import { Injectable, Lo<PERSON>, Scope } from '@nestjs/common';

import { ILoggerJobMessageService } from '@interfaces/services/logger.service.interface';

@Injectable({ scope: Scope.TRANSIENT })
export class LoggerJobMessageService extends Logger implements ILoggerJobMessageService {
  setLogContext(name: string): void {
    this.context = name;
  }

  info(msg: string): void {
    this.log(`| - ${msg || '...'}`);
  }

  jobError(msg: string): void {
    this.error(`| - ${msg || '...'}`);
  }

  jobSubscribe(name: string): void {
    this.log(`| --- Job subscribe ${name || '...'} ---`);
  }

  jobStart(name: string): void {
    this.log(`| --- Job starting ${name || '...'} ---`);
  }

  jobEnd(name: string): void {
    this.log(`| --- Job ended ${name || '...'} ---`);
  }

  resetLogContext(): void {
    this.context = '';
  }
}
