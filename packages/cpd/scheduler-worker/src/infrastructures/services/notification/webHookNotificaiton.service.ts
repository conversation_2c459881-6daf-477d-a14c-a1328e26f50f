import { BulkOpTypeThaiText } from '@iso/lms/types/job.type';
import { TaskOperationParams } from '@iso/lms/types/taskOperation.type';
import { Inject, Injectable } from '@nestjs/common';
import { IncomingWebhook } from '@slack/webhook';

import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';

import { WebhookNotificationChannelEnum } from '@constants/enums/infrastructures/notification.enum';
import { WebHookPayloadParams } from '@constants/types/infrastructures/notification.type';

import { IWebHookNotificationService } from '@interfaces/infrastructures/services/notification.service.interface';

import { date } from '@domains/utils/date.util';

@Injectable()
export class WebHookNotificationService implements IWebHookNotificationService {
  private readonly errorColor = '#BD2828';
  private readonly successColor = '#226f50';

  constructor(
    @Inject(InfrastructuresConfigDIToken.WebhookBotNotification)
    private readonly slackWebhookBot: IncomingWebhook,
    @Inject(InfrastructuresConfigDIToken.WebhookAutoNotification)
    private readonly slackWebhookAuto: IncomingWebhook,
  ) {}

  async sendNotification(payload: WebHookPayloadParams, channel: WebhookNotificationChannelEnum) {
    if (channel === WebhookNotificationChannelEnum.BOT) {
      await this.slackWebhookBot.send(payload);
    }

    if (channel === WebhookNotificationChannelEnum.AUTO) {
      await this.slackWebhookAuto.send(payload);
    }
  }

  async sendNotChangeRemoveCourseItemProgressHistory(): Promise<void> {
    const notifyPayload = {
      text: 'แจ้งเตือน! น้องไม่พบ Progress History หมดอายุ ในครั้งนี้ :tada:\n`Job - remove course item progress history`',
    };
    await this.sendNotification(notifyPayload, WebhookNotificationChannelEnum.BOT);
  }

  async sendUpdateLearningPathEnrollmentExpired(total: number): Promise<void> {
    const notifyPayload = {
      text: 'แจ้งเตือน! น้องได้อัพเดท แผนการเรียนรู้ที่หมดอายุให้แล้วนะ :tada: \n `Job - learning path enrollment expired`',
      attachments: [
        {
          title: ':pencil: Method - Updated',
          color: 'good',
          fields: [
            {
              title: 'Total',
              value: `${total}`,
            },
          ],
        },
      ],
    };

    await this.sendNotification(notifyPayload, WebhookNotificationChannelEnum.BOT);
  }

  async sendEnrollmentExpired(total: number): Promise<void> {
    const notifyPayload = {
      text: 'แจ้งเตือน! น้องได้อัพเดท คอร์สเรียนที่หมดอายุให้แล้วนะ :tada: \n`Job - enrollment expired`',
      attachments: [
        {
          title: ':pencil: Method - Updated',
          color: 'good',
          fields: [
            {
              title: 'Total',
              value: `${total}`,
            },
          ],
        },
      ],
    };
    await this.sendNotification(notifyPayload, WebhookNotificationChannelEnum.BOT);
  }

  async sendCertificateEmail(total: number, totalSuccess: number): Promise<void> {
    const isError = totalSuccess < total;
    const borderColor = isError ? this.errorColor : this.successColor;
    const actionBy = 'ระบบ';
    const dateText = date().format('ddd, DD MMM YYYY HH:mm');

    const notifyPayload = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'แจ้งเตือน! น้องได้ส่งอีเมล์ประกาศนียบัตรหลังเรียนจบให้แล้วนะ :tada: \n`Job - sent certificate email`',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${dateText} โดย ${actionBy}`,
          },
        },
      ],
      attachments: [
        {
          color: borderColor,
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ทั้งหมด:* ${total}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*สำเร็จ:* ${totalSuccess}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ล้มเหลว:* ${total - totalSuccess}`,
              },
            },
          ],
        },
      ],
    };
    await this.sendNotification(notifyPayload, WebhookNotificationChannelEnum.BOT);
  }

  async sendNotChangeEnrollmentExpired(): Promise<void> {
    const notifyPayload = {
      text: 'แจ้งเตือน! น้องไม่พบคอร์สเรียนที่หมดอายุ ในวันนี้ :tada:\n`Job - enrollment expired`',
    };
    await this.sendNotification(notifyPayload, WebhookNotificationChannelEnum.BOT);
  }

  async sendUpdateStatusClassroom(headerText: string, total: number, totalError: number): Promise<void> {
    const isError = totalError > 0;
    const borderColor = isError ? this.errorColor : this.successColor;
    const dateText = date().format('ddd, DD MMM YYYY HH:mm');

    const payload = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: headerText,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${dateText}`,
          },
        },
      ],
      attachments: [
        {
          color: borderColor,
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ทั้งหมด:* ${total}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*สำเร็จ:* ${total - totalError}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ล้มเหลว:* ${totalError}`,
              },
            },
          ],
        },
      ],
    };

    await this.sendNotification(payload, WebhookNotificationChannelEnum.BOT);
  }

  async sendNotChangeBulkPreEnrollmentTransactionUpdateRetailOrder() {
    const notifyPayload = {
      text: 'แจ้งเตือน! น้องไม่พบ `preEnrollmentTransaction.isUpdatedRetailOrder: false` ที่ต้องอัพเดทในครั้งนี้ :tada:\n `Job - bulk update retail order`',
    };
    await this.sendNotification(notifyPayload, WebhookNotificationChannelEnum.BOT);
  }

  async sendBulkPreEnrollmentTransactionUpdateRetailOrder(total: number, totalSuccess: number) {
    const isError = totalSuccess < total;
    const borderColor = isError ? this.errorColor : this.successColor;

    const notiPayload = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'แจ้งเตือน! น้องได้อัพเดท `preEnrollmentTransaction.isUpdatedRetailOrder: false` ที่ต้องอัพเดทในครั้งนี้ :tada: \n `Job - bulk update retail order`',
            emoji: true,
          },
        },
      ],
      attachments: [
        {
          color: borderColor,
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ทั้งหมด:* ${total}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*สำเร็จ:* ${totalSuccess}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ล้มเหลว:* ${total - totalSuccess}`,
              },
            },
          ],
        },
      ],
    };

    await this.sendNotification(notiPayload, WebhookNotificationChannelEnum.BOT);
  }

  async sendOperationExecute(
    domain: string,
    operationType: string,
    total: number,
    totalError: number,
    userId: string,
    urlAction: string,
  ) {
    const isError = totalError > 0;
    const borderColor = isError ? this.errorColor : this.successColor;
    const actionBy = userId ? 'ผู้ใช้งาน' : 'ระบบ';
    const dateText = date().format('ddd, DD MMM YYYY HH:mm');

    const notiPayload = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `${domain} ระบบได้ทำการ${BulkOpTypeThaiText[operationType]}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${dateText} โดย ${actionBy}`,
          },
        },
      ],
      attachments: [
        {
          color: borderColor,
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ทั้งหมด:* ${total}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*สำเร็จ:* ${total - totalError}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ล้มเหลว:* ${totalError}`,
              },
            },
            {
              type: 'actions',
              elements: [
                {
                  type: 'button',
                  text: {
                    type: 'plain_text',
                    text: 'ตรวจสอบผลการดำเนินการ',
                    emoji: true,
                  },
                  value: 'click_me_123',
                  action_id: 'actionId-0',
                  url: urlAction,
                },
              ],
            },
          ],
        },
      ],
    };

    await this.sendNotification(notiPayload, WebhookNotificationChannelEnum.AUTO);
  }

  async sendAutoOperationEmptyExecute(domain: string) {
    const dateText = date().format('ddd, DD MMM YYYY HH:mm');
    const notiPayload = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `${domain} ระบบไม่พบการทำรายการ`,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: dateText,
          },
        },
      ],
      attachments: [
        {
          color: '#DDDDDD',
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: 'ไม่พบการทำรายการ',
              },
            },
          ],
        },
      ],
    };

    await this.sendNotification(notiPayload, WebhookNotificationChannelEnum.AUTO);
  }

  async sendNotifyTaskOperation(domain: string, topic: string, task: TaskOperationParams) {
    const isError = task.error > 0;
    const borderColor = isError ? this.errorColor : this.successColor;
    const dateText = date().format('ddd, DD MMM YYYY HH:mm');
    const notifyPayload = {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `${domain} ระบบได้ทำการ${topic}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${dateText} โดยระบบ`,
          },
        },
      ],
      attachments: [
        {
          color: borderColor,
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ทั้งหมด:* ${task.total}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*สำเร็จ:* ${task.success}`,
              },
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*ล้มเหลว:* ${task.error}`,
              },
            },
          ],
        },
      ],
    };

    await this.sendNotification(notifyPayload, WebhookNotificationChannelEnum.AUTO);
  }
}
