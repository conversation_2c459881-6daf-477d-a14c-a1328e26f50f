import { Inject, Injectable } from '@nestjs/common';
import { AxiosRequestConfig } from 'axios';
import qs from 'qs';

import { AuthenticationDIToken } from '@applications/di/domain/authentication.di';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructureServicesDIToken } from '@applications/di/infrastructures/services';

import { IEnvironments } from '@interfaces/configs/environment.interface';
import { IAuthenticationMemoryCachedRepository } from '@interfaces/memoryCache/authenticationMemoryCached.repository.interface';
import { IHttpService } from '@interfaces/services/httpService.interface';
import { IJwtService } from '@interfaces/services/jwtService.interface';
import { IOAuthService } from '@interfaces/services/oAuth.service.interface';

import { date } from '@domains/utils/date.util';

@Injectable()
export class OAuthService implements IOAuthService {
  constructor(
    @Inject(InfrastructuresConfigDIToken.Environment)
    private readonly environment: IEnvironments,
    @Inject(AuthenticationDIToken.AuthenticationMemoryCachedRepository)
    private readonly authenticationMemoryCachedRepository: IAuthenticationMemoryCachedRepository,
    @Inject(InfrastructureServicesDIToken.JwtService)
    private readonly jwtService: IJwtService,
    @Inject(InfrastructureServicesDIToken.HttpService)
    private readonly httpService: IHttpService,
  ) {}

  async getIDSAccessToken(): Promise<string> {
    const cachedToken = await this.authenticationMemoryCachedRepository.getWithoutPrefix('token');

    if (cachedToken && !this.shouldRequestNewAccessToken(cachedToken)) {
      return cachedToken;
    }

    return this.requestNewAccessToken();
  }

  private shouldRequestNewAccessToken(token: string): boolean {
    const tokenInfo = this.jwtService.decodeJwt(token);
    if (tokenInfo) {
      const { exp } = tokenInfo;
      const expiryTime = date(String(exp));
      const isAboutToExpire = date().add(this.environment.advanceTokenRequestTime, 'second').isAfter(expiryTime);
      return isAboutToExpire;
    }
    return true;
  }

  private async requestNewAccessToken() {
    const data = qs.stringify({
      grant_type: 'client_credentials',
      scope: this.environment.asScope,
    });

    const config: AxiosRequestConfig = {
      auth: {
        username: this.environment.asClientId,
        password: this.environment.asSecret,
      },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    };

    const response = await this.httpService.post(`${this.environment.asURI}/connect/token`, data, config);

    const { access_token } = response;

    await this.authenticationMemoryCachedRepository.setWithoutPrefix('token', access_token);

    return access_token;
  }
}
