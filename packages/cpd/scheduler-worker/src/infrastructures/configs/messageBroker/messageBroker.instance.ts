import { Nullable, Optional } from '@iso/constants/commonTypes';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { connect as ampConnection } from 'amqplib';
import isNumber from 'lodash/isNumber';
import ms from 'ms';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { BrokerChannelParams, BrokerConnectionParams } from '@constants/types/infrastructures/messageBroker.type';

import {
  IMessageBrokerInstance,
  IMessageBrokerEventEmitter,
} from '@interfaces/infrastructures/configs/messageBroker.interface';

@Injectable()
export class MessageBrokerInstance implements OnModuleInit, IMessageBrokerInstance {
  private _connection: BrokerConnectionParams;
  private _channel: BrokerChannelParams;
  private _retryConnection = 0;

  private delayTime: number;
  private maxRetryConnection: Nullable<number>;

  constructor(
    private readonly amqpURI: string,
    private readonly connectEventName: string,
    private readonly messageBrokerEventEmitter: IMessageBrokerEventEmitter,
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    this.delayTime = ms('5s');
    this.maxRetryConnection = 5;

    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect() {
    try {
      this._retryConnection += 1;
      this._connection = await ampConnection(`${this.amqpURI}?heartbeat=${60}`);
      this._channel = await this._connection.createChannel();

      this.logger.log('Connected to RabbitMQ');

      this._retryConnection = 0;
      this.messageBrokerEventEmitter.sendEventConnection(this.connectEventName);

      this._connection.on('error', async (err) => {
        this.logger.error('RabbitMQ connection error', err);
        await this.reconnect();
      });

      this._connection.on('close', async () => {
        this.logger.warn('RabbitMQ connection closed');
        await this.reconnect();
      });
    } catch (error) {
      this.logger.error('Failed to connect to RabbitMQ', error);
      setTimeout(() => this.reconnect(), this.delayTime); // Retry connection after 5 seconds
    }
  }

  async reconnect() {
    this.logger.log('Attempting to reconnect to RabbitMQ...');
    await this.disconnect();

    if (isNumber(this.maxRetryConnection) && this._retryConnection > this.maxRetryConnection) return;
    setTimeout(() => this.connect(), this.delayTime);
  }

  private async disconnect() {
    try {
      await this._channel?.close();
      await this._connection?.close();
      this.logger.log('Disconnected from RabbitMQ');
    } catch (error) {
      this.logger.error('Failed to disconnect from RabbitMQ', error);
    }
  }

  get channel(): Optional<BrokerChannelParams> {
    return this._channel;
  }
}
