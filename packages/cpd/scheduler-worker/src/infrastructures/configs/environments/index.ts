import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { IEnvironments } from '@interfaces/infrastructures/configs/environment.interface';

@Injectable()
export class Environments implements IEnvironments {
  constructor(private readonly configService: ConfigService) {}

  get appName(): string {
    return 'Scheduler-Worker';
  }

  get port(): number {
    const _port = this.configService.get('PORT') || 5000;
    return Number(_port);
  }

  get dbURI(): string {
    return this.configService.getOrThrow('DB_URI');
  }

  get dbName(): string {
    return this.configService.getOrThrow('DB_NAME');
  }

  get amqpURI(): string {
    return this.configService.getOrThrow('AMQP_URI');
  }

  get amqpNotificationURI(): string {
    return this.configService.getOrThrow('AMQP_NOTIFICATION_URI');
  }

  get awsAccessKeyId(): string {
    return this.configService.getOrThrow('AWS_ACCESS_KEY_ID');
  }

  get awsSecretAccessKey(): string {
    return this.configService.getOrThrow('AWS_SECRET_ACCESS_KEY');
  }

  get awsRegion(): string {
    return this.configService.getOrThrow('AWS_REGION');
  }

  get awsUploadBucketName(): string {
    return this.configService.getOrThrow('S3_UPLOAD_BUCKET');
  }

  get redisURL(): string {
    return this.configService.getOrThrow('REDIS_URL');
  }

  get certificateEndpoint(): string {
    return this.configService.getOrThrow('CERTIFICATE_API_ENDPOINT');
  }

  get lmsApiEndpoint(): string {
    return this.configService.getOrThrow('LMS_API_ENDPOINT');
  }

  // Cron JOB
  get cronAutoApproveEnrollment(): string {
    return this.configService.get('CRON_AUTO_APPROVE_ENROLLMENT');
  }

  get cronEnrollmentExpired(): string {
    return this.configService.get('CRON_ENROLLMENT_EXPIRED');
  }

  get cronBulkRemoveCourseItemProgressHistory(): string {
    return this.configService.get('CRON_BULK_REMOVE_COURSE_ITEM_PROGRESS_HISTORY');
  }

  get cronAutoEnrollment(): string {
    return this.configService.get('CRON_AUTO_ENROLLMENT');
  }

  get cronBulkRemovePreEnrollmentTransactionStatusError(): string {
    return this.configService.get('CRON_BULK_REMOVE_PRE_ENROLLMENT_TRANSACTION_STATUS_ERROR');
  }

  get cronBulkUpdateRetailOrder(): string {
    return this.configService.get('CRON_BULK_UPDATE_RETAIL_ORDER');
  }

  get cronSftpImportUsers(): string {
    return this.configService.get('CRON_SFTP_IMPORT_USERS');
  }

  get cronUpdateUserInUserGroupByCondition(): string {
    return this.configService.get('CRON_UPDATE_USER_IN_USERGROUP_BY_CONDITION');
  }

  get cronKnowledgeContentInteractionHistory(): string {
    return this.configService.get('CRON_KNOWLEDGE_CONTENT_INTERACTION_HISTORY');
  }

  get cronSummaryTsiQuizScore(): string {
    return this.configService.get('CRON_SUMMARY_TSI_QUIZ_SCORE');
  }

  get cronUpdateStatusClassroom(): string {
    return this.configService.get('CRON_UPDATE_STATUS_CLASSROOM');
  }

  get cronPromoteNotification(): string {
    return this.configService.getOrThrow('CRON_PROMOTE_NOTIFICATION');
  }

  get cronAutoEnrollmentAchievement(): string {
    return this.configService.getOrThrow('CRON_AUTO_ENROLLMENT_ACHIEVEMENT');
  }

  get cronPlanPackageLicenseExpired(): string {
    return this.configService.getOrThrow('CRON_PLAN_PACKAGE_LICENSE_EXPIRED');
  }

  get webhookBotNotificationUrl(): string {
    return this.configService.getOrThrow('SLACK_WEBHOOK_URL');
  }

  get webhookAutoNotificationUrl(): string {
    return this.configService.getOrThrow('SLACK_WEBHOOK_AUTO_URL');
  }

  get clientProtocol(): string {
    return this.configService.getOrThrow('CLIENT_PROTOCOL');
  }

  get s3BucketUrl(): string {
    return this.configService.getOrThrow('S3_BUCKET_URL');
  }

  get asURI(): string {
    return this.configService.getOrThrow('AS_URI');
  }

  get asClientId(): string {
    return this.configService.getOrThrow('AS_CLIENT_ID');
  }

  get asSecret(): string {
    return this.configService.getOrThrow('AS_SECRET');
  }

  get asScope(): string {
    return this.configService.getOrThrow('AS_SCOPE');
  }

  get advanceTokenRequestTime(): number {
    return +this.configService.getOrThrow<number>('ADVANCE_TOKEN_REQUEST_TIME');
  }

  get renewTokenSecondsBeforeExpires(): number {
    return +this.configService.getOrThrow<number>('RENEW_TOKEN_SECONDS_BEFORE_EXPIRES');
  }

  get basicAuthenticationUsername(): string {
    return this.configService.getOrThrow('BASIC_AUTHENTICATION_USERNAME');
  }

  get basicAuthenticationPassword(): string {
    return this.configService.getOrThrow('BASIC_AUTHENTICATION_PASSWORD');
  }

  get retailApi(): string {
    return this.configService.getOrThrow('RETAIL_API');
  }
}
