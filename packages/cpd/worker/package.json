{"name": "cpd-worker", "version": "1.0.0", "main": "index.js", "license": "SkillLane", "private": true, "packageManager": "pnpm@9.12.0", "scripts": {"clean": "rimraf node_modules", "clean:build": "<PERSON><PERSON><PERSON> dist", "dev": "NODE_ENV=dev concurrently \"npm run watch-compile\" \"npm run watch-dev\"", "release": "NODE_ENV=release concurrently \"npm run watch-compile\" \"npm run watch-dev\"", "master": "NODE_ENV=master concurrently \"npm run watch-compile\" \"npm run watch-dev\"", "start:prod": "node dist/index.js", "test": "jest --bail --silent", "test-watch": "jest --watch", "test-cov": "jest --coverage", "test-pre-merge": "jest --bail --silent --maxWorkers=20%", "build": "swc src --out-dir dist --strip-leading-paths", "watch-compile": "swc src -w --out-dir dist --strip-leading-paths", "watch-dev": "nodemon --delay 100ms --watch \"dist/**/*\" -e js ./dist/index.js", "format": "prettier --write --config ./node_modules/@lms/eslint-config/.prettierrc.json \"src/**/*.js\""}, "dependencies": {"@aws-sdk/client-s3": "3.758.0", "@breejs/later": "^4.2.0", "@iso/constants": "workspace:*", "@iso/email": "workspace:*", "@iso/helpers": "workspace:*", "@iso/lms": "workspace:*", "@sendgrid/mail": "^7.2.2", "@slack/webhook": "6.0.0", "amqplib": "^0.5.6", "awilix": "^4.2.6", "axios": "1.7.7", "axios-retry": "3.2.5", "bcrypt": "5.1.1", "cron": "3.2.1", "cron-parser": "^4.9.0", "csv-parser": "^3.0.0", "dayjs": "1.11.10", "dd-trace": "5.24.0", "dotenv": "^8.2.0", "ejs": "^3.1.9", "exceljs": "^4.3.0", "http-status-codes": "^2.2.0", "ioredis": "^5.0.4", "jsonwebtoken": "^8.5.1", "mandrill-api": "^1.0.45", "module-alias": "^2.2.2", "mongodb": "3.7.3", "ms": "2.1.3", "node-xlsx": "^0.16.1", "nodemon": "^2.0.16", "nunjucks": "^3.2.4", "openpgp": "^5.9.0", "qs": "6.9.4", "randomstring": "1.3.1", "ssh2-sftp-client": "^9.1.0", "thai-id-card": "0.0.15", "uuid": "^8.1.0", "validator": "^12.2.0", "winston": "3.3.3"}, "devDependencies": {"@babel/preset-env": "7.26.0", "@lms/eslint-config": "workspace:*", "concurrently": "7.3.0", "jest": "29.5.0"}, "resolutions": {"triple-beam": "1.3.0"}}