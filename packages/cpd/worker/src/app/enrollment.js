import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { getPublishDateTimeOfNotification } from '@iso/helpers/dateUtils';
import {
  buildPreEnrollmentToEnrollmentSuccessNotificationMessage,
  buildEnrollmentRequestDeductAttachmentNotificationMessage,
} from '@iso/helpers/userNotification';
import {
  CourseObjectiveTypeEnum,
  ContentProviderTypeEnum,
  CourseEnrollTypeEnum,
  ExternalContentTypeEnum,
} from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { EnrollmentStatusEnum, EnrollTypeEnum } from '@iso/lms/enums/enrollment.enum';
import {
  PreEnrollmentTransactionStatusEnum,
  PreEnrollmentTransactionPaymentTypeEnum,
  PreEnrollmentTransactionEnrollByEnum,
} from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { ProductTypeEnum, ProductSKUDistributionChannelEnum } from '@iso/lms/enums/productSKU.enum';
import {
  getAvailablePeriodLicenseByUser,
  checkCompulsoryEnrollableInAvailablePeriodLicense,
  checkHavePlanPackageLicenseAvailableToday,
} from '@iso/lms/services/planPackageLicense.service';
import { cloneDeep, compact, isArray, isNil } from 'lodash';

import { createEnrollment, createEnrollmentCertificate } from '@app/domains/enrollment.domain';
import { createEnrollmentAttachmentDeduct } from '@app/services/enrollmentAttachment.service';
import { creditManagement } from '@app/services/enrollmentCredit.service';
import { DBCollection } from '@constants/dbCollection';
import { BusinessType } from '@constants/enrollment';
import { getErrorMessage } from '@constants/errorMessage';
import { GenericStatusCodes, BulkStatus } from '@constants/generic';
import {
  LearningPathContentProgressRegisterFromEnum,
  LearningPathContentProgressStatusEnum,
  LearningPathEnrollmentStatusEnum,
} from '@constants/learningPathEnrollment';
import { OrganizationSchedulerTypeEnum } from '@constants/organizationScheduler';
import { buildRegistrationModel } from '@domains/registration.domain';
import { buildUserNotificationModel } from '@domains/userNotification.domain';
import { date } from '@infrastructure/dateUtils';
import { AppError } from '@infrastructure/utilities/AppError';
import { runTransaction } from '@infrastructure/utilities/dbSession';
import {
  checkReEnrollableRegularCourse,
  checkUpdateContentProgressLearningPathEnrollment,
  getEnrollmentIdFromLearningPathEnrollment,
} from '@infrastructure/utilities/helper';

module.exports = ({
  db,
  dbClient,
  notificationService,
  logger,
  config,
  roundService,
  slackNotificationService,
  retailService,
  preEnrollmentTransactionService,
  learningPathEnrollmentService,
  enrollmentService,
  courseService,
  organizationSchedulerService,
  courseItemCriteriaConfigService,
  partService,
  productSKUCourseService,
  courseMarketplaceService,
  planService,
  planPackageService,
  planPackageLicenseService,
}) => {
  const delay = (ms) => new Promise((res) => setTimeout(res, ms));

  const bulkEnrollUser = async (msg, channel, { organization }) => {
    const { domain, id: organizationId, fqdn } = organization;
    const { jobId } = msg.properties.headers;
    const {
      payload,
      originalPayload,
      raw,
      preAssignContentId: preAssignContentIdFromBulk = null,
      isAutoEnroll,
    } = JSON.parse(msg.content.toString());

    const { preAssignContentId: preAssignContentIdFromPreEnrollmentTransaction = null } = payload;

    const preAssignContentId = isAutoEnroll
      ? preAssignContentIdFromPreEnrollmentTransaction
      : preAssignContentIdFromBulk;

    await delay(300);

    logger.info(`Start processing the job ID ${jobId} of type ENROLLMENT`);
    const { messageCount } = await channel.assertQueue(domain);
    const session = dbClient.startSession();
    const { preEnrollmentTransactionId } = payload;

    try {
      await runTransaction(executeEnrollmentTransaction, session, [
        { ...payload, preAssignContentId },
        session,
        organizationId,
        isAutoEnroll,
      ]);

      if (preEnrollmentTransactionId) {
        const preEnrollmentTransaction = await db
          .collection(DBCollection.pre_enrollment_transactions)
          .findOne({ id: preEnrollmentTransactionId });

        await db.collection(DBCollection.pre_enrollment_transactions).updateOne(
          {
            id: preEnrollmentTransactionId,
          },
          {
            $set: {
              'operationExecute.isEnrollment': true,
              status: PreEnrollmentTransactionStatusEnum.APPLIED,
              'contentItems.$[].isHavingCost': true,
            },
          },
        );

        if (
          preEnrollmentTransaction.paymentType === PreEnrollmentTransactionPaymentTypeEnum.RETAIL &&
          preEnrollmentTransaction.businessType === BusinessType.B2C
        ) {
          const response = await retailService.updateRetailOrder(preEnrollmentTransactionId);
          if (response.status === 200) {
            await preEnrollmentTransactionService.updatePreEnrollmentTransactionIsUpdatedRetailOrder(
              preEnrollmentTransactionId,
            );
          }
        }
      }
      channel.ack(msg);
    } catch (error) {
      logger.error(`Error processing the job with the ID of ${jobId}, errorMessage: ${error.message}`);

      if (preEnrollmentTransactionId) {
        await db.collection(DBCollection.pre_enrollment_transactions).updateOne(
          {
            id: preEnrollmentTransactionId,
          },
          {
            $set: {
              'operationExecute.isEnrollment': true,
              status: PreEnrollmentTransactionStatusEnum.FAILED_TO_APPLY,
            },
          },
        );
      }

      const updateJob = {
        $push: {
          rawPayloads: [...(isArray(raw) ? raw : []), error.message],
          errorList: {
            originalPayload: {
              ...originalPayload,
              preEnrollmentId: preEnrollmentTransactionId || '',
            },
            message: error.message,
          },
        },
      };
      await db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        updateJob,
      );

      channel.nack(msg, false, false);
    } finally {
      if (messageCount === 0) {
        const job = await db.collection(DBCollection.jobs).findOne({
          guid: jobId,
        });
        if (job) {
          const { errorList } = job;
          await db.collection(DBCollection.jobs).updateOne(
            {
              guid: jobId,
            },
            {
              $set: {
                status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
                updatedAt: date().toDate(),
              },
            },
          );

          const url = `${config.CLIENT_PROTOCOL}://${domain}.${fqdn}/admin/setting/jobs/${job.guid}`;
          slackNotificationService.notiOperationExecute(
            job.operationType,
            job.total,
            job.errorList.length,
            job.userId,
            url,
            domain,
          );
        } else {
          logger.error(`Job id ${jobId} not found`);
        }
      }
      session.endSession();
      logger.info('End Session');
    }
  };

  const sendEnrollmentEmail = async (user, course, enrollment, organization, thumbnailUrl, createBy, sendAt) => {
    const courseItemCriteriaConfigs = await courseItemCriteriaConfigService.find({
      courseVersionId: course.courseVersion.id,
    });

    let parts = [];
    if (course.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      parts = await productSKUCourseService.getPartByCourseId(course.courseVersion.courseId, course.courseVersion.id);
    } else {
      parts = await partService.getPartByCourseId(course.courseVersion.id);
    }

    parts = courseService.combineCourseItemCriteriaConfigToPart(parts, courseItemCriteriaConfigs);
    parts = courseService.removeUnavailableContent(parts);

    const mailPayload = notificationService.createEnrollmentEmail(
      user.email,
      {
        user,
        course,
        enrollment,
        thumbnailUrl,
        createBy,
        parts,
      },
      organization,
    );

    if (sendAt) {
      mailPayload.sendAt = sendAt;
    }

    notificationService.sendEmail(mailPayload);
  };

  const sendNotificationInApplication = async (organizationId, message, sendAt) => {
    notificationService.sendUserNotificationInApplication(message, organizationId, sendAt);
  };

  const insertOperationEnrollmentCertificate = (
    enrollData,
    certificateObj,
    enrollCertOps,
    logoImageUrl,
    issuedBy,
    isSentEmailOnExpiredDate,
  ) => {
    const { id: enrollmentId } = enrollData;
    const result = createEnrollmentCertificate(
      enrollmentId,
      logoImageUrl,
      issuedBy,
      isSentEmailOnExpiredDate,
      certificateObj,
    );

    enrollCertOps.push({
      insertOne: {
        document: {
          ...result,
        },
      },
    });

    return result;
  };

  const getRound = async (roundId) => {
    const round = await roundService.findOne({
      id: roundId,
    });

    if (!round) {
      throw new AppError(GenericStatusCodes.ROUND_NOT_FOUND, getErrorMessage(GenericStatusCodes.ROUND_NOT_FOUND));
    }

    return round;
  };

  const getRoundExpireDate = (roundDate, expiryDay) => {
    const expireDate = date(roundDate).endOf('day').add(expiryDay, 'day').toDate();

    const isOverToday = expireDate > date().toDate();
    if (!isOverToday) {
      throw new AppError(
        GenericStatusCodes.COURSE_EXPIRED_NOT_OVER_TODAY,
        getErrorMessage(GenericStatusCodes.COURSE_EXPIRED_NOT_OVER_TODAY),
      );
    }

    return expireDate;
  };

  const enrollmentAttachmentDeductDocument = async (enrollmentId, refNameAttachmentList, applicantType) => {
    let textRefName = '';
    if (refNameAttachmentList.length > 0) {
      textRefName = refNameAttachmentList.join(', ');
    }

    const resCounter = await db.collection(DBCollection.transaction_counters).findOneAndUpdate(
      {
        name: 'counter',
      },
      {
        $inc: {
          deductRequestNo: 1,
        },
      },
      {
        returnOriginal: false,
      },
    );

    const keyDeductRequestNo = `DEDUCT-${resCounter.value.deductRequestNo}`;

    const enrollmentAttachment = createEnrollmentAttachmentDeduct(
      enrollmentId,
      keyDeductRequestNo,
      applicantType,
      textRefName,
    );

    return enrollmentAttachment;
  };

  const buildEnrollmentSuccessInAppNotificationMessageAndModel = ({
    userId,
    organizationId,
    preEnrollmentTransactionId,
    course,
    enrollData,
    round,
    createBy,
    publishedAt,
  }) => {
    let userNotification = null;
    let message = '';
    const isPreEnrollment = !round;

    if (isPreEnrollment) {
      message = buildPreEnrollmentToEnrollmentSuccessNotificationMessage({
        createBy,
        contentName: course.courseVersion?.name,
        expiredAt: enrollData.expiredAt,
      });

      userNotification = buildUserNotificationModel(
        userId,
        organizationId,
        UserNotificationTypeEnum.ENROLLMENT_SUCCESS,
        {
          mediaId: course.thumbnailMediaId,
          message,
          url: {
            code: course.url,
            enrollmentId: enrollData.id,
            preEnrollmentId: preEnrollmentTransactionId || null,
          },
        },
        publishedAt,
      );
    } else {
      message = buildPreEnrollmentToEnrollmentSuccessNotificationMessage({
        createBy,
        contentName: course.courseVersion?.name,
        roundDate: round.roundDate,
        expiryDay: course.courseVersion?.expiryDay,
      });
      userNotification = buildUserNotificationModel(
        userId,
        organizationId,
        UserNotificationTypeEnum.ENROLLMENT_WITH_ROUND_SUCCESS,
        {
          mediaId: course.thumbnailMediaId,
          message,
          url: {
            code: course.url,
            enrollmentId: enrollData.id,
            preEnrollmentId: preEnrollmentTransactionId || null,
          },
        },
        publishedAt,
      );
    }

    return {
      userNotification,
      message,
    };
  };

  const buildRequestDeductInAppNotificationMessageAndModel = ({
    userId,
    organizationId,
    preEnrollmentTransactionId,
    course,
    enrollmentId,
    publishedAt,
  }) => {
    const message = buildEnrollmentRequestDeductAttachmentNotificationMessage({
      contentName: course.courseVersion?.name,
    });
    const userNotification = buildUserNotificationModel(
      userId,
      organizationId,
      UserNotificationTypeEnum.ENROLLMENT_REQUEST_DEDUCT_ATTACHMENT,
      {
        mediaId: course.thumbnailMediaId,
        message,
        url: {
          code: course.url,
          enrollmentId,
          preEnrollmentId: preEnrollmentTransactionId || null,
        },
      },
      publishedAt,
    );

    return {
      userNotification,
      message,
    };
  };

  const executeEnrollmentTransaction = async (payload, session, organizationId, isAutoEnroll = false) => {
    const {
      enrollment,
      enrollmentCertificates,
      invoice,
      roundId,
      preEnrollmentTransactionId,
      customerPartnerId,
      username,
      userId: userIdPayload,
      isBulkWithCustomer,
      preAssignContentId,
    } = payload;
    const { business, customerCode } = enrollment;

    const isAssignCourse = !isAutoEnroll && !!preAssignContentId;
    const isSendSchedulerNotification = !!preEnrollmentTransactionId && !isAssignCourse;
    const organization = await db.collection(DBCollection.organizations).findOne({
      id: organizationId,
    });

    if (!organization) {
      throw new AppError(GenericStatusCodes.ORGANIZATION_NOT_FOUND, 'organization not found');
    }

    let user = null;
    if (userIdPayload) {
      user = await db.collection(DBCollection.users).findOne({
        guid: userIdPayload,
      });
    } else {
      user = await db.collection(DBCollection.users).findOne({
        username,
        organizationId,
      });
    }

    if (!user) {
      throw new AppError(GenericStatusCodes.USER_NOT_FOUND, getErrorMessage(GenericStatusCodes.USER_NOT_FOUND));
    }

    const userId = user.guid;
    logger.info(`User found with the ID of ${userId}`);

    const { courseId } = enrollment;

    logger.info(`courseId found with the ID of ${courseId}`);

    const [course] = await db
      .collection(DBCollection.courses)
      .aggregate([
        {
          $match: {
            id: courseId,
            isEnabled: true,
          },
        },
        {
          $lookup: {
            from: 'course-versions',
            let: { courseId: '$id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$courseId', '$$courseId'] },
                      { $eq: ['$status', CourseVersionStatusEnum.PUBLISHED] },
                    ],
                  },
                },
              },
            ],
            as: 'courseVersion',
          },
        },
        {
          $unwind: '$courseVersion',
        },
      ])
      .toArray();

    if (!course) {
      throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
    }

    let preEnrollmentTransaction = null;
    const isFromPreEnrollment = !isNil(preEnrollmentTransactionId);
    if (isFromPreEnrollment) {
      preEnrollmentTransaction = await db.collection(DBCollection.pre_enrollment_transactions).findOne({
        id: preEnrollmentTransactionId,
      });
    }

    const enrollType = preEnrollmentTransaction?.enrollType
      ? preEnrollmentTransaction.enrollType
      : enrollment.enrollType;

    let externalContentType = ExternalContentTypeEnum.NONE;
    let productSKU = null;

    if (course.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      const productSKUCourse = await db.collection(DBCollection.product_sku_courses).findOne({
        id: course.productSKUCourseId,
      });

      if (!productSKUCourse) {
        throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
      }

      productSKU = await db.collection(DBCollection.product_skus).findOne({
        id: productSKUCourse.productSKUId,
        isEnabled: true,
        type: ProductTypeEnum.COURSE,
      });

      if (!productSKU) {
        throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
      }

      const isEveryIncludeCourseMarketplace = await courseMarketplaceService.checkEveryIncludeCourseMarketplace(
        organizationId,
        [productSKUCourse.id],
      );

      const organizationPlanPackageContentList = await planService.findPlanPackageContent(organizationId);
      const organizationPlanPackageContentIds = compact(
        organizationPlanPackageContentList.map((item) => item?.planPackage?.id),
      );

      const planPackageContentCustomActive = await planPackageService.findPlanPackageContentCustomActive(
        productSKUCourse.id,
        organizationPlanPackageContentIds,
      );
      const planPackageContentSubscriptionActive = await planPackageService.findPlanPackageContentSubscriptionActive(
        productSKUCourse.id,
        organizationPlanPackageContentIds,
      );

      const planPackageContentActiveList = planPackageContentCustomActive.concat(planPackageContentSubscriptionActive);

      let isExistingCourseInMarketplace = isEveryIncludeCourseMarketplace;
      let isExistingCourseInPlanPackage = planPackageContentActiveList.length > 0;
      if (isFromPreEnrollment) {
        isExistingCourseInMarketplace =
          isExistingCourseInMarketplace &&
          preEnrollmentTransaction?.externalContentType === ExternalContentTypeEnum.MARKETPLACE;

        isExistingCourseInPlanPackage =
          isExistingCourseInPlanPackage &&
          preEnrollmentTransaction?.externalContentType === ExternalContentTypeEnum.PLAN_PACKAGE;
      }

      if (isExistingCourseInPlanPackage) {
        externalContentType = ExternalContentTypeEnum.PLAN_PACKAGE;

        if (isBulkWithCustomer) {
          throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
        }

        if (![EnrollTypeEnum.VOLUNTARY, EnrollTypeEnum.COMPULSORY].includes(enrollType)) {
          throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
        }

        const planPackageContentIds = planPackageContentActiveList.map((item) => item.id);

        const planPackageContentLicenseSOPendingActive =
          await planPackageLicenseService.findPlanPackageLicenseSOPendingActive(userId, planPackageContentIds);

        const planPackageContentLicenseSOApprovedActive =
          await planPackageLicenseService.findPlanPackageLicenseSOApprovedActive(userId, planPackageContentIds);

        const planPackageContentLicenseActive = planPackageContentLicenseSOPendingActive.concat(
          planPackageContentLicenseSOApprovedActive,
        );

        if (!planPackageContentLicenseActive.length) {
          throw new AppError(
            GenericStatusCodes.PLAN_PACKAGE_LICENSE_NOT_FOUND,
            getErrorMessage(GenericStatusCodes.PLAN_PACKAGE_LICENSE_NOT_FOUND),
          );
        }

        const isHavePlanPackageLicenseAvailableToday = checkHavePlanPackageLicenseAvailableToday(
          planPackageContentLicenseActive,
        );
        if (!isHavePlanPackageLicenseAvailableToday) {
          throw new AppError(
            GenericStatusCodes.PLAN_PACKAGE_LICENSE_NOT_FOUND,
            getErrorMessage(GenericStatusCodes.PLAN_PACKAGE_LICENSE_NOT_FOUND),
          );
        }

        let isEnrollableInAvailablePeriodLicense = isHavePlanPackageLicenseAvailableToday;

        if (enrollType === EnrollTypeEnum.COMPULSORY && course.courseVersion?.expiryDay) {
          const enrollmentCreatedAt = date().toDate();
          const enrollmentExpiredAt = date(enrollmentCreatedAt)
            .endOf('day')
            .add(course.courseVersion.expiryDay, 'day')
            .toDate();

          const availablePeriodLicenseByUserList = getAvailablePeriodLicenseByUser(
            planPackageContentLicenseActive,
            userId,
          );

          isEnrollableInAvailablePeriodLicense = checkCompulsoryEnrollableInAvailablePeriodLicense(
            enrollmentCreatedAt,
            enrollmentExpiredAt,
            availablePeriodLicenseByUserList,
          );
        }

        if (!isEnrollableInAvailablePeriodLicense) {
          throw new AppError(
            GenericStatusCodes.PLAN_PACKAGE_LICENSE_NOT_ENOUGH,
            getErrorMessage(GenericStatusCodes.PLAN_PACKAGE_LICENSE_NOT_ENOUGH),
          );
        }
      } else if (isExistingCourseInMarketplace) {
        externalContentType = ExternalContentTypeEnum.MARKETPLACE;

        const productSKUDistribution = productSKU.productSKUDistributions.find(
          (val) => val.channel === ProductSKUDistributionChannelEnum.CPD,
        );

        if (!productSKUDistribution) {
          throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
        }

        // manual enroll error but pre enrollment open api not error
        if (business === BusinessType.B2B && !isBulkWithCustomer && !isFromPreEnrollment) {
          throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
        }
      } else {
        throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
      }
    }

    if (
      course.contentProviderType === ContentProviderTypeEnum.SELF &&
      business === BusinessType.B2B &&
      isBulkWithCustomer
    ) {
      // bulk enrollment with b2b customer can not contentProviderType self
      throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
    }

    let round = null;
    let expireDate = null;
    let isDuplicateEnrollment = false;
    let updateStatusPassedToCompletedEnrollmentId = null;

    const isCheckReEnrollableTypeImmediate = course.enrollType === CourseEnrollTypeEnum.IMMEDIATE;
    if (isCheckReEnrollableTypeImmediate) {
      const [existedEnrollment] = await db
        .collection(DBCollection.enrollments)
        .aggregate([
          {
            $match: { userId, courseId, organizationId },
          },
          {
            $sort: {
              createdAt: -1,
            },
          },
        ])
        .toArray();

      if (existedEnrollment) {
        const isReEnrollable = checkReEnrollableRegularCourse({
          enrollmentStatus: existedEnrollment.status,
          enrollmentPassedAt: existedEnrollment.passedAt,
          enrollmentExpiredAt: existedEnrollment.expiredAt,
          isReEnrollEnabled: course.isReEnrollEnabled,
          isReEnrollExpireEnabled: course.isReEnrollExpireEnabled,
          reEnrollDay: course.reEnrollDay,
          reEnrollExpireDay: course.reEnrollExpireDay,
        });

        if (!isReEnrollable) {
          isDuplicateEnrollment = true;
        }

        if (isReEnrollable && existedEnrollment.status === EnrollmentStatusEnum.PASSED) {
          updateStatusPassedToCompletedEnrollmentId = existedEnrollment.id;
        }
      }

      if (course.courseVersion?.expiryDay) {
        const enrollmentCreatedAt = date(enrollment.createdAt).toDate();
        expireDate = date(enrollmentCreatedAt).endOf('day').add(course.courseVersion?.expiryDay, 'day').toDate();
      }
    }

    if (isDuplicateEnrollment) {
      throw new AppError(
        GenericStatusCodes.DUPLICATED_ENROLLMENT,
        getErrorMessage(GenericStatusCodes.DUPLICATED_ENROLLMENT),
      );
    }

    const isCheckReEnrollableTypePreEnroll = course.enrollType === CourseEnrollTypeEnum.PRE_ENROLL;
    if (isCheckReEnrollableTypePreEnroll) {
      const [existedEnrollment] = await db
        .collection(DBCollection.enrollments)
        .aggregate([
          {
            $match: { userId, courseId, roundId, organizationId },
          },
          {
            $sort: {
              createdAt: -1,
            },
          },
        ])
        .toArray();

      if (course.objectiveType === CourseObjectiveTypeEnum.REGULAR && existedEnrollment) {
        const isReEnrollable = checkReEnrollableRegularCourse({
          enrollmentStatus: existedEnrollment.status,
          enrollmentPassedAt: existedEnrollment.passedAt,
          enrollmentExpiredAt: existedEnrollment.expiredAt,
          isReEnrollEnabled: course.isReEnrollEnabled,
          isReEnrollExpireEnabled: course.isReEnrollExpireEnabled,
          reEnrollDay: course.reEnrollDay,
          reEnrollExpireDay: course.reEnrollExpireDay,
        });

        if (!isReEnrollable) {
          isDuplicateEnrollment = true;
        }

        if (isReEnrollable && existedEnrollment.status === EnrollmentStatusEnum.PASSED) {
          updateStatusPassedToCompletedEnrollmentId = existedEnrollment.id;
        }
      }

      if (course.objectiveType === CourseObjectiveTypeEnum.TRAINING && existedEnrollment) {
        isDuplicateEnrollment = true;
      }

      logger.info('Checking Round Available');
      round = await getRound(roundId);

      if (course.courseVersion?.expiryDay) {
        logger.info('Checking if expiry date of course is over current day');
        expireDate = getRoundExpireDate(round.roundDate, course.courseVersion?.expiryDay);
      }
    }

    if (isDuplicateEnrollment) {
      throw new AppError(
        GenericStatusCodes.DUPLICATED_ENROLLMENT_SAME_ROUND,
        getErrorMessage(GenericStatusCodes.DUPLICATED_ENROLLMENT_SAME_ROUND),
      );
    }

    if (updateStatusPassedToCompletedEnrollmentId) {
      logger.info(`update enrollment status ${EnrollmentStatusEnum.PASSED} to ${EnrollmentStatusEnum.COMPLETED}`);
      await db.collection(DBCollection.enrollments).updateOne(
        {
          id: updateStatusPassedToCompletedEnrollmentId,
          status: EnrollmentStatusEnum.PASSED,
        },
        {
          $set: {
            status: EnrollmentStatusEnum.COMPLETED,
          },
        },
        {
          session,
        },
      );
    }

    logger.info('Checking business and customer code');
    let customer = null;
    if (business === BusinessType.B2B && isBulkWithCustomer) {
      customer = await db.collection(DBCollection.customers).findOne({
        customerCode,
        deletedAt: null,
      });

      if (!customer) {
        throw new AppError(
          GenericStatusCodes.CUSTOMER_NOT_FOUND,
          getErrorMessage(GenericStatusCodes.CUSTOMER_NOT_FOUND),
        );
      }

      logger.info(`Customer found with the name of ${customer.customerName}`);
    }

    logger.info('built enrollment document');
    const { courseVersion } = course;
    const enrollData = createEnrollment(enrollment, {
      business,
      customerCode: customer ? customer.customerCode : '',
      userId: user.guid,
      courseId,
      courseVersionId: courseVersion.id,
      isCountdownArticle: courseVersion.isCountdownArticle,
      isIdentityVerificationEnabled: courseVersion.isIdentityVerificationEnabled,
      roundId: round ? round.id : '',
      preAssignContentId,
      expiredAt: expireDate,
      organizationId,
      externalContentType,
      enrollType,
    });

    logger.info('built enrollment certificates document');

    const certificates = await db
      .collection(DBCollection.course_version_certificate)
      .find({
        courseVersionId: courseVersion.id,
      })
      .toArray();

    const { isMultipleCertificate, isCertificateEnabled, isSentCertificateEmailOnExpiredDate, report } = courseVersion;

    const enrollCertOps = [];
    const refNameAttachmentList = [];

    if (isCertificateEnabled) {
      const certificateLogoImageUrl = '';
      const certificateTextIssuedBy = '';

      if (certificates.length === 0) {
        throw new AppError(GenericStatusCodes.CERT_NOT_FOUND, getErrorMessage(GenericStatusCodes.CERT_NOT_FOUND));
      }

      // Single Cert
      if (!isMultipleCertificate) {
        const certificateObj = certificates[0];
        for (const data of enrollmentCertificates) {
          if (data.refCode) {
            throw new AppError(
              GenericStatusCodes.REF_CODE_DISABLE,
              getErrorMessage(GenericStatusCodes.REF_CODE_DISABLE),
            );
          }

          const enrollmentCertificateDocument = insertOperationEnrollmentCertificate(
            enrollData,
            { ...certificateObj, tsiCode: report.tsiCode, pillarName: report.pillarName },
            enrollCertOps,
            certificateLogoImageUrl,
            certificateTextIssuedBy,
            isSentCertificateEmailOnExpiredDate,
          );

          if (enrollmentCertificateDocument.refName) {
            refNameAttachmentList.push(enrollmentCertificateDocument.refName);
          }
        }
      }
      // Multi Cert
      else {
        for (const data of enrollmentCertificates) {
          if (!data.refCode) {
            throw new AppError(
              GenericStatusCodes.REF_CODE_IS_REQUIRE,
              getErrorMessage(GenericStatusCodes.REF_CODE_IS_REQUIRE),
            );
          }

          const certificateObj = certificates.find((val) => val.refCode === data.refCode);
          if (data.refCode && !certificateObj) {
            throw new AppError(GenericStatusCodes.REF_CODE_NOT_FOUND, 'ไม่พบ ref code ในหลักสูตรนี้');
          }

          const enrollmentCertificateDocument = insertOperationEnrollmentCertificate(
            enrollData,
            { ...certificateObj, tsiCode: report.tsiCode, pillarName: report.pillarName },
            enrollCertOps,
            certificateLogoImageUrl,
            certificateTextIssuedBy,
            isSentCertificateEmailOnExpiredDate,
          );

          if (enrollmentCertificateDocument.refName) {
            refNameAttachmentList.push(enrollmentCertificateDocument.refName);
          }
        }
      }
    } else if (isMultipleCertificate) {
      throw new AppError(GenericStatusCodes.CERT_NOT_FOUND, 'กรุณาเปิด certificate');
    }

    logger.info('Successfully built the document for the enrollment');
    logger.info('Successfully built the document for the enrollment-certificate');
    logger.info('Preparing for insertion ...');

    await db.collection(DBCollection.enrollments).insertOne(enrollData, {
      session,
    });
    logger.info('Successfully enrollments inserted');

    if (enrollCertOps.length > 0) {
      await db.collection(DBCollection.enrollment_certificates).bulkWrite(enrollCertOps, {
        session,
      });
      logger.info('Successfully enrollment-certificates inserted');
    }

    if (course.regulatorInfo?.isRequireDeductDocument) {
      logger.info('Preparing create enrollment-attachments');

      const enrollmentAttachment = await enrollmentAttachmentDeductDocument(
        enrollData.id,
        refNameAttachmentList,
        course.regulatorInfo.applicantType,
      );

      await db.collection(DBCollection.enrollment_attachments).insertOne(enrollmentAttachment, {
        session,
      });

      logger.info('Successfully enrollment-attachments inserted');
    }

    logger.info(`Preparing create registration`);
    const registrationData = buildRegistrationModel(enrollData.id, customerPartnerId);
    await db.collection(DBCollection.registrations).insertOne(registrationData, {
      session,
    });
    logger.info('Successfully registration inserted');

    if (invoice) {
      logger.info(`Preparing credit management`);
      await creditManagement(db, session, logger, productSKU, invoice, registrationData.id);
      logger.info(`Successfully credit management`);
    }

    if (isFromPreEnrollment) {
      logger.info(`Preparing update enrollment regulator report`);
      await db.collection(DBCollection.enrollment_regulator_reports).updateOne(
        {
          preEnrollmentTransactionId,
        },
        {
          $set: {
            registrationId: registrationData.id,
          },
        },
        {
          session,
        },
      );
      logger.info(`Successfully Update enrollment regulator report`);

      logger.info(`Preparing find learning path enrollment of user ${userId} with ${preEnrollmentTransactionId}`);

      const learningPathEnrollment = await db
        .collection(DBCollection.learning_path_enrollments)
        .find({ userId, 'contentProgress.preEnrollmentId': { $in: [preEnrollmentTransactionId] } })
        .toArray();

      logger.info(`Successfully find learning path enrollment of user ${userId}`);

      const isSyncCourseProgressToLearningPathEnrollment = learningPathEnrollment.length;
      if (isSyncCourseProgressToLearningPathEnrollment) {
        logger.info(
          `Preparing update learning path enrollment content progress status ${LearningPathContentProgressStatusEnum.PRE_ENROLL} to ${LearningPathContentProgressStatusEnum.IN_PROGRESS}`,
        );

        const syncCourseProgressOperations = [];

        for (const data of learningPathEnrollment) {
          const { id, status } = data;

          syncCourseProgressOperations.push({
            updateOne: {
              filter: {
                id,
                'contentProgress.preEnrollmentId': preEnrollmentTransactionId,
              },
              update: {
                $set: {
                  'contentProgress.$.enrollmentId': enrollment.id,
                  'contentProgress.$.status': LearningPathContentProgressStatusEnum.IN_PROGRESS,
                  'contentProgress.$.updatedAt': date().toDate(),
                },
              },
            },
          });

          if (status === LearningPathEnrollmentStatusEnum.ASSIGNED) {
            syncCourseProgressOperations.push({
              updateOne: {
                filter: { id },
                update: {
                  $set: {
                    status: LearningPathEnrollmentStatusEnum.IN_PROGRESS,
                  },
                },
              },
            });
          }
        }

        if (syncCourseProgressOperations.length > 0) {
          await db
            .collection(DBCollection.learning_path_enrollments)
            .bulkWrite(syncCourseProgressOperations, { session });
        }

        logger.info(`Successfully update learning path enrollment update status of contentProgress`);
      }
    }

    const isFromManualEnrollment = isNil(preEnrollmentTransactionId);
    if (isFromManualEnrollment) {
      const learningPathEnrollments = await learningPathEnrollmentService.findLearningPathEnrollmentByVersions(
        [userId],
        [courseId],
      );

      const isSyncCourseProgressToLearningPathEnrollment = learningPathEnrollments.length;
      if (isSyncCourseProgressToLearningPathEnrollment) {
        logger.info('| Updating course progress to learning path enrollment');
        const syncCourseProgressOperations = [];
        const dateNow = date().toDate();

        const syncedEnrollmentId = getEnrollmentIdFromLearningPathEnrollment(learningPathEnrollments, userId, courseId);
        for (const learningPathEnrollment of learningPathEnrollments) {
          const { id: learningPathEnrollmentId, contentProgress } = learningPathEnrollment;

          const isUpdateProgressEnrollmentId = !isNil(syncedEnrollmentId);
          if (isUpdateProgressEnrollmentId) {
            const latestEnrollmentUser = await enrollmentService.findOne({ id: syncedEnrollmentId });
            const latestEnrollmentUserStatus = latestEnrollmentUser?.status ?? '';

            const isUpdateContentProgressLearningPathEnrollment = checkUpdateContentProgressLearningPathEnrollment(
              contentProgress,
              courseId,
              latestEnrollmentUserStatus,
            );

            if (!isUpdateContentProgressLearningPathEnrollment) continue;

            const newContentProgress = cloneDeep(contentProgress);
            for (const contentProgressItem of newContentProgress) {
              if (contentProgressItem.enrollmentId === syncedEnrollmentId) {
                contentProgressItem.enrollmentId = enrollment.id;
                contentProgressItem.updatedAt = dateNow;
              }
            }

            syncCourseProgressOperations.push({
              updateOne: {
                filter: { id: learningPathEnrollmentId, userId },
                update: {
                  $set: { contentProgress: newContentProgress },
                },
              },
            });
          }

          const isNewProgressEnrollmentId = isNil(syncedEnrollmentId);
          if (isNewProgressEnrollmentId) {
            const latestEnrollmentUser = await enrollmentService.findLatestEnrollmentUser(userId, courseId);
            const latestEnrollmentUserStatus = latestEnrollmentUser?.status ?? '';

            const isUpdateContentProgressLearningPathEnrollment = checkUpdateContentProgressLearningPathEnrollment(
              contentProgress,
              courseId,
              latestEnrollmentUserStatus,
            );

            if (!isUpdateContentProgressLearningPathEnrollment) continue;

            const newContentProgress = {
              enrollmentId: enrollment.id,
              preEnrollmentId: null,
              courseId,
              status: LearningPathContentProgressStatusEnum.IN_PROGRESS,
              registerFrom: LearningPathContentProgressRegisterFromEnum.COURSE,
              createdAt: dateNow,
              updatedAt: dateNow,
            };

            syncCourseProgressOperations.push({
              updateOne: {
                filter: { id: learningPathEnrollmentId, userId },
                update: {
                  $push: { contentProgress: newContentProgress },
                },
              },
            });
          }
        }

        if (syncCourseProgressOperations.length > 0) {
          await db
            .collection(DBCollection.learning_path_enrollments)
            .bulkWrite(syncCourseProgressOperations, { session });
        }

        logger.info('| Updated course progress to learning path enrollment successfully');
      }
    }

    const thumbnailUrl = course.thumbnailUrl ?? '';
    if (isSendSchedulerNotification) {
      let createBy = '';
      if (preEnrollmentTransaction) {
        createBy =
          preEnrollmentTransaction.enrollBy === PreEnrollmentTransactionEnrollByEnum.ADMIN ? 'ผู้ดูแลระบบ' : '';
      }

      const organizationScheduler = await organizationSchedulerService.findOne({
        type: OrganizationSchedulerTypeEnum.SEND_EMAIL_CREATE_ENROLLMENT,
        organizationId,
      });
      const publishDateTimeNotification = getPublishDateTimeOfNotification(organizationScheduler?.cron);
      await sendEnrollmentEmail(
        user,
        course,
        enrollData,
        organization,
        thumbnailUrl,
        createBy,
        publishDateTimeNotification,
      );

      logger.info('Preparing for build user notification and message');
      const { userNotification, message } = buildEnrollmentSuccessInAppNotificationMessageAndModel({
        userId,
        organizationId,
        preEnrollmentTransactionId,
        course,
        enrollData,
        round,
        publishedAt: publishDateTimeNotification,
      });
      logger.info('Build user notification and message successfully');

      await db.collection(DBCollection.user_notifications).insertOne(userNotification, {
        session,
      });
      logger.info('Successfully user notification inserted');

      logger.info(`Preparing to send message (enrollment success) in application`);
      sendNotificationInApplication(organizationId, message, publishDateTimeNotification);

      if (course.regulatorInfo?.isRequireDeductDocument) {
        const { userNotification: deductUserNotification, message: deDuctMessage } =
          buildRequestDeductInAppNotificationMessageAndModel({
            userId,
            organizationId,
            preEnrollmentTransactionId,
            course,
            enrollmentId: enrollData.id,
            publishedAt: publishDateTimeNotification,
          });

        await db.collection(DBCollection.user_notifications).insertOne(deductUserNotification, {
          session,
        });
        logger.info('Successfully user notification (request deduct) inserted');

        logger.info(`Preparing to send request deduct message in application`);
        sendNotificationInApplication(organizationId, deDuctMessage, publishDateTimeNotification);
      }
    } else {
      let createBy = 'ผู้ดูแลระบบ';
      if (isAssignCourse && preAssignContentId) {
        const preAssignContent = await db.collection(DBCollection.pre_assign_contents).findOne(
          {
            id: preAssignContentId,
          },
          { projection: { id: 1, createdByUserId: 1 } },
        );

        const createByUser = await db.collection(DBCollection.users).findOne(
          {
            guid: preAssignContent?.createdByUserId,
          },
          { projection: { guid: 1, profile: 1 } },
        );

        if (createByUser) {
          createBy = `คุณ${createByUser.profile.firstname} ${createByUser.profile.lastname}`;
        }
      }

      logger.info(`Preparing to send a enroll email to ${user.email}`);
      await sendEnrollmentEmail(user, course, enrollData, organization, thumbnailUrl, createBy);

      logger.info('Preparing for build user notification and message');
      const { userNotification, message } = buildEnrollmentSuccessInAppNotificationMessageAndModel({
        userId,
        organizationId,
        course,
        enrollData,
        round,
        createBy,
      });
      logger.info('Build user notification and message sucessfully');

      await db.collection(DBCollection.user_notifications).insertOne(userNotification, {
        session,
      });
      logger.info('Successfully user notification inserted');

      logger.info(`Preparing to send message (enrollment success) in application`);
      sendNotificationInApplication(organizationId, message);

      if (course.regulatorInfo?.isRequireDeductDocument) {
        const { userNotification: deductUserNotification, message: deDuctMessage } =
          buildRequestDeductInAppNotificationMessageAndModel({
            userId,
            organizationId,
            preEnrollmentTransactionId,
            course,
            enrollmentId: enrollData.id,
          });

        await db.collection(DBCollection.user_notifications).insertOne(deductUserNotification, {
          session,
        });
        logger.info('Successfully user notification (request deduct) inserted');

        logger.info(`Preparing to send request deduct message in application`);
        sendNotificationInApplication(organizationId, deDuctMessage);
      }
    }
  };

  return {
    bulkEnrollUser,
  };
};
