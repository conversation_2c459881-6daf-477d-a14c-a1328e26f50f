import { UserRoleEnum } from '@iso/lms/enums/user.enum';
import _ from 'lodash';
import thaiIdCard from 'thai-id-card';
import validator from 'validator';

import { SFTP_IMPORT_USER_VALIDATION_MESSAGE } from '@constants/sftpImport';
import { isValidThaiName, convertToLowerCase } from '@infrastructure/utilities/helper';
import { LicenseTypeCode, ApplicantTypeTHEnum } from '@root/constants/licenseType';
import { date, isValidDate, DateFormat } from '@root/infrastructure/dateUtils';

const isValidNullableDate = (val) => {
  return val === 'null' || isValidDate(val, [DateFormat.yearMonthDayDash, DateFormat.yearMonthDaySlash]);
};

const convertAndValidateDateFormat = (val) => {
  if (isValidDate(val, [DateFormat.yearMonthDayDash, DateFormat.yearMonthDaySlash])) {
    return date(val).startOf('day').toDate();
  }
  return null;
};

const convertGender = (code) => {
  const genders = {
    M: 'MALE',
    F: 'FEMALE',
    U: 'UNSPECIFIC',
  };
  code = (code || '').toUpperCase();
  return genders[code] || null;
};

const convertToNullableString = (value) => {
  const typeOfValue = typeof value;
  if (typeOfValue === 'string') {
    if (value === 'null' || value.trim() === '') {
      return null;
    }
    return value;
  }
  return value ? String(value) : null;
};

const isNullableBoolean = (value) => {
  return (
    _.isBoolean(value) ||
    value === '' ||
    (value && (value.toLowerCase() === 'true' || value.toLowerCase() === 'false' || value.toLowerCase() === 'null'))
  );
};

const convertToBoolean = (value) => {
  const typeOfValue = typeof value;
  if (typeOfValue === 'string') {
    value = value.toLowerCase();
    if (value === 'null' || value.trim() === '' || (value !== 'true' && value !== 'false')) {
      return null;
    }
    return value.toLowerCase() === 'true';
  } else if (typeOfValue === 'boolean') {
    return value;
  } else if (typeOfValue === 'number') {
    return Boolean(value);
  } else {
    return null;
  }
};

const validateEmail = (text) => {
  if (!text) {
    return false;
  }
  return validator.isEmail(text);
};

const validateUsername = (username) => {
  if (!username) {
    return false;
  }

  const regex = /^(?=.{1,}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/;
  return regex.test(username);
};

const validateCitizenId = (text) => {
  return thaiIdCard.verify(text);
};

const isValidPrefix = (text, userSalutes) => {
  return userSalutes.some((userSalute) => userSalute.saluteName === text);
};

const isValidGender = (gender) => {
  return (gender || '').trim() === '' || (gender && /(M|F|U)/i.test(gender));
};

const isValidPhoneNo = (phoneNo) => {
  return (phoneNo || '').trim() === '' || (phoneNo && /^(66|0)(?:[45789]\d{8}|6[1-6]\d{7})$/.test(phoneNo));
};

const isValidOicLicense = (licenseNo) => {
  return licenseNo && /^([0-9]{2})(0{1}[1-4])([0-9]{6})$/.test(licenseNo);
};

const isValidTsiLicense = (licenseNo) => {
  return licenseNo && /^[0-9]{6}$/.test(licenseNo);
};

const getErrorMessageValidateUserList = (user, userSalutes) => {
  const errorMsgs = [];

  if (convertToNullableString(user.sales_id) === null) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.NO_SALE_ID);
  }

  if (convertToNullableString(user.user_name) === null) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.NO_USER_NAME);
  } else {
    const isValidUserName = validateUsername(user.user_name);
    if (!isValidUserName) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_USER_NAME_FORMAT);
    }
  }

  if (convertToNullableString(user.email) === null) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.NO_EMAIL);
  } else {
    const isValidEmail = validateEmail(user.email);
    if (!isValidEmail) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_EMAIL);
    }
  }

  if (convertToNullableString(user.id_card) === null) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.NO_ID_CARD);
  } else {
    if (!validateCitizenId(user.id_card)) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_CITIZEN_ID);
    }
  }

  if (!isValidPrefix(user.title, userSalutes)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_SALUTE);
  }

  if (!isValidThaiName(user.first_name)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_FIRST_NAME);
  }

  if (!isValidThaiName(user.last_name)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_LAST_NAME);
  }

  if (user.middle_name && !isValidThaiName(user.middle_name)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_MIDDLE_NAME);
  }

  if (!isValidGender(user.gender)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_GENDER);
  }

  if (!isValidPhoneNo(user.phone)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_PHONE_NO);
  }

  if (user.date_of_birth && !isValidNullableDate(user.date_of_birth)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_DATE_OF_BIRTH);
  }

  if (user.onboard_date && !isValidNullableDate(user.onboard_date)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_ONBOARD_DATE);
  }

  if (user.expire_license && !isValidNullableDate(user.expire_license)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_EXPIRY_DATE);
  }

  if (user.ic_license_expire && !isValidNullableDate(user.ic_license_expire)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_IC_EXPIRY_DATE);
  }

  if (!isNullableBoolean(user.is_uk)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_IS_UK);
  }

  if (!isNullableBoolean(user.is_ul)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_IS_UL);
  }

  if (!isNullableBoolean(user.is_active)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_IS_ACTIVE);
  }

  if (!isNullableBoolean(user.is_sso)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_IS_SSO);
  }

  if (user.license_no && !isValidOicLicense(user.license_no)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_OIC_LICENSE_FORMAT);
  }

  if (user.ic_license && !isValidTsiLicense(user.ic_license)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_TSI_LICENSE_FORMAT);
  }

  if (user.ul_sale_performance && !isNullableBoolean(user.ul_sale_performance)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_UL_SALE_PERFORMANCE);
  }

  if (user.is_employment && !isNullableBoolean(user.is_employment)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_IS_EMPLOYMENT);
  }

  if (user.position_start_date && !isValidNullableDate(user.position_start_date)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_POSITION_START_DATE);
  }

  if (user.start_license && !isValidNullableDate(user.start_license)) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_START_LICENSE);
  }

  if (user.sales_id === user.report_to) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_REPORT_TO_IS_DUPLICATED_SALE_ID);
  }

  if (user.start_license && user.expire_license && date(user.expire_license).isSameOrBefore(date(user.start_license))) {
    errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_START_LICENSE_MORE_THAN_EXPIRE_LICENSE);
  }

  return errorMsgs;
};

const getOicLicenseType = (licenseNo) => {
  const matches = (licenseNo || '').match(/^([0-9]{2})(0{1}[1-4])([0-9]{6})$/);
  if (!matches) {
    return null;
  }

  switch (matches[2]) {
    case '01':
    case '03':
      return LicenseTypeCode.OIC_LIFE;
    case '02':
    case '04':
      return LicenseTypeCode.OIC_NON_LIFE;
    default:
      return null;
  }
};

const buildMappingUserDataFromFileToDB = (rawUserData, organizationId) => {
  const last4DigitCitizenId = (rawUserData.id_card || '').slice(-4);

  const username = convertToNullableString(rawUserData.user_name);

  /** convert value type */
  const active = convertToBoolean(rawUserData.is_active);
  const gender = convertGender(rawUserData.gender);
  const dateOfBirth = convertAndValidateDateFormat(rawUserData.date_of_birth);

  /** convert additional field */
  const positionStartDate = convertAndValidateDateFormat(rawUserData.position_start_date);
  const onboardDate = convertAndValidateDateFormat(rawUserData.onboard_date);
  const isEmployment = rawUserData.is_employment === '' ? false : convertToBoolean(rawUserData.is_employment);
  const isSSO = convertToBoolean(rawUserData.is_sso);
  const isUL = rawUserData.is_ul === '' ? false : convertToBoolean(rawUserData.is_ul);
  const ulSalePerformance =
    rawUserData.ul_sale_performance === '' ? false : convertToBoolean(rawUserData.ul_sale_performance);
  const isUK = rawUserData.is_uk === '' ? false : convertToBoolean(rawUserData.is_uk);
  const startLicense = convertAndValidateDateFormat(rawUserData.start_license);
  const isTerminated = rawUserData.is_employment === '' ? false : !convertToBoolean(rawUserData.is_employment);
  const isPassedUlSaleQualify = rawUserData.ul_sale_performance
    ? convertToBoolean(rawUserData.ul_sale_performance)
    : false;
  /**
   * TODO: refactor to implment function buildUserModel
   */
  const userData = {
    email: rawUserData.email ? convertToLowerCase(rawUserData.email) : '',
    username: username !== null ? convertToLowerCase(username) : null,
    passwordHash: '',
    verified: true,
    active,
    isTest: false,
    citizenId: rawUserData.id_card,
    last4DigitCitizenId,
    organizationId,
    profile: {
      salute: rawUserData.title,
      firstname: rawUserData.first_name,
      lastname: rawUserData.last_name,
      middlename: rawUserData.middle_name,
      avatar: '',
      mobilePhoneNumber: rawUserData.phone,
      gender,
      dateOfBirth,
    },
    role: UserRoleEnum.TRAINEE,
    permissions: [],
    isEnableLocalLogin: true,
    isTwoFactorEnable: false,
    isTwoFactorRegister: false,
    isTwoFactorLogin: false,
    twoFactorSecret: null,
    customerCodes: [],
    permissionGroupIds: [],
    additionalField: {
      salesId: rawUserData.sales_id ? convertToLowerCase(rawUserData.sales_id) : null,
      employmentGroup: rawUserData.employment_group,
      positionCode: rawUserData.position_code,
      positionName: rawUserData.position_name,
      positionStartDate,
      positionLevelCode: rawUserData.position_level_code,
      positionLevelName: rawUserData.position_level_name,
      onboardDate,
      reportTo: rawUserData.report_to,
      isEmployment,
      isSSO,
      personId: rawUserData.person_id,
      faiAgent: rawUserData.fai_agent,
      faiDescription: rawUserData.fai_description,
      regionDescription: rawUserData.region_description,
      centerDescription: rawUserData.center_description,
      unitDescription: rawUserData.unit_description,
      branchCode: rawUserData.branch_code,
      branchName: rawUserData.branch_name,
      zone: rawUserData.zone,
      zoneDescription: rawUserData.zone_description,
      lifeSolutionAgent: rawUserData.life_solution_agent,
      lifeSolutionAgentLogo: rawUserData.life_solution_agent_logo,
      qualification1: rawUserData.qualification1,
      qualification2: rawUserData.qualification2,
      qualification3: rawUserData.qualification3,
      qualification4: rawUserData.qualification4,
      educationalQualification: rawUserData.educational_qualification,
      remark1: rawUserData.remark1,
      remark2: rawUserData.remark2,
      isUL,
      ulSalePerformance,
      isUK,
      startLicense,
      nextRenewalTime: rawUserData.next_renewal_time,
      oicDeduct: rawUserData.oic_deduct,
    },
    isTerminated,
    isPassedUlSaleQualify,
  };

  for (const key in userData.additionalField) {
    if (typeof userData.additionalField[key] === 'string') {
      userData.additionalField[key] = userData.additionalField[key].trim();
    }
  }

  const licenses = {};
  if (rawUserData.license_no) {
    licenses.oic = {
      userId: userData.guid,
      licenseTypeCode: getOicLicenseType(rawUserData.license_no),
      licenseNo: rawUserData.license_no || null,
      type: null,
      organizationId,
      startedAt: convertAndValidateDateFormat(rawUserData.start_license),
      expiredAt: convertAndValidateDateFormat(rawUserData.expire_license),
    };
  }

  if (rawUserData.ic_license) {
    licenses.tsi = {
      userId: userData.guid,
      licenseTypeCode: LicenseTypeCode.TSI,
      licenseNo: rawUserData.ic_license || null,
      type: ApplicantTypeTHEnum.ADVISOR,
      organizationId,
      expiredAt: convertAndValidateDateFormat(rawUserData.ic_license_expire),
      createdAt: date().toDate(),
      updatedAt: date().toDate(),
    };
  }

  return { userData, licenses };
};

const convertToUserFormat = (rows = [], organizationId, userSalutes) => {
  const saleIds = [];

  const result = rows.map((row) => {
    const { userData, licenses } = buildMappingUserDataFromFileToDB(row, organizationId);

    const error = getErrorMessageValidateUserList(row, userSalutes);

    if (userData.additionalField.salesId) {
      if (saleIds.includes(row.sales_id)) {
        error.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.DUPLICATED_SALE_ID);
      } else {
        saleIds.push(row.sales_id);
      }
    }

    return {
      payload: {
        ...userData,
        licenses,
      },
      originalPayload: {
        ...row,
      },
      error: error.length > 0 ? error.join(',') : null,
    };
  });

  return result;
};

export { convertToUserFormat };
