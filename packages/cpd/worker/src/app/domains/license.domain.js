import * as uuid from 'uuid';

import { LicenseTypeCode } from '@constants/licenseType';
import { date, startOfDay, toChristianDate } from '@infrastructure/dateUtils';

const getLicenseByPreEnrollmentTransaction = (row) => {
  const {
    tsiLicenseNo,
    tsiLicenseType,
    tsiStartDate,
    tsiEnddate,
    oicLicenseNonLifeNo,
    oicNonLifeStartDate,
    oicNonLifeEndDate,
    oicLicenseLifeNo,
    oicLifeStartDate,
    oicLifeEndDate,
  } = row;
  const licenseList = [];

  for (const [key, value] of Object.entries(LicenseTypeCode)) {
    if (value === LicenseTypeCode.TSI) {
      const licenseNo = tsiLicenseNo || '';
      const type = tsiLicenseType || null;
      const data = licensePayload(licenseNo, value, type, tsiStartDate, tsiEnddate);
      licenseList.push(data);
    }

    if (value === LicenseTypeCode.OIC_NON_LIFE) {
      const licenseNo = oicLicenseNonLifeNo || '';
      const data = licensePayload(licenseNo, value, null, oicNonLifeStartDate, oicNonLifeEndDate);
      licenseList.push(data);
    }

    if (value === LicenseTypeCode.OIC_LIFE) {
      const licenseNo = oicLicenseLifeNo || '';
      const data = licensePayload(licenseNo, value, null, oicLifeStartDate, oicLifeEndDate);
      licenseList.push(data);
    }
  }

  return licenseList;
};

const getLicenseByPreEnrollmentTransactions = (rows) => {
  let licenses = [];
  for (const row of rows) {
    const list = getLicenseByPreEnrollmentTransaction(row.payload);
    licenses = [...licenses, ...list];
  }
  return licenses;
};

const convertTraineeDateToChristianDate = (val) => {
  const possibleDateFormat = ['DD/MM/YYYY', 'DD/M/YYYY', 'D/M/YYYY', 'D/M/YYYY'];
  const formatDate = startOfDay(date(val, possibleDateFormat).toDate());
  return toChristianDate(formatDate);
};

const licensePayload = (licenseNo, licenseTypeCode, type, startedAt, expiredAt) => ({
  licenseNo,
  licenseTypeCode,
  type,
  startedAt: startedAt ? convertTraineeDateToChristianDate(startedAt) : '',
  expiredAt: expiredAt ? convertTraineeDateToChristianDate(expiredAt) : '',
  sheetFieldName: `license_${licenseTypeCode}`,
});

const createLicenseModel = ({ userId, licenseTypeCode, licenseNo, type, organizationId, startedAt, expiredAt }) => ({
  id: uuid.v4(),
  userId,
  licenseTypeCode,
  licenseNo,
  type,
  organizationId,
  startedAt: startedAt ? date(startedAt).toDate() : null,
  expiredAt: expiredAt ? date(expiredAt).toDate() : null,
  createdAt: date().toDate(),
  updatedAt: date().toDate(),
});

export { getLicenseByPreEnrollmentTransaction, getLicenseByPreEnrollmentTransactions, createLicenseModel };
