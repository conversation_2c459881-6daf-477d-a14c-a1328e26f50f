import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import {
  buildPreEnrollmentSuccessNotificationMessage,
  buildAssingCourseCompleteNotificationMessage,
} from '@iso/helpers/userNotification';
import {
  CourseObjectiveTypeEnum,
  LicenseRenewalEnum,
  RegulatorEnum,
  ContentProviderTypeEnum,
  CourseEnrollTypeEnum,
  ExternalContentTypeEnum,
} from '@iso/lms/enums/course.enum';
import { EnrollTypeEnum } from '@iso/lms/enums/enrollment.enum';
import { PreEnrollmentReservationStatusEnum } from '@iso/lms/enums/preEnrollmentReservation.enum';
import {
  OICExtendYearTypeEnum,
  OICReductionTypeEnum,
  PreEnrollmentTransactionStatusEnum,
  PreEnrollmentTransactionPaymentTypeEnum,
} from '@iso/lms/enums/preEnrollmentTransaction.enum';
import {
  getAvailablePeriodLicenseByUser,
  checkCompulsoryEnrollableInAvailablePeriodLicense,
  checkHavePlanPackageLicenseAvailableToday,
  checkHavePlanPackageLicenseAvailablePreEnroll,
} from '@iso/lms/services/planPackageLicense.service';
import { union, chain, find, isEmpty, map, cloneDeep } from 'lodash';

import { UserValidateUsecase } from '@app/usecases/userValidate.usecase';
import { DBCollection } from '@constants/dbCollection';
import { BulkStatus, EmailResultCode, BulkOpType } from '@constants/generic';
import { JobTransactionStatusEnum } from '@constants/jobTransaction';
import { LicenseTypeCode } from '@constants/licenseType';
import { ColumnSettingTemplateEnum } from '@constants/templateColumnSetting';
import { USER_VALIDATION_MESSAGE } from '@constants/userValidationMessage';
import { buildEnrollmentRegulatorReportModel } from '@domains/enrollmentRegulatorReport.domain';
import { buildJobTransactionModel } from '@domains/jobTransaction.domain';
import {
  getPreEnrollmentTransactionStatus,
  buildPreEnrollmentTransactionModel,
} from '@domains/preEnrollmentTransaction.domain';
import { buildUserNotificationModel } from '@domains/userNotification.domain';
import { date, TimeZoneEnum, DateFormat, formatDateInThaiLocale } from '@infrastructure/dateUtils';
import { runTransaction } from '@infrastructure/utilities/dbSession';
import {
  checkReEnrollableRegularCourse,
  checkUpdateContentProgressLearningPathEnrollment,
  delay,
  getFullName,
} from '@infrastructure/utilities/helper';
import {
  LearningPathContentProgressRegisterFromEnum,
  LearningPathContentProgressStatusEnum,
} from '@root/constants/learningPathEnrollment';
import { PreAssignContentTypeEnum } from '@root/constants/preAssignContent';

module.exports = ({
  db,
  dbClient,
  logger,
  userValidateService,
  oicService,
  enrollmentRegulatorReportService,
  preEnrollmentReservationService,
  preEnrollmentTransactionService,
  enrollmentService,
  config,
  transactionCounterService,
  roundService,
  notificationService,
  assignCourseUseCase,
  learningPathEnrollmentService,
  courseService,
  courseItemCriteriaConfigService,
  jobTransactionService,
  userDirectReportsService,
  templateColumnSettingService,
  tsiService,
  partService,
  productSKUCourseService,
  planService,
  planPackageService,
  planPackageLicenseService,
  courseMarketplaceService,
}) => {
  const validate = UserValidateUsecase();

  const userValidationBulk = async (msg, channel, { organization }) => {
    await delay(300);

    const { id: organizationId, domain } = organization;
    const { jobId } = msg.properties.headers;
    const { payload, originalPayload } = JSON.parse(msg.content.toString());

    logger.info(`| Starting the user validation job ID ${jobId}`);

    const queueName = `${domain}:user_validate`;
    const queueInfo = await channel.checkQueue(queueName);
    const session = dbClient.startSession();

    try {
      await runTransaction(executeUserValidateTransaction, session, [jobId, payload, originalPayload, session]);
      channel.ack(msg);
    } catch (error) {
      systemUnprocessable(jobId, originalPayload, error.message);
      channel.nack(msg, false, false);
    } finally {
      const isQueueFinished = queueInfo.messageCount === 0;
      if (isQueueFinished) {
        const { courseCode } = originalPayload;
        const { isAssignCourse, preAssignContentId, preEnrollmentReservationId } = payload;

        const { jobStatus, reservationStatus, preAssignContentStatus, numberOfWarn, numberOfError } =
          await getPreEnrollmentSummaryByJobId(jobId);

        // TODO: change solution to handler occur to system crash or database down
        if (jobStatus === BulkStatus.COMPLETED) {
          try {
            await updateCourseProgressToLearningPathEnrollment(organizationId, jobId);
          } catch (error) {
            systemUnprocessable(jobId, originalPayload, error.message);
          }
        }

        await executeQueueFinish(
          jobId,
          jobStatus,
          reservationStatus,
          preAssignContentId,
          preAssignContentStatus,
          numberOfWarn,
          numberOfError,
        );

        if (isAssignCourse) {
          await sendPreAssignContentCompleteNotification({
            jobId,
            preEnrollmentReservationId,
            preAssignContentId,
            courseCode,
          });
          await assignCourseUseCase.createEnrollmentTransactions(preEnrollmentReservationId, domain);
        }

        await prepareSendPreEnrollmentNotification(jobId, preAssignContentId);

        const message = 'Bulk user validation non credit';
        notificationService.sendUserNotificationInApplication(message, organizationId);
      }

      session.endSession();
    }
  };

  const executeUserValidateTransaction = async (jobId, payload, originalPayload, session) => {
    const { username, courseCode, enrollType } = originalPayload;
    const { isAssignCourse, isValidateRegulatorOnTransaction, preEnrollmentReservationId, preAssignContentId } =
      payload;
    const roundDate = date(payload.roundDate).toDate();
    const errorMessages = [];
    const warningMessages = [];
    const preEnrollmentReservation = await preEnrollmentReservationService.findOne({
      id: preEnrollmentReservationId,
    });
    if (!preEnrollmentReservation) {
      throw new Error('PreEnrollmentReservation not found.');
    }
    const { organizationId } = preEnrollmentReservation;
    const organization = await db.collection(DBCollection.organizations).findOne({ id: organizationId });
    if (!organization) {
      throw new Error('Organization not found.');
    }

    const userData = await db.collection(DBCollection.users).findOne({
      username,
      organizationId,
    });

    if (!userData) {
      errorMessages.push(USER_VALIDATION_MESSAGE.NOT_EXIST_USER);
    }

    const userId = userData ? userData.guid : '';
    const licenseDatas = await db.collection(DBCollection.licenses).find({ userId }).toArray();
    const tsiLicense = licenseDatas.find((val) => val.licenseTypeCode === LicenseTypeCode.TSI);
    const oicLicenseNonLifeLicense = licenseDatas.find((val) => val.licenseTypeCode === LicenseTypeCode.OIC_NON_LIFE);
    const oicLicenseLifeLicense = licenseDatas.find((val) => val.licenseTypeCode === LicenseTypeCode.OIC_LIFE);

    const courseData = await getOneCourseByCode(courseCode, organizationId);

    const isValidCourse = validateCourseCode(courseCode, courseData);

    if (!isValidCourse) {
      errorMessages.push(USER_VALIDATION_MESSAGE.INVALID_COURSE_CODE);
    }

    let externalContentType = ExternalContentTypeEnum.NONE;

    if (courseData?.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      const isEveryIncludeCourseMarketplace = await courseMarketplaceService.checkEveryIncludeCourseMarketplace(
        organizationId,
        [courseData.productSKUCourseId],
      );

      const organizationPlanPackageContentList = await planService.findPlanPackageContent(organizationId);
      const planPackageContentIds = organizationPlanPackageContentList.map((item) => item.planPackage.id);
      const planPackageContentCustomList = await planPackageService.findPlanPackageContentCustomActive(
        courseData.productSKUCourseId,
        planPackageContentIds,
      );
      const planPackageContentSubscriptionList = await planPackageService.findPlanPackageContentSubscriptionActive(
        courseData.productSKUCourseId,
        planPackageContentIds,
      );

      const planPackageContentList = planPackageContentCustomList.concat(planPackageContentSubscriptionList);
      const isExistingCourseInPlanPackage = planPackageContentList.length > 0;
      const isExistingCourseInMarketplace = !isExistingCourseInPlanPackage && isEveryIncludeCourseMarketplace;

      if (isExistingCourseInMarketplace) {
        externalContentType = ExternalContentTypeEnum.MARKETPLACE;
        errorMessages.push(USER_VALIDATION_MESSAGE.NOT_SUPPORT_COURSE_CREDIT);
      } else if (isExistingCourseInPlanPackage) {
        externalContentType = ExternalContentTypeEnum.PLAN_PACKAGE;
        const planPackageIds = planPackageContentList.map((item) => item.id);
        const approvedSalesOrderLicenses = await planPackageLicenseService.findPlanPackageLicenseSOApprovedActive(
          userId,
          planPackageIds,
        );
        const pendingSalesOrderLicenses = await planPackageLicenseService.findPlanPackageLicenseSOPendingActive(
          userId,
          planPackageIds,
        );

        const currentDate = date().toDate();
        const planPackageLicenses = pendingSalesOrderLicenses.concat(approvedSalesOrderLicenses);
        const isCourseHasExpiryDay = courseData.courseVersion.expiryDay > 0;
        const availablePeriodLicense = getAvailablePeriodLicenseByUser(planPackageLicenses, userId);
        let isHavePlanPackageLicenseAvailable = false;

        if (payload.roundDate) {
          isHavePlanPackageLicenseAvailable = checkHavePlanPackageLicenseAvailablePreEnroll(
            roundDate,
            availablePeriodLicense,
          );
        } else {
          isHavePlanPackageLicenseAvailable = checkHavePlanPackageLicenseAvailableToday(availablePeriodLicense);
        }

        if (!planPackageLicenses.length || !isHavePlanPackageLicenseAvailable) {
          errorMessages.push(USER_VALIDATION_MESSAGE.NOT_EXIST_PLAN_PACKAGE_LICENSE);
        } else if (enrollType === EnrollTypeEnum.COMPULSORY) {
          let isEnrollableInAvailablePeriodLicense = isHavePlanPackageLicenseAvailable;

          if (isCourseHasExpiryDay) {
            const enrollmentStartedAt = roundDate ?? currentDate;
            const enrollmentExpiredAt = date(enrollmentStartedAt)
              .endOf('day')
              .add(courseData.courseVersion.expiryDay, 'day')
              .toDate();

            isEnrollableInAvailablePeriodLicense = checkCompulsoryEnrollableInAvailablePeriodLicense(
              enrollmentStartedAt,
              enrollmentExpiredAt,
              availablePeriodLicense,
            );
          }

          if (!isEnrollableInAvailablePeriodLicense) {
            errorMessages.push(USER_VALIDATION_MESSAGE.INVALID_COURSE_LICENSE_DURATION);
          }
        }
      } else {
        errorMessages.push(USER_VALIDATION_MESSAGE.INVALID_COURSE_CODE);
      }
    }

    let oicExtendYearType = '';
    let oicReductionType = '';
    if (courseData) {
      if (
        courseData.objectiveType === CourseObjectiveTypeEnum.TRAINING &&
        courseData.regulatorInfo.regulator === RegulatorEnum.OIC &&
        courseData.regulatorInfo.licenseRenewal === LicenseRenewalEnum.RENEW4
      ) {
        oicReductionType = courseData.regulatorInfo.isDeduct ? OICReductionTypeEnum.YES : OICReductionTypeEnum.NO;

        if (oicLicenseNonLifeLicense && oicLicenseLifeLicense) {
          oicExtendYearType = OICExtendYearTypeEnum.BOTH;
        } else if (oicLicenseNonLifeLicense) {
          oicExtendYearType = OICExtendYearTypeEnum.NONLIFE;
        } else if (oicLicenseLifeLicense) {
          oicExtendYearType = OICExtendYearTypeEnum.LIFE;
        }
      }
    }

    let newPayload = {
      username,
      prefix: userData?.profile?.salute || '',
      firstname: userData?.profile?.firstname || '',
      lastname: userData?.profile?.lastname || '',
      citizenId: userData?.citizenId || '',
      last4DigitCitizenId: userData?.last4DigitCitizenId || '',
      email: userData?.email || '',
      mobile: userData?.profile?.mobilePhoneNumber || '',
      tsiLicenseNo: tsiLicense?.licenseNo || '',
      tsiLicenseType: tsiLicense?.type || '',
      tsiStartDate: covertFormatDateInThaiLocale(tsiLicense?.startedAt),
      tsiEnddate: covertFormatDateInThaiLocale(tsiLicense?.expiredAt),
      oicLicenseNonLifeNo: oicLicenseNonLifeLicense?.licenseNo || '',
      oicNonLifeStartDate: covertFormatDateInThaiLocale(oicLicenseNonLifeLicense?.startedAt),
      oicNonLifeEndDate: covertFormatDateInThaiLocale(oicLicenseNonLifeLicense?.expiredAt),
      oicLicenseLifeNo: oicLicenseLifeLicense?.licenseNo || '',
      oicLifeStartDate: covertFormatDateInThaiLocale(oicLicenseLifeLicense?.startedAt),
      oicLifeEndDate: covertFormatDateInThaiLocale(oicLicenseLifeLicense?.expiredAt),
      oicExtendYearType,
      oicReductionType,
      graduatedDate: '',
      university: '',
      courseCode,
    };

    let isCheckRegulatorServiceSuccess = true;
    let validationMessages = {};
    let regulatorProfile = {
      prefix: '',
      firstname: '',
      lastname: '',
    };

    const isEnableConfigOICValidation = config.ENABLE_OIC_VALIDATION;
    const isOICValidation = isEnableConfigOICValidation && isValidateRegulatorOnTransaction;
    const objectiveTypeTrainging = organization.courseObjectiveConfigs?.find(
      (val) => val.objectiveType === CourseObjectiveTypeEnum.TRAINING,
    );
    const tsiRegulatorConfig = objectiveTypeTrainging.regulators.find((val) => val.name === RegulatorEnum.TSI);

    try {
      if (
        userData &&
        (courseData?.objectiveType === CourseObjectiveTypeEnum.REGULAR ||
          (courseData?.objectiveType === CourseObjectiveTypeEnum.TRAINING &&
            courseData?.regulatorInfo.regulator !== RegulatorEnum.OIC))
      ) {
        isCheckRegulatorServiceSuccess = true;
      } else if (
        userData &&
        courseData?.objectiveType === CourseObjectiveTypeEnum.TRAINING &&
        courseData?.regulatorInfo.regulator === RegulatorEnum.OIC &&
        isOICValidation
      ) {
        const oicRemoteLicenseList = await oicService.getProfileByCitizenId(userData.citizenId);

        const licenseInformation = userValidateService.getLicenseInformation(
          newPayload.oicLicenseNonLifeNo,
          newPayload.oicLicenseLifeNo,
          oicRemoteLicenseList,
        );

        const payloadWithInformation = {
          ...newPayload,
          ...licenseInformation,
        };

        regulatorProfile = userValidateService.getRegulatorProfile(
          payloadWithInformation,
          courseData,
          oicRemoteLicenseList,
        );

        const enrollmentULHistory = await enrollmentService.getEnrollmentLicenseRenewalULHistory(userId);
        const approvalAt = enrollmentULHistory?.approvalAt ?? null;

        const tsiRemoteRegulator = { isEnabled: false, tsiLicenseList: [] };
        if (courseData?.regulatorInfo?.licenseRenewal === LicenseRenewalEnum.UK) {
          if (tsiRegulatorConfig && tsiRegulatorConfig.isEnabled && tsiRegulatorConfig.config?.subscriptionKey) {
            const subscriptionKey = `${tsiRegulatorConfig.config.subscriptionKey}`;
            const userTSIProfiles = await tsiService.getProfilesByLicenseNo(subscriptionKey, newPayload.tsiLicenseNo);
            const [userTSIProfile] = userTSIProfiles;
            tsiRemoteRegulator.isEnabled = tsiRegulatorConfig.isEnabled;
            tsiRemoteRegulator.tsiLicenseList = await tsiService.getLicensesByUniqueId(
              subscriptionKey,
              userTSIProfile?.unique_id,
            );
          }
        }

        validationMessages = validate.generateValidateMessageOICDataForShortTemplate(
          payloadWithInformation,
          courseData,
          oicRemoteLicenseList,
          roundDate,
          approvalAt,
          userData,
          tsiRemoteRegulator,
        );

        newPayload.oicExtendYearType = validationMessages.oicExtendYearType;

        isCheckRegulatorServiceSuccess = true;
      } else {
        isCheckRegulatorServiceSuccess = false;
      }
      logger.info('| Successful get information by regulator service');
    } catch (error) {
      logger.error(`| Something wrong get information by regulator service, errorMessage: ${error.message || error}`);
      isCheckRegulatorServiceSuccess = false;
    }

    const warningMessagesFromValidateRecord = await generateWarnMsgFromValidateRecord(
      newPayload,
      preEnrollmentReservation.organizationId,
      isAssignCourse,
    );

    warningMessages.push.apply(warningMessages, warningMessagesFromValidateRecord);

    if (!isAssignCourse) {
      warningMessages.push.apply(warningMessages, validationMessages.warningMessages);
    }

    const errorMessagesFromValidateRecord = await generateErrorMsgFromValidateRecord(
      jobId,
      newPayload,
      roundDate,
      courseData,
      preEnrollmentReservation.organizationId,
      userId,
      isAssignCourse,
    );

    errorMessages.push.apply(errorMessages, errorMessagesFromValidateRecord);
    errorMessages.push.apply(errorMessages, validationMessages.errorMessages);

    if (
      userData &&
      courseData &&
      isEnableConfigOICValidation &&
      isValidateRegulatorOnTransaction &&
      !isCheckRegulatorServiceSuccess
    ) {
      if (!isAssignCourse) {
        warningMessages.push.apply(warningMessages, [USER_VALIDATION_MESSAGE.UNCHECK_REGULATOR]);
      } else {
        errorMessages.push.apply(errorMessages, [USER_VALIDATION_MESSAGE.UNCHECK_REGULATOR.message]);
      }
    }

    if (warningMessages.length > 0) {
      const warning = {
        originalPayload: {
          email: newPayload.email,
        },
        warnings: warningMessages.map((warning) => ({
          message: warning.message,
          code: warning.code,
        })),
      };

      await db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            warnList: {
              $each: [warning],
            },
          },
        },
      );
    }

    const isCheckValidateLicenseType =
      [CourseObjectiveTypeEnum.TRAINING].includes(courseData?.objectiveType) && errorMessages.length === 0;

    if (isCheckValidateLicenseType) {
      const licenseType = validate.getLicenseTypeByCourse(newPayload, courseData);
      const newPreParePayload = validate.prepareLicensePayload(newPayload, licenseType);
      newPayload = newPreParePayload;
    }

    const round = await getRound(courseData?.id, roundDate);
    const roundId = round?.id ?? null;
    const errorMsg = errorMessages.join(',');
    const status = getPreEnrollmentTransactionStatus(warningMessages, errorMessages);
    const warnMsg = warningMessages.map((warning) => warning.message).join(',');
    const warnList = warningMessages.map((warning) => warning.code);
    const preEnrollmentTransactionPayload = {
      ...newPayload,
      regulatorProfile,
    };

    // Create pre enrollment transaction
    const preEnrollmentTransactionId = await transactionCounterService.findOneAndIncrementPreEnrollmentTransactionId();

    const newPreEnrollmentTransaction = buildPreEnrollmentTransactionModel({
      id: preEnrollmentTransactionId,
      jobId,
      roundId,
      preAssignContentId: preAssignContentId || null,
      customerPartnerId: null,
      userId,
      payload: preEnrollmentTransactionPayload,
      status,
      isCheckRegulator: isCheckRegulatorServiceSuccess,
      errorMsg,
      warnMsg,
      warnList,
      contentItems: [],
      preEnrollmentReservationId: preEnrollmentReservation.id,
      organizationId,
      paymentType: PreEnrollmentTransactionPaymentTypeEnum.NON_CREDIT,
      autoBulkOparation: [BulkOpType.ENROLLMENT],
      externalContentType,
      enrollType,
    });

    await preEnrollmentTransactionService.save(newPreEnrollmentTransaction, {
      session,
    });

    // Create job transaction
    const columnSettingDatas = await templateColumnSettingService.getColumnSettingByTemplate(
      organizationId,
      ColumnSettingTemplateEnum.assignContentManagement,
    );

    let departmentName = '';
    let supervisorName = '';
    if (userData) {
      const departmentData = await db
        .collection(DBCollection.departments)
        .findOne({ userIds: { $in: [userData.guid] } });

      departmentName = departmentData?.name ?? '';

      const userDirectReportData = await userDirectReportsService.findOne({ userId: userData.guid });
      if (userDirectReportData) {
        const supervisor = await db.collection(DBCollection.users).findOne({
          guid: userDirectReportData.directReportUserId,
          organizationId,
        });

        supervisorName = getFullName(supervisor.profile);
      }
    }

    let jobTransactionStatus = JobTransactionStatusEnum.ERROR;
    if (status === PreEnrollmentTransactionStatusEnum.ERROR) {
      jobTransactionStatus = JobTransactionStatusEnum.ERROR;
    } else if (status === PreEnrollmentTransactionStatusEnum.WARNING) {
      jobTransactionStatus = JobTransactionStatusEnum.WARNING;
    } else {
      jobTransactionStatus = JobTransactionStatusEnum.PASSED;
    }

    const userTransaction = jobTransactionService.getUserData(columnSettingDatas, userData);
    const licenseTransactions = jobTransactionService.getUserLicenseDatas(licenseDatas);
    const jobTransaction = {
      jobId,
      preEnrollmentTransactionId,
      learningPathEnrollmentId: null,
      status: jobTransactionStatus,
      payload: {
        user: userTransaction,
        userLicenses: licenseTransactions,
        departmentName,
        supervisorName,
        originalPayload,
      },
      errorMessages,
      warningMessages: warningMessages.map((warning) => warning.message),
    };

    const jobTransactionModel = buildJobTransactionModel(jobTransaction);

    await jobTransactionService.save(jobTransactionModel, {
      session,
    });

    // Create enrollment regulator report
    await createEnrollmentRegulatorReport(session, preEnrollmentTransactionId, courseData);

    logger.info('| Successful to validate data');
  };

  const executeQueueFinish = async (
    jobId,
    jobStatus,
    reservationStatus,
    preAssignContentId,
    preAssignContentStatus,
    numberOfWarn,
    numberOfError,
  ) => {
    const job = await db.collection(DBCollection.jobs).findOne({
      guid: jobId,
    });

    let filePath = '';

    try {
      const fileMeta = await userValidateService.generateShortTemplateFile(job);
      filePath = fileMeta.path;

      logger.info(`| Successful, created file & uploaded to S3, file: ${fileMeta.key}`);
    } catch (error) {
      logger.error(`| Something wrong, can't create file. ${error.message}`);
    }

    const preEnrollmentReservation = await preEnrollmentReservationService.findOne({
      jobId,
    });

    if (preEnrollmentReservation) {
      preEnrollmentReservation.status = reservationStatus;
      preEnrollmentReservation.totalPoint = 0;
      preEnrollmentReservation.filePath = filePath;

      await preEnrollmentReservationService.save(preEnrollmentReservation);
    } else {
      jobStatus = BulkStatus.ERROR;
      preAssignContentStatus = BulkStatus.ERROR;
      logger.info('| Something wrong, the pre-enrollment-reservation not found.');
    }

    await db.collection(DBCollection.jobs).updateOne(
      {
        guid: jobId,
      },
      {
        $set: {
          status: jobStatus,
          updatedAt: date().toDate(),
          totalWarn: numberOfWarn,
          totalError: numberOfError,
        },
      },
    );

    await db.collection(DBCollection.pre_assign_contents).updateOne(
      {
        id: preAssignContentId,
      },
      {
        $set: {
          status: preAssignContentStatus,
          updatedAt: date().toDate(),
        },
      },
    );

    logger.info('| Successful, job done!');
  };

  const prepareSendPreEnrollmentNotification = async (jobId, preAssignContentId) => {
    const preEnrollmentReservation = await preEnrollmentReservationService.findOne({
      jobId,
      status: PreEnrollmentReservationStatusEnum.PASSED,
    });

    if (!preEnrollmentReservation) {
      logger.info(`Can't send preEnrollment email: PreEnrollmentReservation not found.`);
      return;
    }

    logger.info(`Preparing to send a preEnrollment email PreEnrollmentReservationId ${preEnrollmentReservation.id}`);

    const { organizationId } = preEnrollmentReservation;

    const organization = await db.collection(DBCollection.organizations).findOne({ id: organizationId });

    if (!organization) return;

    const preEnrollmentTransactions = await preEnrollmentTransactionService.find({
      preEnrollmentReservationId: preEnrollmentReservation.id,
      status: PreEnrollmentReservationStatusEnum.PASSED,
    });

    if (preEnrollmentTransactions.length === 0) return;

    logger.info(
      `Preparing to send a preEnrollment email PreEnrollmentTransactions total ${preEnrollmentTransactions.length} record`,
    );

    const courseCodes = union(preEnrollmentTransactions.map((val) => val.payload.courseCode));

    const courseList = await db
      .collection(DBCollection.courses)
      .find({
        code: { $in: courseCodes },
        organizationId,
        isEnabled: true,
      })
      .toArray();

    const courseIds = union(courseList.map((val) => val.id));

    const courses = await courseService.findPublishedCourseWithVersionByIds(courseIds);

    const round = await roundService.findOne({ id: preEnrollmentTransactions[0].roundId });
    if (!round) {
      return;
    }

    let createBy = 'ผู้ดูแลระบบ';

    const preAssignContent = await db.collection(DBCollection.pre_assign_contents).findOne(
      {
        id: preAssignContentId,
      },
      { projection: { id: 1, createdByUserId: 1 } },
    );

    if (preAssignContent) {
      const createByUser = await db.collection(DBCollection.users).findOne(
        {
          guid: preAssignContent?.createdByUserId,
        },
        { projection: { guid: 1, profile: 1 } },
      );

      if (createByUser) {
        createBy = `คุณ${createByUser.profile.firstname} ${createByUser.profile.lastname}`;
      }
    }

    for (const preEnrollmentTransaction of preEnrollmentTransactions) {
      const transactionPayload = preEnrollmentTransaction.payload;
      const targetEmail = transactionPayload.email;
      const fullName = `${transactionPayload.firstname} ${transactionPayload.lastname}`;

      logger.info(`Preparing to send a preEnrollment email to ${targetEmail}`);
      const courseData = courses.find((val) => val.code === preEnrollmentTransaction.payload.courseCode);

      if (!courseData) continue;

      sendPreEnrollmentEmail(targetEmail, fullName, round.roundDate, createBy, courseData, organization);

      const { userNotification } = buildPreEnrollmentSuccessInAppNotificationMessageAndModel({
        userId: preEnrollmentTransaction.userId,
        organizationId,
        preEnrollmentTransactionId: preEnrollmentTransaction.id,
        course: courseData,
        round,
        createBy,
      });

      await db.collection(DBCollection.user_notifications).insertOne(userNotification);
    }
  };

  const buildPreEnrollmentSuccessInAppNotificationMessageAndModel = ({
    userId,
    organizationId,
    preEnrollmentTransactionId,
    course,
    round,
    createBy,
  }) => {
    const message = buildPreEnrollmentSuccessNotificationMessage({
      createBy,
      contentName: course.courseVersion?.name,
      roundDate: round.roundDate,
      expiryDay: course.courseVersion?.expiryDay,
    });
    const userNotification = buildUserNotificationModel(
      userId,
      organizationId,
      UserNotificationTypeEnum.PRE_ENROLLMENT_SUCCESS,
      {
        mediaId: course.thumbnailMediaId,
        message,
        url: {
          code: course.url,
          enrollmentId: null,
          preEnrollmentId: preEnrollmentTransactionId || null,
        },
      },
    );

    return {
      userNotification,
      message,
    };
  };

  const sendPreEnrollmentEmail = async (email, fullName, roundDate, createBy, course, organization) => {
    const courseItemCriteriaConfigs = await courseItemCriteriaConfigService.find({
      courseVersionId: course.courseVersion.id,
    });

    let parts = [];
    if (course.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      parts = await productSKUCourseService.getPartByCourseId(course.id, course.courseVersion.id);
    } else {
      parts = await partService.getPartByCourseId(course.courseVersion.id);
    }

    parts = courseService.combineCourseItemCriteriaConfigToPart(parts, courseItemCriteriaConfigs);
    parts = courseService.removeUnavailableContent(parts);

    const mailPayload = notificationService.preEnrollmentWelcomeEmail(
      email,
      {
        fullName,
        courseName: course.courseVersion.name,
        startDate: roundDate,
        endDate: course.courseVersion?.expiryDay
          ? date(roundDate).add(course.courseVersion?.expiryDay, 'day').toDate()
          : null,
        thumbnailUrl: course.thumbnailUrl ?? '',
        url: course.url,
        createBy,
        objectiveType: course.objectiveType,
        parts,
        courseVersion: course.courseVersion,
      },
      organization,
    );

    notificationService.sendEmail(mailPayload);
  };

  const generateErrorMsgFromValidateRecord = async (
    jobId,
    record,
    roundDate,
    courseData,
    organizationId,
    userId,
    isAssignCourse,
  ) => {
    const errorMsgs = [];
    const {
      username,
      email,
      mobile,
      prefix,
      firstname,
      lastname,
      citizenId,
      tsiLicenseNo,
      tsiLicenseType,
      oicLicenseNonLifeNo,
      oicLicenseLifeNo,
      oicExtendYearType,
      oicReductionType,
      courseCode,
    } = record;

    const userSalutes = await getUserSalutes();

    const isValidEmail = validate.validateEmail(email);
    if (!isValidEmail) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_EMAIL);
    }

    if (!validate.validateMobilePhone(mobile)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_MOBILE_PHONE);
    }

    if (!validate.validatePrefix(prefix, userSalutes)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_SALUTE);
    }

    if (!validate.validateThaiName(firstname)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_FIRST_NAME);
    }

    if (!validate.validateThaiName(lastname)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_LAST_NAME);
    }

    if (!validate.validateCitizenId(citizenId) && courseData.objectiveType !== CourseObjectiveTypeEnum.REGULAR) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_CITIZEN_ID);
    }

    if (!validate.validateLicenseNumberWithRef(tsiLicenseNo, tsiLicenseType, RegulatorEnum.TSI)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_TSI_LICENSE_NUMBER);
    }

    if (!validate.validateLicenseType(tsiLicenseType, tsiLicenseNo)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_TSI_LICENSE_TYPE);
    }

    if (!validate.validateLicenseNumber(oicLicenseNonLifeNo)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_OIC_NON_LIFE_LICENSE_NUMBER);
    }

    if (!validate.validateLicenseNumber(oicLicenseLifeNo)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_OIC_LIFE_LICENSE_NUMBER);
    }

    const isValidOICExtendYearType = validate.validateOICExtendYearTypeWithOICReduction(
      oicExtendYearType,
      oicReductionType,
    );

    if (!isValidOICExtendYearType) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_OIC_EXTEND_YEAR_TYPE);
    }

    if (!validate.validateOICReductionWithOICExtendYear(oicReductionType, oicExtendYearType)) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_OIC_REDUCTION);
    }

    const isValidCourse = validateCourseCode(courseCode, courseData);

    if (isValidCourse) {
      const isCheckValidateRound = courseData.enrollType === CourseEnrollTypeEnum.PRE_ENROLL || !isAssignCourse;
      if (isCheckValidateRound) {
        const errorMessageRound = await validateRoundDateAndRegistrationDate(courseData, roundDate, jobId);

        if (errorMessageRound) {
          errorMsgs.push(errorMessageRound);
        }
      }

      const courseDataList = [courseData];
      const isValidCourseEnableCertificate = validate.validateCourseEnableCertificate(courseDataList);
      const isValidCourseMultiCertificate = await validateCourseMultiCertificateAndCertificateType(
        courseDataList,
        oicExtendYearType,
      );
      const isRegularCourse = courseData.objectiveType === CourseObjectiveTypeEnum.REGULAR;

      if (!isRegularCourse && (!isValidCourseEnableCertificate || !isValidCourseMultiCertificate)) {
        errorMsgs.push(USER_VALIDATION_MESSAGE.COURSE_INCORRECT_SETTING);
      }

      if (isValidOICExtendYearType && !validate.validateCourseAndOICExtendYearType(courseDataList, oicExtendYearType)) {
        errorMsgs.push(USER_VALIDATION_MESSAGE.INVALID_OIC_EXTEND_YEAR_TYPE);
      }

      const isCheckValidateReEnrollableRegularCourse = courseData.objectiveType === CourseObjectiveTypeEnum.REGULAR;

      if (isCheckValidateReEnrollableRegularCourse) {
        const [existedEnrollment] = await db
          .collection(DBCollection.enrollments)
          .aggregate([
            {
              $match: { userId, courseId: courseData.id },
            },
            {
              $sort: {
                createdAt: -1,
              },
            },
          ])
          .toArray();

        if (existedEnrollment) {
          const isReEnrollableRegularCourse = checkReEnrollableRegularCourse({
            enrollmentStatus: existedEnrollment.status,
            enrollmentPassedAt: existedEnrollment.passedAt,
            enrollmentExpiredAt: existedEnrollment.expiredAt,
            isReEnrollEnabled: courseData.isReEnrollEnabled,
            isReEnrollExpireEnabled: courseData.isReEnrollExpireEnabled,
            reEnrollDay: courseData.reEnrollDay,
            reEnrollExpireDay: courseData.reEnrollExpireDay,
          });

          if (!isReEnrollableRegularCourse) {
            errorMsgs.push(USER_VALIDATION_MESSAGE.ENROLLED);
          }
        }
      }

      const isCheckValidateEnrollableTrainingCourse = [CourseObjectiveTypeEnum.TRAINING].includes(
        courseData.objectiveType,
      );

      if (isCheckValidateEnrollableTrainingCourse) {
        const isEnrollableTrainingCourse = await userValidateService.validateUserEnrolled(
          username,
          organizationId,
          courseData.id,
        );

        if (!isEnrollableTrainingCourse) {
          errorMsgs.push(USER_VALIDATION_MESSAGE.ENROLLED);
        }

        const isExistLicense = await userValidateService.validateExistLicense(record, organizationId);
        if (isExistLicense) {
          errorMsgs.push(USER_VALIDATION_MESSAGE.DUPLICATE_LICENSE);
        }
      }
    }

    const isValidDuplicateInFile = await userValidateService.validateDuplicateRecordByCourseInFile(
      jobId,
      citizenId,
      courseCode,
    );

    if (!isValidDuplicateInFile) {
      errorMsgs.push(USER_VALIDATION_MESSAGE.DUPLICATE_RECORD_IN_FILE);
    }

    return errorMsgs;
  };

  const generateWarnMsgFromValidateRecord = async (record, organizationId, isAssignCourse) => {
    const warnings = [];
    const { email, citizenId, courseCode } = record;

    const isValidEmail = validate.validateEmail(email);
    if (isValidEmail && !isAssignCourse) {
      const result = await userValidateService.validateEmailDebounce(email);
      if ([EmailResultCode.ERROR].includes(result.code)) {
        warnings.push(USER_VALIDATION_MESSAGE.CANNOT_CHECK_EMAIL);
      }
      if (
        [
          EmailResultCode.SPAM_TRAP,
          EmailResultCode.DISPOSABLE,
          EmailResultCode.INVALID,
          EmailResultCode.UNKNOWN,
          EmailResultCode.ROLE,
        ].includes(result.code)
      ) {
        warnings.push(USER_VALIDATION_MESSAGE.UNCERTAIN_EMAIL);
      }
    }

    const isDuplicate = await userValidateService.validateDuplicateRecordByCourseInPreEnrollments(
      citizenId,
      courseCode,
      organizationId,
    );

    if (isDuplicate) {
      warnings.push(USER_VALIDATION_MESSAGE.DUPLICATE_RECORD);
    }

    return warnings;
  };

  const getOneCourseByCode = async (courseCode, organizationId) => {
    const course = await db.collection(DBCollection.courses).findOne({
      code: courseCode,
      isEnabled: true,
      organizationId,
      objectiveType: {
        $in: [CourseObjectiveTypeEnum.TRAINING, CourseObjectiveTypeEnum.REGULAR],
      },
    });

    const courses = await courseService.findPublishedCourseWithVersionByIds([course?.id]);

    if (courses.length === 0) {
      return null;
    }

    return courses[0];
  };

  const validateCourseCode = (val, course) => {
    if (!val) return false;

    return !!course;
  };

  const getRound = async (courseId, roundDate) => {
    const result = await roundService.getRoundByCourseIdAndRoundDate(courseId, roundDate);

    return result;
  };

  const getUserSalutes = async () => {
    const userSalutes = await db.collection(DBCollection.user_salutes).find({}).toArray();

    return userSalutes;
  };

  const validateRoundDateAndRegistrationDate = async (course, roundDate, jobId) => {
    let errorMessage = '';

    const { id } = course;

    const round = await getRound(id, roundDate);

    if (!round) {
      errorMessage = USER_VALIDATION_MESSAGE.INVALID_TRAINING_ROUND_DATE;
      return errorMessage;
    }

    const job = await db.collection(DBCollection.jobs).findOne({
      guid: jobId,
    });
    const { createdAt } = job;
    const { firstRegistrationDate, lastRegistrationDate } = round;

    const isValidFirstRegistration = date(createdAt).isSameOrAfter(date(firstRegistrationDate));
    const isValidLastRegistration = date(createdAt).isSameOrBefore(date(lastRegistrationDate));

    if (!isValidFirstRegistration || !isValidLastRegistration) {
      errorMessage = USER_VALIDATION_MESSAGE.INVALID_REGISTRATION_DATE;
      return errorMessage;
    }

    return errorMessage;
  };

  const validateCourseMultiCertificateAndCertificateType = async (items, oicExtendYearType) => {
    for (const item of items) {
      if (item.regulatorInfo.licenseRenewal === LicenseRenewalEnum.RENEW4) {
        const isValidMultiCertificate = item.courseVersion.isMultipleCertificate;
        const certificates = await db
          .collection(DBCollection.course_version_certificate)
          .find({
            courseVersionId: item?.courseVersion?.id,
          })
          .toArray();

        if (isValidMultiCertificate && oicExtendYearType === OICExtendYearTypeEnum.BOTH) {
          const oicExtendYearTypes = [OICExtendYearTypeEnum.NONLIFE, OICExtendYearTypeEnum.LIFE];
          const isValidCertificateType = oicExtendYearTypes.every((x) => certificates.find((y) => y.type === x));

          return isValidCertificateType;
        }

        if (oicExtendYearType === OICExtendYearTypeEnum.NONLIFE || oicExtendYearType === OICExtendYearTypeEnum.LIFE) {
          const isValidCertificateType = certificates.some((x) => x.type === oicExtendYearType);

          return isValidCertificateType;
        }
      }
    }

    return true;
  };

  const createEnrollmentRegulatorReport = async (session, preEnrollmentTransactionId, courseData) => {
    if (!courseData) return;

    const { id } = courseData;

    const enrollmentRegulatorReportId =
      await transactionCounterService.findOneAndIncrementEnrollmentRegulatorReportId();

    const newEnrollmentRegulatorReport = buildEnrollmentRegulatorReportModel(
      enrollmentRegulatorReportId,
      id,
      preEnrollmentTransactionId,
    );

    await enrollmentRegulatorReportService.save(newEnrollmentRegulatorReport, {
      session,
    });
  };

  const covertFormatDateInThaiLocale = (val) => {
    const result = val
      ? formatDateInThaiLocale(val, TimeZoneEnum.Bangkok, DateFormat.buddhistDayMonthYearWithLeadingZero)
      : '';

    return result;
  };

  const sendPreAssignContentCompleteNotification = async ({
    jobId,
    preEnrollmentReservationId,
    preAssignContentId,
    courseCode,
  }) => {
    const preEnrollmentReservation = await db.collection(DBCollection.pre_enrollment_reservations).findOne({
      id: preEnrollmentReservationId,
    });

    if (!preEnrollmentReservation) {
      this.logger.error(
        `Can't sending email preEnrollmentSuccess: PreEnrollmentReservation id ${preEnrollmentReservationId} not found.`,
      );
      return;
    }

    const { organizationId, userId, roundDate } = preEnrollmentReservation;

    const job = await db.collection(DBCollection.jobs).findOne({ guid: jobId });
    if (!job) {
      this.logger.error(`Can't sending email preEnrollmentSuccess: Job id ${jobId} not found.`);
      return;
    }

    const organization = await db.collection(DBCollection.organizations).findOne({ id: organizationId });
    if (!organization) {
      this.logger.error(`Can't sending email preEnrollmentSuccess: Organization id ${organizationId} not found.`);
      return;
    }

    const user = await db.collection(DBCollection.users).findOne({ guid: userId });
    if (!user) {
      this.logger.error(`Can't sending email preEnrollmentSuccess: User id ${userId} not found.`);
      return;
    }

    const courseData = await db.collection(DBCollection.courses).findOne({
      code: courseCode,
      organizationId,
    });

    if (!courseData) {
      this.logger.error(`Can't sending email preEnrollmentSuccess: Course code ${courseCode} not found.`);
      return;
    }
    const course = await courseService.findOnePublishedCourseWithVersionById(courseData.id);

    let round = null;
    if (roundDate) {
      round = {
        roundDate,
        expiredDate: course.courseVersion?.expiryDay
          ? date(roundDate).add(course.courseVersion?.expiryDay, 'days').endOf('day').toDate()
          : null,
      };
    }

    const payload = {
      contentName: course.courseVersion.name,
      contentType: PreAssignContentTypeEnum.COURSE,
      fullName: `${user.profile.firstname ?? ''} ${user.profile.lastname ?? ''}`.trim(),
      userTotal: job.total,
      userTotalError: job.totalError,
      round,
      redirectUrl: 'myTeam?tab=pre-assign-content',
    };

    const mailPayload = notificationService.preAssignContentCompleteEmail(user.email, payload, organization);
    notificationService.sendEmail(mailPayload);

    const { userNotification, message } = buildAsignCourseCompleteInAppNotificationMessageAndModel({
      userId,
      organizationId,
      preEnrollmentReservationId,
      preAssignContentId,
      course,
      round,
      userTotal: job.total,
      userTotalError: job.totalError,
    });

    await db.collection(DBCollection.user_notifications).insertOne(userNotification);
    notificationService.sendUserNotificationInApplication(message, organizationId);
  };

  const updateCourseProgressToLearningPathEnrollment = async (organizationId, jobId) => {
    const preEnrollmentTransactions = await db
      .collection(DBCollection.pre_enrollment_transactions)
      .find({ jobId, status: PreEnrollmentTransactionStatusEnum.PASSED })
      .toArray();

    const courseCodes = chain(preEnrollmentTransactions).map('payload.courseCode').uniq().value();

    const courses = await db
      .collection(DBCollection.courses)
      .find({ organizationId, code: { $in: courseCodes } })
      .toArray();

    if (isEmpty(courses)) return;

    const courseIds = map(courses, 'id');
    const userIds = chain(preEnrollmentTransactions).map('userId').uniq().value();

    for (const preEnrollmentTransaction of preEnrollmentTransactions) {
      const mapCourse = courses.find((item) => item.code === preEnrollmentTransaction.payload.courseCode);
      if (mapCourse) {
        preEnrollmentTransaction.courseId = mapCourse.id;
      }
    }

    const learningPathEnrollments = await learningPathEnrollmentService.findLearningPathEnrollmentByVersions(
      userIds,
      courseIds,
    );

    const isSyncCourseProgressToLearningPathEnrollment = learningPathEnrollments.length;
    if (isSyncCourseProgressToLearningPathEnrollment) {
      logger.info('| Updating course progress to learning path enrollment');
      const syncCourseProgressOperations = [];
      const dateNow = date().toDate();

      const latestEnrollmentUsers = await enrollmentService.findLatestEnrollmentUsers(userIds, courseIds);

      for (const learningPathEnrollment of learningPathEnrollments) {
        const { id: learningPathEnrollmentId, contentProgress } = learningPathEnrollment;
        let newContentProgress = cloneDeep(contentProgress ?? []);

        for (const preEnrollmentTransaction of preEnrollmentTransactions) {
          const latestEnrollmentUser = find(
            latestEnrollmentUsers,
            (item) =>
              item.userId === preEnrollmentTransaction.userId && item.courseId === preEnrollmentTransaction.courseId,
          );

          const latestEnrollmentUserStatus = latestEnrollmentUser?.status ?? '';

          const isUpdateContentProgressLearningPathEnrollment = checkUpdateContentProgressLearningPathEnrollment(
            contentProgress,
            preEnrollmentTransaction.courseId,
            latestEnrollmentUserStatus,
          );

          if (!isUpdateContentProgressLearningPathEnrollment) continue;

          const newContentProgressItem = {
            enrollmentId: null,
            preEnrollmentId: preEnrollmentTransaction.id,
            courseId: preEnrollmentTransaction.courseId,
            status: LearningPathContentProgressStatusEnum.PRE_ENROLL,
            registerFrom: LearningPathContentProgressRegisterFromEnum.COURSE,
            createdAt: dateNow,
            updatedAt: dateNow,
          };

          newContentProgress = newContentProgress.filter((item) => item.courseId !== preEnrollmentTransaction.courseId);
          newContentProgress.push(newContentProgressItem);
        }

        syncCourseProgressOperations.push({
          updateOne: {
            filter: { id: learningPathEnrollmentId },
            update: {
              $set: { contentProgress: newContentProgress },
            },
          },
        });
      }

      if (syncCourseProgressOperations.length > 0) {
        await db.collection(DBCollection.learning_path_enrollments).bulkWrite(syncCourseProgressOperations);
      }

      logger.info('| Updated course progress to learning path enrollment successfully');
    }
  };

  const systemUnprocessable = async (jobId, originalPayload, errorMessage) => {
    logger.error(`| Something wrong, the job ID ${jobId} found error, errorMessage: ${errorMessage}`);

    await db.collection(DBCollection.jobs).updateOne(
      {
        guid: jobId,
      },
      {
        $push: {
          errorList: {
            originalPayload,
            message: errorMessage,
          },
        },
      },
    );
  };

  const getPreEnrollmentSummaryByJobId = async (jobId) => {
    let jobStatus = BulkStatus.COMPLETED;
    let reservationStatus = PreEnrollmentReservationStatusEnum.PASSED;
    let preAssignContentStatus = BulkStatus.COMPLETED;

    const job = await db.collection(DBCollection.jobs).findOne({
      guid: jobId,
    });

    const numberOfWarn = await db.collection(DBCollection.pre_enrollment_transactions).countDocuments({
      jobId,
      status: PreEnrollmentTransactionStatusEnum.WARNING,
    });
    const numberOfError = await db.collection(DBCollection.pre_enrollment_transactions).countDocuments({
      jobId,
      status: PreEnrollmentTransactionStatusEnum.ERROR,
    });

    if (numberOfWarn > 0) {
      jobStatus = BulkStatus.WARN;
      reservationStatus = PreEnrollmentReservationStatusEnum.WAITING;
      preAssignContentStatus = BulkStatus.ERROR;
      preAssignContentStatus = BulkStatus.IN_PROGRESS;
    }
    const isErrorList = job.errorList && job.errorList.length;
    if (numberOfError > 0 || isErrorList) {
      jobStatus = BulkStatus.ERROR;
      reservationStatus = PreEnrollmentReservationStatusEnum.ERROR;
      preAssignContentStatus = BulkStatus.ERROR;
    }

    return {
      jobStatus,
      reservationStatus,
      preAssignContentStatus,
      numberOfWarn,
      numberOfError,
    };
  };

  const buildAsignCourseCompleteInAppNotificationMessageAndModel = ({
    userId,
    organizationId,
    preEnrollmentReservationId,
    preAssignContentId,
    course,
    round,
    userTotal,
    userTotalError,
  }) => {
    const message = buildAssingCourseCompleteNotificationMessage({
      contentName: course.courseVersion?.name,
      roundDate: round?.roundDate,
      expiryDay: course.courseVersion?.expiryDay,
      userTotal,
      userTotalError,
    });
    const userNotification = buildUserNotificationModel(
      userId,
      organizationId,
      UserNotificationTypeEnum.PRE_ASSIGN_COURSE_COMPLETED,
      {
        mediaId: course.thumbnailMediaId,
        message,
        url: {
          code: course.url,
          preAssignContentId: preAssignContentId || null,
          preEnrollmentReservationId: preEnrollmentReservationId || null,
        },
      },
    );

    return {
      userNotification,
      message,
    };
  };

  return {
    userValidationBulk,
  };
};
