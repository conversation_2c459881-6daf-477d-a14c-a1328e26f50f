import { DBCollection } from '@constants/dbCollection';
import { BusinessType } from '@constants/enrollment';
import { getErrorMessage } from '@constants/errorMessage';
import { BulkStatus, GenericStatusCodes } from '@constants/generic';
import { PreEnrollmentReservationStatusEnum } from '@constants/preEnrollmentReservation';
import { PreEnrollmentTransactionStatusEnum } from '@constants/preEnrollmentTransaction';
import { redisKeyConfig } from '@constants/rediskey';
import { createLicenseModel } from '@domains/license.domain';
import { date } from '@infrastructure/dateUtils';
import { AppError, getFormattedErrorMessage } from '@infrastructure/utilities/AppError';
import { runTransaction } from '@infrastructure/utilities/dbSession';
import { LicenseTypeCode } from '@root/constants/licenseType';

// eslint-disable-next-line no-undef
module.exports = ({ db, dbClient, config, logger, slackNotificationService, memoryCachedService }) => {
  const delay = (ms) => new Promise((res) => setTimeout(res, ms));

  const validateTSILicense = (licenseTypeCode, licenseNo, typeTSILicense, resLicense) => {
    if (licenseTypeCode === LicenseTypeCode.TSI) {
      // licenseNo && type must exist
      if (typeTSILicense !== 'n/a' && licenseNo === 'n/a') {
        throw new AppError(
          GenericStatusCodes.TSI_LICENSE_TYPE_NOT_FOUND,
          'ไม่สามารถลบข้อมูล TSI License ได้ ข้อมูลไม่ครบถ้วน, required licenseNo.',
        );
      } else if (typeTSILicense === 'n/a' && licenseNo !== 'n/a') {
        throw new AppError(
          GenericStatusCodes.TSI_LICENSE_NO_NOT_FOUND,
          'ไม่สามารถลบข้อมูล TSI License ได้ ข้อมูลไม่ครบถ้วน, required type.',
        );
      }

      if (!resLicense) {
        if (typeTSILicense && typeTSILicense !== 'n/a' && !licenseNo) {
          throw new AppError(GenericStatusCodes.TSI_LICENSE_NO_NOT_FOUND, 'ไม่พบเลขที่ใบอนุญาตการลงทุน (TSI)');
        }

        if (!typeTSILicense && licenseNo && licenseNo !== 'n/a') {
          throw new AppError(GenericStatusCodes.TSI_LICENSE_TYPE_NOT_FOUND, 'ไม่พบประเภทใบอนุญาตการลงทุน (TSI)');
        }
      }
    }
  };

  const deleteRedisValueByKey = async (guid) => {
    await memoryCachedService.delete(`${redisKeyConfig.users}${guid}`);
    await memoryCachedService.delete(`${redisKeyConfig.users_authentications}${guid}`);
  };

  const editUserBulkTransaction = async (payload, session, organizationId) => {
    const {
      username,
      citizenId,
      last4DigitCitizenId,
      email,
      profile,
      licenses: userLicenses,
      regulatorProfile,
      isPassedUlSaleQualify,
      isTerminated,
      additionalField = {},
      employeeId,
      position,
    } = payload;
    const { salute, firstname, middlename, lastname, mobilePhoneNumber, gender, dateOfBirth } = profile;
    const userExists = await db.collection(DBCollection.users).findOne({
      username,
      organizationId,
    });

    if (!userExists) {
      throw new AppError(GenericStatusCodes.USER_NOT_FOUND, getErrorMessage(GenericStatusCodes.USER_NOT_FOUND));
    }

    if (citizenId) {
      const userDupCitizenId = await db
        .collection(DBCollection.users)
        .findOne({ citizenId, organizationId, guid: { $ne: userExists.guid } });

      if (userDupCitizenId) {
        throw new AppError(GenericStatusCodes.EXIST_USER, 'มีหมายเลขบัตรประชาชนนี้ในระบบแล้ว');
      }
    }

    const isManualActivateUser = !payload.preEnrollmentTransactionId;
    if (isManualActivateUser) {
      const matchUserEmailorCitizenParams = [
        {
          'payload.email': email,
        },
        {
          'payload.email': userExists.email,
        },
      ];

      if (citizenId) {
        matchUserEmailorCitizenParams.push({
          'payload.citizenId': citizenId,
        });
      }

      if (userExists.citizenId) {
        matchUserEmailorCitizenParams.push({
          'payload.citizenId': userExists.citizenId,
        });
      }

      const aggregateParams = [
        {
          $project: {
            _id: 1,
            id: 1,
            jobId: 1,
            status: 1,
            businessType: 1,
            payload: 1,
            preEnrollmentReservationId: 1,
            organizationId: 1,
          },
        },
        {
          $match: {
            $and: [
              {
                $or: [...matchUserEmailorCitizenParams],
              },
              {
                organizationId,
              },
              {
                status: {
                  $in: [
                    PreEnrollmentTransactionStatusEnum.PASSED,
                    PreEnrollmentTransactionStatusEnum.WAITING_VERIFY,
                    PreEnrollmentTransactionStatusEnum.EDITED_AFTER_VERIFY,
                  ],
                },
              },
            ],
          },
        },
        {
          $facet: {
            b2b: [
              {
                $match: {
                  businessType: BusinessType.B2B,
                },
              },
              {
                $lookup: {
                  from: 'pre-enrollment-reservations',
                  let: {
                    preEnrollmentReservationId: '$preEnrollmentReservationId',
                  },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $and: [
                            {
                              $eq: ['$id', '$$preEnrollmentReservationId'],
                            },
                            {
                              $eq: ['$status', PreEnrollmentReservationStatusEnum.PASSED],
                            },
                          ],
                        },
                      },
                    },
                    {
                      $project: {
                        _id: 0,
                        operationType: 1,
                        customerCode: 1,
                        roundDate: 1,
                        status: 1,
                      },
                    },
                  ],
                  as: 'preEnrollmentReservation',
                },
              },
              {
                $unwind: '$preEnrollmentReservation',
              },
            ],
            b2c: [
              {
                $match: {
                  businessType: BusinessType.B2C,
                },
              },
            ],
          },
        },
        {
          $project: {
            transactions: {
              $concatArrays: ['$b2b', '$b2c'],
            },
          },
        },
        {
          $unwind: {
            path: '$transactions',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $ifNull: ['$transactions', {}],
            },
          },
        },
        {
          $project: {
            _id: 1,
            id: 1,
            jobId: 1,
            status: 1,
            businessType: 1,
            payload: 1,
          },
        },
        {
          $limit: 1,
        },
      ];

      const preEnrollments = await db
        .collection(DBCollection.pre_enrollment_transactions)
        .aggregate(aggregateParams)
        .toArray();

      const [preEnrollment] = preEnrollments;

      const isSameEmail = userExists.email === email;
      const isDuplicateEmail =
        preEnrollment?.payload?.email === email || preEnrollment?.payload?.email === userExists.email;

      const isSameCitizenId = userExists.citizenId === citizenId;
      const isDuplicateCitizenId =
        preEnrollment?.payload?.citizenId === citizenId || preEnrollment?.payload?.citizenId === userExists.citizenId;

      if (email && !isSameEmail && isDuplicateEmail) {
        throw new AppError(GenericStatusCodes.EXIST_USER, 'พบอีเมลซ้ำในรายการรอลงอบรม');
      }
      if (citizenId && !isSameCitizenId && isDuplicateCitizenId) {
        throw new AppError(GenericStatusCodes.EXIST_USER, 'พบบัตรประชาชนซ้ำในรายการรอลงอบรม');
      }
    }

    await deleteRedisValueByKey(userExists.guid);

    const newUserProfile = {
      profile: {
        salute: salute || userExists.profile.salute,
        firstname: firstname || userExists.profile.firstname,
        lastname: lastname || userExists.profile.lastname,
        middlename: middlename || userExists.profile.middlename,
        avatar: payload?.profile?.avatar,
        mobilePhoneNumber: mobilePhoneNumber || userExists.profile.mobilePhoneNumber,
        gender: gender || userExists.profile.gender,
        dateOfBirth: dateOfBirth ? date(dateOfBirth).toDate() : userExists.profile.dateOfBirth,
      },
    };

    if (regulatorProfile) {
      const regulatorPrefix = regulatorProfile.prefix || payload.regulatorProfile?.prefix || '';
      const regulatorFirstname = regulatorProfile.firstname || payload.regulatorProfile?.firstname || '';
      const regulatorLastname = regulatorProfile.lastname || payload.regulatorProfile?.lastname || '';

      if (regulatorPrefix && regulatorFirstname && regulatorLastname) {
        newUserProfile.regulatorProfile = {
          prefix: regulatorPrefix,
          firstname: regulatorFirstname,
          lastname: regulatorLastname,
        };
      }
    }

    const { guid: userId } = userExists;
    logger.info(`User found with the ID of ${userId}`);

    const newEmail = email || userExists.email;
    const newCitizenId = citizenId || userExists.citizenId;
    const newLast4DigitCitizenId = last4DigitCitizenId || userExists.last4DigitCitizenId;
    const newIsPassedUlSaleQualify = isPassedUlSaleQualify ?? userExists.isPassedUlSaleQualify;
    const newIsTerminated = isTerminated ?? userExists.isTerminated;
    const newEmployeeId = employeeId || userExists.employeeId;
    const newPosition = position || userExists.position;

    Object.keys(additionalField).forEach((key) => {
      if (!additionalField[key]) {
        delete additionalField[key];
      }
    });

    const newAdditionalField = { ...userExists?.additionalField, ...additionalField };

    await db.collection(DBCollection.users).updateOne(
      {
        guid: userId,
      },
      {
        $set: {
          citizenId: newCitizenId,
          last4DigitCitizenId: newLast4DigitCitizenId,
          email: newEmail,
          ...newUserProfile,
          employeeId: newEmployeeId,
          position: newPosition,
          isPassedUlSaleQualify: newIsPassedUlSaleQualify,
          isTerminated: newIsTerminated,
          updatedAt: date().toDate(),
          additionalField: newAdditionalField,
        },
      },
      {
        session,
      },
    );

    if (userLicenses) {
      for (const userLicense of userLicenses) {
        const { licenseTypeCode, licenseNo, type: typeTSILicense, startedAt, expiredAt } = userLicense;

        const resLicense = await db.collection(DBCollection.licenses).findOne({
          userId,
          licenseTypeCode,
        });

        // TSI License validate
        validateTSILicense(licenseTypeCode, licenseNo, typeTSILicense, resLicense);

        if (!licenseNo) {
          continue;
        }

        // case delete license
        if (licenseNo === 'n/a') {
          await db.collection(DBCollection.licenses).deleteOne(
            {
              userId,
              licenseTypeCode,
            },
            {
              session,
            },
          );
        } else {
          const updateLicense = resLicense;
          if (updateLicense) {
            updateLicense.licenseNo = licenseNo ?? updateLicense.licenseNo;

            if (licenseTypeCode === LicenseTypeCode.TSI) {
              updateLicense.type = typeTSILicense ?? updateLicense.type;
            } else {
              updateLicense.type = null;
            }

            await db.collection(DBCollection.licenses).updateOne(
              {
                userId,
                licenseTypeCode,
              },
              {
                $set: {
                  licenseNo: updateLicense.licenseNo,
                  type: updateLicense.type,
                  startedAt: startedAt ? date(startedAt).toDate() : null,
                  expiredAt: expiredAt ? date(expiredAt).toDate() : null,
                  updatedAt: date().toDate(),
                },
              },
              {
                session,
              },
            );
          } else {
            const licenseData = createLicenseModel({
              userId,
              licenseTypeCode,
              licenseNo,
              type: typeTSILicense,
              organizationId,
              startedAt,
              expiredAt,
            });

            await db.collection(DBCollection.licenses).insertOne(licenseData, {
              session,
            });
          }
        }
      }
    }
  };

  const editUserBulk = async (msg, channel, { organization }) => {
    await delay(300);

    const { domain, id: organizationId, fqdn } = organization;
    const { jobId } = msg.properties.headers;
    const { payload, originalPayload, raw } = JSON.parse(msg.content.toString());
    logger.info(`Start processing the job with the ID of ${jobId}`);
    const { messageCount } = await channel.assertQueue(domain);
    const session = dbClient.startSession();

    try {
      await runTransaction(editUserBulkTransaction, session, [payload, session, organizationId]);
      channel.ack(msg);
    } catch (error) {
      logger.error(`Error processing the job with the ID of ${jobId}, errorMessage: ${error.message}`);
      const msgErr = error.code === 11000 ? getFormattedErrorMessage(error) : error.message;

      await db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...raw, msgErr],
            errorList: {
              originalPayload: {
                ...originalPayload,
                preEnrollmentId: payload.preEnrollmentTransactionId || '',
              },
              message: msgErr,
            },
          },
        },
      );
      channel.nack(msg, false, false);
    } finally {
      if (payload.preEnrollmentTransactionId) {
        await db.collection(DBCollection.pre_enrollment_transactions).updateOne(
          {
            id: payload.preEnrollmentTransactionId,
          },
          {
            $set: {
              'operationExecute.isEditUser': true,
            },
          },
        );
      }

      if (messageCount === 0) {
        const job = await db.collection(DBCollection.jobs).findOne({
          guid: jobId,
        });
        const { errorList } = job;
        await db.collection(DBCollection.jobs).updateOne(
          {
            guid: jobId,
          },
          {
            $set: {
              status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
              updatedAt: date().toDate(),
            },
          },
        );

        const url = `${config.CLIENT_PROTOCOL}://${domain}.${fqdn}/admin/setting/jobs/${job.guid}`;
        slackNotificationService.notiOperationExecute(
          job.operationType,
          job.total,
          job.errorList.length,
          job.userId,
          url,
          domain,
        );
      }

      logger.info(`End processing the job with the ID of ${jobId}`);
    }
  };

  return {
    editUserBulk,
  };
};
