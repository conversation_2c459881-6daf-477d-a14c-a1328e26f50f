import { SaleOrderStatusEnum } from '@iso/lms/enums/plan.enum';
import { PlanPackageHistoryStatusEnum } from '@iso/lms/enums/planPackageLicenseHistory.enum';
import { chain, isEmpty } from 'lodash';
import * as rs from 'randomstring';
import * as uuid from 'uuid';

import { DBCollection } from '@constants/dbCollection';
import { GenericStatusCodes } from '@constants/generic';
import { LicenseTypeCode } from '@constants/licenseType';
import { LoginProviderMethodEnum, LoginProviderTypeEnum } from '@constants/loginProvider';
import { SFTP_IMPORT_USER_VALIDATION_MESSAGE } from '@constants/sftpImport';
import { createLicenseModel } from '@domains/license.domain';
import { buildPlanPackageLicenseModel } from '@domains/planPackageLicense.domain';
import { buildPlanPackageLicenseHistoryModel } from '@domains/planPackageLicenseHistory.domain';
import { createUserLogin } from '@domains/userLogin.domain';
import { date } from '@infrastructure/dateUtils';
import { AppError, getFormattedErrorMessage } from '@infrastructure/utilities/AppError';
import { runTransaction } from '@infrastructure/utilities/dbSession';
import { createFirstPasswordToken, stringFormat } from '@infrastructure/utilities/helper';

import { createUserDirectReportModel } from './domains/userDirectReport.domain';

module.exports = ({
  cryptography,
  db,
  dbClient,
  logger,
  notificationService,
  config,
  sftpImportJobService,
  userService,
  departmentService,
  userDirectReportsService,
  columnSettingService,
  planService,
  planPackageService,
  planPackageLicenseService,
  planPackageLicenseHistoryService,
}) => {
  const isUserNameDuplicated = async (username, organizationId, salesId) => {
    const existed = await db.collection(DBCollection.users).findOne({
      $and: [{ username }, { organizationId }, { 'additionalField.salesId': { $ne: salesId } }],
    });
    return !!existed;
  };

  const isCitizenIdDuplicated = async (citizenId, organizationId, salesId) => {
    const existed = await db.collection(DBCollection.users).findOne({
      $and: [{ citizenId }, { organizationId }, { 'additionalField.salesId': { $ne: salesId } }],
    });
    return !!existed;
  };

  const isEmailDuplicated = async (email, organizationId, salesId) => {
    const existed = await db.collection(DBCollection.users).findOne({
      $and: [{ email }, { organizationId }, { 'additionalField.salesId': { $ne: salesId } }],
    });
    return !!existed;
  };

  const isDuplicatedOicLicense = async (user, organizationId) => {
    const license = user.licenses.oic;

    if (!license || !license.licenseNo) {
      return false;
    }

    const { salesId } = user.additionalField;
    const userDocument = await db.collection(DBCollection.users).findOne({ 'additionalField.salesId': salesId });

    const filter = userDocument
      ? { $and: [{ licenseNo: license.licenseNo }, { organizationId }, { userId: { $ne: userDocument.guid } }] }
      : { licenseNo: license.licenseNo, organizationId };
    const result = await db.collection(DBCollection.licenses).findOne(filter);
    return !!result;
  };

  const isDuplicatedTsiLicense = async (user, organizationId) => {
    const license = user.licenses.tsi;

    if (!license || !license.licenseNo) {
      return false;
    }

    const { salesId } = user.additionalField;
    const userDocument = await db.collection(DBCollection.users).findOne({ 'additionalField.salesId': salesId });

    const filter = userDocument
      ? { $and: [{ licenseNo: license.licenseNo }, { organizationId }, { userId: { $ne: userDocument.guid } }] }
      : { licenseNo: license.licenseNo, organizationId };
    const result = await db.collection(DBCollection.licenses).findOne(filter);
    return !!result;
  };

  const validateExistingValue = async (user) => {
    const errorMsgs = [];
    const [
      citizenIdDuplicatedPromise,
      userNameDuplicatedPromise,
      emailDuplicatedPromise,
      duplicatedOicLicensePromise,
      duplicatedTsiLicensePromise,
      findDepartmentPromise,
      findUserDirectReportPromise,
    ] = await Promise.all([
      isCitizenIdDuplicated(user.citizenId, user.organizationId, user.additionalField.salesId),
      isUserNameDuplicated(user.username, user.organizationId, user.additionalField.salesId),
      isEmailDuplicated(user.email, user.organizationId, user.additionalField.salesId),
      isDuplicatedOicLicense(user, user.organizationId),
      isDuplicatedTsiLicense(user, user.organizationId),
      departmentService.findOne({ code: user.additionalField.positionLevelCode, organizationId: user.organizationId }),
      userService.getUserBySalesId({ salesId: user.additionalField.reportTo, organizationId: user.organizationId }),
    ]);

    if (citizenIdDuplicatedPromise) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.DUPLICATED_CITIZEN_ID);
    }
    if (userNameDuplicatedPromise) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.DUPLICATED_USER_NAME);
    }

    if (emailDuplicatedPromise) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.DUPLICATED_EMAIL);
    }

    if (duplicatedOicLicensePromise) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.DUPLICATED_OIC_LICENSE);
    }

    if (duplicatedTsiLicensePromise) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.DUPLICATED_TSI_LICENSE);
    }

    if (!findDepartmentPromise && user.additionalField.positionLevelCode) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_DEPARTMENT);
    }

    if (!findUserDirectReportPromise && user.additionalField.reportTo) {
      errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_USER_DIRECT_REPORT);
    }
    return errorMsgs;
  };

  const activateUserTransaction = async (payload, session) => {
    const { organizationId } = payload;

    const organization = await db.collection(DBCollection.organizations).findOne({
      id: organizationId,
    });

    if (!organization) {
      throw new AppError(GenericStatusCodes.ORGANIZATION_NOT_FOUND, 'organization not found');
    }

    const { licenses, active: isUserActive, ...user } = payload;
    const currentDate = date().toDate();
    const plainPassword = rs.generate(8);
    const passwordHash = await cryptography.hashPassword(plainPassword);

    user.guid = uuid.v4();
    user.passwordHash = passwordHash;
    user.profile.dateOfBirth = user.profile.dateOfBirth ? date(user.profile.dateOfBirth).toDate() : null;
    user.additionalField.onboardDate = user.additionalField.onboardDate
      ? date(user.additionalField.onboardDate).toDate()
      : null;
    user.additionalField.positionStartDate = user.additionalField.positionStartDate
      ? date(user.additionalField.positionStartDate).toDate()
      : null;
    user.additionalField.startLicense = user.additionalField.startLicense
      ? date(user.additionalField.startLicense).toDate()
      : null;
    user.createdAt = date().toDate();

    const errorMsgs = await validateExistingValue(payload);
    if (errorMsgs.length > 0) {
      throw new Error(errorMsgs.join(','));
    }

    const firstPasswordToken = await createFirstPasswordToken({
      guid: user.guid,
      email: user.email,
      organizationId: user.organizationId,
    });

    if (user.additionalField.positionLevelCode) {
      await departmentService.updateDepartmentUserIdsByCode(
        {
          organizationId,
          code: user.additionalField.positionLevelCode,
          userId: user.guid,
        },
        { session },
      );
    }

    if (user.additionalField.reportTo) {
      const userReportTo = await userService.getUserBySalesId({
        organizationId: user.organizationId,
        salesId: user.additionalField.reportTo,
      });

      await userDirectReportsService.save(
        createUserDirectReportModel({
          organizationId,
          userId: user.guid,
          directReportUserId: userReportTo.guid,
        }),
        { session },
      );
    }

    if (licenses) {
      const bulkWrites = [];

      if (licenses.oic) {
        const { licenseTypeCode, licenseNo, type, startedAt, expiredAt } = licenses.oic;
        const licenseData = createLicenseModel({
          userId: user.guid,
          licenseTypeCode,
          licenseNo,
          type,
          organizationId,
          startedAt,
          expiredAt,
        });

        bulkWrites.push({
          insertOne: {
            document: licenseData,
          },
        });
      }

      if (licenses.tsi) {
        const { licenseTypeCode, licenseNo, type, expiredAt } = licenses.tsi;
        const licenseData = createLicenseModel({
          userId: user.guid,
          licenseTypeCode,
          licenseNo,
          type,
          organizationId,
          expiredAt,
        });

        bulkWrites.push({
          insertOne: {
            document: licenseData,
          },
        });
      }

      if (bulkWrites.length > 0) {
        await db.collection(DBCollection.licenses).bulkWrite(bulkWrites, {
          session,
        });
      }
    }

    const updateColumnSettingKeys = [
      'user.zone',
      'user.faiAgent',
      'user.employmentGroup',
      'd.knowledge_content_dashboard.position_name',
      'd.knowledge_content_dashboard.position_level_name',
      'd.knowledge_content_dashboard.zone',
      'd.knowledge_content_dashboard.zone_description',
      'd.knowledge_content_dashboard.fai_agent',
      'd.knowledge_content_dashboard.fai_description',
      'd.knowledge_content_dashboard.region_description',
      'd.knowledge_content_dashboard.center_description',
      'd.knowledge_content_dashboard.unit_description',
    ];
    const additionalFieldKeyMapper = {
      'user.zone': 'zone',
      'user.faiAgent': 'faiAgent',
      'user.employmentGroup': 'employmentGroup',
      'd.knowledge_content_dashboard.position_name': 'positionName',
      'd.knowledge_content_dashboard.position_level_name': 'positionLevelName',
      'd.knowledge_content_dashboard.zone': 'zone',
      'd.knowledge_content_dashboard.zone_description': 'zoneDescription',
      'd.knowledge_content_dashboard.fai_agent': 'faiAgent',
      'd.knowledge_content_dashboard.fai_description': 'faiDescription',
      'd.knowledge_content_dashboard.region_description': 'regionDescription',
      'd.knowledge_content_dashboard.center_description': 'centerDescription',
      'd.knowledge_content_dashboard.unit_description': 'unitDescription',
    };
    await addDropdownValueColumnSetting(updateColumnSettingKeys, additionalFieldKeyMapper, user, session);

    if (organization.isEnableLocalLogin) {
      user.firstPasswordToken = firstPasswordToken;
    }

    await userService.save(user, { session });

    // Set JWT login provider
    const organizationLoginProviderCollection = db.collection(DBCollection.organization_login_providers);
    const loginProviderCollection = db.collection(DBCollection.login_providers);
    const userLogInCollection = db.collection(DBCollection.user_logins);

    const [organizationLoginProvider, loginProvider] = await Promise.all([
      organizationLoginProviderCollection.findOne({ organizationId }),
      loginProviderCollection.findOne({ type: LoginProviderTypeEnum.TLI, method: LoginProviderMethodEnum.JWT }),
    ]);
    if (organizationLoginProvider && loginProvider) {
      const userLoginEntity = createUserLogin({
        organizationLoginProviderId: organizationLoginProvider.id,
        loginProviderKey: user.additionalField?.salesId || '',
        userId: user.guid,
      });

      await userLogInCollection.insertOne(userLoginEntity, { session });
    }

    if (isUserActive) {
      // assign plan package license
      const updatePlanPackageList = [];
      const updatePlanPackageLicenseList = [];
      const updatePlanPackageLicenseHistoryList = [];

      const result = await processAutoAssignPlanPackageLicense({
        userId: user.guid,
        organizationId,
        currentDate,
      });

      updatePlanPackageList.push(...result.updatePlanPackageList);
      updatePlanPackageLicenseList.push(...result.updatePlanPackageLicenseList);
      updatePlanPackageLicenseHistoryList.push(...result.updatePlanPackageLicenseHistoryList);

      // update planPackage
      if (updatePlanPackageList.length > 0) {
        await planPackageService.saveMany(updatePlanPackageList, { session });
      }

      // create or update planPackageLicense
      if (updatePlanPackageLicenseList.length > 0) {
        await planPackageLicenseService.saveMany(updatePlanPackageLicenseList, { session });
      }

      // create planPackageLicenseHistory
      if (updatePlanPackageLicenseHistoryList.length > 0) {
        await planPackageLicenseHistoryService.saveMany(updatePlanPackageLicenseHistoryList, { session });
      }

      await prepareAndSendFirstPasswordEmail(user, organization);
    }
  };

  const activateSftpImportUser = async (msg, channel, { organization }) => {
    const { jobId, totalRows } = msg.properties.headers;
    const { payload, originalPayload, error: errorFormatList } = JSON.parse(msg.content.toString());
    const { ...user } = payload;

    logger.info(`Start processing the job with the ID of ${jobId}`);

    const session = dbClient.startSession();
    try {
      if (errorFormatList) {
        throw new Error(errorFormatList);
      }

      await runTransaction(activateUserTransaction, session, [payload, session]);

      channel.ack(msg);
    } catch (error) {
      logger.error(`Error processing the job with the ID of ${jobId}, errorMessage: ${error.message}`);

      const msgErr = error.code === 11000 ? getFormattedErrorMessage(error) : error.message;

      const formattedPayload = {
        username: originalPayload.user_name,
        firstname: originalPayload.first_name,
        lastname: originalPayload.last_name,
        salute: originalPayload.title,
        email: originalPayload.email,
        citizenId: user.citizenId,
        last4DigitCitizenId: user.last4DigitCitizenId,
      };

      await db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...originalPayload.raw, msgErr],
            errorList: {
              originalPayload: formattedPayload,
              message: msgErr,
            },
          },
          $set: {
            updatedAt: date().toDate(),
          },
        },
      );
      channel.nack(msg, false, false);
    } finally {
      await sftpImportJobService.endJob(jobId, totalRows, organization.id);

      session.endSession();
      logger.info('End Session');
    }
  };

  const updateUserTransaction = async (payload, session) => {
    const { licenses, active: isUserActive, ...userData } = payload;
    const currentDate = date().toDate();

    const existedUser = await userService.findOne({
      'additionalField.salesId': userData.additionalField.salesId,
    });
    if (!existedUser) {
      throw new AppError(`Not found user by saleId ${userData?.additionalField?.salesId}`);
    }

    const onboardDate = userData.additionalField.onboardDate
      ? date(userData.additionalField.onboardDate).toDate()
      : null;
    const positionStartDate = userData.additionalField.positionStartDate
      ? date(userData.additionalField.positionStartDate).toDate()
      : null;
    const startLicense = userData.additionalField.startLicense
      ? date(userData.additionalField.startLicense).toDate()
      : null;
    const icLicenseExpire = userData.additionalField.icLicenseExpire
      ? date(userData.additionalField.icLicenseExpire).toDate()
      : null;

    const profile = {
      ...userData.profile,
      dateOfBirth: userData.profile.dateOfBirth ? date(userData.profile.dateOfBirth).toDate() : null,
      avatar: existedUser.avatar,
    };

    const updateUserData = {
      ...existedUser,
      username: userData.username,
      email: userData.email,
      citizenId: userData.citizenId,
      last4DigitCitizenId: userData.last4DigitCitizenId,
      profile,
      additionalField: {
        ...userData.additionalField,
        onboardDate,
        positionStartDate,
        startLicense,
        icLicenseExpire,
      },
      isTerminated: userData.isTerminated,
      isPassedUlSaleQualify: userData.isPassedUlSaleQualify,
    };

    const errorMsgs = await validateExistingValue(payload);
    if (errorMsgs.length > 0) {
      throw new Error(errorMsgs.join(','));
    }

    await userService.save(updateUserData, { session });

    if (updateUserData.additionalField.positionLevelCode) {
      const existingDepartment = await departmentService.findOne({
        organizationId: userData.organizationId,
        code: userData.additionalField.positionLevelCode,
      });

      if (existingDepartment) {
        await departmentService.updateDepartmentUserIdsByCode(
          {
            organizationId: userData.organizationId,
            code: userData.additionalField.positionLevelCode,
            userId: updateUserData.guid,
          },
          { session },
        );

        if (
          existedUser.additionalField.positionLevelCode !== userData.additionalField.positionLevelCode &&
          existedUser.additionalField.positionLevelCode
        ) {
          await departmentService.deleteUserIds(
            {
              code: existedUser.additionalField.positionLevelCode,
              organizationId: existedUser.organizationId,
              userId: existedUser.guid,
            },
            { session },
          );
        }
      } else {
        errorMsgs.push(SFTP_IMPORT_USER_VALIDATION_MESSAGE.INVALID_DEPARTMENT);
      }
    } else {
      const existingDepartment = await departmentService.findOne({
        userIds: existedUser.guid,
      });
      if (existingDepartment) {
        await departmentService.updateOne(
          { id: existingDepartment.id },
          { $pull: { userIds: existedUser.guid }, $set: { updatedAt: date().toDate() } },
          { session },
        );
      }
    }

    if (updateUserData.additionalField.reportTo) {
      const userReportTo = await userService.getUserBySalesId({
        organizationId: updateUserData.organizationId,
        salesId: updateUserData.additionalField.reportTo,
      });

      const userDirectReport = await userDirectReportsService.findOne({
        organizationId: updateUserData.organizationId,
        userId: updateUserData.guid,
      });

      if (userDirectReport) {
        userDirectReport.directReportUserId = userReportTo.guid;
        await userDirectReportsService.save(userDirectReport, { session });
      } else {
        await userDirectReportsService.save(
          createUserDirectReportModel({
            organizationId: updateUserData.organizationId,
            userId: updateUserData.guid,
            directReportUserId: userReportTo.guid,
          }),
          { session },
        );
      }
    }

    if (!updateUserData.additionalField.reportTo) {
      await userDirectReportsService.delete(
        { organizationId: updateUserData.organizationId, userId: updateUserData.guid },
        { session },
      );
    }

    const bulkWriteUpdateLicensePipelines = [];

    if (existedUser.citizenId !== userData.citizenId) {
      await db.collection(DBCollection.licenses).deleteMany({ userId: existedUser.guid });

      if (licenses) {
        if (licenses.oic) {
          const { licenseTypeCode, licenseNo, type, startedAt, expiredAt } = licenses.oic;
          const licenseData = createLicenseModel({
            userId: existedUser.guid,
            licenseTypeCode,
            licenseNo,
            type,
            startedAt,
            expiredAt,
          });

          bulkWriteUpdateLicensePipelines.push({
            insertOne: {
              document: licenseData,
            },
          });
        }

        if (licenses.tsi) {
          const { licenseTypeCode, licenseNo, type, expiredAt } = licenses.tsi;
          const licenseData = createLicenseModel({
            userId: existedUser.guid,
            licenseTypeCode,
            licenseNo,
            type,
            expiredAt,
          });

          bulkWriteUpdateLicensePipelines.push({
            insertOne: {
              document: licenseData,
            },
          });
        }
      }
    } else {
      if (!isEmpty(licenses)) {
        if (licenses.tsi) {
          bulkWriteUpdateLicensePipelines.push({
            updateOne: {
              filter: {
                licenseTypeCode: licenses.tsi.licenseTypeCode,
                userId: licenses.tsi?.guid || existedUser.guid,
              },
              update: {
                $set: {
                  licenseNo: licenses.tsi.licenseNo,
                  expiredAt: licenses.tsi.expiredAt ? date(licenses.tsi.expiredAt).toDate() : null,
                  updatedAt: date().toDate(),
                },
                $setOnInsert: {
                  id: uuid.v4(),
                  userId: licenses.tsi?.guid || existedUser.guid,
                  licenseTypeCode: licenses.tsi.licenseTypeCode,
                  type: licenses.tsi.type,
                  organizationId: existedUser.organizationId,
                  createdAt: date().toDate(),
                },
              },
              upsert: true,
            },
          });
        } else {
          bulkWriteUpdateLicensePipelines.push({
            deleteOne: {
              filter: {
                licenseTypeCode: LicenseTypeCode.TSI,
                userId: licenses.tsi?.guid || existedUser.guid,
              },
            },
          });
        }

        if (licenses.oic) {
          bulkWriteUpdateLicensePipelines.push({
            updateOne: {
              filter: {
                licenseTypeCode: licenses.oic.licenseTypeCode,
                userId: licenses.oic?.guid || existedUser.guid,
              },
              update: {
                $set: {
                  licenseNo: licenses.oic.licenseNo,
                  startedAt: licenses.oic.startedAt ? date(licenses.oic.startedAt).toDate() : null,
                  expiredAt: licenses.oic.expiredAt ? date(licenses.oic.expiredAt).toDate() : null,
                  updatedAt: date().toDate(),
                },
                $setOnInsert: {
                  id: uuid.v4(),
                  userId: licenses.oic?.guid || existedUser.guid,
                  licenseTypeCode: licenses.oic.licenseTypeCode,
                  type: null,
                  organizationId: existedUser.organizationId,
                  createdAt: date().toDate(),
                },
              },
              upsert: true,
            },
          });
        } else {
          bulkWriteUpdateLicensePipelines.push({
            deleteMany: {
              filter: {
                $and: [
                  { licenseTypeCode: { $ne: LicenseTypeCode.TSI } },
                  { userId: licenses.oic?.guid || existedUser.guid },
                ],
              },
            },
          });
        }
      } else {
        await db.collection(DBCollection.licenses).deleteMany({ userId: existedUser.guid });
      }
    }

    if (bulkWriteUpdateLicensePipelines.length > 0) {
      await db.collection(DBCollection.licenses).bulkWrite(bulkWriteUpdateLicensePipelines, {
        session,
      });
    }

    const updateColumnSettingKeys = [
      'user.zone',
      'user.faiAgent',
      'user.employmentGroup',
      'd.knowledge_content_dashboard.position_name',
      'd.knowledge_content_dashboard.position_level_name',
      'd.knowledge_content_dashboard.zone',
      'd.knowledge_content_dashboard.zone_description',
      'd.knowledge_content_dashboard.fai_agent',
      'd.knowledge_content_dashboard.fai_description',
      'd.knowledge_content_dashboard.region_description',
      'd.knowledge_content_dashboard.center_description',
      'd.knowledge_content_dashboard.unit_description',
    ];
    const additionalFieldKeyMapper = {
      'user.zone': 'zone',
      'user.faiAgent': 'faiAgent',
      'user.employmentGroup': 'employmentGroup',
      'd.knowledge_content_dashboard.position_name': 'positionName',
      'd.knowledge_content_dashboard.position_level_name': 'positionLevelName',
      'd.knowledge_content_dashboard.zone': 'zone',
      'd.knowledge_content_dashboard.zone_description': 'zoneDescription',
      'd.knowledge_content_dashboard.fai_agent': 'faiAgent',
      'd.knowledge_content_dashboard.fai_description': 'faiDescription',
      'd.knowledge_content_dashboard.region_description': 'regionDescription',
      'd.knowledge_content_dashboard.center_description': 'centerDescription',
      'd.knowledge_content_dashboard.unit_description': 'unitDescription',
    };
    await addDropdownValueColumnSetting(updateColumnSettingKeys, additionalFieldKeyMapper, userData, session);

    const organization = await db.collection(DBCollection.organizations).findOne({
      id: userData.organizationId,
    });

    if (isUserActive === null) return;

    const userActivePlatformLicenses = await planPackageLicenseService.findActivePlanPackagePlatformLicenseByUserId(
      existedUser.guid,
    );

    const isCurrentUserActive = !isEmpty(userActivePlatformLicenses);

    const updatePlanPackageList = [];
    const updatePlanPackageLicenseList = [];
    const updatePlanPackageLicenseHistoryList = [];

    if (isCurrentUserActive && !isUserActive) {
      // revoke plan package license
      const result = await processAutoCanceledPlanPackageLicense({
        userId: updateUserData.guid,
        organizationId: updateUserData.organizationId,
        currentDate,
      });

      updatePlanPackageList.push(...result.updatePlanPackageList);
      updatePlanPackageLicenseList.push(...result.updatePlanPackageLicenseList);
      updatePlanPackageLicenseHistoryList.push(...result.updatePlanPackageLicenseHistoryList);

      // update planPackage
      if (updatePlanPackageList.length > 0) {
        await planPackageService.saveMany(updatePlanPackageList, { session });
      }

      // create or update planPackageLicense
      if (updatePlanPackageLicenseList.length > 0) {
        await planPackageLicenseService.saveMany(updatePlanPackageLicenseList, { session });
      }

      // create planPackageLicenseHistory
      if (updatePlanPackageLicenseHistoryList.length > 0) {
        await planPackageLicenseHistoryService.saveMany(updatePlanPackageLicenseHistoryList, { session });
      }

      await prepareAndSendInActiveEmail(updateUserData, organization);
    }

    if (!isCurrentUserActive && isUserActive) {
      // assign plan package license
      const result = await processAutoAssignPlanPackageLicense({
        userId: updateUserData.guid,
        organizationId: updateUserData.organizationId,
        currentDate,
      });

      updatePlanPackageList.push(...result.updatePlanPackageList);
      updatePlanPackageLicenseList.push(...result.updatePlanPackageLicenseList);
      updatePlanPackageLicenseHistoryList.push(...result.updatePlanPackageLicenseHistoryList);

      // update planPackage
      if (updatePlanPackageList.length > 0) {
        await planPackageService.saveMany(updatePlanPackageList, { session });
      }

      // create or update planPackageLicense
      if (updatePlanPackageLicenseList.length > 0) {
        await planPackageLicenseService.saveMany(updatePlanPackageLicenseList, { session });
      }

      // create planPackageLicenseHistory
      if (updatePlanPackageLicenseHistoryList.length > 0) {
        await planPackageLicenseHistoryService.saveMany(updatePlanPackageLicenseHistoryList, { session });
      }

      if (updateUserData.setPasswordDate) {
        await prepareAndSendActiveEmail(updateUserData, organization);
      }

      if (!updateUserData.setPasswordDate) {
        await prepareAndSendFirstPasswordEmail(updateUserData, organization);
      }
    }
  };

  const updateSftpImportUser = async (msg, channel, { organization }) => {
    const { jobId, totalRows } = msg.properties.headers;
    const { payload: user, originalPayload, error: errorFormatList } = JSON.parse(msg.content.toString());

    logger.info(`Start processing the job with the ID of ${jobId}`);

    const session = dbClient.startSession();
    try {
      if (errorFormatList) {
        throw new Error(errorFormatList);
      }
      await runTransaction(updateUserTransaction, session, [user, session]);

      channel.ack(msg);
    } catch (error) {
      logger.error(`Error processing the job with the ID of ${jobId}, errorMessage: ${error.message}`);

      const msgErr = error.code === 11000 ? getFormattedErrorMessage(error) : error.message;

      const formattedPayload = {
        username: originalPayload.user_name,
        firstname: originalPayload.first_name,
        lastname: originalPayload.last_name,
        salute: originalPayload.title,
        email: originalPayload.email,
        citizenId: user.citizenId,
        last4DigitCitizenId: user.last4DigitCitizenId,
      };

      await db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...originalPayload.raw, msgErr],
            errorList: {
              originalPayload: formattedPayload,
              message: msgErr,
            },
          },
        },
      );
      channel.nack(msg, false, false);
    } finally {
      await session.endSession();
      await sftpImportJobService.endJob(jobId, totalRows, organization.id);
      logger.info('End Session');
    }
  };

  const addDropdownValueColumnSetting = async (keys, userKeyMapper, user, session) => {
    const { organizationId, additionalField } = user;
    const columSettingKeys = await db
      .collection(DBCollection.organization_column_settings)
      .find({ organizationId, key: { $in: keys } })
      .toArray();

    if (columSettingKeys.length > 0) {
      const bulkWriteUpdateColumnSettingPipelines = [];
      const tempDropdownValueList = [];

      for (const key of keys) {
        const additionalFieldKey = userKeyMapper[key];
        if (!additionalFieldKey) continue;

        const value = additionalField[additionalFieldKey]?.trim();
        if (!value || value === '') continue;

        const columSetting = columSettingKeys.find((val) => val.key === key);
        let isDuplicate = false;
        if (columSetting) {
          const dropdownValueList = columSetting.dropdownValue.filter((val) => val.value === value);
          const tempDropdownValue = tempDropdownValueList.find((val) => val === value);
          isDuplicate = dropdownValueList.length > 0 || !!tempDropdownValue;
        }

        if (!isDuplicate) {
          tempDropdownValueList.push(value);
          bulkWriteUpdateColumnSettingPipelines.push({
            updateOne: {
              filter: { organizationId, key },
              update: {
                $push: {
                  dropdownValue: {
                    value,
                    label: value,
                  },
                },
              },
            },
          });
        }
      }

      if (bulkWriteUpdateColumnSettingPipelines.length > 0) {
        await db
          .collection(DBCollection.organization_column_settings)
          .bulkWrite(bulkWriteUpdateColumnSettingPipelines, session);
      }
    }
  };

  const processAutoAssignPlanPackageLicense = async ({ userId, organizationId, currentDate }) => {
    const updatePlanPackageList = [];
    const updatePlanPackageLicenseList = [];
    const updatePlanPackageLicenseHistoryList = [];

    const planPackagePlatformAvailableList = await planService.findActivePlanPackagePlatform(organizationId); // plan and planPackage
    if (planPackagePlatformAvailableList.length === 0) {
      throw new Error(SFTP_IMPORT_USER_VALIDATION_MESSAGE.NOT_FOUND_PLAN_PACKAGE_PLATFORM);
    }

    logger.info(`Process Auto Assign Plan Package License`);
    const planPackageList = planPackagePlatformAvailableList.map((val) => val.planPackage);
    const planPackageIds = planPackageList.map((val) => val.id);

    // planPackageLicense expired Date >= Date Now
    const currentUserActivePlanPackageLicenseByUserIdsList =
      await planPackageLicenseService.findActivePlanPackageLicenseByUserIds([userId], planPackageIds);

    const planPackageLicenseActiveIds = currentUserActivePlanPackageLicenseByUserIdsList.map(
      (val) => val.planPackageId,
    );

    const userNoPlanPackageLicenseActiveIds = planPackageIds.filter(
      (val) => !planPackageLicenseActiveIds.includes(val),
    );

    //  มีอยู่แล้วไม่ต้อง assign เพิ่ม
    if (userNoPlanPackageLicenseActiveIds.length === 0) {
      logger.info('Notthing assign plan package license');
      return {
        planList: planPackagePlatformAvailableList,
        updatePlanPackageList,
        updatePlanPackageLicenseList,
        updatePlanPackageLicenseHistoryList,
      };
    }

    const selectPlanPackageList = planPackageList.filter((val) => userNoPlanPackageLicenseActiveIds.includes(val.id));

    const selectPlanPackageIds = selectPlanPackageList.map((val) => val.id);

    const isRemainAvailable = selectPlanPackageList.some((val) => val.content.remainLicense > 0);
    if (!isRemainAvailable) {
      throw new Error(
        stringFormat(
          SFTP_IMPORT_USER_VALIDATION_MESSAGE.PLAN_PACKAGE_PLATFORM_NOT_ENOUGH_REMAIN_LICENSE,
          selectPlanPackageList[0].name,
        ),
      );
    }

    const userRevokeIds = [null];
    const oldUserActivePlanPackageLicenseList = await planPackageLicenseService.findActivePlanPackageLicenseByUserIds(
      userRevokeIds,
      selectPlanPackageIds,
    );

    const planPackageHaveRemainLicense = getPlanPackageLicenseHaveRemainRevokeToAssign(
      selectPlanPackageList,
      oldUserActivePlanPackageLicenseList,
    );

    if (!planPackageHaveRemainLicense) {
      throw new Error(
        stringFormat(
          SFTP_IMPORT_USER_VALIDATION_MESSAGE.PLAN_PACKAGE_PLATFORM_NOT_ENOUGH_REMAIN_LICENSE,
          selectPlanPackageList[0].name,
        ),
      );
    }

    assignNewLicense({
      userId,
      organizationId,
      currentDate,
      planPackageHaveRemainLicense,
      planList: planPackagePlatformAvailableList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistoryList,
    });

    return {
      planList: planPackagePlatformAvailableList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistoryList,
    };
  };

  const processAutoCanceledPlanPackageLicense = async ({ userId, organizationId, currentDate }) => {
    const updatePlanPackageList = [];
    const updatePlanPackageLicenseList = [];
    const updatePlanPackageLicenseHistoryList = [];

    const planAndPackagePlatformList = await planService.findPendingPlanPackagePlatform(organizationId); // plan + packages
    if (planAndPackagePlatformList.length > 0) {
      logger.info(`Process Auto Canceled Plan Package License`);
      // revoke plan package license platform ทุกใบที่ expired Date >= Date Now
      const planPackageList = planAndPackagePlatformList.map((val) => val.planPackage);
      const planPackageIds = planPackageList.map((val) => val.id);
      const planPackageLicenseList = await planPackageLicenseService.find({
        userId,
        planPackageId: { $in: planPackageIds },
      });
      const userPlanPackageLicenseIds = planPackageLicenseList.map((val) => val.id);
      const assignPlanPackageLicenseHistorieList = await planPackageLicenseHistoryService.find({
        userId,
        status: PlanPackageHistoryStatusEnum.ASSIGNED,
        planPackageLicenseId: { $in: userPlanPackageLicenseIds },
      });

      for (const planPackageLicense of planPackageLicenseList) {
        const planPackage = planPackageList.find((val) => val.id === planPackageLicense.planPackageId);

        const isRemainAvailable = planPackage.content.remainTransferLicense > 0;
        if (!isRemainAvailable) {
          throw new Error(
            stringFormat(
              SFTP_IMPORT_USER_VALIDATION_MESSAGE.PLAN_PACKAGE_PLATFORM_NOT_ENOUGH_REMAIN_TRANSFER_LICENSE,
              planPackage.name,
            ),
          );
        }

        const planPackagePlatform = planAndPackagePlatformList.find((val) => val.id === planPackage.planId);

        const isSaleOrderStatusApprove = planPackagePlatform.saleOrderStatus === SaleOrderStatusEnum.APPROVED;

        planPackageLicense.userId = null;
        updatePlanPackageLicenseList.push(planPackageLicense);

        prepareCanceledPlanPackageLicenseHistoryData({
          userId,
          organizationId,
          currentDate,
          planPackageLicense,
          isSaleOrderStatusApprove,
          assignPlanPackageLicenseHistorieList,
          updatePlanPackageLicenseHistoryList,
        });

        updatePlanPackageRemainLicense({
          type: PlanPackageHistoryStatusEnum.CANCELED,
          planPackage,
          isSaleOrderStatusApprove,
        });

        updatePlanPackageList.push(planPackage);
      }
    }

    return {
      planList: planAndPackagePlatformList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistoryList,
    };
  };

  const getPlanPackageLicenseHaveRemainList = (planPackageList) => {
    const planPackageHaveRemainLicense = planPackageList.filter((val) => val.content.remainLicense > 0);

    return planPackageHaveRemainLicense;
  };

  const getPlanPackageLicenseHaveRemainRevokeToAssign = (planPackageList, revokableLicenseList) => {
    const planPackageHaveRemainLicense = getPlanPackageLicenseHaveRemainList(planPackageList);

    const planPackageHaveRemainRevoke = planPackageHaveRemainLicense.filter((planPackage) => {
      const revokableCountForPackage = revokableLicenseList.filter(
        (license) => license.planPackageId === planPackage.id,
      ).length;

      const currentRemainLicense = planPackage.content.remainLicense;
      const effectiveRemainLicense = currentRemainLicense - revokableCountForPackage;

      return effectiveRemainLicense > 0;
    });

    if (planPackageHaveRemainRevoke.length === 0) {
      return null;
    }

    const filterAndSortPlanPackages = planPackageHaveRemainRevoke.slice().sort((a, b) => {
      // Ensure expiredAt and createdAt are valid dates
      const aExpiredAt = date(a.endDate).toDate().getTime();
      const bExpiredAt = date(b.endDate).toDate().getTime();
      const aCreatedAt = date(a.createdAt).toDate().getTime();
      const bCreatedAt = date(b.createdAt).toDate().getTime();

      // 1. Sort by expiredAt (ascending - closer to current date first)
      if (aExpiredAt !== bExpiredAt) {
        return aExpiredAt - bExpiredAt;
      }
      // 2. If expiredAt is the same, sort by createdAt (ascending - older first)
      return aCreatedAt - bCreatedAt;
    });

    return filterAndSortPlanPackages[0];
  };

  const assignNewLicense = ({
    userId,
    organizationId,
    currentDate,
    planPackageHaveRemainLicense,
    planList,
    updatePlanPackageList,
    updatePlanPackageLicenseList,
    updatePlanPackageLicenseHistoryList,
  }) => {
    const plan = planList.find((val) => val.id === planPackageHaveRemainLicense.planId);
    const isSaleOrderStatusApprove = plan.saleOrderStatus === SaleOrderStatusEnum.APPROVED;

    const newPlanPackageLicense = prepareAssignPlanPackageLicenseData({
      userId,
      organizationId,
      currentDate,
      planPackage: planPackageHaveRemainLicense,
      isSaleOrderStatusApprove,
    });
    updatePlanPackageLicenseList.push(newPlanPackageLicense);

    prepareAssignPlanPackageLicenseHistoryData({
      organizationId,
      currentDate,
      isSaleOrderStatusApprove,
      planPackageLicense: newPlanPackageLicense,
      updatePlanPackageLicenseHistoryList,
    });

    updatePlanPackageRemainLicense({
      type: PlanPackageHistoryStatusEnum.ASSIGNED,
      planPackage: planPackageHaveRemainLicense,
      isSaleOrderStatusApprove,
    });
    updatePlanPackageList.push(planPackageHaveRemainLicense);
  };

  const prepareAssignPlanPackageLicenseData = ({ userId, currentDate, isSaleOrderStatusApprove, planPackage }) => {
    let startedAt = null;
    let expiredAt = null;
    if (isSaleOrderStatusApprove) {
      const isPlanPackageStarted = date(planPackage.startDate).isSameOrBefore(currentDate);
      const startedLicense = isPlanPackageStarted ? currentDate : planPackage.startDate;
      const calExpiredLicense = date(startedLicense).add(planPackage.totalUsageDay, 'day').endOf('day');
      startedAt = startedLicense;
      expiredAt = calExpiredLicense.isAfter(planPackage.endDate) ? planPackage.endDate : calExpiredLicense.toDate();
    }

    const planPackageLicense = buildPlanPackageLicenseModel({
      planPackageId: planPackage.id,
      userId,
      startedAt,
      expiredAt,
    });

    return planPackageLicense;
  };

  const prepareAssignPlanPackageLicenseHistoryData = ({
    organizationId,
    currentDate,
    isSaleOrderStatusApprove,
    planPackageLicense,
    updatePlanPackageLicenseHistoryList,
  }) => {
    if (isSaleOrderStatusApprove) {
      const planPackageLicenseHistory = buildPlanPackageLicenseHistoryModel({
        organizationId,
        planPackageLicenseId: planPackageLicense.id,
        userId: planPackageLicense.userId,
        status: PlanPackageHistoryStatusEnum.ASSIGNED,
        startedAt: currentDate,
        expiredAt: planPackageLicense.expiredAt,
      });
      updatePlanPackageLicenseHistoryList.push(planPackageLicenseHistory);
    }
  };

  const prepareCanceledPlanPackageLicenseHistoryData = async ({
    userId,
    organizationId,
    currentDate,
    isSaleOrderStatusApprove,
    planPackageLicense,
    assignPlanPackageLicenseHistorieList,
    updatePlanPackageLicenseHistoryList,
  }) => {
    if (isSaleOrderStatusApprove) {
      const latestAssignPlanPackageLicenseHistory = chain(assignPlanPackageLicenseHistorieList)
        .filter({ planPackageLicenseId: planPackageLicense.id })
        .maxBy((item) => date(item.createdAt).valueOf())
        .value();

      const planPackageLicenseHistory = buildPlanPackageLicenseHistoryModel({
        organizationId,
        planPackageLicenseId: planPackageLicense.id,
        userId,
        status: PlanPackageHistoryStatusEnum.CANCELED,
        startedAt: latestAssignPlanPackageLicenseHistory?.startedAt,
        expiredAt: currentDate,
      });
      updatePlanPackageLicenseHistoryList.push(planPackageLicenseHistory);
    }
  };

  const updatePlanPackageRemainLicense = ({ type, planPackage, isSaleOrderStatusApprove }) => {
    if (type === PlanPackageHistoryStatusEnum.ASSIGNED) {
      planPackage.content.remainLicense -= 1;
    }

    if (type === PlanPackageHistoryStatusEnum.CANCELED) {
      planPackage.content.remainLicense += 1;
      if (isSaleOrderStatusApprove) {
        planPackage.content.remainTransferLicense = Math.max(planPackage.content.remainTransferLicense - 1, 0);
      }
    }
  };

  const prepareAndSendFirstPasswordEmail = async (user, organization) => {
    const { firstPasswordToken } = user;
    if (organization.isEnableLocalLogin && firstPasswordToken) {
      logger.info('Send email welcome');
      const instructionBanner = `${config.S3_BUCKET_URL}/assets/new_user_instruction_v2.png`;
      const urlEmail = `${config.CLIENT_PROTOCOL}://${organization.domain}.${organization.fqdn}/firstPassword?token=${firstPasswordToken}`;
      const emailData = {
        url: urlEmail,
        instructionBanner,
        username: user.username,
        fullName: `${user.profile.firstname} ${user.profile.lastname}`,
      };
      const mailPayload = await notificationService.firstSetPasswordEmail(user.email, emailData, organization);
      notificationService.sendEmail(mailPayload);
    }
  };

  const prepareAndSendActiveEmail = async (user, organization) => {
    logger.info('Send email active user');
    const emailData = {
      fullName: `${user.profile.firstname} ${user.profile.lastname}`,
      url: `${config.CLIENT_PROTOCOL}://${organization.domain}.${organization.fqdn}/login`,
    };
    const mailPayload = await notificationService.activeUserEmail(user.email, emailData, organization);
    notificationService.sendEmail(mailPayload);
  };

  const prepareAndSendInActiveEmail = async (user, organization) => {
    logger.info('Send email inactive user');
    const emailData = {
      fullName: `${user.profile.firstname} ${user.profile.lastname}`,
    };

    const mailPayload = await notificationService.inActiveUserEmail(user.email, emailData, organization);
    notificationService.sendEmail(mailPayload);
  };

  return {
    activateSftpImportUser,
    updateSftpImportUser,
  };
};
