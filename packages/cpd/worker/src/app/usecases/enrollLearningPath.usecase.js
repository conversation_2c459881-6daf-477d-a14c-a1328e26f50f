import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { getPublishDateTimeOfNotification } from '@iso/helpers/dateUtils';
import {
  buildLearningPathPreEnrollmentToEnrollmentSuccessNotificationMessage,
  buildLearningPathCompleteWithCertificateNotificationMessage,
  buildAssignEnrollmentCompletedNotificationMessage,
} from '@iso/helpers/userNotification';
import { CertificatePropertyModuleEnum, CertificatePropertyTypeEnum } from '@iso/lms/enums/certificate.enum';
import { isNil, isNull, keyBy } from 'lodash';
import * as uuid from 'uuid';

import { DBCollection } from '@constants/dbCollection';
import { GenericStatusCodes, BulkStatus, DomainMetaDataEnum } from '@constants/generic';
import { JobTransactionStatusEnum } from '@constants/jobTransaction';
import { LearningPathEnrollmentStatusEnum } from '@constants/learningPathEnrollment';
import { OrganizationSchedulerTypeEnum } from '@constants/organizationScheduler';
import { buildJobTransactionModel } from '@domains/jobTransaction.domain';
import { buildLearningPathEnrollmentCertificateModel } from '@domains/learningPathEnrollmentCertificate.domain';
import { buildUserNotificationModel } from '@domains/userNotification.domain';
import { date, addDays, startOfDay, endOfDay } from '@infrastructure/dateUtils';
import { AppError } from '@infrastructure/utilities/AppError';
import { getFullName, delay } from '@infrastructure/utilities/helper';
import { PreAssignContentTypeEnum } from '@root/constants/preAssignContent';
import { ColumnSettingTemplateEnum } from '@root/constants/templateColumnSetting';

class EnrollLearningPathUseCase {
  constructor({
    config,
    db,
    dbClient,
    notificationService,
    scheduleService,
    jobService,
    roundService,
    preAssignContentService,
    userNotificationService,
    slackNotificationService,
    organizationService,
    learningPathService,
    learningPathVersionService,
    learningPathEnrollmentService,
    learningPathEnrollmentCertificateService,
    syncCourseLearningProgressService,
    jobTransactionService,
    templateColumnSettingService,
    userDirectReportsService,
    licenseService,
    certificateService,
    emailTransactionsService,
    organizationSchedulerService,
    amqp,
    logger,
    organizationStorageService,
    mediaService,
  }) {
    this.db = db;
    this.dbClient = dbClient;

    // Service
    this.scheduleService = scheduleService;
    this.slackNotificationService = slackNotificationService;
    this.notificationService = notificationService;
    this.jobService = jobService;
    this.queueService = amqp;
    this.roundService = roundService;
    this.preAssignContentService = preAssignContentService;
    this.userNotificationService = userNotificationService;
    this.organizationService = organizationService;
    this.learningPathService = learningPathService;
    this.learningPathVersionService = learningPathVersionService;
    this.learningPathEnrollmentService = learningPathEnrollmentService;
    this.learningPathEnrollmentCertificateService = learningPathEnrollmentCertificateService;
    this.syncCourseLearningProgressService = syncCourseLearningProgressService;
    this.jobTransactionService = jobTransactionService;
    this.templateColumnSettingService = templateColumnSettingService;
    this.userDirectReportsService = userDirectReportsService;
    this.licenseService = licenseService;
    this.certificateService = certificateService;
    this.emailTransactionsService = emailTransactionsService;
    this.organizationSchedulerService = organizationSchedulerService;
    this.organizationStorageService = organizationStorageService;
    this.mediaService = mediaService;
    // Config
    this.config = config;
    this.logger = logger;
  }

  async execute(msg, channel, { organization }) {
    const { domain, id: organizationId, fqdn } = organization;
    const { jobId } = msg.properties.headers;
    const payload = JSON.parse(msg.content.toString());
    await delay(300);

    this.logger.info(`Start processing the job ID ${jobId} of type LEARNING_PATH_ENROLLMENT`);

    const { messageCount } = await channel.assertQueue(domain);
    const session = this.dbClient.startSession();

    session.startTransaction();

    try {
      await this.enrollLearningPathTransaction(payload, session, organizationId, jobId);
      await session.commitTransaction();

      channel.ack(msg);
    } catch (error) {
      this.logger.error(`| Something wrong, the job ID ${jobId} found error, errorMessage: ${error.message}`);
      await session.abortTransaction();

      channel.nack(msg, false, false);
    } finally {
      if (messageCount === 0) {
        const job = await this.jobService.findOne({
          guid: jobId,
        });
        const { errorList } = job;
        await this.db.collection(DBCollection.jobs).updateOne(
          {
            guid: jobId,
          },
          {
            $set: {
              status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
              updatedAt: date().toDate(),
            },
          },
        );

        // TODO: implement send message to slack later
        // const url = `${config.CLIENT_PROTOCOL}://${domain}.${fqdn}/admin/setting/jobs/${job.guid}`;
        // slackNotificationService.notiOperationExecute(
        //   job.operationType,
        //   job.total,
        //   job.errorList.length,
        //   job.userId,
        //   url,
        //   domain,
        // );
      }
      session.endSession();
      this.logger.info('End Session');
    }
  }

  async enrollLearningPathTransaction(payload, session, organizationId, jobId) {
    const { id, learningPathId, userId, status, version } = payload;
    const errorMessageList = [];

    const organization = await this.organizationService.findOne({
      id: organizationId,
    });

    if (!organization) {
      throw new AppError(GenericStatusCodes.ORGANIZATION_NOT_FOUND, 'organization not found');
    }

    let user = null;
    if (userId) {
      user = await this.db.collection(DBCollection.users).findOne({
        guid: userId,
        active: true,
      });
    }

    if (!user) {
      errorMessageList.push('บัญชีผู้ใช้งานไม่ถูกต้อง');
    }

    const learningPath = await this.learningPathService.findOne({
      organizationId,
      id: learningPathId,
      isEnabled: true,
    });

    if (!learningPath) {
      errorMessageList.push('ไม่พบแผนการเรียนรู้');
    }

    const learningPathVersion = await this.learningPathVersionService.findOne({
      learningPathId,
      version,
    });

    if (!learningPathVersion) {
      errorMessageList.push('ไม่พบเวอร์ชันแผนการเรียนรู้');
    }

    const courses = await this.syncCourseLearningProgressService.getCourseListByContents({
      contents: learningPathVersion.contents,
      organizationId,
    });

    const courseIds = courses.map((course) => course.id);
    const courseCodes = courses.map((course) => course.code);
    const enrollments = await this.syncCourseLearningProgressService.getEnrollmentsByContents({
      userId: user?.guid,
      courseIds,
    });

    const preEnrollments = await this.syncCourseLearningProgressService.getPreEnrollmentsByContents({
      organizationId,
      userId: user?.guid,
      courseIds,
    });

    let learningPathContentProgress = [];
    let completedContentItem = 0;
    let learningPathEnrollmentStatus = LearningPathEnrollmentStatusEnum.ASSIGNED;
    let isLearningPathComplete = false;
    if (status === LearningPathEnrollmentStatusEnum.PRE_ASSIGN && learningPath && learningPathVersion) {
      learningPathContentProgress = this.syncCourseLearningProgressService.prepareContentProgress({
        contents: learningPathVersion.contents,
        courses,
        enrollments,
        preEnrollments,
      });

      const { status, totalCompleted } =
        this.syncCourseLearningProgressService.getStatusAndCountCompleteCourseByContentProgress(
          learningPathContentProgress,
          learningPathVersion.contents.length,
        );
      completedContentItem = totalCompleted;
      learningPathEnrollmentStatus = status;
      if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED) {
        isLearningPathComplete = true;
      }
    }

    const currentDate = date().toDate();
    const { isCertificateEnabled } = learningPathVersion;
    let isPassCriteriaCertificate = false;
    let certificateRefName;
    let certificateUrl;
    let certificateCode;

    if (isCertificateEnabled) {
      const enrollmentGroupById = keyBy(enrollments, 'id');
      const enrollmentSyncProgress = learningPathContentProgress
        .map((content) => {
          if (!content?.enrollmentId) return null;
          return enrollmentGroupById[content.enrollmentId];
        })
        .filter((enrollment) => !isNil(enrollment));

      // fixed
      const payloadParams = {
        refCode: learningPathVersion?.certificate?.refCode || '',
        refName: learningPathVersion?.certificate?.refName || '',
      };
      const newLearningPathCertificateEnrollment = buildLearningPathEnrollmentCertificateModel({
        learningPathEnrollmentId: id,
        slugName: learningPathVersion?.certificate?.mandatoryFields?.slugName,
        payload: payloadParams,
        logoImageUrl: organization.certificateConfig?.logoImageUrl,
        issuedBy: organization.certificateConfig?.textDynamicCertificate,
        isSentEmail: false,
      });

      isPassCriteriaCertificate = this.learningPathEnrollmentCertificateService.validateIsPassCriteriaCertificate({
        courses,
        enrollments: enrollmentSyncProgress,
        learningPathContent: learningPathVersion.contents,
      });

      if (isPassCriteriaCertificate) {
        certificateRefName = learningPathVersion?.certificate?.refName || '';

        const { organizationCertificateId } = learningPathVersion.certificate;

        const organizationCertificate =
          await organizationCertificateService.findWithCertificateDetail(organizationCertificateId);

        const { certificateDetail } = organizationCertificate;
        const certificateProperties = certificateDetail.properties;
        const organizationCertificateProperties = organizationCertificate.properties;
        const resultProperties = this.certificateService.mergeProperties(
          certificateProperties,
          organizationCertificateProperties,
        );
        const { slugName } = certificateDetail;

        const dynamicValue = await this.getCertificateDynamicValue({
          resultProperties,
          organizationId: organization.id,
          userId: user.id,
          courseId: null,
          courseVersionId: null,
          learningPathId: learningPath.id,
          learningPathVersionId: learningPathVersion.id,
        });

        const certificatePayload = {
          dynamic_value: dynamicValue,
          slug_name: slugName,
          metadata_url: this.certificateService.genMetaDataUrl(
            learningPathEnrollmentModel.id,
            DomainMetaDataEnum.LEARNING_PATH_ENROLLMENT,
          ),
        };

        ({ certificateCode, certificateUrl } = await this.certificateService.create(certificatePayload));

        if (certificateCode) {
          newLearningPathCertificateEnrollment.certificateUrl = certificateUrl;
          newLearningPathCertificateEnrollment.certificateCode = certificateCode;
          newLearningPathCertificateEnrollment.isSentEmail = true;
        }
      }

      await this.learningPathEnrollmentCertificateService.save(newLearningPathCertificateEnrollment, {
        session,
      });
      this.logger.info('Successfully learning path enrollment certificate inserted');
    }

    if (errorMessageList.length > 0) {
      await this.db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...payload, errorMessageList],
            errorList: {
              originalPayload: payload,
              message: errorMessageList,
            },
          },
          $set: {
            updatedAt: date().toDate(),
          },
        },
      );

      // Create job transaction
      const columnSettingDatas = await this.templateColumnSettingService.getColumnSettingByTemplate(
        organizationId,
        ColumnSettingTemplateEnum.assignContentManagement,
      );

      let departmentName = '';
      let supervisorName = '';
      if (user) {
        const departmentData = await this.db
          .collection(DBCollection.departments)
          .findOne({ userIds: { $in: [user.guid] } });
        departmentName = departmentData?.name ?? '';

        const userDirectReportData = await this.userDirectReportsService.findOne({ userId: user.guid });
        if (userDirectReportData) {
          const supervisor = await this.db.collection(DBCollection.users).findOne({
            guid: userDirectReportData.directReportUserId,
            organizationId,
          });
          supervisorName = getFullName(supervisor.profile);
        }
      }

      const userTransaction = this.jobTransactionService.getUserData(columnSettingDatas, user);
      const userLicenseData = await this.licenseService.find({
        userId: user?.guid,
      });
      const licenseTransactions = this.jobTransactionService.getUserLicenseDatas(userLicenseData);

      const jobTransaction = {
        jobId,
        preEnrollmentTransactionId: null,
        learningPathEnrollmentId: null,
        status: JobTransactionStatusEnum.ERROR,
        payload: {
          user: userTransaction,
          userLicenses: licenseTransactions,
          departmentName,
          supervisorName,
          originalPayload: null,
        },
        errorMessages: errorMessageList,
        warningMessages: [],
      };

      const jobTransactionModel = buildJobTransactionModel(jobTransaction);

      await this.jobTransactionService.save(jobTransactionModel, {
        session,
      });
    } else {
      const startDate = date().toDate();

      const learningPathEnrollmentData = await this.learningPathEnrollmentService.findOne({
        id,
      });

      await this.db.collection(DBCollection.learning_path_enrollments).updateOne(
        {
          id,
          userId,
          organizationId,
        },
        {
          $set: {
            status: learningPathEnrollmentStatus,
            contentProgress: learningPathContentProgress,
            completedContentItem,
            startedAt:
              learningPathEnrollmentData.startedAt ||
              this.learningPathEnrollmentService.getStartDate(learningPathEnrollmentStatus, startDate),
            finishedAt:
              learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED ? currentDate : null,
            updatedAt: currentDate,
          },
        },
      );
      this.logger.info(`Successfully assign learning path enrollment ${id}`);

      if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED) {
        const preAssignContent = await this.preAssignContentService.findOne({
          id: learningPathEnrollmentData.preAssignContentId,
          organizationId: learningPathEnrollmentData.organizationId,
        });

        const assignee = isNull(preAssignContent)
          ? null
          : await this.userService.findOne({ guid: preAssignContent.createdByUserId, organizationId });

        if (assignee) {
          const assignEnrollmentCompleteMailPayloadParams = {
            learnerUserId: user.guid,
            contentName: learningPathVersion.name,
            contentType: PreAssignContentTypeEnum.LEARNING_PATH,
            fullName: `${assignee.profile.firstname ?? ''} ${assignee.profile.lastname ?? ''}`.trim(),
            learnerFullName: `${user.profile.firstname ?? ''} ${user.profile.lastname ?? ''}`.trim(),
            round: {
              roundDate: round?.roundDate,
              expiredDate: learningPathEnrollmentData.expiredAt,
            },
          };

          const assignEnrollmentCompleteMailPayload = this.notificationService.assignEnrollmentCompleteEmail(
            assignee.email,
            assignEnrollmentCompleteMailPayloadParams,
            organization,
          );
          if (assignEnrollmentCompleteMailPayload) {
            this.notificationService.sendEmail(assignEnrollmentCompleteMailPayload);
          }

          const { message, userNotification } =
            await this.buildAssignLearningPathEnrollmentCompleteMessageAndInAppUserNotificationModel({
              learningPath,
              learningPathVersion,
              learningPathEnrollment: learningPathEnrollmentData,
              round,
              assignee,
              fullName: `${user.profile.firstname ?? ''} ${user.profile.lastname ?? ''}`.trim(),
              publishedAt: publishDateNotification,
            });

          if (userNotification) {
            await this.userNotificationService.save(userNotification, {
              session,
            });
            this.notificationService.sendUserNotificationInApplication(message, organizationId);
          }
        }
      }

      // Create job transaction
      const columnSettingDatas = await this.templateColumnSettingService.getColumnSettingByTemplate(
        organizationId,
        ColumnSettingTemplateEnum.assignContentManagement,
      );

      let departmentName = '';
      let supervisorName = '';
      if (user) {
        const departmentData = await this.db
          .collection(DBCollection.departments)
          .findOne({ userIds: { $in: [user.guid] } });
        departmentName = departmentData?.name ?? '';

        const userDirectReportData = await this.userDirectReportsService.findOne({ userId: user.guid });
        if (userDirectReportData) {
          const supervisor = await this.db.collection(DBCollection.users).findOne({
            guid: userDirectReportData.directReportUserId,
            organizationId,
          });
          supervisorName = getFullName(supervisor.profile);
        }
      }

      const userTransaction = this.jobTransactionService.getUserData(columnSettingDatas, user);
      const userLicenseData = await this.licenseService.find({
        userId: user?.guid,
      });
      const licenseTransactions = this.jobTransactionService.getUserLicenseDatas(userLicenseData);

      const jobTransaction = {
        id: uuid.v4(),
        jobId,
        preEnrollmentTransactionId: null,
        learningPathEnrollmentId: id,
        status: JobTransactionStatusEnum.PASSED,
        payload: {
          user: userTransaction,
          userLicenses: licenseTransactions,
          departmentName,
          supervisorName,
          originalPayload: null,
        },
        errorMessages: [],
        warningMessages: [],
      };

      const jobTransactionModel = buildJobTransactionModel(jobTransaction);

      await this.jobTransactionService.save(jobTransactionModel, {
        session,
      });

      const round = await this.roundService.getRoundByLearningPathIdAndRoundDate(
        learningPath.id,
        startOfDay(currentDate),
      );

      const isRound = round ? true : false;
      const courseNames = courses.map((course) => course.courseVersion.name);

      const emailData = {
        fullName: `คุณ${user.profile.firstname} ${user.profile.lastname ?? ''}`.trim(),
        isComplete: isLearningPathComplete,
        isCertificate: isPassCriteriaCertificate,
        learningPathName: learningPathVersion.name,
        learningPathCode: learningPath.code,
        courseNames,
        refName: certificateRefName,
        isRound,
        startDate:
          learningPathEnrollmentData.startedAt ||
          this.learningPathEnrollmentService.getStartDate(learningPathEnrollmentStatus, startDate) ||
          round?.roundDate,
        expiredDate:
          learningPathVersion.expiryDay > 0 ? endOfDay(addDays(startDate, learningPathVersion.expiryDay)) : null,
        thumbnailUrl: learningPath.thumbnailUrl,
        certificateCode,
        certificatePDFUrl: certificateUrl,
      };

      const organizationScheduler = await this.organizationSchedulerService.findOne({
        organizationId,
        type: OrganizationSchedulerTypeEnum.SEND_EMAIL_ASSIGN_LEARNING_PATH_ENROLLMENT,
      });
      const publishDateNotification = getPublishDateTimeOfNotification(organizationScheduler?.cron);

      await this.sendAssignLearningPathEmailScheduler(user, organization, emailData, publishDateNotification);

      const { userNotification, message } = this.buildLearningPathEnrollmentSuccessInAppNotificationMessageAndModel({
        userId,
        organizationId,
        learningPath,
        learningPathVersion,
        learningPathEnrollment: learningPathEnrollmentData,
        round,
        isComplete: isLearningPathComplete,
        isCertificate: isCertificateEnabled && isPassCriteriaCertificate,
        certificateUrl,
        publishedAt: publishDateNotification,
      });

      await this.db.collection(DBCollection.user_notifications).insertOne(userNotification, {
        session,
      });

      this.sendNotificationInApplication(organizationId, message, publishDateNotification);
    }
  }

  async sendAssignLearningPathEmailScheduler(user, organization, emailData, sentAt) {
    const mailPayload = await this.notificationService.assignLearningPathEmail(user.email, emailData, organization);
    mailPayload.sendAt = sentAt;
    this.notificationService.sendEmail(mailPayload);
  }

  async sendNotificationInApplication(organizationId, message, sendAt) {
    this.notificationService.sendUserNotificationInApplication(message, organizationId, sendAt);
  }

  buildLearningPathEnrollmentSuccessInAppNotificationMessageAndModel({
    userId,
    organizationId,
    learningPath,
    learningPathVersion,
    learningPathEnrollment,
    round,
    isComplete,
    isCertificate,
    certificateUrl,
    publishedAt,
  }) {
    let userNotification = null;
    let message = '';
    let type;

    if (isCertificate && isComplete) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE;
    } else if (isCertificate && !isComplete) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE;
    } else if (!isCertificate && isComplete) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED;
    } else if (round) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_ROUND_SUCCESS;
    } else {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_SUCCESS;
    }

    if (isCertificate || isComplete) {
      message = buildLearningPathCompleteWithCertificateNotificationMessage({
        contentName: learningPathVersion.name,
        isComplete,
        isCertificate,
      });

      userNotification = buildUserNotificationModel(
        userId,
        organizationId,
        type,
        {
          mediaId: learningPath.thumbnailMediaId,
          message,
          url: {
            code: learningPath.code,
            learningPathEnrollmentId: learningPathEnrollment.id,
            certificateUrl,
          },
        },
        publishedAt,
      );
    } else {
      message = buildLearningPathPreEnrollmentToEnrollmentSuccessNotificationMessage({
        contentName: learningPathVersion.name,
        roundDate: round?.roundDate,
        expiredAt: learningPathEnrollment?.expiredAt,
        expiryDay: learningPathVersion?.expiryDay,
      });

      userNotification = buildUserNotificationModel(
        userId,
        organizationId,
        type,
        {
          mediaId: learningPath.thumbnailMediaId,
          message,
          url: {
            code: learningPath.code,
            learningPathEnrollmentId: learningPathEnrollment.id,
          },
        },
        publishedAt,
      );
    }

    return {
      userNotification,
      message,
    };
  }

  async buildAssignLearningPathEnrollmentCompleteMessageAndInAppUserNotificationModel({
    learningPath,
    learningPathVersion,
    learningPathEnrollment,
    round,
    assignee,
    fullName,
    publishedAt,
  }) {
    const message = buildAssignEnrollmentCompletedNotificationMessage({
      fullName: fullName || '',
      contentName: learningPathVersion.name,
      roundDate: round?.roundDate,
      expiryDay: learningPathVersion?.expiryDay || 0,
    });

    const userNotification = buildUserNotificationModel(
      assignee.guid, //supervisor
      learningPathEnrollment.organizationId,
      UserNotificationTypeEnum.ASSIGN_LEARNING_PATH_ENROLLMENT_COMPLETED,
      {
        mediaId: learningPath.thumbnailMediaId,
        message,
        url: {
          code: learningPath.code,
          userId: learningPathEnrollment.userId,
          learningPathEnrollmentId: learningPathEnrollment.id,
        },
      },
      publishedAt,
    );

    return { message, userNotification };
  }

  async mainRepositoryFactory(collection, pipeline) {
    switch (collection) {
      case ColumnSettingNameCollectionEnum.USERS: {
        return userService.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.COURSES: {
        return courseService.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.LEARNING_PATH: {
        return learningPathService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async repositoryFactory(collection, pipeline) {
    switch (collection) {
      case ColumnSettingNameCollectionEnum.USERS: {
        return userService.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.DEPARTMENTS: {
        return departmentService.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.USER_DIRECT_REPORTS: {
        return userDirectReportsService.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.LICENSES: {
        return licenseService.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.LEARNING_PATH_VERSIONS: {
        return learningPathVersionService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async getCertificateDynamicValue(params) {
    const {
      resultProperties,
      organizationId,
      userId,
      courseId,
      courseVersionId,
      learningPathId,
      learningPathVersionId,
    } = params;

    const currentDate = date().toDate();
    const transformedCertificatePropertiesData = resultProperties.map((item) => {
      const key = ['t', 'i'].some((prefix) => item.key.startsWith(prefix)) ? item.key.slice(1) : item.key;
      const columnSettingParts = item.columnSettingKey ? item.columnSettingKey.split('.') : [null, null];

      return {
        key,
        columnSettingModule: columnSettingParts[0],
        columnSettingKey: item.columnSettingKey,
        type: item.type,
        value: item.value,
        mediaId: item.mediaId ? item.mediaId : null,
        name: item.name,
      };
    });

    const dynamicValue = {};
    for (const item of transformedCertificatePropertiesData) {
      if (item.type === CertificatePropertyTypeEnum.CURRENT_DATE) {
        dynamicValue[item.key] = getDateLocale(item.value, currentDate);
      } else if (item.type === CertificatePropertyTypeEnum.COLUMN_SETTING) {
        const columnSettings = await this.columnSettingRepository.findColumnSettingWithTemplate(item.columnSettingKey);

        const organizationColumnSettings = await this.organizationColumnSettingRepository.findColumnSettingWithTemplate(
          organizationId,
          item.columnSettingKey,
        );

        const columnSettingData = columnSettings.length > 0 ? columnSettings : organizationColumnSettings;

        if (item.columnSettingModule === CertificatePropertyModuleEnum.USER) {
          const userData = await this.buildAggreateCertificateAdapter.getUserColumnSettingData(
            organizationId,
            userId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const userValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            userData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = userValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.LEARNING_PATH) {
          const learningPathData = await this.buildAggreateCertificateAdapter.getLearningPathColumnSettingData(
            organizationId,
            learningPathId,
            learningPathVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            learningPathData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.COURSE) {
          const courseData = await this.buildAggreateCertificateAdapter.getCourseColumnSettingData(
            organizationId,
            courseId,
            courseVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );

          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            courseData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        }
      } else if (item.type === CertificatePropertyTypeEnum.TEXT) {
        dynamicValue[item.key] = item.value;
      } else if (item.type === CertificatePropertyTypeEnum.IMAGE_URL) {
        const media = await this.mediaService.findOne({ id: item.mediaId });
        const organizationStorage = await this.organizationStorageService.findOne({
          organizationId: media.organizationId,
          storageType: OrganizationStorageTypeEnum.RESOURCE,
        });
        const mediaPath = this.mediaService.getMediaURL(media, organizationStorage);
        dynamicValue[item.key] = mediaPath;
      }
    }
    return dynamicValue;
  }
}

export default EnrollLearningPathUseCase;
