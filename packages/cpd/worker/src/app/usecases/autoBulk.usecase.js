import { BulkOpTypeEnum, JobStatusEnum } from '@iso/lms/enums/job.enum';
import _ from 'lodash';

import { createInvoiceModel } from '@app/domains/invoice.domain';
import {
  AutoCreateUserColumn,
  AutoEditUserColumn,
  AutoEnrollmentWithCustomerColumn,
  AutoEnrollmentNoneCustomerColumn,
  AutoEnrollmentBundleWithCustomerColumn,
  AutoAssignPlanPackageLicenseColumn,
} from '@constants/autoEnrollHeaderColumn';
import { DBCollection } from '@constants/dbCollection';
import { TransactionCounterIdEnum } from '@constants/transactionCouter';
import { buildJobModel } from '@domains/job.domain';
import {
  convertToQueueActiveUserFormat,
  convertToQueueEditUserFormat,
  convertToQueueEnrollmentCourseFormat,
  convertToQueueEnrollmentBundleFormat,
  convertToQueueAssignPlanPackageLicenseFormat,
} from '@domains/preEnrollmentTransaction.domain';
import { date } from '@infrastructure/dateUtils';

class AutoBulkUseCase {
  constructor({
    config,
    db,
    webHookService,
    notificationService,
    scheduleService,
    jobService,
    transactionCounterService,
    amqp,
    slackNotificationService,
  }) {
    // Service
    this.scheduleService = scheduleService;
    this.slackNotificationService = slackNotificationService;
    this.webHookService = webHookService;
    this.notificationService = notificationService;
    this.jobService = jobService;
    this.transactionCounterService = transactionCounterService;
    this.queueService = amqp;

    // Repository
    this.enrollmentRepository = db.collection(DBCollection.enrollments);
    this.userRepository = db.collection(DBCollection.users);
    this.organizationRepository = db.collection(DBCollection.organizations);
    this.courseItemProgressHistoryRepository = db.collection(DBCollection.course_item_progress_histories);
    this.prenEnrollmentReservationRepository = db.collection(DBCollection.pre_enrollment_reservations);
    this.permissionGroupRepository = db.collection(DBCollection.permission_groups);
    this.roundRepository = db.collection(DBCollection.rounds);
    this.certificateRepository = db.collection(DBCollection.course_version_certificate);
    this.transactionCounterRepository = db.collection(DBCollection.transaction_counters);

    // Config
    this.retryDuration = config.RETRY_QUEUE_MS_DURATION || 5000; // default 1 minute
  }

  async execute(msg, channel, { organization }) {
    const { id: organizationId, domain } = organization;
    const {
      operationType,
      dataList,
      preAssignContentId = '',
      isAutoEnroll = false,
    } = JSON.parse(msg.content.toString());
    const queueNames = [domain, `${domain}:v2`];
    const pendingJob = await this.jobService.findPendingJob(organizationId, queueNames);

    if (pendingJob) {
      setTimeout(() => {
        this.execute(msg, channel, { organization });
      }, this.retryDuration);
      return;
    }

    if (dataList.length === 0) {
      channel.ack(msg);
      return;
    }

    if (
      [
        BulkOpTypeEnum.ACTIVATE,
        BulkOpTypeEnum.EDIT_USER,
        BulkOpTypeEnum.ENROLLMENT,
        BulkOpTypeEnum.ENROLLMENT_BUNDLE,
        BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE,
      ].includes(operationType)
    ) {
      await this.publishBulkByOperationType(operationType, dataList, organization, preAssignContentId, isAutoEnroll);
    }

    channel.ack(msg);
  }

  async publishBulkByOperationType(operationType, dataList, organization, preAssignContentId, isAutoEnroll) {
    const { domain, id: organizationId } = organization;
    let queueName = `${domain}`;
    let convertDataList = [];
    let rawHeaders = [];
    let operationPayload = null;

    if (operationType === BulkOpTypeEnum.ACTIVATE) {
      rawHeaders = [...AutoCreateUserColumn, 'error'];
      convertDataList = convertToQueueActiveUserFormat(dataList, organizationId);
    } else if (operationType === BulkOpTypeEnum.EDIT_USER) {
      rawHeaders = [...AutoEditUserColumn, 'error'];
      convertDataList = convertToQueueEditUserFormat(dataList, organizationId);
    } else if (operationType === BulkOpTypeEnum.ENROLLMENT) {
      const customerCodes = _.union(
        dataList
          .filter((val) => !!val.preEnrollmentReservation?.customerCode)
          .map((val) => val.preEnrollmentReservation.customerCode),
      );
      const invoiceList = await this.getInvoiceList(customerCodes);
      rawHeaders = this.getRawHeaderAndRawDataEnrollment(customerCodes);
      convertDataList = convertToQueueEnrollmentCourseFormat(dataList, organizationId, invoiceList);
    } else if (operationType === BulkOpTypeEnum.ENROLLMENT_BUNDLE) {
      const customerCodes = _.union(
        dataList
          .filter((val) => !!val.preEnrollmentReservation?.customerCode)
          .map((val) => val.preEnrollmentReservation.customerCode),
      );
      const invoiceList = await this.getInvoiceList(customerCodes);
      rawHeaders = this.getRawHeaderAndRawDataEnrollmentBundle();
      convertDataList = convertToQueueEnrollmentBundleFormat(dataList, organizationId, invoiceList);
    } else if (operationType === BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE) {
      queueName = `${domain}:v2`;
      rawHeaders = [...AutoAssignPlanPackageLicenseColumn, 'error'];
      convertDataList = convertToQueueAssignPlanPackageLicenseFormat(organizationId, dataList);
      operationPayload = { plans: convertDataList[0]?.payload?.plans };
    }

    // create job
    const jobData = buildJobModel(
      operationType,
      organizationId,
      convertDataList.length,
      queueName,
      JobStatusEnum.PENDING,
      operationPayload,
    );
    jobData.rawHeaders = rawHeaders || [];
    await this.jobService.save(jobData);
    const { guid: jobId } = jobData;

    for (const dataMessage of convertDataList) {
      this.queueService.publish(queueName, JSON.stringify({ ...dataMessage, preAssignContentId, isAutoEnroll }), {
        persistent: true,
        headers: {
          jobId,
          operationType,
        },
      });
    }
  }

  getRawHeaderAndRawDataEnrollment(customerCodes) {
    const isBulkWithCustomer = customerCodes.length > 0;
    let rawHeaders = [];
    if (isBulkWithCustomer) {
      rawHeaders = [...AutoEnrollmentWithCustomerColumn, 'error'];
    } else {
      rawHeaders = [...AutoEnrollmentNoneCustomerColumn, 'error'];
    }

    return rawHeaders;
  }

  getRawHeaderAndRawDataEnrollmentBundle() {
    const rawHeaders = [...AutoEnrollmentBundleWithCustomerColumn, 'error'];

    return rawHeaders;
  }

  async getInvoiceList(customerCodes) {
    const invoiceList = [];
    for (const customerCode of customerCodes) {
      const counterId = await this.transactionCounterService.findOneAndIncrementInvoiceId();
      const formatId = date().format('YYYYMM');
      const invoiceId = `${TransactionCounterIdEnum.INVOICE}${formatId}-${counterId}`;
      const model = {
        id: invoiceId,
        customerCode,
      };

      const invoice = createInvoiceModel(model);
      invoiceList.push(invoice);
    }
    return invoiceList;
  }
}

export default AutoBulkUseCase;
