import { getPublishDateTimeOfNotification } from '@iso/helpers/dateUtils';
import { ContentProviderTypeEnum, ExternalContentTypeEnum } from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import {
  PreEnrollmentTransactionStatusEnum,
  PreEnrollmentTransactionPaymentTypeEnum,
} from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { ProductTypeEnum, ProductSKUDistributionChannelEnum } from '@iso/lms/enums/productSKU.enum';

import { createEnrollmentBundle, createEnrollmentCertificate } from '@app/domains/enrollment.domain';
import { createEnrollmentAttachmentDeduct } from '@app/services/enrollmentAttachment.service';
import { creditManagementBundle } from '@app/services/enrollmentCredit.service';
import { DBCollection } from '@constants/dbCollection';
import { BusinessType } from '@constants/enrollment';
import { getErrorMessage } from '@constants/errorMessage';
import { GenericStatusCodes, BulkStatus } from '@constants/generic';
import { OrganizationSchedulerTypeEnum } from '@constants/organizationScheduler';
import { buildRegistrationModel } from '@domains/registration.domain';
import { date } from '@infrastructure/dateUtils';
import { AppError } from '@infrastructure/utilities/AppError';
import { runTransaction } from '@infrastructure/utilities/dbSession';

module.exports = ({
  db,
  dbClient,
  notificationService,
  logger,
  config,
  courseService,
  courseItemCriteriaConfigService,
  roundService,
  slackNotificationService,
  retailService,
  preEnrollmentTransactionService,
  organizationSchedulerService,
  partService,
  productSKUCourseService,
  courseMarketplaceService,
}) => {
  const delay = (ms) => new Promise((res) => setTimeout(res, ms));

  const sendEnrollmentEmail = async (user, course, enrollment, organization, thumbnailUrl, sendAt) => {
    const courseItemCriteriaConfigs = await courseItemCriteriaConfigService.find({
      courseVersionId: course.courseVersion.id,
    });

    let parts = [];
    if (course.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      parts = await productSKUCourseService.getPartByCourseId(course.courseVersion.courseId, course.courseVersion.id);
    } else {
      parts = await partService.getPartByCourseId(course.courseVersion.id);
    }

    parts = courseService.combineCourseItemCriteriaConfigToPart(parts, courseItemCriteriaConfigs);
    parts = courseService.removeUnavailableContent(parts);

    const mailPayload = notificationService.createEnrollmentEmail(
      user.email,
      {
        user,
        course,
        enrollment,
        thumbnailUrl,
        parts,
      },
      organization,
    );
    if (sendAt) {
      mailPayload.sendAt = sendAt;
    }

    notificationService.sendEmail(mailPayload);
  };

  const insertOperationEnrollmentCertificate = (
    enrollData,
    certificateObj,
    enrollCertOps,
    logoImageUrl,
    issuedBy,
    isSentEmailOnExpiredDate,
  ) => {
    const { id: enrollmentId } = enrollData;
    const result = createEnrollmentCertificate(
      enrollmentId,
      logoImageUrl,
      issuedBy,
      isSentEmailOnExpiredDate,
      certificateObj,
    );

    enrollCertOps.push({
      insertOne: {
        document: {
          ...result,
        },
      },
    });

    return result;
  };

  const getRound = async (roundId) => {
    const round = await roundService.findOne({
      id: roundId,
    });

    if (!round) {
      throw new AppError(GenericStatusCodes.ROUND_NOT_FOUND, getErrorMessage(GenericStatusCodes.ROUND_NOT_FOUND));
    }

    return round;
  };

  const getRoundExpireDate = (roundDate, expiryDay) => {
    const expireDate = date(roundDate).endOf('day').add(expiryDay, 'day').toDate();

    const isOverToday = expireDate > date().toDate();
    if (!isOverToday) {
      throw new AppError(
        GenericStatusCodes.COURSE_EXPIRED_NOT_OVER_TODAY,
        getErrorMessage(GenericStatusCodes.COURSE_EXPIRED_NOT_OVER_TODAY),
      );
    }

    return expireDate;
  };

  const validateEnrollmentSameRound = async ({ userId, courseId, roundId, organizationId }) => {
    const existingEnrollment = await db.collection(DBCollection.enrollments).findOne({
      userId,
      courseId,
      roundId,
      organizationId,
    });

    if (existingEnrollment) {
      throw new AppError(
        GenericStatusCodes.DUPLICATED_ENROLLMENT_SAME_ROUND,
        getErrorMessage(GenericStatusCodes.DUPLICATED_ENROLLMENT_SAME_ROUND),
      );
    }
  };

  const enrollmentAttachmentDeductDocument = async (enrollmentId, refNameAttachmentList, applicantType) => {
    let textRefName = '';
    if (refNameAttachmentList.length > 0) {
      textRefName = refNameAttachmentList.join(', ');
    }

    const resCounter = await db.collection(DBCollection.transaction_counters).findOneAndUpdate(
      {
        name: 'counter',
      },
      {
        $inc: {
          deductRequestNo: 1,
        },
      },
      {
        returnOriginal: false,
      },
    );

    const keyDeductRequestNo = `DEDUCT-${resCounter.value.deductRequestNo}`;

    const enrollmentAttachment = createEnrollmentAttachmentDeduct(
      enrollmentId,
      keyDeductRequestNo,
      applicantType,
      textRefName,
    );

    return enrollmentAttachment;
  };

  const executeEnrollmentBundleTransaction = async (payload, session, organizationId, isAutoEnroll = false) => {
    const {
      enrollment,
      enrollmentCertificates,
      invoice,
      roundId,
      preAssignContentId,
      preEnrollmentTransactionId,
      customerPartnerId,
      username,
      isBulkWithCustomer,
      userId: userIdPayload,
    } = payload;
    const { business, customerCode } = enrollment;

    const organization = await db.collection(DBCollection.organizations).findOne({
      id: organizationId,
    });

    if (!organization) {
      throw new AppError(GenericStatusCodes.ORGANIZATION_NOT_FOUND, 'organization not found');
    }

    let user = null;
    if (userIdPayload) {
      user = await db.collection(DBCollection.users).findOne({
        guid: userIdPayload,
      });
    } else {
      user = await db.collection(DBCollection.users).findOne({
        username,
        organizationId,
      });
    }

    if (!user) {
      throw new AppError(GenericStatusCodes.USER_NOT_FOUND, getErrorMessage(GenericStatusCodes.USER_NOT_FOUND));
    }

    const userId = user.guid;
    logger.info(`User found with the ID of ${userId}`);

    const productSKUId = parseInt(enrollment.productSKUId, 10);

    const [productSKUBundle] = await db
      .collection(DBCollection.product_skus)
      .aggregate([
        {
          $match: {
            id: productSKUId,
            isEnabled: true,
            type: ProductTypeEnum.BUNDLE,
          },
        },
        {
          $lookup: {
            from: 'product-sku-bundles',
            let: { id: '$id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ['$productSKUId', '$$id'],
                  },
                },
              },
            ],
            as: 'bundle',
          },
        },
        { $unwind: '$bundle' },
      ])
      .toArray();

    const productSKUDistribution = productSKUBundle?.productSKUDistributions?.find(
      (val) => val.channel === ProductSKUDistributionChannelEnum.CPD,
    );

    // product sku bundle not found or
    // product sku bundle have not course in bundle or
    // product sku bundle have not distribution cpd
    if (!productSKUBundle || productSKUBundle?.bundle?.productSKUIds.length === 0 || !productSKUDistribution) {
      throw new AppError(
        GenericStatusCodes.COURSE_NOT_FOUND,
        'ไม่พบกลุ่มหลักสูตรนี้ในระบบ หรือ กลุ่มหลักสูตรนี้ตั้งค่าผิดพลาด (ไม่ได้เปิดใช้บนระบบ)',
      );
    }

    const { bundle } = productSKUBundle;

    const productSKUs = await db
      .collection(DBCollection.product_skus)
      .aggregate([
        {
          $match: {
            id: { $in: bundle.productSKUIds },
            isEnabled: true,
            type: ProductTypeEnum.COURSE,
          },
        },
        {
          $lookup: {
            from: 'product-sku-courses',
            let: { id: '$id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ['$productSKUId', '$$id'],
                      },
                    ],
                  },
                },
              },
              {
                $project: {
                  id: 1,
                  productSKUId: 1,
                  productSKUCode: '$code',
                },
              },
            ],
            as: 'productSKUCourse',
          },
        },
        { $unwind: '$productSKUCourse' },
        {
          $lookup: {
            from: 'courses',
            let: { productSKUCourseId: '$productSKUCourse.id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$productSKUCourseId', '$$productSKUCourseId'] },
                      { $eq: ['$organizationId', organizationId] },
                    ],
                  },
                },
              },
            ],
            as: 'course',
          },
        },
        {
          $unwind: '$course',
        },
        {
          $lookup: {
            from: 'course-versions',
            let: { courseId: '$course.id' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$courseId', '$$courseId'] },
                  $and: [{ status: CourseVersionStatusEnum.PUBLISHED }],
                },
              },
            ],
            as: 'courseVersion',
          },
        },
        {
          $unwind: {
            path: '$courseVersion',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            id: 1,
            productId: 1,
            productSKUDistributions: 1,
            productSKUCourseId: '$course.productSKUCourseId',
            type: 1,
            isEnabled: 1,
            courseId: '$course.id',
            courseVersion: 1,
            courseVersionId: '$courseVersion.id',
            name: '$courseVersion.name',
            expiryDay: '$courseVersion.expiryDay',
            isCountdownArticle: '$courseVersion.isCountdownArticle',
            isMultipleCertificate: '$courseVersion.isMultipleCertificate',
            isCertificateEnabled: '$courseVersion.isCertificateEnabled',
            isSentCertificateEmailOnExpiredDate: '$courseVersion.isSentCertificateEmailOnExpiredDate',
            isIdentityVerificationEnabled: '$courseVersion.isIdentityVerificationEnabled',
            url: '$course.url',
            thumbnailUrl: '$course.thumbnailUrl',
            contentProviderType: '$course.contentProviderType',
            objectiveType: '$course.objectiveType',
            regulatorInfo: '$course.regulatorInfo',
            report: '$courseVersion.report',
          },
        },
      ])
      .toArray();

    if (bundle.productSKUIds.length !== productSKUs.length) {
      throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
    }

    const bundleProductSKUCourseIds = productSKUs.map((val) => val.productSKUCourseId);

    const isEveryIncludeCourseMarketplace = await courseMarketplaceService.checkEveryIncludeCourseMarketplace(
      organizationId,
      bundleProductSKUCourseIds,
    );

    if (!isEveryIncludeCourseMarketplace) {
      throw new AppError(GenericStatusCodes.COURSE_NOT_FOUND, getErrorMessage(GenericStatusCodes.COURSE_NOT_FOUND));
    }

    const round = await getRound(roundId);

    let customer = null;
    if (business === BusinessType.B2B && isBulkWithCustomer) {
      customer = await db.collection(DBCollection.customers).findOne({
        customerCode,
        deletedAt: null,
      });

      if (!customer) {
        throw new AppError(
          GenericStatusCodes.CUSTOMER_NOT_FOUND,
          getErrorMessage(GenericStatusCodes.CUSTOMER_NOT_FOUND),
        );
      }

      logger.info(`Customer found with the name of ${customer.customerName}`);
    }

    const courseVersionIds = productSKUs.map((val) => val.courseVersionId);
    const allCertificates = await db
      .collection(DBCollection.course_version_certificate)
      .find({ courseVersionId: { $in: courseVersionIds } })
      .toArray();

    const isCourseAllSingleCertificates = productSKUs.every(
      (val) => val.isCertificateEnabled && !val.isMultipleCertificate,
    );

    const isNotEmptyRefCode = enrollmentCertificates.some((val) => val.refCode);

    if (isCourseAllSingleCertificates && isNotEmptyRefCode) {
      throw new AppError(GenericStatusCodes.REF_CODE_DISABLE, getErrorMessage(GenericStatusCodes.REF_CODE_DISABLE));
    }

    const enrollDatas = [];
    for (const course of productSKUs) {
      logger.info(`productSKU found with the name of ${course.name}`);

      logger.info('Checking if expiry date of course is over current day...');
      const expireDate = getRoundExpireDate(round.roundDate, course.expiryDay);

      logger.info('Checking has enroll same round');
      await validateEnrollmentSameRound({ userId, courseId: course.courseId, roundId, organizationId });

      const enrollData = createEnrollmentBundle(enrollment, {
        business,
        customerCode: customer?.customerCode,
        userId,
        courseId: course.courseId,
        courseVersionId: course.courseVersionId,
        isCountdownArticle: course.isCountdownArticle,
        isIdentityVerificationEnabled: course.isIdentityVerificationEnabled,
        roundId: round.id,
        preAssignContentId,
        expiredAt: expireDate,
        organizationId,
        externalContentType: ExternalContentTypeEnum.MARKETPLACE,
      });

      enrollDatas.push({
        enrollment: enrollData,
        course,
      });
      logger.info('Successfully built the document for the enrollment');

      logger.info('Preparing for insertion ...');
      await db.collection(DBCollection.enrollments).bulkWrite(
        [
          {
            insertOne: {
              document: enrollData,
            },
          },
        ],
        {
          session,
        },
      );
      logger.info('Successfully insert enrollment');

      const certificates = allCertificates.filter((val) => val.courseVersionId === course.courseVersionId);
      const { isMultipleCertificate, isCertificateEnabled, isSentCertificateEmailOnExpiredDate, report } = course;
      const enrollCertOps = [];
      const refNameAttachmentList = [];
      if (isCertificateEnabled) {
        const isDynamicCertificate = customer?.certificateConfig?.isDynamicCertificate || false;
        let certificateLogoImageUrl = '';
        let certificateTextIssuedBy = '';
        if (isDynamicCertificate) {
          certificateLogoImageUrl = customer?.certificateConfig?.logoImageUrl || '';
          certificateTextIssuedBy = customer?.certificateConfig?.textDynamicCertificate || '';
        } else {
          certificateLogoImageUrl = organization.certificateConfig?.logoImageUrl || '';
          certificateTextIssuedBy = organization.certificateConfig?.textDynamicCertificate || '';
        }

        if (certificates.length === 0) {
          throw new AppError(GenericStatusCodes.CERT_NOT_FOUND, getErrorMessage(GenericStatusCodes.CERT_NOT_FOUND));
        }

        // Single Cert
        if (!isMultipleCertificate) {
          const certificateObj = certificates[0];
          const enrollmentCertificateDocument = insertOperationEnrollmentCertificate(
            enrollData,
            { ...certificateObj, tsiCode: report.tsiCode, pillarName: report.pillarName },
            enrollCertOps,
            certificateLogoImageUrl,
            certificateTextIssuedBy,
            isSentCertificateEmailOnExpiredDate,
          );
          if (enrollmentCertificateDocument.refName) {
            refNameAttachmentList.push(enrollmentCertificateDocument.refName);
          }
        }
        // Multi Cert
        else {
          let isValidMultiCert = false;
          for (const data of enrollmentCertificates) {
            const certificateObj = allCertificates.find((val) => val.refCode === data.refCode);
            if (data.refCode && !certificateObj) {
              throw new AppError(
                GenericStatusCodes.REF_CODE_NOT_FOUND,
                getErrorMessage(GenericStatusCodes.REF_CODE_NOT_FOUND),
              );
            }

            if (certificateObj && certificateObj.courseId === course.courseId) {
              isValidMultiCert = true;
              const enrollmentCertificateDocument = insertOperationEnrollmentCertificate(
                enrollData,
                { ...certificateObj, tsiCode: report.tsiCode, pillarName: report.pillarName },
                enrollCertOps,
                certificateLogoImageUrl,
                certificateTextIssuedBy,
                isSentCertificateEmailOnExpiredDate,
              );

              if (enrollmentCertificateDocument.refName) {
                refNameAttachmentList.push(enrollmentCertificateDocument.refName);
              }
            }
          }

          if (!isValidMultiCert) {
            throw new AppError(
              GenericStatusCodes.REF_CODE_NOT_FOUND,
              getErrorMessage(GenericStatusCodes.REF_CODE_NOT_FOUND),
            );
          }
        }
      } else if (isMultipleCertificate) {
        throw new AppError(GenericStatusCodes.CERT_NOT_FOUND, 'กรุณาเปิด certificate');
      }
      logger.info('Successfully built the document for the enrollment-certificate');
      if (enrollCertOps.length > 0) {
        await db.collection(DBCollection.enrollment_certificates).bulkWrite(enrollCertOps, {
          session,
        });
        logger.info('Successfully insert enrollment-certificate');
      }

      if (course.regulatorInfo?.isRequireDeductDocument) {
        logger.info('Preparing create enrollment-attachments');
        const enrollmentAttachment = await enrollmentAttachmentDeductDocument(
          enrollData.id,
          refNameAttachmentList,
          course.regulatorInfo.applicantType,
        );

        await db.collection(DBCollection.enrollment_attachments).insertOne(enrollmentAttachment, {
          session,
        });

        logger.info('Successfully enrollment-attachments inserted');
      }
    }

    const registrationDatas = [];
    for (const item of enrollDatas) {
      logger.info(`Preparing create registrations`);
      const enrollmentId = item.enrollment.id;
      const registration = buildRegistrationModel(enrollmentId, customerPartnerId);

      await db.collection(DBCollection.registrations).insertOne(registration, {
        session,
      });
      logger.info('Successfully insert registrations');

      registrationDatas.push({
        registrationId: registration.id,
        courseId: item.course.courseId,
      });
    }
    const registrationIds = registrationDatas.map((val) => val.registrationId);

    if (invoice) {
      logger.info(`Preparing credit management`);
      await creditManagementBundle(db, session, logger, productSKUBundle, invoice, registrationIds);
      logger.info(`Successfully credit management`);
    }

    if (preEnrollmentTransactionId) {
      logger.info(`Preparing update enrollment regulator report`);
      for (const registrationData of registrationDatas) {
        await db.collection(DBCollection.enrollment_regulator_reports).updateOne(
          {
            preEnrollmentTransactionId,
            courseId: registrationData.courseId,
          },
          {
            $set: {
              registrationId: registrationData.registrationId,
            },
          },
          {
            session,
          },
        );
      }
      logger.info(`Successfully Update enrollment regulator report`);
    }

    const isSendSchedulerNotification = !!preEnrollmentTransactionId;
    let publishedDateTimeNotification = null;
    if (isSendSchedulerNotification) {
      const organizationScheduler = await organizationSchedulerService.findOne({
        type: OrganizationSchedulerTypeEnum.SEND_EMAIL_CREATE_ENROLLMENT,
        organizationId: organization.id,
      });
      publishedDateTimeNotification = getPublishDateTimeOfNotification(organizationScheduler?.cron);
    }
    for (const item of enrollDatas) {
      logger.info(`Preparing to send a enroll email to ${user.email}`);
      const thumbnailUrl = item.course.thumbnailUrl ?? '';
      if (isSendSchedulerNotification) {
        await sendEnrollmentEmail(
          user,
          item.course,
          item.enrollment,
          organization,
          thumbnailUrl,
          publishedDateTimeNotification,
        );
      } else {
        await sendEnrollmentEmail(user, item.course, item.enrollment, organization, thumbnailUrl);
      }
    }
  };

  const bulkEnrollUserBundle = async (msg, channel, { organization }) => {
    const { domain, id: organizationId, fqdn } = organization;
    const { jobId } = msg.properties.headers;

    const {
      payload,
      originalPayload,
      raw,
      preAssignContentId: preAssignContentIdFromBulk = null,
      isAutoEnroll,
    } = JSON.parse(msg.content.toString());

    const { preAssignContentId: preAssignContentIdFromPreEnrollmentTransaction = null } = payload;

    const preAssignContentId = isAutoEnroll
      ? preAssignContentIdFromPreEnrollmentTransaction
      : preAssignContentIdFromBulk;

    await delay(300);
    logger.info(`Start processing the job ID ${jobId} of type ENROLLMENT BUNDLE`);

    const { messageCount } = await channel.assertQueue(domain);
    const session = dbClient.startSession();
    const { preEnrollmentTransactionId } = payload;

    try {
      await runTransaction(executeEnrollmentBundleTransaction, session, [
        { ...payload, preAssignContentId },
        session,
        organizationId,
        isAutoEnroll,
      ]);
      if (preEnrollmentTransactionId) {
        const preEnrollmentTransaction = await db
          .collection(DBCollection.pre_enrollment_transactions)
          .findOne({ id: preEnrollmentTransactionId });

        await db.collection(DBCollection.pre_enrollment_transactions).updateOne(
          {
            id: payload.preEnrollmentTransactionId,
          },
          {
            $set: {
              'operationExecute.isEnrollmentBundle': true,
              status: PreEnrollmentTransactionStatusEnum.APPLIED,
              'contentItems.$[].isHavingCost': true,
            },
          },
        );

        if (
          preEnrollmentTransaction.paymentType === PreEnrollmentTransactionPaymentTypeEnum.RETAIL &&
          preEnrollmentTransaction.businessType === BusinessType.B2C
        ) {
          const response = await retailService.updateRetailOrder(preEnrollmentTransactionId);
          if (response.status === 200) {
            await preEnrollmentTransactionService.updatePreEnrollmentTransactionIsUpdatedRetailOrder(
              preEnrollmentTransactionId,
            );
          }
        }
      }
      channel.ack(msg);
    } catch (error) {
      logger.error(`Error processing the job with the ID of ${jobId}, errorMessage: ${error.message}`);

      if (preEnrollmentTransactionId) {
        await db.collection(DBCollection.pre_enrollment_transactions).updateOne(
          {
            id: payload.preEnrollmentTransactionId,
          },
          {
            $set: {
              'operationExecute.isEnrollmentBundle': true,
              status: PreEnrollmentTransactionStatusEnum.FAILED_TO_APPLY,
            },
          },
        );
      }

      const updateJob = {
        $push: {
          rawPayloads: [...raw, error.message],
          errorList: {
            originalPayload: {
              ...originalPayload,
              preEnrollmentId: preEnrollmentTransactionId || '',
            },
            message: error.message,
          },
        },
      };
      await db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        updateJob,
      );

      channel.nack(msg, false, false);
    } finally {
      if (messageCount === 0) {
        const job = await db.collection(DBCollection.jobs).findOne({
          guid: jobId,
        });
        const { errorList } = job;
        await db.collection(DBCollection.jobs).updateOne(
          {
            guid: jobId,
          },
          {
            $set: {
              status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
              updatedAt: date().toDate(),
            },
          },
        );

        const url = `${config.CLIENT_PROTOCOL}://${domain}.${fqdn}/admin/setting/jobs/${job.guid}`;
        slackNotificationService.notiOperationExecute(
          job.operationType,
          job.total,
          job.errorList.length,
          job.userId,
          url,
          domain,
        );
      }
      session.endSession();
      logger.info('End Session');
    }
  };

  return {
    bulkEnrollUserBundle,
  };
};
