import { getPublishDateTimeOfNotification } from '@iso/helpers/dateUtils';
import * as rs from 'randomstring';

import { DBCollection } from '@constants/dbCollection';
import { BusinessType } from '@constants/enrollment';
import { BulkStatus, GenericStatusCodes } from '@constants/generic';
import { OrganizationSchedulerTypeEnum } from '@constants/organizationScheduler';
import { PreEnrollmentReservationStatusEnum } from '@constants/preEnrollmentReservation';
import { PreEnrollmentTransactionStatusEnum } from '@constants/preEnrollmentTransaction';
import { createLicenseModel } from '@domains/license.domain';
import { createUserLogin } from '@domains/userLogin.domain';
import { date } from '@infrastructure/dateUtils';
import { AppError, getFormattedWriteErrorMessage } from '@infrastructure/utilities/AppError';
import { runTransaction } from '@infrastructure/utilities/dbSession';
import { delay, createFirstPasswordToken } from '@infrastructure/utilities/helper';
module.exports = ({
  cryptography,
  notificationService,
  db,
  dbClient,
  logger,
  config,
  slackNotificationService,
  idsService,
  organizationSchedulerService,
}) => {
  const activateUserBulkTransaction = async (payload, session) => {
    const user = {
      ...payload,
    };
    const {
      username,
      email,
      citizenId,
      licenses: userLicenses,
      organizationId,
      profile,
      isPassedUlSaleQualify,
      isTerminated,
    } = user;
    const { salute, firstname, lastname, mobilePhoneNumber, dateOfBirth } = profile;

    let userExist = null;

    const organization = await db.collection(DBCollection.organizations).findOne({
      id: organizationId,
    });

    if (!organization) {
      throw new AppError(GenericStatusCodes.ORGANIZATION_NOT_FOUND, 'organization not found');
    }

    if (citizenId) {
      userExist = await db.collection(DBCollection.users).findOne({ citizenId, organizationId });
      if (userExist) {
        throw new AppError(GenericStatusCodes.EXIST_USER, 'มีหมายเลขบัตรประชาชนนี้ในระบบแล้ว');
      }
    }

    userExist = await db.collection(DBCollection.users).findOne({ email, organizationId });
    if (userExist) {
      throw new AppError(GenericStatusCodes.EXIST_USER, 'มีอีเมลนี้ในระบบแล้ว');
    }

    userExist = await db.collection(DBCollection.users).findOne({ username, organizationId });
    if (userExist) {
      throw new AppError(GenericStatusCodes.EXIST_USER, 'มีชื่อผู้ใช่งานนี้ในระบบแล้ว');
    }

    const plainPassword = rs.generate(8);

    const hash = await cryptography.hashPassword(plainPassword);

    const isManualActivateUser = !user.preEnrollmentTransactionId;

    if (isManualActivateUser) {
      const preEnrollments = await db
        .collection(DBCollection.pre_enrollment_transactions)
        .aggregate([
          {
            $project: {
              _id: 1,
              id: 1,
              jobId: 1,
              status: 1,
              businessType: 1,
              payload: 1,
              preEnrollmentReservationId: 1,
              organizationId: 1,
            },
          },
          {
            $match: {
              $and: [
                {
                  $or: [
                    {
                      'payload.email': email,
                    },
                    {
                      'payload.citizenId': citizenId,
                    },
                  ],
                },
                {
                  organizationId,
                },
                {
                  status: {
                    $in: [
                      PreEnrollmentTransactionStatusEnum.PASSED,
                      PreEnrollmentTransactionStatusEnum.WAITING_VERIFY,
                      PreEnrollmentTransactionStatusEnum.EDITED_AFTER_VERIFY,
                    ],
                  },
                },
              ],
            },
          },
          {
            $facet: {
              b2b: [
                {
                  $match: {
                    businessType: BusinessType.B2B,
                  },
                },
                {
                  $lookup: {
                    from: 'pre-enrollment-reservations',
                    let: {
                      preEnrollmentReservationId: '$preEnrollmentReservationId',
                    },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [
                              {
                                $eq: ['$id', '$$preEnrollmentReservationId'],
                              },
                              {
                                $eq: ['$status', PreEnrollmentReservationStatusEnum.PASSED],
                              },
                            ],
                          },
                        },
                      },
                      {
                        $project: {
                          _id: 0,
                          operationType: 1,
                          customerCode: 1,
                          roundDate: 1,
                          status: 1,
                        },
                      },
                    ],
                    as: 'preEnrollmentReservation',
                  },
                },
                {
                  $unwind: '$preEnrollmentReservation',
                },
              ],
              b2c: [
                {
                  $match: {
                    businessType: BusinessType.B2C,
                  },
                },
              ],
            },
          },
          {
            $project: {
              transactions: {
                $concatArrays: ['$b2b', '$b2c'],
              },
            },
          },
          {
            $unwind: {
              path: '$transactions',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $replaceRoot: {
              newRoot: {
                $ifNull: ['$transactions', {}],
              },
            },
          },
          {
            $project: {
              _id: 1,
              id: 1,
              jobId: 1,
              status: 1,
              businessType: 1,
              payload: 1,
            },
          },
          {
            $limit: 1,
          },
        ])
        .toArray();

      const [preEnrollment] = preEnrollments;
      if (preEnrollment?.payload?.email === email) {
        throw new AppError(GenericStatusCodes.EXIST_USER, 'พบอีเมลซ้ำในรายการรอลงอบรม');
      }
      if (preEnrollment?.payload?.citizenId === citizenId) {
        throw new AppError(GenericStatusCodes.EXIST_USER, 'พบบัตรประชาชนซ้ำในรายการรอลงอบรม');
      }
    }

    user.passwordHash = hash;
    user.profile.dateOfBirth = dateOfBirth ? date(dateOfBirth).toDate() : null;
    user.isPassedUlSaleQualify = isPassedUlSaleQualify ?? false;
    user.isTerminated = isTerminated ?? false;
    user.createdAt = date(user.createdAt).toDate();
    user.updatedAt = date(user.updatedAt).toDate();

    const firstPasswordToken = await createFirstPasswordToken({
      guid: user.guid,
      email: user.email,
      organizationId: user.organizationId,
    });

    if (organization.isEnableLocalLogin) {
      user.firstPasswordToken = firstPasswordToken;
    }

    delete user.licenses;
    delete user.preEnrollmentTransactionId;

    logger.info('Create User');
    await db.collection(DBCollection.users).insertOne(user, {
      session,
    });

    if (userLicenses.length > 0) {
      const bulkLicenseOperations = [];

      for (const license of userLicenses) {
        const { licenseTypeCode, licenseNo, type, startedAt, expiredAt } = license;
        const licenseData = createLicenseModel({
          userId: user.guid,
          licenseTypeCode,
          licenseNo,
          type,
          organizationId,
          startedAt,
          expiredAt,
        });

        if (licenseNo) {
          bulkLicenseOperations.push({
            insertOne: {
              document: licenseData,
            },
          });
        }
      }

      if (bulkLicenseOperations.length > 0) {
        logger.info('Create licenses');
        await db.collection(DBCollection.licenses).bulkWrite(bulkLicenseOperations, {
          session,
        });
      }
    }

    if (organization.isEnableLocalLogin) {
      logger.info('Send email');
      const instructionBanner = `${config.S3_BUCKET_URL}/assets/new_user_instruction_v2.png`;
      const urlEmail = `${config.CLIENT_PROTOCOL}://${organization.domain}.${organization.fqdn}/firstPassword?token=${firstPasswordToken}`;
      const emailData = {
        url: urlEmail,
        instructionBanner,
        username,
        fullName: `${user.profile.firstname} ${user.profile.lastname}`,
      };
      const mailPayload = await notificationService.firstSetPasswordEmail(user.email, emailData, organization);
      if (isManualActivateUser) {
        notificationService.sendEmail(mailPayload);
      } else {
        const organizationScheduler = await organizationSchedulerService.findOne({
          type: OrganizationSchedulerTypeEnum.SEND_EMAIL_CREATE_USER,
          organizationId,
        });
        mailPayload.sendAt = getPublishDateTimeOfNotification(organizationScheduler?.cron);
        notificationService.sendEmail(mailPayload);
      }
    }

    const loginProviders = await getOrganizationLoginProviderSkilllane(organizationId);
    const isEnableSSOLogin = !!loginProviders.length;

    if (isEnableSSOLogin) {
      const [loginProvider] = loginProviders;

      const userLogin = createUserLogin({
        organizationLoginProviderId: loginProvider.organizationLoginProviderId,
        loginProviderKey: null,
        userId: user.guid,
      });

      await db.collection(DBCollection.user_logins).insertOne(userLogin, {
        session,
      });

      if (!organization.isEnableLocalLogin) {
        const idsProfile = {
          Email: email,
          CitizenId: cryptography.encrypt(citizenId),
          Salute: salute,
          FirstName: firstname,
          LastName: lastname,
          Phone: mobilePhoneNumber,
          Guid: user.guid,
        };

        const createUserUrl = idsService.generateCreateUserUrl(loginProvider.organizationLoginProviderId, idsProfile);

        logger.info('Send email');
        const instructionBanner = `${config.S3_BUCKET_URL}/assets/citizenId_instruction.png`;
        const emailData = {
          url: createUserUrl,
          instructionBanner,
          username: 'เลขประจำตัวประชาชนของคุณ',
          fullName: `${user.profile.firstname} ${user.profile.lastname}`,
        };
        const mailPayload = await notificationService.firstSetPasswordEmail(user?.email, emailData, organization);

        if (isManualActivateUser) {
          notificationService.sendEmail(mailPayload);
        } else {
          const organizationScheduler = await organizationSchedulerService.findOne({
            type: OrganizationSchedulerTypeEnum.SEND_EMAIL_CREATE_USER,
            organizationId,
          });
          mailPayload.sendAt = getPublishDateTimeOfNotification(organizationScheduler?.cron);
          notificationService.sendEmail(mailPayload);
        }
      }
    }
  };

  async function activateUserBulk(msg, channel, { organization }) {
    await delay(300);
    const { domain, fqdn } = organization;
    const { jobId } = msg.properties.headers;
    const { payload: user, originalPayload, raw } = JSON.parse(msg.content.toString());

    logger.info(`Start processing the job with the ID of ${jobId}`);

    const { messageCount } = await channel.assertQueue(domain);
    const session = dbClient.startSession();
    try {
      await runTransaction(activateUserBulkTransaction, session, [user, session]);
      channel.ack(msg);
    } catch (error) {
      logger.error(`Error processing the job with the ID of ${jobId}, errorMessage: ${error.message}`);
      const msgErr = error.code === 11000 ? getFormattedWriteErrorMessage(error) : error.message;

      await db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...raw, msgErr],
            errorList: {
              originalPayload: {
                ...originalPayload,
                preEnrollmentId: user.preEnrollmentTransactionId || '',
              },
              message: msgErr,
            },
          },
        },
      );
      channel.nack(msg, false, false);
    } finally {
      if (user.preEnrollmentTransactionId) {
        await db.collection(DBCollection.pre_enrollment_transactions).updateOne(
          {
            id: user.preEnrollmentTransactionId,
          },
          {
            $set: {
              'operationExecute.isActivateUser': true,
            },
          },
        );
      }

      if (messageCount === 0) {
        const job = await db.collection(DBCollection.jobs).findOne({
          guid: jobId,
        });
        const { errorList } = job;
        await db.collection(DBCollection.jobs).updateOne(
          {
            guid: jobId,
          },
          {
            $set: {
              status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
              updatedAt: date().toDate(),
            },
          },
        );

        const url = `${config.CLIENT_PROTOCOL}://${domain}.${fqdn}/admin/setting/jobs/${job.guid}`;
        slackNotificationService.notiOperationExecute(
          job.operationType,
          job.total,
          job.errorList.length,
          job.userId,
          url,
          domain,
        );
      }

      session.endSession();
      logger.info('End Session');
    }
  }

  async function getOrganizationLoginProviderSkilllane(organizationId) {
    return db
      .collection(DBCollection.login_providers)
      .aggregate([
        {
          $match: {
            type: 'skilllane',
          },
        },
        {
          $lookup: {
            from: 'organization-login-providers',
            let: { loginProviderId: '$id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ['$loginProviderId', '$$loginProviderId'],
                      },
                      {
                        $eq: ['$organizationId', organizationId],
                      },
                      {
                        $eq: ['$isEnabled', true],
                      },
                    ],
                  },
                },
              },
            ],
            as: 'organizationLoginProvider',
          },
        },
        {
          $unwind: '$organizationLoginProvider',
        },
        {
          $project: {
            id: 1,
            type: 1,
            organizationLoginProviderId: '$organizationLoginProvider.id',
          },
        },
      ])
      .toArray();
  }

  return {
    activateUserBulk,
  };
};
