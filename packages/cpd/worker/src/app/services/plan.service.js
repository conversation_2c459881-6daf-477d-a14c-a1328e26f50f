import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { PackageTypeEnum } from '@iso/lms/enums/packages.enum';

import { date } from '@infrastructure/dateUtils';
import BaseService from '@infrastructure/services/base.service';

class PlanService extends BaseService {
  constructor({ db, logger }) {
    super(db, DBCollectionEnum.PLANS, logger);
  }

  async findPlanPackageContent(organizationId) {
    const aggregateParams = [
      {
        $match: {
          organizationId,
          endDate: { $gte: date().toDate() },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$planId', '$$id'],
                    },
                    { $eq: ['$type', PackageTypeEnum.CONTENT] },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      { $unwind: '$planPackage' },
    ];

    return this.aggregate(aggregateParams);
  }

  async findPendingPlanPackagePlatform(organizationId) {
    const aggregateParams = [
      {
        $match: {
          organizationId,
          endDate: { $gte: date().toDate() },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$planId', '$$id'],
                    },
                    { $eq: ['$type', PackageTypeEnum.PLATFORM] },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      { $unwind: '$planPackage' },
    ];

    const output = await this.aggregate(aggregateParams);
    return output;
  }

  async findActivePlanPackagePlatform(organizationId) {
    const aggregateParams = [
      {
        $match: {
          organizationId,
          startDate: { $lte: date().toDate() },
          endDate: { $gte: date().toDate() },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$planId', '$$id'],
                    },
                    { $eq: ['$type', PackageTypeEnum.PLATFORM] },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      { $unwind: '$planPackage' },
    ];

    const output = await this.aggregate(aggregateParams);
    return output;
  }
}

export default PlanService;
