import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { PackageTypeEnum } from '@iso/lms/enums/packages.enum';

import { date } from '@infrastructure/dateUtils';
import BaseService from '@infrastructure/services/base.service';

class PlanPackageLicenseService extends BaseService {
  constructor({ db, logger }) {
    super(db, DBCollectionEnum.PLAN_PACKAGE_LICENSES, logger);
  }

  async findPlanPackageLicenseSOPendingActive(userId, planPackageIds) {
    const pipeline = [
      {
        $match: {
          userId,
          planPackageId: {
            $in: planPackageIds,
          },
          startedAt: null,
          expiredAt: null,
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: {
            planPackageId: '$planPackageId',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$id', '$$planPackageId'],
                    },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      {
        $unwind: '$planPackage',
      },
      {
        $project: {
          _id: 0,
          id: 1,
          planPackageId: 1,
          userId: 1,
          startedAt: '$planPackage.startDate',
          expiredAt: '$planPackage.endDate',
          createdAt: 1,
          updatedAt: 1,
          deletedAt: 1,
        },
      },
      {
        $match: {
          expiredAt: {
            $gte: date().toDate(),
          },
        },
      },
    ];

    return this.aggregate(pipeline);
  }

  async findPlanPackageLicenseSOApprovedActive(userId, planPackageIds) {
    return this.find({
      userId,
      planPackageId: {
        $in: planPackageIds,
      },
      startedAt: { $ne: null },
      expiredAt: { $gte: date().toDate() },
    });
  }

  async findActivePlanPackageLicenseByUserIds(userIds, planPackageIds) {
    const aggregateParams = [
      {
        $match: {
          userId: {
            $in: userIds,
          },
          planPackageId: {
            $in: planPackageIds,
          },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: {
            planPackageId: '$planPackageId',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$id', '$$planPackageId'],
                    },
                    { $gte: ['$endDate', date().toDate()] },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      {
        $unwind: '$planPackage',
      },
      {
        $project: {
          id: 1,
          planPackageId: 1,
          userId: 1,
          startedAt: {
            $ifNull: ['$startedAt', '$planPackage.startDate'],
          },
          expiredAt: {
            $ifNull: ['$expiredAt', '$planPackage.endDate'],
          },
          createdAt: 1,
          updatedAt: 1,
        },
      },
    ];

    const output = this.aggregate(aggregateParams);
    return output;
  }

  async findActivePlanPackagePlatformLicenseByUserId(userId) {
    const dateNow = date().toDate();
    const aggregateParams = [
      {
        $match: {
          userId,
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { planPackageId: '$planPackageId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$id', '$$planPackageId'],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      {
        $match: {
          $and: [
            { 'planPackage.type': PackageTypeEnum.PLATFORM },
            {
              $or: [
                {
                  startedAt: null,
                  expiredAt: null,
                  'planPackage.startDate': {
                    $lte: dateNow,
                  },
                  'planPackage.endDate': {
                    $gte: dateNow,
                  },
                },
                {
                  startedAt: {
                    $lte: dateNow,
                  },
                  expiredAt: {
                    $gte: dateNow,
                  },
                },
              ],
            },
          ],
        },
      },
    ];

    const results = await this.aggregate(aggregateParams);
    return results;
  }
}

export default PlanPackageLicenseService;
