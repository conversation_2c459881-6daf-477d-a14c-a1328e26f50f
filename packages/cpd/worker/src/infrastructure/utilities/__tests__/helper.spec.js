import { getPublishDateTimeOfNotification } from '@iso/helpers/dateUtils';

import { isValidThaiName } from '@infrastructure/utilities/helper';
import { date, DateFormat } from '@root/infrastructure/dateUtils';

describe('when call function isValidThaiName', () => {
  describe('when correct', () => {
    it('when Thai characters expect return true', () => {
      const result = 'สมชาย';
      expect(isValidThaiName(result)).toBe(true);
    });

    it('when Thai characters and space expect return true', () => {
      const result = 'ณ อยุธยา';
      expect(isValidThaiName(result)).toBe(true);
    });

    it('when Thai characters and space expect return true', () => {
      const result = 'สมชาย ณ อยุธยา';
      expect(isValidThaiName(result)).toBe(true);
    });

    it('when Thai characters with dot expect return true', () => {
      const result = 'ส.สุริศักดิ์';
      expect(isValidThaiName(result)).toBe(true);
    });

    it('when Thai characters with dot expect return true', () => {
      const result = 'สมชาย ส.สุริศักดิ์';
      expect(isValidThaiName(result)).toBe(true);
    });

    it('when Thai characters end with dot expect return true', () => {
      const result = 'ส.สุริศักดิ์ โพธิ์ ร.ห.';
      expect(isValidThaiName(result)).toBe(true);
    });
  });

  describe('when incorrect', () => {
    it('when Thai characters and 2 space expect return false', () => {
      const result = 'สมชาย  ณ อยุธยา';
      expect(isValidThaiName(result)).toBe(false);
    });

    it('when non-Thai characters expect return false', () => {
      const result = 'John Doe';
      expect(isValidThaiName(result)).toBe(false);
    });

    it('when number expect return false', () => {
      const text = '123456';
      const result = isValidThaiName(text);
      expect(result).toBe(false);
    });

    it('when emtpy expect return false', () => {
      const text = '';
      const result = isValidThaiName(text);
      expect(result).toBe(false);
    });

    it('when null expect return false', () => {
      const text = null;
      const result = isValidThaiName(text);
      expect(result).toBe(false);
    });

    it('when Thai and non-Thai characters expect return false', () => {
      const result = 'สมชายIII';
      expect(isValidThaiName(result)).toBe(false);
    });

    it('when Thai characters with special characters expect return false', () => {
      const result = 'สมชาย#ใจดี';
      expect(isValidThaiName(result)).toBe(false);
    });

    it('when Thai characters start with space expect return flase', () => {
      const result = ' สุริศักดิ์';
      expect(isValidThaiName(result)).toBe(false);
    });

    it('when Thai characters start with dot expect return flase', () => {
      const result = '.สุริศักดิ์';
      expect(isValidThaiName(result)).toBe(false);
    });

    it('when Thai characters with 2 dot expect return false', () => {
      const result = 'ส..สุริศักดิ์';
      expect(isValidThaiName(result)).toBe(false);
    });

    it('when Thai characters end with space expect return flase', () => {
      const result = 'ส.สุริศักดิ์ โพธิ์ ร.ห ';
      expect(isValidThaiName(result)).toBe(false);
    });
  });

  describe('Test Case: getPublishDateTimeOfNotification', () => {
    it('when input is cron, expect it change to date', () => {
      const publishDate = getPublishDateTimeOfNotification('0 9 * * *');
      expect(publishDate).toBeInstanceOf(Date);
      const output = date(publishDate).format(DateFormat.hourMinute);
      expect(output).toBe('09:00');
    });
  });
});
