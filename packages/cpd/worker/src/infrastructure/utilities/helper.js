import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { sign } from 'jsonwebtoken';
import { find, some } from 'lodash';

import config from '@config/main';
import { date } from '@infrastructure/dateUtils';

export function getChristianYear(year) {
  return parseInt(year) - 543;
}

export function getBuddhishYearr(year) {
  return parseInt(year) + 543;
}

export function stringFormat(msg, ...arr) {
  const args = arr;
  return msg.replace(/{(\d+)}/g, (match, number) => (typeof args[number] !== 'undefined' ? args[number] : match));
}

export function onlyUnique(arrayList) {
  return [...new Set(arrayList)];
}

export async function createFirstPasswordToken(params) {
  const { guid, email, organizationId } = params;
  const data = { guid, email, organizationId };

  const tokenSecret = {
    key: base64Decode(config.MANAGE_PASSWORD_TOKEN_PRIVATEKEY || ''),
    passphrase: config.MANAGE_PASSWORD_TOKEN_PASSPHRASE || '',
  };
  const jwtOptions = {
    algorithm: config.TOKEN_ALGORITHM,
    expiresIn: config.SET_FIRST_PASSWORD_TOKEN_EXPIRES,
    issuer: config.TOKEN_ISSUER,
    audience: config.TOKEN_AUDIENCE,
  };

  return createJwt(data, tokenSecret, jwtOptions);
}

export function createJwt(data, tokenSecret, jwtOptions) {
  return new Promise((resolve, reject) => {
    sign(data, tokenSecret, jwtOptions, (err, token) => {
      if (!err) {
        resolve(token);
      } else {
        reject(err);
      }
    });
  });
}

export function base64Decode(dataEncode) {
  // ascii, utf-8, etc..
  return Buffer.from(dataEncode, 'base64').toString();
}

export const delay = (ms) => new Promise((res) => setTimeout(res, ms));

export const convertToLowerCase = (val) => val.toString().toLowerCase().trim();

export const isValidThaiName = (name) => {
  const thaiRegex = /^[\u0E00-\u0E7F\s.]+$/g;

  // Check if the name matches the Thai regex
  return (
    thaiRegex.test(name) &&
    !name.includes('  ') &&
    !name.startsWith(' ') &&
    !name.endsWith(' ') &&
    !name.includes('..') &&
    !name.startsWith('.')
  );
};

export const checkReEnrollableRegularCourse = ({
  enrollmentStatus,
  enrollmentPassedAt,
  enrollmentExpiredAt,
  isReEnrollEnabled,
  isReEnrollExpireEnabled,
  reEnrollDay,
  reEnrollExpireDay,
}) => {
  const dateNow = date().toDate();
  const enrollableDate = date(enrollmentPassedAt).startOf('day').add(reEnrollDay, 'day').toDate();
  const isReEnrollableDate = enrollableDate <= dateNow;

  const enrollableExpireDate = date(enrollmentExpiredAt).startOf('day').add(reEnrollExpireDay, 'day').toDate();
  const isReEnrollableExpireDate = enrollableExpireDate <= dateNow;

  const isEnrollmentExpired =
    enrollmentStatus === EnrollmentStatusEnum.EXPIRED && isReEnrollExpireEnabled && isReEnrollableExpireDate;
  if (isEnrollmentExpired) return true;

  const isEnrollmentPassed =
    enrollmentStatus === EnrollmentStatusEnum.PASSED && isReEnrollEnabled && isReEnrollableDate;
  if (isEnrollmentPassed) return true;

  const isEnrollmentCompleted =
    enrollmentStatus === EnrollmentStatusEnum.COMPLETED && isReEnrollEnabled && isReEnrollableDate;
  if (isEnrollmentCompleted) return true;

  return false;
};

export const checkUpdateContentProgressLearningPathEnrollment = (
  learningPathEnrollmentContentProgress,
  courseId,
  enrollmentStatus,
) => {
  const isDuplicateCourseId = learningPathEnrollmentContentProgress.some((val) => val.courseId === courseId);

  const isNeverSyncProgress = !isDuplicateCourseId;
  if (isNeverSyncProgress) return true;

  const isSyncedEnrollmentExpired = isDuplicateCourseId && enrollmentStatus === EnrollmentStatusEnum.EXPIRED;
  if (isSyncedEnrollmentExpired) return true;

  const isSyncedEnrollmentRejected = isDuplicateCourseId && enrollmentStatus === EnrollmentStatusEnum.REJECTED;
  if (isSyncedEnrollmentRejected) return true;

  return false;
};

export const getEnrollmentIdFromLearningPathEnrollment = (learningPathEnrollments, userId, courseId) => {
  const learningPathEnrollment = find(
    learningPathEnrollments,
    (item) => item.userId === userId && some(item.contentProgress, (progress) => progress.courseId === courseId),
  );

  if (learningPathEnrollment) {
    const { contentProgress } = learningPathEnrollment;
    const findMatchContentProgress = find(contentProgress, { courseId });
    if (findMatchContentProgress) {
      const { enrollmentId } = findMatchContentProgress;
      return enrollmentId;
    }
  }

  return null;
};

export function getFullName({ salute = '', firstname = '', lastname = '' }) {
  const result = `${salute ?? ''} ${firstname ?? ''} ${lastname ?? ''}`.trim();
  return result;
}
