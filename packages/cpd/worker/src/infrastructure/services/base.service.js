import { StatusCodes } from 'http-status-codes';

import { date } from '@infrastructure/dateUtils';
import { AppError } from '@infrastructure/utilities/AppError';

class BaseService {
  constructor(db, tableName, logger) {
    this.repository = db.collection(tableName);
    this.logger = logger;
  }

  async find(filter, options = {}) {
    const { includeDeleted, ...mongoOptions } = options || {};
    const query = this.handleQueryDeleteMatch(filter, includeDeleted);

    const res = await this.repository.find(query, mongoOptions).toArray();
    return res;
  }

  async findOne(filter, options = {}) {
    const { includeDeleted, ...mongoOptions } = options || {};
    const query = this.handleQueryDeleteMatch(filter, includeDeleted);

    const res = await this.repository.findOne(query, mongoOptions);
    return res;
  }

  async aggregate(filter, options = {}) {
    const { includeDeleted, ...mongoOptions } = options || {};

    const aggregatePipeline = filter.map((state) => {
      if (state.$match) return this.handleQueryDeleteMatch(state, includeDeleted);
      if (state.$lookup?.pipeline) {
        state.$lookup.pipeline = this.handleQueryDeleteLookupPipeline(state.$lookup?.pipeline, includeDeleted);
      }

      return state;
    });

    return this.repository
      .aggregate(aggregatePipeline, {
        ...mongoOptions,
        allowDiskUse: true,
      })
      .toArray();
  }

  async save(document, opts) {
    const { id } = document;
    document.updatedAt = date().toDate();

    const option = { upsert: true };
    if (opts) {
      option.session = opts.session;
    }

    await this.repository
      .replaceOne(
        {
          id,
        },
        document,
        option,
      )
      .catch((err) => {
        throw new AppError(StatusCodes.INTERNAL_SERVER_ERROR, err.message);
      });
  }

  async saveMany(documents, opts) {
    const option = {};
    if (opts) {
      option.session = opts.session;
    }
    const bulkData = documents.map((document) => ({
      replaceOne: {
        filter: { id: document.id },
        replacement: { ...document, updatedAt: date().toDate() },
        upsert: true,
      },
    }));
    await this.bulkWrite(bulkData, option);
  }

  async updateOne(filter, update, opts = {}) {
    await this.repository.updateOne(filter, update, opts).catch((err) => {
      throw new AppError(StatusCodes.INTERNAL_SERVER_ERROR, err.message);
    });
  }

  async updateMany(filter, update, opts = {}) {
    const res = await this.repository.updateMany(filter, update, opts).catch((err) => {
      throw new AppError(StatusCodes.INTERNAL_SERVER_ERROR, err.message);
    });
    return res;
  }

  async bulkWrite(operations, opts) {
    await this.repository.bulkWrite(operations, { ...opts }).catch((err) => {
      throw new AppError(StatusCodes.INTERNAL_SERVER_ERROR, err.message);
    });
  }

  async insertMany(data, opts = {}) {
    const res = await this.repository.insertMany(data, opts).catch((err) => {
      throw new AppError(StatusCodes.INTERNAL_SERVER_ERROR, err.message);
    });
    return res;
  }

  async aggregateStream(filter, options = {}) {
    const { includeDeleted, ...mongoOptions } = options || {};

    const aggregatePipeline = filter.map((state) => {
      if (state.$match) return this.handleQueryDeleteMatch(state, includeDeleted);
      if (state.$lookup?.pipeline) {
        state.$lookup.pipeline = this.handleQueryDeleteLookupPipeline(state.$lookup?.pipeline, includeDeleted);
      }

      return state;
    });

    return this.repository.aggregate(aggregatePipeline, {
      ...mongoOptions,
      allowDiskUse: true,
    });
  }

  handleQueryDeleteMatch(query, includeDeleted) {
    if (!query.$match) {
      if (includeDeleted) {
        delete query.deletedAt;
        return query;
      }

      return { ...query, deletedAt: { $eq: null } };
    }

    if (includeDeleted) {
      delete query.$match.deletedAt;
      return { $match: { ...query.$match } };
    }

    return { $match: { ...query.$match, deletedAt: { $eq: null } } };
  }
  handleQueryDeleteLookupPipeline(pipeline, includeDeleted) {
    return pipeline.map((state) => {
      if (includeDeleted) return state;

      if (state.$match) {
        return {
          $match: {
            ...state.$match,
            deletedAt: { $eq: null },
          },
        };
      }

      if (state.$lookup?.pipeline) {
        return {
          $lookup: {
            ...state.$lookup,
            pipeline: this.handleQueryDeleteLookupPipeline(state.$lookup?.pipeline, includeDeleted),
          },
        };
      }

      return state;
    });
  }
}

export default BaseService;
