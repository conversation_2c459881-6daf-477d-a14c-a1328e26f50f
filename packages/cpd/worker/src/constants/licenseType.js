import { ApplicantTypeEnum } from '@iso/lms/enums/course.enum';

export const LicenseTypeCode = {
  TSI: 'TSI-001',
  OIC_NON_LIFE: 'OIC-001',
  OIC_LIFE: 'OIC-002',
};

export const LicenseTypeEnum = {
  NONLIFE: 'NON-LIFE',
  LIFE: 'LIFE',
  BOTH: 'BOTH',
  INVESTMENT: 'INVESTMENT',
  UNIT_LINKED: 'UNIT_LINKED',
};

export const ApplicantTypeTHEnum = {
  AGENT: 'ตัวแทน',
  BROKER: 'นายหน้า',
  ADVISOR_ANALYST_PLANNER: 'ผู้แนะนำการลงทุน / ผู้วางแผนการลงทุน / นักวิเคราะห์การลงทุน',
  ADVISOR: 'ผู้แนะนำการลงทุน',
  ANALYST: 'นักวิเคราะห์การลงทุน',
  PLANNER: 'ผู้วางแผนการลงทุน',
};

export const NewRenewalLicenseCode = {
  [ApplicantTypeEnum.AGENT]: {
    [LicenseTypeEnum.LIFE]: '01',
    [LicenseTypeEnum.NONLIFE]: '02',
  },
  [ApplicantTypeEnum.BROKER]: {
    [LicenseTypeEnum.LIFE]: '03',
    [LicenseTypeEnum.NONLIFE]: '04',
  },
};
