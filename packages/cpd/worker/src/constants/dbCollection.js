export const DBCollection = {
  achievements: 'achievements',
  announcement: 'announcements',
  bundles: 'bundles',
  certificates: 'certificates',
  classroom_location_enrollments: 'classroom-location-enrollments',
  classroom_locations: 'classroom-locations',
  classroom_rounds: 'classroom-rounds',
  classrooms: 'classrooms',
  column_settings: 'column-settings',
  comments: 'comments',
  course_marketplaces: 'course-marketplaces',
  course_item_criteria_configs: 'course-item-criteria-configs',
  course_item_progress_histories: 'course-item-progress-histories',
  course_items: 'course-items',
  course_versions: 'course-versions',
  course_version_certificate: 'course-version-certificates',
  courses_quizzes_config: 'courses-quizzes-config',
  courses: 'courses',
  customer_partners: 'customer-partners',
  customer_product_skus: 'customer-product-skus',
  customers: 'customers',
  debounce_emails: 'debounce-emails',
  departments: 'departments',
  email_transactions: 'email-transactions',
  enrollment_achievements: 'enrollment-achievements',
  enrollment_attachments: 'enrollment-attachments',
  enrollment_badges: 'enrollment-badges',
  enrollment_certificates: 'enrollment-certificates',
  enrollment_regulator_reports: 'enrollment-regulator-reports',
  enrollments: 'enrollments',
  instructors: 'instructors',
  invoice_items: 'invoice-items',
  invoices: 'invoices',
  job_transactions: 'job-transactions',
  jobEmailNotifications: 'job-email-notifications',
  jobs: 'jobs',
  knowledgeContentDurationHistories: 'knowledge-content-duration-histories',
  knowledgeContentInteractionHistories: 'knowledge-content-interaction-histories',
  knowledgeContentInteractions: 'knowledge-content-interactions',
  knowledgeContentItems: 'knowledge-content-items',
  learning_path_enrollment_certificates: 'learning-path-enrollment-certificates',
  learning_path_enrollments: 'learning-path-enrollments',
  learning_path_versions: 'learning-path-versions',
  learning_paths: 'learning-paths',
  license_sub_types: 'license-sub-types',
  license_types: 'license-types',
  licenses: 'licenses',
  login_providers: 'login-providers',
  logs_snapshot_enrollments: 'logs-snapshot-enrollments',
  material_media: 'material-medias',
  media: 'media',
  organization_column_settings: 'organization-column-settings',
  organization_login_providers: 'organization-login-providers',
  organization_schedulers: 'organization-schedulers',
  organization_storages: 'organization-storages',
  organizations: 'organizations',
  parts: 'parts',
  permission_groups: 'permission-groups',
  permissions: 'permissions',
  point_histories: 'point-histories',
  pre_assign_contents: 'pre-assign-contents',
  pre_assign_content_jobs: 'pre-assign-content-jobs',
  pre_enrollment_reservations: 'pre-enrollment-reservations',
  pre_enrollment_transactions: 'pre-enrollment-transactions',
  product_sku_bundles: 'product-sku-bundles',
  product_sku_courses: 'product-sku-courses',
  product_skus: 'product-skus',
  promote_notifications: 'promote-notifications',
  purchase_orders: 'purchase-orders',
  quiz_answers: 'quiz-answers',
  registrations: 'registrations',
  report_histories: 'report-histories',
  rounds: 'rounds',
  schedulers: 'schedulers',
  sessions: 'sessions',
  template_column_settings: 'template-column-settings',
  transaction_counters: 'transaction-counters',
  universities: 'universities',
  user_direct_reports: 'user-direct-reports',
  user_groups: 'user-groups',
  user_logins: 'user-logins',
  user_notifications: 'user-notifications',
  user_salutes: 'user-salutes',
  users: 'users',
  verify_enrollment_attachments: 'verify-enrollment-attachments',
  verify_enrollment: 'verify-enrollment',
  course_versions: 'course-versions',
  pre_assign_contents: 'pre-assign-contents',
  promote_notifications: 'promote-notifications',
  instructors: 'instructors',
  media: 'media',
  material_media: 'material-medias',
  classroom_rounds: 'classroom-rounds',
  classroom_locations: 'classroom-locations',
  classroom_location_enrollments: 'classroom-location-enrollments',
  course_item_criteria_configs: 'course-item-criteria-configs',
};
