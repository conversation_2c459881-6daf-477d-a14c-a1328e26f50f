import { asClass, asFunction, asValue, createContainer } from 'awilix';

import App from '@app/bootstrap';

/*** app/services ***/
import AirflowService from '@app/services/airflow.service';
import AnnouncementService from '@app/services/announcement.service';
import BuildColumnSettingService from '@app/services/buildColumnSetting.service';
import BundleService from '@app/services/bundle.service';
import CertificateService from '@app/services/certificate.service';
import ClassroomLocationService from '@app/services/classroomLocation.service';
import ClassroomLocationEnrollmentService from '@app/services/classroomLocationEnrollment.service';
import ClassroomRoundService from '@app/services/classroomRound.service';
import ColumnSettingService from '@app/services/columnSetting.service';
import CourseService from '@app/services/course.service';
import CourseItemService from '@app/services/courseItem.service';
import CourseItemCriteriaConfigService from '@app/services/courseItemCriteriaConfig.service';
import CourseItemProgressHistoryService from '@app/services/courseItemProgressHistory.service';
import CourseMarketplaceService from '@app/services/courseMarketplace.service';
import CourseVersionService from '@app/services/courseVersion.service';
import CustomerPartnerService from '@app/services/customerPartner.service';
import CustomerProductSKUService from '@app/services/customerProductSKU.service';
import DepartmentService from '@app/services/department.service';
import EmailTransactionsService from '@app/services/emailTransactions.service';
import EnrollmentService from '@app/services/enrollment.service';
import EnrollmentCertificateService from '@app/services/enrollmentCertificate.service';
import EnrollmentRegulatorReportService from '@app/services/enrollmentRegulatorReport.service';
import OpenPgpService from '@app/services/gpg.service';
import HttpService from '@app/services/http.service';
import IDSService from '@app/services/ids.service';
import InstructorService from '@app/services/instructor.service';
import JobService from '@app/services/job.service';
import JobTransactionService from '@app/services/jobTransaction.service';
import KnowledgeContentInteractionService from '@app/services/knowledgeContentInteraction.service';
import KnowledgeContentInteractionHistoryService from '@app/services/knowledgeContentInteractionHistory.service';
import KnowledgeContentItemService from '@app/services/knowledgeContentItem.service';
import LearningPathService from '@app/services/learningPath.service';
import LearningPathEnrollmentService from '@app/services/learningPathEnrollment.service';
import LearningPathEnrollmentCertificateService from '@app/services/learningPathEnrollmentCertificate.service';
import LearningPathVersionService from '@app/services/learningPathVersion.service';
import LicenseService from '@app/services/license.service';
import MaterialMediaService from '@app/services/materialMedia.service';
import MediaService from '@app/services/media.service';
import MemoryCachedService from '@app/services/memoryCached.service';
import NotificationService from '@app/services/notification.service';
import OAuthService from '@app/services/oauth.service';
import OICService from '@app/services/oic.service';
import OrganizationService from '@app/services/organization.service';
import OrganizationCertificateService from '@app/services/organizationCertificate.service';
import OrganizationSchedulerService from '@app/services/organizationScheduler.service';
import OrganizationStorageService from '@app/services/organizationStorage.service';
import PartService from '@app/services/part.service';
import PlanService from '@app/services/plan.service';
import PlanPackageService from '@app/services/planPackage.service';
import PlanPackageLicenseService from '@app/services/planPackageLicense.service';
import PlanPackageLicenseHistoryService from '@app/services/planPackageLicenseHistory.service';
import PreAssignContentService from '@app/services/preAssignContent.service';
import PreEnrollmentReservationService from '@app/services/preEnrollmentReservation.service';
import PreEnrollmentTransactionService from '@app/services/preEnrollmentTransaction.service';
import PrepareDataAutoBulkService from '@app/services/prepareDataAutoBulk.service';
import PrepareSftpUsersImport from '@app/services/prepareSftpUsersImport.service';
import ProductSKUService from '@app/services/productSKU.service';
import ProductSKUBundleService from '@app/services/productSKUBundle.service';
import ProductSKUCourseService from '@app/services/productSKUCourse.service';
import PromoteNotificationService from '@app/services/promoteNotification.service';
import QuizAnswerService from '@app/services/quizAnswer.service';
import RetailService from '@app/services/retail.service';
import RoundService from '@app/services/round.service';
import SftpService from '@app/services/sftp.service';
import SftpImportJobService from '@app/services/sftpImportJob.service';
import SlackNotificationService from '@app/services/slackNotification.service';
import SyncCourseLearningProgressService from '@app/services/syncCourseLearningProgress.service';
import TemplateColumnSettingService from '@app/services/templateColumnSetting.service';
import TransactionCounterService from '@app/services/transactionCounter.service';
import TSIService from '@app/services/tsi.service';
import UserService from '@app/services/user.service';
import UserDirectReportsService from '@app/services/userDirectReports.service';
import UserGroupService from '@app/services/userGroup.service';
import UserNotificationService from '@app/services/userNotification.service';
import UserValidateService from '@app/services/userValidate.service';

/*** app/usecases ***/
import JobSubscribeUpdateUserGroupByConditionUseCase from '@app/usecases/appScheduler/jobUpdateUserGroupByCondition';
import UpdateUserInUserGroupByConditionUseCase from '@app/usecases/appScheduler/updateUserInUserGroupByCondition.usecase';
import AssignCourseUseCase from '@app/usecases/assignCourse.usecase';
import AutoBulkUseCase from '@app/usecases/autoBulk.usecase';
import AutoEnrollLearningPathUseCase from '@app/usecases/autoEnrollLearningPath.usecase';
import BulkEditLearningPathEnrollmentExpireDateUsecase from '@app/usecases/bulkEditLearningPathEnrollmentExpireDate.usecase';
import CancelPreAssignLearningPathUseCase from '@app/usecases/cancelPreAssignLearningPath.usecase';
import CancelPreEnrollmentUseCase from '@app/usecases/cancelPreEnrollment.usecase';
import EnrollLearningPathUseCase from '@app/usecases/enrollLearningPath.usecase';
import PreAssignLearningPathEnrollmentUseCase from '@app/usecases/preAssignLearningPathEnrollment.usecase';
import ScheduleUseCase from '@app/usecases/schedule.usecase';
import SftpImportEnrollmentHistoryUseCase from '@app/usecases/sftpImportEnrollmentHistory.usecase';
import SftpImportUsersUseCase from '@app/usecases/sftpImportUsers.usecase';
import Config from '@config/main';
import AmqpNotificationAdaptor from '@infrastructure/adaptors/amqpNotification';
import AMQP from '@infrastructure/amqp';
import { getAMQPConnection } from '@infrastructure/amqp/init';
import Cryptography from '@infrastructure/cryptography';
import { redisClient } from '@infrastructure/db/redisClient';
import Logger from '@infrastructure/logger';
import { createConnection } from '@infrastructure/mongodb/client';
import Schedule from '@infrastructure/schedule';
import ImageBucketService from '@infrastructure/services/imageBucket.service';
import Webhook from '@infrastructure/webhook';
import Listeners from '@interfaces/listeners';
import AppScheduler from '@interfaces/schedulers/appScheduler';

export default class IOC {
  static container;

  static async init() {
    const container = createContainer();

    const { db, client } = await createConnection(Config.MONGODB_URI, Config.DB_NAME);

    const amqpConnection = await getAMQPConnection(Config.AMQP_URI);
    const amqpNotificationConnection = await getAMQPConnection(Config.AMQP_NOTIFICATION_URI);

    container.register({
      amqp: asClass(AMQP),
      amqpConnection: asValue(amqpConnection),

      amqpNotificationAdaptor: asClass(AmqpNotificationAdaptor),
      amqpNotificationConnection: asValue(amqpNotificationConnection),
      app: asFunction(App).singleton(),
      appScheduler: asClass(AppScheduler),
      config: asValue(Config),
      cryptography: asFunction(Cryptography).singleton(),
      db: asValue(db),
      dbClient: asValue(client),
      listeners: asFunction(Listeners).singleton(),
      logger: asFunction(Logger).singleton(),
      redisClient: asValue(redisClient),

      // Service
      airflowService: asClass(AirflowService).singleton(),
      gpgService: asClass(OpenPgpService).singleton(),
      notificationService: asClass(NotificationService).singleton(),
      memoryCachedService: asClass(MemoryCachedService).singleton(),
      slackNotificationService: asClass(SlackNotificationService).singleton(),
      prepareDataAutoBulkService: asClass(PrepareDataAutoBulkService).singleton(),
      prepareSftpUsersImport: asClass(PrepareSftpUsersImport).singleton(),
      scheduleService: asValue(Schedule),
      sftpService: asClass(SftpService).singleton(),
      userValidateService: asClass(UserValidateService).singleton(),
      webHookService: asClass(Webhook).singleton(),
      syncCourseLearningProgressService: asClass(SyncCourseLearningProgressService).singleton(),
      imageBucketService: asClass(ImageBucketService).singleton(),
      buildColumnSettingService: asClass(BuildColumnSettingService).singleton(),

      // Domain Service
      announcementService: asClass(AnnouncementService).singleton(),
      bundleService: asClass(BundleService).singleton(),
      certificateService: asClass(CertificateService).singleton(),
      courseService: asClass(CourseService).singleton(),
      columnSettingService: asClass(ColumnSettingService).singleton(),
      customerPartnerService: asClass(CustomerPartnerService).singleton(),
      customerProductSKUService: asClass(CustomerProductSKUService).singleton(),
      emailTransactionsService: asClass(EmailTransactionsService),
      enrollmentRegulatorReportService: asClass(EnrollmentRegulatorReportService).singleton(),
      httpService: asClass(HttpService).singleton(),
      idsService: asClass(IDSService).singleton(),
      jobService: asClass(JobService).singleton(),
      knowledgeContentInteractionService: asClass(KnowledgeContentInteractionService),
      knowledgeContentInteractionHistoryService: asClass(KnowledgeContentInteractionHistoryService),
      knowledgeContentItemService: asClass(KnowledgeContentItemService),
      licenseService: asClass(LicenseService).singleton(),
      oicService: asClass(OICService).singleton(),
      preEnrollmentReservationService: asClass(PreEnrollmentReservationService).singleton(),
      preEnrollmentTransactionService: asClass(PreEnrollmentTransactionService).singleton(),
      productSKUService: asClass(ProductSKUService).singleton(),
      productSKUBundleService: asClass(ProductSKUBundleService).singleton(),
      productSKUCourseService: asClass(ProductSKUCourseService).singleton(),
      promoteNotificationService: asClass(PromoteNotificationService).singleton(),
      retailService: asClass(RetailService).singleton(),
      roundService: asClass(RoundService).singleton(),
      sftpImportJobService: asClass(SftpImportJobService).singleton(),
      transactionCounterService: asClass(TransactionCounterService).singleton(),
      userService: asClass(UserService).singleton(),
      enrollmentService: asClass(EnrollmentService).singleton(),
      quizAnswerService: asClass(QuizAnswerService).singleton(),
      partService: asClass(PartService).singleton(),
      departmentService: asClass(DepartmentService).singleton(),
      userDirectReportsService: asClass(UserDirectReportsService).singleton(),
      learningPathService: asClass(LearningPathService).singleton(),
      learningPathVersionService: asClass(LearningPathVersionService).singleton(),
      learningPathEnrollmentService: asClass(LearningPathEnrollmentService).singleton(),
      courseVersionService: asClass(CourseVersionService).singleton(),
      learningPathEnrollmentCertificateService: asClass(LearningPathEnrollmentCertificateService).singleton(),
      jobTransactionService: asClass(JobTransactionService).singleton(),
      preAssignContentService: asClass(PreAssignContentService).singleton(),
      organizationService: asClass(OrganizationService).singleton(),
      templateColumnSettingService: asClass(TemplateColumnSettingService).singleton(),
      oauthService: asClass(OAuthService).singleton(),
      userGroupService: asClass(UserGroupService).singleton(),
      userNotificationService: asClass(UserNotificationService).singleton(),
      organizationStorageService: asClass(OrganizationStorageService).singleton(),
      instructorService: asClass(InstructorService).singleton(),
      mediaService: asClass(MediaService).singleton(),
      classroomLocationService: asClass(ClassroomLocationService).singleton(),
      classroomLocationEnrollmentService: asClass(ClassroomLocationEnrollmentService).singleton(),
      classroomRoundService: asClass(ClassroomRoundService).singleton(),
      materialMediaService: asClass(MaterialMediaService).singleton(),
      courseItemService: asClass(CourseItemService).singleton(),
      organizationSchedulerService: asClass(OrganizationSchedulerService).singleton(),
      tsiService: asClass(TSIService).singleton(),
      courseItemCriteriaConfigService: asClass(CourseItemCriteriaConfigService).singleton(),
      courseItemProgressHistoryService: asClass(CourseItemProgressHistoryService).singleton(),
      enrollmentCertificateService: asClass(EnrollmentCertificateService).singleton(),
      organizationCertificateService: asClass(OrganizationCertificateService).singleton(),
      courseMarketplaceService: asClass(CourseMarketplaceService).singleton(),
      planService: asClass(PlanService).singleton(),
      planPackageService: asClass(PlanPackageService).singleton(),
      planPackageLicenseService: asClass(PlanPackageLicenseService).singleton(),
      planPackageLicenseHistoryService: asClass(PlanPackageLicenseHistoryService).singleton(),

      // Usecase
      autoBulkUseCase: asClass(AutoBulkUseCase).singleton(),
      autoEnrollLearningPathUseCase: asClass(AutoEnrollLearningPathUseCase).singleton(),
      scheduleUsecase: asClass(ScheduleUseCase).singleton(),
      jobSubscribeUpdateUserGroupByConditionUseCase: asClass(JobSubscribeUpdateUserGroupByConditionUseCase).singleton(),
      sftpImportUsersUseCase: asClass(SftpImportUsersUseCase).singleton(),
      sftpImportEnrollmentHistoryUseCase: asClass(SftpImportEnrollmentHistoryUseCase).singleton(),
      updateUserInUserGroupByConditionUseCase: asClass(UpdateUserInUserGroupByConditionUseCase).singleton(),
      assignCourseUseCase: asClass(AssignCourseUseCase).singleton(),
      preAssignLearningPathEnrollmentUseCase: asClass(PreAssignLearningPathEnrollmentUseCase).singleton(),
      enrollLearningPathUseCase: asClass(EnrollLearningPathUseCase).singleton(),
      bulkEditLearningPathEnrollmentExpireDateUsecase: asClass(
        BulkEditLearningPathEnrollmentExpireDateUsecase,
      ).singleton(),
      cancelPreEnrollmentUseCase: asClass(CancelPreEnrollmentUseCase).singleton(),
      cancelPreAssignLearningPathUseCase: asClass(CancelPreAssignLearningPathUseCase).singleton(),
    });
    IOC.container = container;
  }
}
