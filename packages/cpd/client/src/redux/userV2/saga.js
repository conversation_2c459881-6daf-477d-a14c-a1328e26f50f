import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { all, call, put, take, takeEvery } from 'redux-saga/effects';

import { StatusCode } from '@constants/http';
import { conflictErrorMessage, getJobErrorMessageV2 } from '@helpers/errorMessage';
import { onSaveBlob } from '@helpers/utility';
import messageActions from '@redux/message/actions';
import userActions from '@redux/user/actions';
import userActionsV2 from '@redux/userV2/actions';
import userAPIV2 from '@redux/userV2/api';

const { set_message } = messageActions;

const {
  manageUserListAction,
  manageUserSummaryAction,
  fetchUserSaluteListAction,
  updateUserPermissionGroupAction,
  updateUserLoginAction,
  sendUserSetFirstPasswordEmailAction,
  sendUserConnectSSOEmailAction,
  updateUserProfileByAdminAction,
  updateUserLicensesByAdminAction,
  updateUserAvatarByAdminAction,
  updateUserAdditionalAction,
  exportUserAction,
  fetchManageUserEnrollmentHistoriesAction,
  fetchUsersExcludeSubordinateAction,
  fetchUserListByIdsAction,
  updateUserOrganizationAction,
  fetchMyTeamAction,
  fetchMyTeamModerationAction,
  fetchMyDirectReportAction,
  fetchMyTeamSummaryAction,
  fetchMyTeamModerationSummaryAction,
  fetchMyDirectReportSummaryAction,
  fetchMyTeamAccessibilityAction,
  fetchMyTeamViewerAccessibilityAction,
  fetchMyTeamUserProfileAction,
  fetchMyTeamUserEnrollmentHistoryAction,
  fetchDirectReportUserDropdownAction,
  fetchSupervisorJobDropdownAction,
  fetchUserPlanListAction,
  fetchUserPlanPackageLicenseHistoryListAction,
  downloadBulkAssignPlanPlanPackageLicenseTemplateAction,
  bulkAssignPlanPackageLicenseAction,
  downloadBulkTransferPlanPackageLicenseTemplateAction,
  bulkTransferPlanPackageLicenseAction,
} = userActionsV2;

function* fetch_manage_user_list(payload) {
  const api = yield call(userAPIV2.fetchManageUsers, payload);
  if (api.status === StatusCode.SUCCESS) {
    yield put(manageUserListAction.success(api.data));
  } else {
    yield put(manageUserListAction.error(api));
  }
}

function* fetch_manage_user_summary() {
  const api = yield call(userAPIV2.fetchManageUserSummary);
  if (api.status === StatusCode.SUCCESS) {
    yield put(manageUserSummaryAction.success(api.data));
  } else {
    yield put(manageUserSummaryAction.error(api));
  }
}

function* fetch_user_salute_list() {
  const res = yield call(userAPIV2.fetchUserSalute);

  if (res.status === StatusCode.SUCCESS) {
    yield put(fetchUserSaluteListAction.success(res.data));
  } else {
    yield put(fetchUserSaluteListAction.error(res));
  }
}

function* update_user_permission_group({ payload }) {
  const { userId, formData } = payload;

  const api = yield call(userAPIV2.updateUserPermissionGroup, userId, formData);

  if (api.status === StatusCode.SUCCESS) {
    yield put(updateUserPermissionGroupAction.success(api.data));
  } else {
    yield put(updateUserPermissionGroupAction.error(api));
  }
}

function* update_user_login({ payload }) {
  const { userId, formData } = payload;

  const api = yield call(userAPIV2.updateUserLogin, userId, formData);

  if (api.status === StatusCode.SUCCESS) {
    yield put(updateUserLoginAction.success(api.data));
  } else {
    yield put(updateUserLoginAction.error(api));
  }
}

function* update_user_profile_by_admin({ payload }) {
  const { userId, formData } = payload;
  const response = yield call(userAPIV2.updateUserProfileByAdmin, userId, formData);

  if (response.status === StatusCode.SUCCESS) {
    yield put(updateUserProfileByAdminAction.success(formData));
    yield put(set_message('success', 'แก้ไขข้อมูลสำเร็จ'));
  } else if (response.status === StatusCode.CONFLICT) {
    const msg = `${conflictErrorMessage[Object.keys(response.data)]}`;
    const errorInfo = { [Object.keys(response.data)]: msg };
    yield put(updateUserProfileByAdminAction.error(errorInfo));
  } else {
    yield put(updateUserProfileByAdminAction.error(response));
  }
}

function* update_user_licenses_by_admin({ payload }) {
  const { userId, formData } = payload;
  const response = yield call(userAPIV2.updateUserLicensesByAdmin, userId, formData);
  if (response.status === StatusCode.SUCCESS) {
    yield put(updateUserLicensesByAdminAction.success(formData));
    yield put(set_message('success', 'แก้ไขข้อมูลสำเร็จ'));
  } else if (response.status === StatusCode.CONFLICT) {
    const msg = 'หมายเลขใบอนุญาตนี้มีอยู่ในระบบแล้ว';
    const errorInfo = { [Object.keys(response.data)]: msg };
    yield put(updateUserLicensesByAdminAction.error(errorInfo));
  } else {
    yield put(updateUserLicensesByAdminAction.error(response));
    yield put(set_message('error', 'เกิดข้อผิดพลาด ไม่สามารถแก้ไขข้อมูลได้'));
  }
}

function* update_user_avatar_by_admin({ payload }) {
  const { userId, data } = payload;
  const formData = new FormData();
  formData.append('uploadedFile', data);
  const response = yield call(userAPIV2.uploadUserAvatarByAdmin, userId, formData);
  if (response.status === StatusCode.SUCCESS) {
    yield put(updateUserAvatarByAdminAction.success(response.data));
  } else {
    yield put(updateUserAvatarByAdminAction.error(response));
  }
}

function* update_user_additional({ payload }) {
  const { userId, data } = payload;
  const response = yield call(userAPIV2.updateUserAdditional, userId, data);
  if (response.status === StatusCode.SUCCESS) {
    yield put(updateUserAdditionalAction.success(data));
    yield put(set_message('success', 'แก้ไขข้อมูลสำเร็จ'));
  } else {
    yield put(updateUserAdditionalAction.error(response));
    yield put(set_message('error', 'เกิดข้อผิดพลาด ไม่สามารถแก้ไขข้อมูลได้'));
  }
}

function* send_user_set_first_password_email({ payload }) {
  const { userId } = payload;

  const api = yield call(userAPIV2.sendUserSetFirstPasswordEmail, userId);

  if (api.status === StatusCode.SUCCESS) {
    yield put(sendUserSetFirstPasswordEmailAction.success(api.data));
  } else {
    yield put(sendUserSetFirstPasswordEmailAction.error(api));
  }
}

function* send_user_connect_sso_email({ payload }) {
  const { userId } = payload;

  const api = yield call(userAPIV2.sendUserConnectSSOEmail, userId);

  if (api.status === StatusCode.SUCCESS) {
    yield put(sendUserConnectSSOEmailAction.success(api.data));
  } else {
    yield put(sendUserConnectSSOEmailAction.error(api));
  }
}

function* export_user({ payload }) {
  const response = yield call(userAPIV2.exportUser, payload);

  try {
    while (true) {
      const result = yield take(response);

      if (result.httpStatus && result.httpStatus !== 200) {
        yield put(exportUserAction.error(result));
        return;
      }
      if (result.data instanceof Blob) {
        yield put(exportUserAction.success(result.data));
      }
    }
  } catch (error) {
    yield put(exportUserAction.error(error));
  }
}

function* download_bulk_create_user_template() {
  const api = yield call(userAPIV2.downloadImportBulkCreateUserTemplate);
  if (api.httpStatus && api.httpStatus !== 200) {
    yield put(set_message('error', 'เกิดข้อผิดพลาด ไม่สามารถดาวน์โหลดเอกสารได้'));
    yield put(userActionsV2.downloadBulkCreateUserTemplateAction.error(api));
    return;
  }

  const FILE_NAME = 'bulk-activation-user-template';
  onSaveBlob(api.data, FILE_NAME);

  yield put(userActionsV2.downloadBulkCreateUserTemplateAction.success());
}

function* download_bulk_edit_user_template() {
  const api = yield call(userAPIV2.downloadImportBulkEditUserTemplate);
  if (api.httpStatus && api.httpStatus !== 200) {
    yield put(set_message('error', 'เกิดข้อผิดพลาด ไม่สามารถดาวน์โหลดเอกสารได้'));
    yield put(userActionsV2.downloadBulkCreateUserTemplateAction.error(api));
    return;
  }

  const FILE_NAME = 'bulk-edit-user-template';
  onSaveBlob(api.data, FILE_NAME);

  yield put(userActionsV2.downloadBulkCreateUserTemplateAction.success());
}

function* import_bulk_create_user({ payload }) {
  const formData = new FormData();
  formData.append('excelFile', payload.file);

  const apiResponse = yield call(userAPIV2.importBulkCreateUser, formData);

  const jobId = apiResponse.data ? apiResponse.data.guid : '';

  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.importBulkCreateUserAction.success(apiResponse.data));
    return;
  }
  const { email = '', text = '' } = apiResponse?.data ?? {};
  const status = apiResponse.status || 'BO-999';
  const errorInfo = getJobErrorMessageV2({
    status,
    email,
    text,
    jobId,
  });

  yield put(userActionsV2.importBulkCreateUserAction.error(errorInfo));
}

function* import_bulk_edit_user_profile({ payload }) {
  const formData = new FormData();
  formData.append('excelFile', payload.file);
  formData.append('operationType', BulkOpTypeEnum.EDIT_USER);

  const apiResponse = yield call(userAPIV2.importBulkEditUser, formData);

  const jobId = apiResponse.data ? apiResponse.data.guid : '';

  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.importBulkEditUserAction.success(apiResponse.data));
    return;
  }

  const { email, text = '' } = apiResponse?.data ?? {};
  const status = apiResponse.status || 'BO-999';
  const errorInfo = getJobErrorMessageV2({
    status,
    email,
    text,
    jobId,
  });

  yield put(userActionsV2.importBulkEditUserAction.error(errorInfo));
}

function* import_bulk_edit_username({ payload }) {
  const formData = new FormData();
  formData.append('excelFile', payload.file);
  formData.append('operationType', BulkOpTypeEnum.CHANGE_USERNAME);

  const apiResponse = yield call(userAPIV2.importBulkEditUser, formData);

  const jobId = apiResponse.data ? apiResponse.data.guid : '';

  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.importBulkChangeUsernameAction.success(apiResponse.data));
    return;
  }

  const { email } = apiResponse?.data ?? {};
  let text = '';
  if (apiResponse?.data) {
    text = apiResponse.data.row || apiResponse.data.username;
  }
  const status = apiResponse.status || 'BO-999';
  const errorInfo = getJobErrorMessageV2({
    status,
    email,
    text,
    jobId,
  });

  yield put(userActionsV2.importBulkChangeUsernameAction.error(errorInfo));
}

function* fetch_manage_user_enrollment_histories({ payload }) {
  const response = yield call(userAPIV2.fetchUserEnrollmentHistories, payload);
  if (response.status === StatusCode.SUCCESS) {
    yield put(fetchManageUserEnrollmentHistoriesAction.success(response.data));
  } else {
    yield put(fetchManageUserEnrollmentHistoriesAction.error(response));
  }
}

function* fetch_users_exclude_subordinate({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchUsersExcludeSubordinate, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchUsersExcludeSubordinateAction.success(apiResponse.data));
  } else {
    yield put(fetchUsersExcludeSubordinateAction.error(apiResponse));
  }
}

function* fetch_user_list_by_ids({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchUserListByIds, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchUserListByIdsAction.success(apiResponse.data));
  } else {
    yield put(fetchUserListByIdsAction.error(apiResponse));
  }
}

function* update_user_organization({ payload }) {
  const apiResponse = yield call(userAPIV2.updateDepartmentAndSupervisor, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(updateUserOrganizationAction.success(apiResponse.data));
  } else {
    yield put(updateUserOrganizationAction.error(apiResponse));
  }
}

function* fetch_my_team({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamAction.error(apiResponse));
  }
}

function* fetch_my_team_moderation({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamModerationAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamModerationAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamModerationAction.error(apiResponse));
  }
}

function* fetch_my_direct_report({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyDirectReportAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyDirectReportAction.success(apiResponse.data));
  } else {
    yield put(fetchMyDirectReportAction.error(apiResponse));
  }
}

function* fetch_my_team_summary({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamSummaryAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamSummaryAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamSummaryAction.error(apiResponse));
  }
}

function* fetch_my_team_moderation_summary({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamModerationSummaryAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamModerationSummaryAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamModerationSummaryAction.error(apiResponse));
  }
}

function* fetch_my_direct_report_summary({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyDirectReportSummaryAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyDirectReportSummaryAction.success(apiResponse.data));
  } else {
    yield put(fetchMyDirectReportSummaryAction.error(apiResponse));
  }
}

function* fetch_my_team_accessibility() {
  const apiResponse = yield call(userAPIV2.fetchMyTeamAccessibilityAPI);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamAccessibilityAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamAccessibilityAction.error(apiResponse));
  }
}

function* fetch_my_team_viewer_accessibility({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamViewerAccessibilityAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamViewerAccessibilityAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamViewerAccessibilityAction.error(apiResponse));
  }
}

function* fetch_my_team_user_profile({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamUserProfileAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamUserProfileAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamUserProfileAction.error(apiResponse));
  }
}

function* fetch_my_team_user_enrollment_history({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamUserEnrollmentHistoryAPI, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchMyTeamUserEnrollmentHistoryAction.success(apiResponse.data));
  } else {
    yield put(fetchMyTeamUserEnrollmentHistoryAction.error(apiResponse));
  }
}

function* fetch_direct_report_user_dropdown() {
  const apiResponse = yield call(userAPIV2.fetchDirectReportUserDropdown);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchDirectReportUserDropdownAction.success(apiResponse.data));
  } else {
    yield put(fetchDirectReportUserDropdownAction.error(apiResponse));
  }
}

function* fetch_supervisor_job_dropdown({ payload }) {
  const { jobId } = payload;

  const apiResponse = yield call(userAPIV2.fetchSupervisorJobDropdown, { jobId });
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchSupervisorJobDropdownAction.success(apiResponse.data));
  } else {
    yield put(fetchSupervisorJobDropdownAction.error(apiResponse));
  }
}

function* fetch_transfer_user_my_team_subordinate({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamSubordinate, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.fetchTransferUserMyTeamSubordinateAction.success(apiResponse.data));
  } else {
    yield put(userActionsV2.fetchTransferUserMyTeamSubordinateAction.error(apiResponse));
  }
}

function* fetch_transfer_user_my_team_assign_content({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamAssignCourse, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.fetchTransferUserMyTeamAssignCourseAction.success(apiResponse.data));
  } else {
    yield put(userActionsV2.fetchTransferUserMyTeamAssignCourseAction.error(apiResponse));
  }
}

function* fetch_user_my_team_assign_content({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchMyTeamAssignCourse, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.fetchUserMyTeamAssignContentAction.success(apiResponse.data));
  } else {
    yield put(userActionsV2.fetchUserMyTeamAssignContentAction.error(apiResponse));
  }
}

function* update_user_two_factor({ userId, payload }) {
  const apiResponse = yield call(userAPIV2.updateTwoFactor, userId, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.updateUserTwoFactorAction.success());
  } else {
    yield put(userActionsV2.updateUserTwoFactorAction.error(apiResponse));
  }
}

function* fetch_user_two_factor_credential() {
  const apiResponse = yield call(userAPIV2.fetchUserTwoFactorCredential);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActions.updateUserManagementInfoClientAction.update_profile(apiResponse.data));
    yield put(userActionsV2.fetchUserTwoFactorCredentialAction.success(apiResponse.data));
  } else {
    yield put(userActionsV2.fetchUserTwoFactorCredentialAction.error(apiResponse));
  }
}

function* fetch_user_plan_package_license({ payload }) {
  const apiResponse = yield call(userAPIV2.fetchUserPlanPackageLicense, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.fetchUserPlanPackageLicenseAction.success(apiResponse.data));
  } else {
    yield put(userActionsV2.fetchUserPlanPackageLicenseAction.error(apiResponse));
  }
}

function* update_user_plan_package_license({ payload }) {
  const { userId, formData } = payload;
  const apiResponse = yield call(userAPIV2.updateUserPlanPackageLicense, userId, formData);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(userActionsV2.updateUserPlanPackageLicenseAction.success({ ...apiResponse.data, type: formData.type }));
  } else {
    yield put(userActionsV2.updateUserPlanPackageLicenseAction.error(apiResponse));
  }
}

function* fetch_user_plan_list({ userId, payload }) {
  const apiResponse = yield call(userAPIV2.fetchUserPlanList, userId, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchUserPlanListAction.success(apiResponse.data));
  } else {
    yield put(fetchUserPlanListAction.error(apiResponse));
  }
}

function* fetch_user_plan_package_license_history_list({ userId, planId, payload }) {
  const apiResponse = yield call(userAPIV2.fetchUserPlanPackageLicenseHistoryList, userId, planId, payload);
  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(fetchUserPlanPackageLicenseHistoryListAction.success(apiResponse.data));
  } else {
    yield put(fetchUserPlanPackageLicenseHistoryListAction.error(apiResponse));
  }
}

function* download_bulk_assign_plan_package_license_template() {
  const apiResponse = yield call(userAPIV2.downloadBulkAssignPlanPackageLicenseTemplate);
  if (apiResponse.status !== StatusCode.SUCCESS) {
    yield put(downloadBulkAssignPlanPlanPackageLicenseTemplateAction.error(apiResponse));
  }

  const FILE_NAME = 'bulk-assign-plan-package-license-template';
  onSaveBlob(apiResponse.data, FILE_NAME);

  yield put(downloadBulkAssignPlanPlanPackageLicenseTemplateAction.success());
}

function* bulk_assign_plan_package_license({ payload }) {
  const formData = new FormData();
  formData.append('excelFile', payload.file);
  formData.append('plans', JSON.stringify(payload.plans));
  formData.append('isAssginNewAlways', payload.isAssginNewAlways);

  const apiResponse = yield call(userAPIV2.bulkAssignPlanPackageLicense, formData);

  const jobId = apiResponse.data ? apiResponse.data.guid : '';

  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(bulkAssignPlanPackageLicenseAction.success(apiResponse.data));
    return;
  }
  const { email = '', text = '' } = apiResponse?.data ?? {};
  const status = apiResponse.status || 'BO-999';
  const errorInfo = getJobErrorMessageV2({
    status,
    email,
    text,
    jobId,
  });

  yield put(bulkAssignPlanPackageLicenseAction.error(errorInfo));
}

function* download_bulk_transfer_plan_package_license_template() {
  const apiResponse = yield call(userAPIV2.downloadBulkTransferPlanPackageLicenseTemplate);
  if (apiResponse.status !== StatusCode.SUCCESS) {
    yield put(downloadBulkTransferPlanPackageLicenseTemplateAction.error(apiResponse));
  }

  const FILE_NAME = 'bulk-transfer-plan-package-license-template';
  onSaveBlob(apiResponse.data, FILE_NAME);

  yield put(downloadBulkTransferPlanPackageLicenseTemplateAction.success());
}

function* bulk_transfer_plan_package_license({ payload }) {
  const formData = new FormData();
  formData.append('excelFile', payload.file);
  formData.append('plans', JSON.stringify(payload.plans));

  const apiResponse = yield call(userAPIV2.bulkTransferPlanPackageLicense, formData);

  if (apiResponse.status === StatusCode.SUCCESS) {
    yield put(bulkTransferPlanPackageLicenseAction.success(apiResponse.data));
  } else {
    const jobId = apiResponse.data ? apiResponse.data.guid : '';
    const { email = '', text = '' } = apiResponse?.data ?? {};
    const status = apiResponse.status || 'BO-999';
    const errorInfo = getJobErrorMessageV2({
      status,
      email,
      text,
      jobId,
    });

    yield put(bulkTransferPlanPackageLicenseAction.error(errorInfo));
  }
}

export default function* rootSaga() {
  yield all([
    takeEvery(userActionsV2.FETCH_MANAGE_USERS_LIST_REQUEST, fetch_manage_user_list),
    takeEvery(userActionsV2.FETCH_MANAGE_USERS_SUMMARY_REQUEST, fetch_manage_user_summary),
    takeEvery(userActionsV2.FETCH_USER_SALUTE_LIST_REQUEST, fetch_user_salute_list),
    takeEvery(userActionsV2.UPDATE_USER_PERMISSION_GROUP_REQUEST, update_user_permission_group),
    takeEvery(userActionsV2.UPDATE_USER_LOGIN_REQUEST, update_user_login),
    takeEvery(userActionsV2.SEND_USER_SET_FIRST_PASSWORD_EMAIL_REQUEST, send_user_set_first_password_email),
    takeEvery(userActionsV2.SEND_USER_CONNECT_SSO_EMAIL_REQUEST, send_user_connect_sso_email),
    takeEvery(userActionsV2.UPDATE_USER_PROFILE_BY_ADMIN_REQUEST, update_user_profile_by_admin),
    takeEvery(userActionsV2.UPDATE_USER_LICENSES_BY_ADMIN_REQUEST, update_user_licenses_by_admin),
    takeEvery(userActionsV2.UPDATE_USER_AVATAR_BY_ADMIN_REQUEST, update_user_avatar_by_admin),
    takeEvery(userActionsV2.UPDATE_USER_ADDITIONAL_REQUEST, update_user_additional),
    takeEvery(userActionsV2.EXPORT_USER_REQUEST, export_user),
    takeEvery(userActionsV2.DOWNLOAD_BULK_CREATE_USER_TEMPLATE_REQUEST, download_bulk_create_user_template),
    takeEvery(userActionsV2.DOWNLOAD_BULK_EDIT_USER_TEMPLATE_REQUEST, download_bulk_edit_user_template),
    takeEvery(userActionsV2.ADMIN_BULK_CREATE_USER_REQUEST, import_bulk_create_user),
    takeEvery(userActionsV2.ADMIN_BULK_EDIT_USER_REQUEST, import_bulk_edit_user_profile),
    takeEvery(userActionsV2.ADMIN_BULK_CHANGE_USERNAME_REQUEST, import_bulk_edit_username),
    takeEvery(userActionsV2.FETCH_MANAGE_USER_ENROLLMENT_HISTORIES_REQUEST, fetch_manage_user_enrollment_histories),
    takeEvery(userActionsV2.FETCH_USERS_EXCLUDE_SUBORDINATE_REQUEST, fetch_users_exclude_subordinate),
    takeEvery(userActionsV2.FETCH_USER_LIST_BY_IDS_REQUEST, fetch_user_list_by_ids),
    takeEvery(userActionsV2.UPDATE_USER_ORGANIZATION_REQUEST, update_user_organization),
    takeEvery(userActionsV2.FETCH_MY_TEAM_REQUEST, fetch_my_team),
    takeEvery(userActionsV2.FETCH_MY_TEAM_MODERATION_REQUEST, fetch_my_team_moderation),
    takeEvery(userActionsV2.FETCH_MY_DIRECT_REPORT_REQUEST, fetch_my_direct_report),
    takeEvery(userActionsV2.FETCH_MY_TEAM_SUMMARY_REQUEST, fetch_my_team_summary),
    takeEvery(userActionsV2.FETCH_MY_TEAM_MODERATION_SUMMARY_REQUEST, fetch_my_team_moderation_summary),
    takeEvery(userActionsV2.FETCH_MY_DIRECT_REPORT_SUMMARY_REQUEST, fetch_my_direct_report_summary),
    takeEvery(userActionsV2.FETCH_MY_TEAM_ACCESSIBILITY_REQUEST, fetch_my_team_accessibility),
    takeEvery(userActionsV2.FETCH_MY_TEAM_VIEWER_ACCESSIBILITY_REQUEST, fetch_my_team_viewer_accessibility),
    takeEvery(userActionsV2.FETCH_MY_TEAM_USER_PROFILE_REQUEST, fetch_my_team_user_profile),
    takeEvery(userActionsV2.FETCH_MY_TEAM_USER_ENROLLMENT_HISTORY_REQUEST, fetch_my_team_user_enrollment_history),
    takeEvery(userActionsV2.FETCH_DIRECT_REPORT_USER_DROPDOWN_REQUEST, fetch_direct_report_user_dropdown),
    takeEvery(userActionsV2.FETCH_SUPERVISOR_JOB_DROPDOWN_REQUEST, fetch_supervisor_job_dropdown),
    takeEvery(userActionsV2.FETCH_TRANSFER_USER_MY_TEAM_SUBORDINATE_REQUEST, fetch_transfer_user_my_team_subordinate),
    takeEvery(
      userActionsV2.FETCH_TRANSFER_USER_MY_TEAM_ASSIGN_CONTENT_REQUEST,
      fetch_transfer_user_my_team_assign_content,
    ),
    takeEvery(userActionsV2.FETCH_USER_MY_TEAM_ASSIGN_CONTENT_REQUEST, fetch_user_my_team_assign_content),
    takeEvery(userActionsV2.UPDATE_USER_TWO_FACTOR_REQUEST, update_user_two_factor),
    takeEvery(userActionsV2.FETCH_USER_TWO_FACTOR_CREDENTIAL_REQUEST, fetch_user_two_factor_credential),
    takeEvery(userActionsV2.FETCH_USER_PLAN_PACKAGE_LICENSE_REQUEST, fetch_user_plan_package_license),
    takeEvery(userActionsV2.UPDATE_USER_PLAN_PACKAGE_LICENSE_REQUEST, update_user_plan_package_license),
    takeEvery(userActionsV2.FETCH_USER_PLAN_LIST_REQUEST, fetch_user_plan_list),
    takeEvery(
      userActionsV2.FETCH_USER_PLAN_PACKAGE_LICENSE_HISTORY_LIST_REQUEST,
      fetch_user_plan_package_license_history_list,
    ),
    takeEvery(
      userActionsV2.DOWNLOAD_BULK_ASSIGN_PLAN_PACKAGE_LICENSE_TEMPLATE_REQUEST,
      download_bulk_assign_plan_package_license_template,
    ),
    takeEvery(userActionsV2.BULK_ASSIGN_PLAN_PACKAGE_LICENSE_REQUEST, bulk_assign_plan_package_license),
    takeEvery(
      userActionsV2.DOWNLOAD_BULK_TRANSFER_PLAN_PACKAGE_LICENSE_TEMPLATE_REQUEST,
      download_bulk_transfer_plan_package_license_template,
    ),
    takeEvery(userActionsV2.BULK_TRANSFER_PLAN_PACKAGE_LICENSE_REQUEST, bulk_transfer_plan_package_license),
  ]);
}
