import { failedState, loadingState, resetState, successState } from '@helpers/reduxState';

import actions from './actions';
import { userInitialState, userStore } from './state';

export default function UserReducerV2(state = userInitialState, { type, payload, error } = {}) {
  switch (type) {
    // Manage User List
    case actions.FETCH_MANAGE_USERS_LIST_REQUEST:
      return loadingState(state, userStore.manageUserList);
    case actions.FETCH_MANAGE_USERS_LIST_SUCCESS:
      return successState(state, userStore.manageUserList, payload);
    case actions.FETCH_MANAGE_USERS_LIST_ERROR:
      return failedState(state, userStore.manageUserList, error);
    case actions.FETCH_MANAGE_USERS_LIST_RESET:
      return resetState(state, userStore.manageUserList, userInitialState);

    // Manage User Summary
    case actions.FETCH_MANAGE_USERS_SUMMARY_REQUEST:
      return loadingState(state, userStore.manageUserSummary);
    case actions.FET<PERSON>_MANAGE_USERS_SUMMARY_SUCCESS:
      return successState(state, userStore.manageUserSummary, payload);
    case actions.FETCH_MANAGE_USERS_SUMMARY_ERROR:
      return failedState(state, userStore.manageUserSummary, error);

    // Fetch User Salute
    case actions.FETCH_USER_SALUTE_LIST_REQUEST:
      return loadingState(state, userStore.userSaluteList);
    case actions.FETCH_USER_SALUTE_LIST_SUCCESS:
      return successState(state, userStore.userSaluteList, payload);
    case actions.FETCH_USER_SALUTE_LIST_ERROR:
      return failedState(state, userStore.userSaluteList, error);

    // Update User Permission Group
    case actions.UPDATE_USER_PERMISSION_GROUP_REQUEST:
      return loadingState(state, userStore.updateUserPermissionGroup);
    case actions.UPDATE_USER_PERMISSION_GROUP_SUCCESS:
      return successState(state, userStore.updateUserPermissionGroup, payload);
    case actions.UPDATE_USER_PERMISSION_GROUP_ERROR:
      return failedState(state, userStore.updateUserPermissionGroup, error);
    case actions.UPDATE_USER_PERMISSION_GROUP_RESET:
      return resetState(state, userStore.updateUserPermissionGroup, userInitialState);

    // Update user login
    case actions.UPDATE_USER_LOGIN_REQUEST:
      return loadingState(state, userStore.updateUserLogin);
    case actions.UPDATE_USER_LOGIN_SUCCESS:
      return successState(state, userStore.updateUserLogin, payload);
    case actions.UPDATE_USER_LOGIN_ERROR:
      return failedState(state, userStore.updateUserLogin, error);
    case actions.UPDATE_USER_LOGIN_RESET:
      return resetState(state, userStore.updateUserLogin, userInitialState);

    // Send user set first password email
    case actions.SEND_USER_SET_FIRST_PASSWORD_EMAIL_REQUEST:
      return loadingState(state, userStore.sendUserSetFirstPasswordEmail);
    case actions.SEND_USER_SET_FIRST_PASSWORD_EMAIL_SUCCESS:
      return successState(state, userStore.sendUserSetFirstPasswordEmail, payload);
    case actions.SEND_USER_SET_FIRST_PASSWORD_EMAIL_ERROR:
      return failedState(state, userStore.sendUserSetFirstPasswordEmail, error);
    case actions.SEND_USER_SET_FIRST_PASSWORD_EMAIL_RESET:
      return resetState(state, userStore.sendUserSetFirstPasswordEmail, userInitialState);

    case actions.SEND_USER_CONNECT_SSO_EMAIL_REQUEST:
      return loadingState(state, userStore.sendUserConnectSSOEmail);
    case actions.SEND_USER_CONNECT_SSO_EMAIL_SUCCESS:
      return successState(state, userStore.sendUserConnectSSOEmail, payload);
    case actions.SEND_USER_CONNECT_SSO_EMAIL_ERROR:
      return failedState(state, userStore.sendUserConnectSSOEmail, payload);
    case actions.SEND_USER_CONNECT_SSO_EMAIL_RESET:
      return resetState(state, userStore.sendUserConnectSSOEmail, userInitialState);

    // Update User Profile By Admin
    case actions.UPDATE_USER_PROFILE_BY_ADMIN_REQUEST:
      return loadingState(state, userStore.updateUserProfileByAdmin);
    case actions.UPDATE_USER_PROFILE_BY_ADMIN_SUCCESS:
      return successState(state, userStore.updateUserProfileByAdmin, payload);
    case actions.UPDATE_USER_PROFILE_BY_ADMIN_ERROR:
      return failedState(state, userStore.updateUserProfileByAdmin, error);
    case actions.UPDATE_USER_PROFILE_BY_ADMIN_RESET:
      return resetState(state, userStore.updateUserProfileByAdmin, userInitialState);

    // Update User Licenses By Admin
    case actions.UPDATE_USER_LICENSES_BY_ADMIN_REQUEST:
      return loadingState(state, userStore.updateUserLicensesByAdmin);
    case actions.UPDATE_USER_LICENSES_BY_ADMIN_SUCCESS:
      return successState(state, userStore.updateUserLicensesByAdmin, payload);
    case actions.UPDATE_USER_LICENSES_BY_ADMIN_ERROR:
      return failedState(state, userStore.updateUserLicensesByAdmin, error);
    case actions.UPDATE_USER_LICENSES_BY_ADMIN_RESET:
      return resetState(state, userStore.updateUserLicensesByAdmin, userInitialState);

    // Update User Avatar By Admin
    case actions.UPDATE_USER_AVATAR_BY_ADMIN_REQUEST:
      return loadingState(state, userStore.updateUserAvatarByAdmin);
    case actions.UPDATE_USER_AVATAR_BY_ADMIN_SUCCESS:
      return successState(state, userStore.updateUserAvatarByAdmin, payload);
    case actions.UPDATE_USER_AVATAR_BY_ADMIN_ERROR:
      return failedState(state, userStore.updateUserAvatarByAdmin, error);
    case actions.UPDATE_USER_AVATAR_BY_ADMIN_RESET:
      return resetState(state, userStore.updateUserAvatarByAdmin, userInitialState);

    // Update User Additional
    case actions.UPDATE_USER_ADDITIONAL_REQUEST:
      return loadingState(state, userStore.updateUserAdditional);
    case actions.UPDATE_USER_ADDITIONAL_SUCCESS:
      return successState(state, userStore.updateUserAdditional, payload).setIn(
        ['adminManageUser', 'data', 'user'],
        payload,
      );
    case actions.UPDATE_USER_ADDITIONAL_ERROR:
      return failedState(state, userStore.updateUserAdditional, error);
    case actions.UPDATE_USER_ADDITIONAL_RESET:
      return resetState(state, userStore.updateUserAdditional, userInitialState);

    // Export User
    case actions.EXPORT_USER_REQUEST:
      return loadingState(state, userStore.exportUser);
    case actions.EXPORT_USER_SUCCESS:
      return successState(state, userStore.exportUser, payload);
    case actions.EXPORT_USER_ERROR:
      return failedState(state, userStore.exportUser, error);
    case actions.EXPORT_USER_RESET:
      return resetState(state, userStore.exportUser, userInitialState);

    // Import Bulk
    case actions.ADMIN_BULK_CREATE_USER_REQUEST:
      return loadingState(state, userStore.bulkCreateUser);
    case actions.ADMIN_BULK_CREATE_USER_SUCCESS:
      return successState(state, userStore.bulkCreateUser, payload);
    case actions.ADMIN_BULK_CREATE_USER_ERROR:
      return failedState(state, userStore.bulkCreateUser, error);
    case actions.ADMIN_BULK_CREATE_USER_RESET:
      return resetState(state, userStore.bulkCreateUser, userInitialState);

    case actions.ADMIN_BULK_EDIT_USER_REQUEST:
      return loadingState(state, userStore.bulkEditUser);
    case actions.ADMIN_BULK_EDIT_USER_SUCCESS:
      return successState(state, userStore.bulkEditUser, payload);
    case actions.ADMIN_BULK_EDIT_USER_ERROR:
      return failedState(state, userStore.bulkEditUser, error);
    case actions.ADMIN_BULK_EDIT_USER_RESET:
      return resetState(state, userStore.bulkEditUser, userInitialState);

    case actions.ADMIN_BULK_CHANGE_USERNAME_REQUEST:
      return loadingState(state, userStore.bulkChangeUsername);
    case actions.ADMIN_BULK_CHANGE_USERNAME_SUCCESS:
      return successState(state, userStore.bulkChangeUsername, payload);
    case actions.ADMIN_BULK_CHANGE_USERNAME_ERROR:
      return failedState(state, userStore.bulkChangeUsername, error);
    case actions.ADMIN_BULK_CHANGE_USERNAME_RESET:
      return resetState(state, userStore.bulkChangeUsername, userInitialState);

    case actions.FETCH_MANAGE_USER_ENROLLMENT_HISTORIES_REQUEST:
      return loadingState(state, userStore.fetchManageUserEnrollmentHistories);
    case actions.FETCH_MANAGE_USER_ENROLLMENT_HISTORIES_SUCCESS:
      return successState(state, userStore.fetchManageUserEnrollmentHistories, payload);
    case actions.FETCH_MANAGE_USER_ENROLLMENT_HISTORIES_ERROR:
      return failedState(state, userStore.fetchManageUserEnrollmentHistories, payload);
    case actions.FETCH_MANAGE_USER_ENROLLMENT_HISTORIES_RESET:
      return resetState(state, userStore.fetchManageUserEnrollmentHistories, userInitialState);

    // Fetch users exclude subordinate
    case actions.FETCH_USERS_EXCLUDE_SUBORDINATE_REQUEST:
      return loadingState(state, userStore.usersExcludeSubordinate);
    case actions.FETCH_USERS_EXCLUDE_SUBORDINATE_SUCCESS:
      return successState(state, userStore.usersExcludeSubordinate, payload);
    case actions.FETCH_USERS_EXCLUDE_SUBORDINATE_ERROR:
      return failedState(state, userStore.usersExcludeSubordinate, error);
    case actions.FETCH_USERS_EXCLUDE_SUBORDINATE_RESET:
      return resetState(state, userStore.usersExcludeSubordinate, userInitialState);

    // Fetch user list by userIds
    case actions.FETCH_USER_LIST_BY_IDS_REQUEST:
      return loadingState(state, userStore.userListByIds);
    case actions.FETCH_USER_LIST_BY_IDS_SUCCESS:
      return successState(state, userStore.userListByIds, payload);
    case actions.FETCH_USER_LIST_BY_IDS_ERROR:
      return failedState(state, userStore.userListByIds, error);
    case actions.FETCH_USER_LIST_BY_IDS_RESET:
      return resetState(state, userStore.userListByIds, userInitialState);

    // Update user organization
    case actions.UPDATE_USER_ORGANIZATION_REQUEST:
      return loadingState(state, userStore.updateUserOrganization);
    case actions.UPDATE_USER_ORGANIZATION_SUCCESS:
      return successState(state, userStore.updateUserOrganization, payload);
    case actions.UPDATE_USER_ORGANIZATION_ERROR:
      return failedState(state, userStore.updateUserOrganization, error);
    case actions.UPDATE_USER_ORGANIZATION_RESET:
      return resetState(state, userStore.updateUserOrganization, userInitialState);

    // Fetch my team
    case actions.FETCH_MY_TEAM_REQUEST:
      return loadingState(state, userStore.myTeam, payload);
    case actions.FETCH_MY_TEAM_SUCCESS:
      return successState(state, userStore.myTeam, payload);
    case actions.FETCH_MY_TEAM_ERROR:
      return failedState(state, userStore.myTeam, error);
    case actions.FETCH_MY_TEAM_RESET:
      return resetState(state, userStore.myTeam, userInitialState);

    // Fetch my team moderation
    case actions.FETCH_MY_TEAM_MODERATION_REQUEST:
      return loadingState(state, userStore.myTeamModeration, payload);
    case actions.FETCH_MY_TEAM_MODERATION_SUCCESS:
      return successState(state, userStore.myTeamModeration, payload);
    case actions.FETCH_MY_TEAM_MODERATION_ERROR:
      return failedState(state, userStore.myTeamModeration, error);
    case actions.FETCH_MY_TEAM_MODERATION_RESET:
      return resetState(state, userStore.myTeamModeration, userInitialState);

    // Fetch my direct report
    case actions.FETCH_MY_DIRECT_REPORT_REQUEST:
      return loadingState(state, userStore.myDirectReport, payload);
    case actions.FETCH_MY_DIRECT_REPORT_SUCCESS:
      return successState(state, userStore.myDirectReport, payload);
    case actions.FETCH_MY_DIRECT_REPORT_ERROR:
      return failedState(state, userStore.myDirectReport, error);
    case actions.FETCH_MY_DIRECT_REPORT_RESET:
      return resetState(state, userStore.myDirectReport, userInitialState);

    // Fetch my team summary
    case actions.FETCH_MY_TEAM_SUMMARY_REQUEST:
      return loadingState(state, userStore.myTeamSummary, payload);
    case actions.FETCH_MY_TEAM_SUMMARY_SUCCESS:
      return successState(state, userStore.myTeamSummary, payload);
    case actions.FETCH_MY_TEAM_SUMMARY_ERROR:
      return failedState(state, userStore.myTeamSummary, error);
    case actions.FETCH_MY_TEAM_SUMMARY_RESET:
      return resetState(state, userStore.myTeamSummary, userInitialState);

    // Fetch my team moderation summary
    case actions.FETCH_MY_TEAM_MODERATION_SUMMARY_REQUEST:
      return loadingState(state, userStore.myTeamModerationSummary, payload);
    case actions.FETCH_MY_TEAM_MODERATION_SUMMARY_SUCCESS:
      return successState(state, userStore.myTeamModerationSummary, payload);
    case actions.FETCH_MY_TEAM_MODERATION_SUMMARY_ERROR:
      return failedState(state, userStore.myTeamModerationSummary, error);
    case actions.FETCH_MY_TEAM_MODERATION_SUMMARY_RESET:
      return resetState(state, userStore.myTeamModerationSummary, userInitialState);

    // Fetch my direct report summary
    case actions.FETCH_MY_DIRECT_REPORT_SUMMARY_REQUEST:
      return loadingState(state, userStore.myDirectReportSummary, payload);
    case actions.FETCH_MY_DIRECT_REPORT_SUMMARY_SUCCESS:
      return successState(state, userStore.myDirectReportSummary, payload);
    case actions.FETCH_MY_DIRECT_REPORT_SUMMARY_ERROR:
      return failedState(state, userStore.myDirectReportSummary, error);
    case actions.FETCH_MY_DIRECT_REPORT_SUMMARY_RESET:
      return resetState(state, userStore.myDirectReportSummary, userInitialState);

    // Fetch my team accessibility
    case actions.FETCH_MY_TEAM_ACCESSIBILITY_REQUEST:
      return loadingState(state, userStore.myTeamAccessibility);
    case actions.FETCH_MY_TEAM_ACCESSIBILITY_SUCCESS:
      return successState(state, userStore.myTeamAccessibility, payload);
    case actions.FETCH_MY_TEAM_ACCESSIBILITY_ERROR:
      return failedState(state, userStore.myTeamAccessibility, error);
    case actions.FETCH_MY_TEAM_ACCESSIBILITY_RESET:
      return resetState(state, userStore.myTeamAccessibility, userInitialState);

    // Fetch my team viewer accessibility
    case actions.FETCH_MY_TEAM_VIEWER_ACCESSIBILITY_REQUEST:
      return loadingState(state, userStore.myTeamViewerAccessibility);
    case actions.FETCH_MY_TEAM_VIEWER_ACCESSIBILITY_SUCCESS:
      return successState(state, userStore.myTeamViewerAccessibility, payload);
    case actions.FETCH_MY_TEAM_VIEWER_ACCESSIBILITY_ERROR:
      return failedState(state, userStore.myTeamViewerAccessibility, error);
    case actions.FETCH_MY_TEAM_VIEWER_ACCESSIBILITY_RESET:
      return resetState(state, userStore.myTeamViewerAccessibility, userInitialState);

    // Fetch my team user profile
    case actions.FETCH_MY_TEAM_USER_PROFILE_REQUEST:
      return loadingState(state, userStore.myTeamUserProfile);
    case actions.FETCH_MY_TEAM_USER_PROFILE_SUCCESS:
      return successState(state, userStore.myTeamUserProfile, payload);
    case actions.FETCH_MY_TEAM_USER_PROFILE_ERROR:
      return failedState(state, userStore.myTeamUserProfile, error);
    case actions.FETCH_MY_TEAM_USER_PROFILE_RESET:
      return resetState(state, userStore.myTeamUserProfile, userInitialState);

    // Fetch my team user enrollment history
    case actions.FETCH_MY_TEAM_USER_ENROLLMENT_HISTORY_REQUEST:
      return loadingState(state, userStore.myTeamUserEnrollmentHistory);
    case actions.FETCH_MY_TEAM_USER_ENROLLMENT_HISTORY_SUCCESS:
      return successState(state, userStore.myTeamUserEnrollmentHistory, payload);
    case actions.FETCH_MY_TEAM_USER_ENROLLMENT_HISTORY_ERROR:
      return failedState(state, userStore.myTeamUserEnrollmentHistory, error);
    case actions.FETCH_MY_TEAM_USER_ENROLLMENT_HISTORY_RESET:
      return resetState(state, userStore.myTeamUserEnrollmentHistory, userInitialState);

    case actions.FETCH_DIRECT_REPORT_USER_DROPDOWN_REQUEST:
      return loadingState(state, userStore.fetchDirectReportUserDropdown);
    case actions.FETCH_DIRECT_REPORT_USER_DROPDOWN_SUCCESS:
      return successState(state, userStore.fetchDirectReportUserDropdown, payload);
    case actions.FETCH_DIRECT_REPORT_USER_DROPDOWN_ERROR:
      return failedState(state, userStore.fetchDirectReportUserDropdown, error);
    case actions.FETCH_DIRECT_REPORT_USER_DROPDOWN_RESET:
      return resetState(state, userStore.fetchDirectReportUserDropdown, userInitialState);

    case actions.FETCH_SUPERVISOR_JOB_DROPDOWN_REQUEST:
      return loadingState(state, userStore.fetchSupervisorJobDropdown);
    case actions.FETCH_SUPERVISOR_JOB_DROPDOWN_SUCCESS:
      return successState(state, userStore.fetchSupervisorJobDropdown, payload);
    case actions.FETCH_SUPERVISOR_JOB_DROPDOWN_ERROR:
      return failedState(state, userStore.fetchSupervisorJobDropdown, error);
    case actions.FETCH_SUPERVISOR_JOB_DROPDOWN_RESET:
      return resetState(state, userStore.fetchSupervisorJobDropdown, userInitialState);

    case actions.FETCH_TRANSFER_USER_MY_TEAM_SUBORDINATE_REQUEST:
      return loadingState(state, userStore.fetchTransferUserMyTeamSubordinate, payload);
    case actions.FETCH_TRANSFER_USER_MY_TEAM_SUBORDINATE_SUCCESS:
      return successState(state, userStore.fetchTransferUserMyTeamSubordinate, payload);
    case actions.FETCH_TRANSFER_USER_MY_TEAM_SUBORDINATE_ERROR:
      return failedState(state, userStore.fetchTransferUserMyTeamSubordinate, error);
    case actions.FETCH_TRANSFER_USER_MY_TEAM_SUBORDINATE_RESET:
      return resetState(state, userStore.fetchTransferUserMyTeamSubordinate, userInitialState);

    case actions.FETCH_TRANSFER_USER_MY_TEAM_ASSIGN_CONTENT_REQUEST:
      return loadingState(state, userStore.fetchTransferUserMyTeamAssignCourse, payload);
    case actions.FETCH_TRANSFER_USER_MY_TEAM_ASSIGN_CONTENT_SUCCESS:
      return successState(state, userStore.fetchTransferUserMyTeamAssignCourse, payload);
    case actions.FETCH_TRANSFER_USER_MY_TEAM_ASSIGN_CONTENT_ERROR:
      return failedState(state, userStore.fetchTransferUserMyTeamAssignCourse, error);
    case actions.FETCH_TRANSFER_USER_MY_TEAM_ASSIGN_CONTENT_RESET:
      return resetState(state, userStore.fetchTransferUserMyTeamAssignCourse, userInitialState);

    case actions.FETCH_USER_MY_TEAM_ASSIGN_CONTENT_REQUEST:
      return loadingState(state, userStore.fetchUserMyTeamAssignCourse, payload);
    case actions.FETCH_USER_MY_TEAM_ASSIGN_CONTENT_SUCCESS:
      return successState(state, userStore.fetchUserMyTeamAssignCourse, payload);
    case actions.FETCH_USER_MY_TEAM_ASSIGN_CONTENT_ERROR:
      return failedState(state, userStore.fetchUserMyTeamAssignCourse, error);
    case actions.FETCH_USER_MY_TEAM_ASSIGN_CONTENT_RESET:
      return resetState(state, userStore.fetchUserMyTeamAssignCourse, userInitialState);

    // Update User Two Factor
    case actions.UPDATE_USER_TWO_FACTOR_REQUEST:
      return loadingState(state, userStore.updateUserTwoFactor, payload);
    case actions.UPDATE_USER_TWO_FACTOR_SUCCESS:
      return successState(state, userStore.updateUserTwoFactor);
    case actions.UPDATE_USER_TWO_FACTOR_ERROR:
      return failedState(state, userStore.updateUserTwoFactor, error);
    case actions.UPDATE_USER_TWO_FACTOR_RESET:
      return resetState(state, userStore.updateUserTwoFactor, userInitialState);

    // Fetch User Two Factor Credential
    case actions.FETCH_USER_TWO_FACTOR_CREDENTIAL_REQUEST: {
      return loadingState(state, userStore.fetchUserTwoFactorCredential);
    }

    case actions.FETCH_USER_TWO_FACTOR_CREDENTIAL_SUCCESS:
      return successState(state, userStore.fetchUserTwoFactorCredential, payload);
    case actions.FETCH_USER_TWO_FACTOR_CREDENTIAL_ERROR:
      return failedState(state, userStore.fetchUserTwoFactorCredential, error);
    case actions.FETCH_USER_TWO_FACTOR_CREDENTIAL_RESET:
      return resetState(state, userStore.fetchUserTwoFactorCredential, userInitialState);

    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_REQUEST:
      return loadingState(state, userStore.fetchUserPlanPackageLicense, payload);
    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_SUCCESS:
      return successState(state, userStore.fetchUserPlanPackageLicense, payload);
    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_ERROR:
      return failedState(state, userStore.fetchUserPlanPackageLicense, error);
    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_RESET:
      return resetState(state, userStore.fetchUserPlanPackageLicense, userInitialState);

    case actions.UPDATE_USER_PLAN_PACKAGE_LICENSE_REQUEST:
      return loadingState(state, userStore.updateUserPlanPackageLicense, payload);
    case actions.UPDATE_USER_PLAN_PACKAGE_LICENSE_SUCCESS:
      return successState(state, userStore.updateUserPlanPackageLicense, payload);
    case actions.UPDATE_USER_PLAN_PACKAGE_LICENSE_ERROR:
      return failedState(state, userStore.updateUserPlanPackageLicense, error);
    case actions.UPDATE_USER_PLAN_PACKAGE_LICENSE_RESET:
      return resetState(state, userStore.updateUserPlanPackageLicense, userInitialState);

    case actions.FETCH_USER_PLAN_LIST_REQUEST:
      return loadingState(state, userStore.fetchUserPlanList, payload);
    case actions.FETCH_USER_PLAN_LIST_SUCCESS:
      return successState(state, userStore.fetchUserPlanList, payload);
    case actions.FETCH_USER_PLAN_LIST_ERROR:
      return failedState(state, userStore.fetchUserPlanList, error);
    case actions.FETCH_USER_PLAN_LIST_RESET:
      return resetState(state, userStore.fetchUserPlanList, userInitialState);

    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_HISTORY_LIST_REQUEST:
      return loadingState(state, userStore.fetchUserPlanPackageLicenseHistoryList, payload);
    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_HISTORY_LIST_SUCCESS:
      return successState(state, userStore.fetchUserPlanPackageLicenseHistoryList, payload);
    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_HISTORY_LIST_ERROR:
      return failedState(state, userStore.fetchUserPlanPackageLicenseHistoryList, error);
    case actions.FETCH_USER_PLAN_PACKAGE_LICENSE_HISTORY_LIST_RESET:
      return resetState(state, userStore.fetchUserPlanPackageLicenseHistoryList, userInitialState);

    case actions.DOWNLOAD_BULK_ASSIGN_PLAN_PACKAGE_LICENSE_TEMPLATE_REQUEST:
      return loadingState(state, userStore.downloadBulkAssignPlanPackageLicenseTemplate, payload);
    case actions.DOWNLOAD_BULK_ASSIGN_PLAN_PACKAGE_LICENSE_TEMPLATE_SUCCESS:
      return successState(state, userStore.downloadBulkAssignPlanPackageLicenseTemplate, payload);
    case actions.DOWNLOAD_BULK_ASSIGN_PLAN_PACKAGE_LICENSE_TEMPLATE_ERROR:
      return failedState(state, userStore.downloadBulkAssignPlanPackageLicenseTemplate, error);
    case actions.DOWNLOAD_BULK_ASSIGN_PLAN_PACKAGE_LICENSE_TEMPLATE_RESET:
      return resetState(state, userStore.downloadBulkAssignPlanPackageLicenseTemplate, userInitialState);

    case actions.BULK_ASSIGN_PLAN_PACKAGE_LICENSE_REQUEST:
      return loadingState(state, userStore.bulkAssignPlanPackageLicense, payload);
    case actions.BULK_ASSIGN_PLAN_PACKAGE_LICENSE_SUCCESS:
      return successState(state, userStore.bulkAssignPlanPackageLicense, payload);
    case actions.BULK_ASSIGN_PLAN_PACKAGE_LICENSE_ERROR:
      return failedState(state, userStore.bulkAssignPlanPackageLicense, error);
    case actions.BULK_ASSIGN_PLAN_PACKAGE_LICENSE_RESET:
      return resetState(state, userStore.bulkAssignPlanPackageLicense, userInitialState);

    case actions.DOWNLOAD_BULK_TRANSFER_PLAN_PACKAGE_LICENSE_TEMPLATE_REQUEST:
      return loadingState(state, userStore.downloadBulkTransferPlanPackageLicenseTemplate, payload);
    case actions.DOWNLOAD_BULK_TRANSFER_PLAN_PACKAGE_LICENSE_TEMPLATE_SUCCESS:
      return successState(state, userStore.downloadBulkTransferPlanPackageLicenseTemplate, payload);
    case actions.DOWNLOAD_BULK_TRANSFER_PLAN_PACKAGE_LICENSE_TEMPLATE_ERROR:
      return failedState(state, userStore.downloadBulkTransferPlanPackageLicenseTemplate, error);
    case actions.DOWNLOAD_BULK_TRANSFER_PLAN_PACKAGE_LICENSE_TEMPLATE_RESET:
      return resetState(state, userStore.downloadBulkTransferPlanPackageLicenseTemplate, userInitialState);

    case actions.BULK_TRANSFER_PLAN_PACKAGE_LICENSE_REQUEST:
      return loadingState(state, userStore.bulkTransferPlanPackageLicense, payload);
    case actions.BULK_TRANSFER_PLAN_PACKAGE_LICENSE_SUCCESS:
      return successState(state, userStore.bulkTransferPlanPackageLicense, payload);
    case actions.BULK_TRANSFER_PLAN_PACKAGE_LICENSE_ERROR:
      return failedState(state, userStore.bulkTransferPlanPackageLicense, error);
    case actions.BULK_TRANSFER_PLAN_PACKAGE_LICENSE_RESET:
      return resetState(state, userStore.bulkTransferPlanPackageLicense, userInitialState);

    default:
      return state;
  }
}
