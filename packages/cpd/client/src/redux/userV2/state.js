import { createInitialState } from '@helpers/reduxState';

export const userStore = {
  manageUserList: 'manageUserlist',
  manageUserSummary: 'manageUserSummary',
  userSaluteList: 'userSaluteList',
  updateUserPermissionGroup: 'updateUserPermissionGroup',
  updateUserLogin: 'updateUserLogin',
  sendUserSetFirstPasswordEmail: 'sendUserSetFirstPasswordEmail',
  sendUserConnectSSOEmail: 'sendUserConnectSSOEmail',
  updateUserProfileByAdmin: 'updateUserProfileByAdmin',
  updateUserLicensesByAdmin: 'updateUserLicensesByAdmin',
  updateUserAvatarByAdmin: 'updateUserAvatarByAdmin',
  updateUserAdditional: 'updateUserAdditional',
  exportUser: 'exportUser',
  downloadBulkCreateUserTemplate: 'downloadBulkCreateUserTemplate',
  downloadBulkEditUser: 'downloadBulkEditUser',
  bulkCreateUser: 'bulkCreateUser',
  bulkEditUser: 'bulkEditUser',
  bulkChangeUsername: 'bulkChangeUsername',
  fetchManageUserEnrollmentHistories: 'fetchManageUserEnrollmentHistories',
  usersExcludeSubordinate: 'usersExcludeSubordinate',
  userListByIds: 'userListByIds',
  updateUserOrganization: 'updateUserOrganization',
  myTeam: 'myTeam',
  myTeamModeration: 'myTeamModeration',
  myDirectReport: 'myDirectReport',
  myTeamSummary: 'myTeamSummary',
  myTeamModerationSummary: 'myTeamModerationSummary',
  myDirectReportSummary: 'myDirectReportSummary',
  myTeamAccessibility: 'myTeamAccessibility',
  myTeamViewerAccessibility: 'myTeamViewerAccessibility',
  myTeamUserProfile: 'myTeamUserProfile',
  myTeamUserEnrollmentHistory: 'myTeamUserEnrollmentHistory',
  fetchDirectReportUserDropdown: 'fetchDirectReportUserDropdown',
  fetchSupervisorJobDropdown: 'fetchSupervisorJobDropdown',
  fetchTransferUserMyTeamSubordinate: 'fetchTransferUserMyTeamSubordinate',
  fetchTransferUserMyTeamAssignCourse: 'fetchTransferUserMyTeamAssignCourse',
  fetchUserMyTeamAssignCourse: 'fetchUserMyTeamAssignCourse',
  updateUserTwoFactor: 'updateUserTwoFactor',
  fetchUserTwoFactorCredential: 'fetchUserTwoFactorCredential',
  fetchUserPlanPackageLicense: 'fetchUserPlanPackageLicense',
  updateUserPlanPackageLicense: 'updateUserPlanPackageLicense',
  fetchUserPlanList: 'fetchUserPlanList',
  fetchUserPlanPackageLicenseHistoryList: 'fetchUserPlanPackageLicenseHistoryList',
  downloadBulkAssignPlanPackageLicenseTemplate: 'downloadBulkAssignPlanPackageLicenseTemplate',
  bulkAssignPlanPackageLicense: 'bulkAssignPlanPackageLicense',
  downloadBulkTransferPlanPackageLicenseTemplate: 'downloadBulkTransferPlanPackageLicenseTemplate',
  bulkTransferPlanPackageLicense: 'bulkTransferPlanPackageLicense',
};

const userInitialData = {
  [userStore.manageUserList]: [],
  [userStore.manageUserSummary]: [],
  [userStore.userSaluteList]: [],
  [userStore.updateUserPermissionGroup]: null,
  [userStore.updateUserLogin]: null,
  [userStore.sendUserSetFirstPasswordEmail]: null,
  [userStore.sendUserConnectSSOEmail]: null,
  [userStore.updateUserProfileByAdmin]: null,
  [userStore.updateUserLicensesByAdmin]: null,
  [userStore.updateUserAvatarByAdmin]: null,
  [userStore.updateUserAdditional]: null,
  [userStore.exportUser]: [],
  [userStore.bulkCreateUser]: null,
  [userStore.bulkEditUser]: null,
  [userStore.bulkChangeUsername]: null,
  [userStore.usersExcludeSubordinate]: null,
  [userStore.userListByIds]: null,
  [userStore.updateUserOrganization]: null,
  [userStore.myTeam]: null,
  [userStore.myTeamModeration]: null,
  [userStore.myDirectReport]: null,
  [userStore.myTeamSummary]: null,
  [userStore.myTeamModerationSummary]: null,
  [userStore.myDirectReportSummary]: null,
  [userStore.myTeamAccessibility]: null,
  [userStore.myTeamViewerAccessibility]: null,
  [userStore.myTeamUserProfile]: null,
  [userStore.myTeamUserEnrollmentHistory]: null,
  [userStore.fetchManageUserEnrollmentHistories]: null,
  [userStore.fetchDirectReportUserDropdown]: null,
  [userStore.fetchSupervisorJobDropdown]: null,
  [userStore.fetchTransferUserMyTeamSubordinate]: null,
  [userStore.fetchTransferUserMyTeamAssignCourse]: null,
  [userStore.fetchUserMyTeamAssignCourse]: null,
  [userStore.updateUserTwoFactor]: null,
  [userStore.fetchUserTwoFactorCredential]: null,
  [userStore.fetchUserPlanPackageLicense]: null,
  [userStore.updateUserPlanPackageLicense]: null,
  [userStore.fetchUserPlanList]: null,
  [userStore.fetchUserPlanPackageLicenseHistoryList]: null,
  [userStore.bulkAssignPlanPackageLicense]: null,
  [userStore.downloadBulkAssignPlanPackageLicenseTemplate]: null,
  [userStore.downloadBulkTransferPlanPackageLicenseTemplate]: null,
  [userStore.bulkTransferPlanPackageLicense]: null,
};

export const userInitialState = createInitialState(userInitialData);
