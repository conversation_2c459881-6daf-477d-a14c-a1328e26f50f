import { IsObject, IsArray } from 'class-validator';

import {
  BulkAssignPlanPackageLicenseParams,
  BulkAssignPlanPackageLicensePayloadParams,
} from '@constants/types/planPackage.type';

import { BaseSchema } from '@presenters/consumers/schema/base.schema';

export class BulkAssignPlanPackageLicenseSchema extends BaseSchema<BulkAssignPlanPackageLicenseParams> {
  @IsObject()
  payload: BulkAssignPlanPackageLicensePayloadParams;

  @IsObject()
  originalPayload: Record<string, any>;

  @IsArray()
  raw: string[];
}
