import { IsArray, IsObject } from 'class-validator';

import { BulkTransferPlanPackageLicenseParams } from '@constants/types/planPackage.type';

import { BaseSchema } from '@presenters/consumers/schema/base.schema';

export class BulkTransferPlanPackageLicenseSchema extends BaseSchema<BulkTransferPlanPackageLicenseParams> {
  @IsObject()
  payload: BulkTransferPlanPackageLicenseParams;

  @IsObject()
  originalPayload: Record<string, any>;

  @IsArray()
  raw: string[];
}
