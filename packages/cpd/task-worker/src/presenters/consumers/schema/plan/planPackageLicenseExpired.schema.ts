import { GenericID } from '@iso/constants/commonTypes';
import { IsArray, IsString } from 'class-validator';

import { PlanPackageLicenseExpiredParams } from '@constants/types/plan.type';

import { BaseSchema } from '@presenters/consumers/schema/base.schema';
import { TaskOperationKeyParams } from '@iso/lms/types/taskOperation.type';

export class PlanPackageLicenseExpiredSchema extends BaseSchema<PlanPackageLicenseExpiredParams> {
  @IsString()
  memoryTaskOperationKey: TaskOperationKeyParams;

  @IsString()
  userId: GenericID;

  @IsString()
  organizationId: GenericID;

  @IsArray()
  @IsString({ each: true })
  planPackageLicenseIds: GenericID[];
}
