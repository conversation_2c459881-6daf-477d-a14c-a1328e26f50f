import { GenericID } from '@iso/constants/commonTypes';
import { BulkUpdateCourseCategoryOperationEnum } from '@iso/lms/enums/courseCategory.enum';
import { IsEnum, IsString } from 'class-validator';

import { BulkUpdateCourseCategoryParams } from '@constants/types/courseCategory.type';

import { BaseSchema } from '@presenters/consumers/schema/base.schema';

export class BulkUpdateCourseCategorySchema extends BaseSchema<BulkUpdateCourseCategoryParams> {
  @IsString()
  courseId: GenericID;

  @IsString()
  courseCategoryId: GenericID;

  @IsEnum(BulkUpdateCourseCategoryOperationEnum)
  operation: BulkUpdateCourseCategoryOperationEnum;
}
