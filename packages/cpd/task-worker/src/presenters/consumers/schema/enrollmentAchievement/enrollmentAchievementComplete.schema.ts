import { GenericID } from '@iso/constants/commonTypes';
import { IsOptional, IsString } from 'class-validator';

import { EnrollmentAchievementCompleteParams } from '@constants/types/enrollmentAchievement.type';

import { BaseSchema } from '@presenters/consumers/schema/base.schema';

export class EnrollmentAchievementCompleteSchema extends BaseSchema<EnrollmentAchievementCompleteParams> {
  @IsString()
  enrollmentId: GenericID;

  @IsOptional()
  @IsString()
  memoryTaskOperationKey: string;
}
