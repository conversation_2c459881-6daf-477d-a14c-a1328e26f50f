import { ObjectValue } from '@iso/constants/commonTypes';
import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import isUndefined from 'lodash/isUndefined';

import { BrokerValidateHeaderPropertyHandlerParams } from '@constants/types/infrastructures/messageBroker.type';

export const validateHeaderPropertyBulkOperationType = (
  props: ObjectValue & { operationType?: string },
  bulkOperations: string[],
): boolean => {
  if (isUndefined(props?.operationType)) {
    return false;
  }
  if (!bulkOperations.includes(String(props?.operationType))) {
    return false;
  }

  return true;
};

export const customerUpdateBulkOperation: BrokerValidateHeaderPropertyHandlerParams = (props) => {
  return validateHeaderPropertyBulkOperationType(props, [BulkOpTypeEnum.USER_VALIDATE]);
};

export const customerUploadUserBulkOperation: BrokerValidateHeaderPropertyHandlerParams = (props) => {
  return validateHeaderPropertyBulkOperationType(props, [BulkOpTypeEnum.UPLOAD_USER_VALIDATION]);
};

export const customerNewCustomerBulkOperation: BrokerValidateHeaderPropertyHandlerParams = (props) => {
  return validateHeaderPropertyBulkOperationType(props, [BulkOpTypeEnum.UPDATE_CUSTOMER]);
};

export const validateTaskHeaderOperation: BrokerValidateHeaderPropertyHandlerParams = (props) => {
  return validateHeaderPropertyBulkOperationType(props, [
    BulkOpTypeEnum.OIC_PRE_REPORT,
    BulkOpTypeEnum.OIC_REGULATOR_PRE_REPORT,
    BulkOpTypeEnum.OIC_POST_REPORT,
    BulkOpTypeEnum.OIC_REGULATOR_POST_REPORT,
    BulkOpTypeEnum.TSI_REPORT,
    BulkOpTypeEnum.OIC_DEDUCT_REPORT,
    BulkOpTypeEnum.PRE_ENROLLMENT_REPORT,
    BulkOpTypeEnum.REGULAR_PRE_ENROLLMENT_REPORT,
    BulkOpTypeEnum.SURVEY_SUBMISSION_REPORT,
    BulkOpTypeEnum.CUSTOMER_CREDIT_BALANCE_REPORT,
    BulkOpTypeEnum.LEARNER_REPORT,
  ]);
};

export const enrollmentBulkOperation: BrokerValidateHeaderPropertyHandlerParams = (props) => {
  return validateHeaderPropertyBulkOperationType(props, [BulkOpTypeEnum.ENROLLMENT_HISTORY]);
};

export const achievementBulkOperation: BrokerValidateHeaderPropertyHandlerParams = (props) => {
  return validateHeaderPropertyBulkOperationType(props, [BulkOpTypeEnum.ENROLLMENT_ACHIEVEMENT_COMPLETED]);
};

export const validateHeaderBulkOperationDomainQueue: BrokerValidateHeaderPropertyHandlerParams = (props) => {
  return validateHeaderPropertyBulkOperationType(props, [
    BulkOpTypeEnum.ACTIVATE,
    BulkOpTypeEnum.ENROLLMENT,
    BulkOpTypeEnum.ENROLLMENT_BUNDLE,
    BulkOpTypeEnum.EDIT_USER,
    BulkOpTypeEnum.CHANGE_USERNAME,
    BulkOpTypeEnum.EDIT_LEARNING_PATH_EXPIRE_DATE,
    BulkOpTypeEnum.LEARNING_PATH_ENROLLMENT,
    BulkOpTypeEnum.CLASSROOM_MARK_RESULT,
    BulkOpTypeEnum.ENROLLMENT_HISTORY,
    BulkOpTypeEnum.CANCEL_ENROLLMENT_HISTORY,
    BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE,
    BulkOpTypeEnum.UPDATE_COURSE_CATEGORY,
    BulkOpTypeEnum.TRANSFER_PLAN_PACKAGE_LICENSE,
  ]);
};

export const validateHeaderUploadUserValidateOperationDomainQueue: BrokerValidateHeaderPropertyHandlerParams = (
  props,
) => {
  return validateHeaderPropertyBulkOperationType(props, [
    BulkOpTypeEnum.UPLOAD_USER_VALIDATION,
    BulkOpTypeEnum.UPLOAD_PRE_ASSIGN_LEARNING_PATH_ENROLLMENT,
    BulkOpTypeEnum.CANCEL_PRE_ENROLLMENT,
    BulkOpTypeEnum.CANCEL_PRE_ASSIGN_LEARNING_PATH,
  ]);
};
