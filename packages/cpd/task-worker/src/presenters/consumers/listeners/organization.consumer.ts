import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Channel } from 'amqplib';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { CourseCategoryDIToken, OrganizationDIToken, PlanDIToken } from '@applications/di/domain';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import { IBulkUpdateCourseCategoryUseCase } from '@interfaces/usecases/courseCategory.interface';
import { IGetOrganizationDomainsUseCase } from '@interfaces/usecases/organization.interface';
import {
  IBulkAssignPlanPackageLicenseUseCase,
  IBulkTransferPlanPackageLicenseUseCase,
} from '@interfaces/usecases/planPackage.interface';

import { BulkUpdateCourseCategorySchema } from '@schemas/consumers/courseCategory/bulkUpdateCourseCategory.schema';
import { BulkAssignPlanPackageLicenseSchema } from '@schemas/consumers/planPackage/bulkAssignPlanPackageLicense.schema';
import { BulkTransferPlanPackageLicenseSchema } from '@schemas/consumers/planPackage/bulkTransferPlanPackageLicense.schema';

import {
  ConsumerHeaderOperationTypeHandler,
  MessageBody,
  MessageChannel,
  MessageHeader,
} from '@presenters/consumers/decorator';
import { validateHeaderBulkOperationDomainQueue } from '@presenters/consumers/middleware/validator.middleware';

@Injectable()
export class OrganizationConsumer implements IConsumer {
  // Queue Name
  private readonly uploadUserValidateQueueName = '{domain}:upload_user_validate';
  private readonly organizationQueueTemplateName = '{domain}:v2';

  constructor(
    @Inject(OrganizationDIToken.GetOrganizationDomainsUseCase)
    private readonly getOrganizationDomainsUseCase: IGetOrganizationDomainsUseCase,
    @Inject(PlanDIToken.BulkAssignPlanPackageLicenseUseCase)
    private readonly bulkAssignPlanPackageLicenseUseCase: IBulkAssignPlanPackageLicenseUseCase,
    @Inject(CourseCategoryDIToken.BulkUpdateCourseCategoryUseCase)
    private readonly bulkUpdateCourseCategoryUseCase: IBulkUpdateCourseCategoryUseCase,
    @Inject(PlanDIToken.BulkTransferPlanPackageLicenseUseCase)
    private readonly bulkTransferPlanPackageLicenseUseCase: IBulkTransferPlanPackageLicenseUseCase,

    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  onModuleInit() {
    this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const organizations = await this.getOrganizationDomainsUseCase.execute(null);

    for (const organization of organizations) {
      const organizationQueueName = this.organizationQueueTemplateName.replace('{domain}', organization.domain);
      this.logger.log(`consume: ${organizationQueueName}`);
      this.messageBrokerServiceAdaptor.consumers(
        organizationQueueName,
        async (msg, channel) => {
          this.bulkAssignPlanPackageLicenseConsumerListener.bind(this)(msg, channel);
          this.bulkUpdateCourseCategoryConsumerListener.bind(this)(msg, channel);
          this.bulkTransferPlanPackageLicenseConsumerListener.bind(this)(msg, channel);
        },
        {
          noAck: false,
        },
      );
    }
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE,
    onValidateHeader: validateHeaderBulkOperationDomainQueue,
  })
  async bulkAssignPlanPackageLicenseConsumerListener(
    @MessageChannel() channel: Channel,
    @MessageBody(BulkAssignPlanPackageLicenseSchema) content: BulkAssignPlanPackageLicenseSchema,
    @MessageHeader() headers: Record<string, unknown>,
  ) {
    this.logger.log('consume: bulk assign plan package license');
    const data = content.toJson();

    const payload = {
      channel,
      content: data,
      headers,
    };

    await this.bulkAssignPlanPackageLicenseUseCase.execute(payload);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.UPDATE_COURSE_CATEGORY,
    onValidateHeader: validateHeaderBulkOperationDomainQueue,
  })
  async bulkUpdateCourseCategoryConsumerListener(
    @MessageChannel() channel: Channel,
    @MessageBody(BulkUpdateCourseCategorySchema) content: BulkUpdateCourseCategorySchema,
    @MessageHeader() headers: Record<string, unknown>,
  ) {
    this.logger.log('consume: bulk update course category');
    const data = content.toJson();
    const { createdByUserId, organizationId, jobId, raw } = headers;

    const payload = {
      channel,
      payload: {
        params: data,
        createdByUserId: createdByUserId as string,
        organizationId: organizationId as string,
        jobId: jobId as string,
        raw: raw as string[],
      },
    };

    await this.bulkUpdateCourseCategoryUseCase.execute(payload);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.TRANSFER_PLAN_PACKAGE_LICENSE,
    onValidateHeader: validateHeaderBulkOperationDomainQueue,
  })
  async bulkTransferPlanPackageLicenseConsumerListener(
    @MessageChannel() channel: Channel,
    @MessageBody(BulkTransferPlanPackageLicenseSchema) content: BulkTransferPlanPackageLicenseSchema,
    @MessageHeader() headers: Record<string, unknown>,
  ) {
    this.logger.log('consume: bulk transfer plan package license');
    const data = content.toJson();

    const payload = {
      channel,
      content: data,
      headers,
    };

    await this.bulkTransferPlanPackageLicenseUseCase.execute(payload);
  }
}
