import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { JobDIToken } from '@applications/di/domain/job.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import {
  IUploadCancelPreAssignLearningPathEnrollmentUseCase,
  IUploadCancelPreEnrollmentUseCase,
  IUploadPreAssignLearningPathEnrollmentUseCase,
  IUploadUserValidateUseCase,
} from '@interfaces/usecases/job.interface';
import { IGetOrganizationDomainsUseCase } from '@interfaces/usecases/organization.interface';

import { UploadCancelPreAssignLearningPathEnrollmentSchema } from '@schemas/consumers/jobs/uploadCancelPreAssignContentLearningPathEnrollment.schema';
import { UploadCancelPreEnrollmentSchema } from '@schemas/consumers/jobs/uploadCancelPreEnrollment.schema';
import { UploadPreAssignContentLearningPathEnrollmentSchema } from '@schemas/consumers/jobs/uploadPreAssignContentLearningPathEnrollment.schema';
import { UploadUserValidatePayloadSchema } from '@schemas/consumers/jobs/uploadUserValidatePayload.schema';

import { ConsumerHeaderOperationTypeHandler, MessageBody } from '@presenters/consumers/decorator';
import { validateHeaderUploadUserValidateOperationDomainQueue } from '@presenters/consumers/middleware/validator.middleware';

@Injectable()
export class JobConsumer implements IConsumer {
  // Queue Name
  private readonly bulkUploadUserValidateOperation = '{domain}:upload_user_validate';

  constructor(
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,

    // UserCase
    @Inject(OrganizationDIToken.GetOrganizationDomainsUseCase)
    private readonly getOrganizationDomainsUseCase: IGetOrganizationDomainsUseCase,
    @Inject(JobDIToken.UploadUserValidateUserCase)
    private readonly uploadUserValidateUseCase: IUploadUserValidateUseCase,
    @Inject(JobDIToken.UploadCancelPreEnrollmentUseCase)
    private readonly uploadCancelPreEnrollmentUseCase: IUploadCancelPreEnrollmentUseCase,
    @Inject(JobDIToken.UploadPreAssignLearningPathEnrollmentUseCase)
    private readonly uploadPreAssignLearningPathEnrollmentUseCase: IUploadPreAssignLearningPathEnrollmentUseCase,
    @Inject(JobDIToken.UploadCancelPreAssignLearningPathEnrollmentUseCase)
    private readonly uploadCancelPreAssignLearningPathEnrollmentUseCase: IUploadCancelPreAssignLearningPathEnrollmentUseCase,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const organizations = await this.getOrganizationDomainsUseCase.execute(null);

    for (const organization of organizations) {
      const uploadUserValidateQueueName = this.bulkUploadUserValidateOperation.replace('{domain}', organization.domain);
      this.logger.log(`consume: ${uploadUserValidateQueueName}`);

      this.messageBrokerServiceAdaptor.consumers(
        uploadUserValidateQueueName,
        (msg, channel) => {
          this.uploadUserValidateConsumerListener.bind(this)(msg, channel);
          this.uploadPreAssignContentValidationConsumerListener.bind(this)(msg, channel);
          this.uploadCancelPreEnrollmentConsumerListener.bind(this)(msg, channel);
          this.uploadCancelPreAssignLearningPathConsumerListener.bind(this)(msg, channel);
        },
        {
          noAck: false,
        },
      );
    }
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.UPLOAD_USER_VALIDATION,
    onValidateHeader: validateHeaderUploadUserValidateOperationDomainQueue,
  })
  async uploadUserValidateConsumerListener(
    @MessageBody(UploadUserValidatePayloadSchema) content: UploadUserValidatePayloadSchema,
  ): Promise<void> {
    const data = content.toJson();
    await this.uploadUserValidateUseCase.execute(data);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.CANCEL_PRE_ENROLLMENT,
    onValidateHeader: validateHeaderUploadUserValidateOperationDomainQueue,
  })
  async uploadCancelPreEnrollmentConsumerListener(
    @MessageBody(UploadCancelPreEnrollmentSchema) content: UploadCancelPreEnrollmentSchema,
  ): Promise<void> {
    const data = content.toJson();
    await this.uploadCancelPreEnrollmentUseCase.execute(data);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.UPLOAD_PRE_ASSIGN_LEARNING_PATH_ENROLLMENT,
    onValidateHeader: validateHeaderUploadUserValidateOperationDomainQueue,
  })
  async uploadPreAssignContentValidationConsumerListener(
    @MessageBody(UploadPreAssignContentLearningPathEnrollmentSchema)
    content: UploadPreAssignContentLearningPathEnrollmentSchema,
  ): Promise<void> {
    const data = content.toJson();
    await this.uploadPreAssignLearningPathEnrollmentUseCase.execute(data);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.CANCEL_PRE_ASSIGN_LEARNING_PATH,
    onValidateHeader: validateHeaderUploadUserValidateOperationDomainQueue,
  })
  async uploadCancelPreAssignLearningPathConsumerListener(
    @MessageBody(UploadCancelPreAssignLearningPathEnrollmentSchema)
    content: UploadCancelPreAssignLearningPathEnrollmentSchema,
  ): Promise<void> {
    const data = content.toJson();
    await this.uploadCancelPreAssignLearningPathEnrollmentUseCase.execute(data);
  }
}
