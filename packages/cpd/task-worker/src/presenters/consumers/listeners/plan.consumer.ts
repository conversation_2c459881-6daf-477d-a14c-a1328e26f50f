import { Inject, Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { PlanDIToken } from '@applications/di/domain';

import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import { IPlanPackageLicenseExpiredUseCase } from '@interfaces/usecases/plan.interface';

import { PlanPackageLicenseExpiredSchema } from '@schemas/consumers/plan/planPackageLicenseExpired.schema';

import { ConsumerHeaderOperationTypeHandler, MessageBody } from '@presenters/consumers/decorator';
import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';

@Injectable()
export class PlanConsumer implements IConsumer {
  private readonly logger = new Logger(PlanConsumer.name);

  // Queue Name
  private readonly consumePlanPackageLicenseExpiredOperation = 'plan-package-license';

  constructor(
    @Inject(PlanDIToken.PlanPackageLicenseExpiredUseCase)
    private readonly planPackageLicenseExpiredUseCase: IPlanPackageLicenseExpiredUseCase,

    // service
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
  ) {}

  async onModuleInit() {
    await this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const queueNamePlanPackageLicenseExpired = this.consumePlanPackageLicenseExpiredOperation;
    this.logger.log(`consume: ${queueNamePlanPackageLicenseExpired}`);
    this.messageBrokerServiceAdaptor.consumers(
      queueNamePlanPackageLicenseExpired,
      this.planPackageLicenseExpiredConsumerListener.bind(this),
      {
        noAck: false,
      },
    );
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.PLAN_PACKAGE_LICENSE_EXPIRED,
  })
  async planPackageLicenseExpiredConsumerListener(
    @MessageBody(PlanPackageLicenseExpiredSchema) content: PlanPackageLicenseExpiredSchema,
  ): Promise<void> {
    this.logger.log('Consume: plan package license expired');
    const payload = content.toJson();
    await this.planPackageLicenseExpiredUseCase.execute(payload);
  }
}
