import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { AchievementDIToken } from '@applications/di/domain';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import { IEnrollmentAchievementCompleteUseCase } from '@interfaces/usecases/achievement.interface';
import { IGetOrganizationDomainsUseCase } from '@interfaces/usecases/organization.interface';

import { EnrollmentAchievementCompleteSchema } from '@schemas/consumers/enrollmentAchievement/enrollmentAchievementComplete.schema';

import { ConsumerHeaderOperationTypeHandler, MessageBody } from '@presenters/consumers/decorator';
import { achievementBulkOperation } from '@presenters/consumers/middleware/validator.middleware';

@Injectable()
export class EnrollmentAchievementConsumer implements IConsumer {
  private readonly logger = new Logger(EnrollmentAchievementConsumer.name);

  // Queue Name
  private readonly consumerTaskOperation = '{domain}:enrollment_achievement';

  constructor(
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
    @Inject(OrganizationDIToken.GetOrganizationDomainsUseCase)
    private readonly getOrganizationDomainsUseCase: IGetOrganizationDomainsUseCase,
    @Inject(AchievementDIToken.EnrollmentAchievementCompleteUseCase)
    private readonly enrollmentAchievementCompleteUseCase: IEnrollmentAchievementCompleteUseCase,
  ) {}

  async onModuleInit() {
    this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const organizations = await this.getOrganizationDomainsUseCase.execute(null);

    for (const organization of organizations) {
      const queueName = this.consumerTaskOperation.replace('{domain}', organization.domain);
      this.logger.log(`consume: ${queueName}`);
      this.messageBrokerServiceAdaptor.consumers(
        queueName,
        async (msg, channel) => {
          this.enrollmentAchievementCompleteConsumerListener.bind(this)(msg, channel);
        },
        {
          noAck: false,
        },
      );
    }
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.ENROLLMENT_ACHIEVEMENT_COMPLETED,
    onValidateHeader: achievementBulkOperation,
  })
  async enrollmentAchievementCompleteConsumerListener(
    @MessageBody(EnrollmentAchievementCompleteSchema) content: EnrollmentAchievementCompleteSchema,
  ): Promise<void> {
    this.logger.log(`Consumer: Enrollment Achievement Complete`);
    const params = content.toJson();
    await this.enrollmentAchievementCompleteUseCase.execute(params);
  }
}
