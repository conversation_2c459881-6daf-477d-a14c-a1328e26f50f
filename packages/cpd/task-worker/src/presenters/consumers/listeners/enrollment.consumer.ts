import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Channel } from 'amqplib';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import {
  IAutoApproveEnrollmentUseCase,
  IBulkEnrollmentHistoryUseCase,
  ICancelEnrollmentHistoryUseCase,
} from '@interfaces/usecases/enrollment.interface';
import { IGetOrganizationDomainsUseCase } from '@interfaces/usecases/organization.interface';
import { ICalculateCumulativeTSIQuizScoreUseCase } from '@interfaces/usecases/summaryTSIQuizScore.interface';

import { AutoApproveEnrollmentSchema } from '@schemas/consumers/enrollment/autoApproveEnrollment.schema';
import { BulkEnrollmentHistorySchema } from '@schemas/consumers/enrollment/bulkEnrollmentHistory.schema';
import { CancelEnrollmentHistorySchema } from '@schemas/consumers/enrollment/cancelEnrollmentHistory.schema';
import { CalculateSummaryTSIQuizScoreSchema } from '@schemas/consumers/summaryTSIQuizScore/calculateSummaryTSIQuizScore.schema';

import {
  ConsumerHandler,
  ConsumerHeaderOperationTypeHandler,
  MessageBody,
  MessageChannel,
  MessageHeader,
} from '@presenters/consumers/decorator';
import { summaryTSIQuizScoreExceptionMiddleware } from '@presenters/consumers/middleware/exception.middleware';
import { validateHeaderBulkOperationDomainQueue } from '@presenters/consumers/middleware/validator.middleware';

@Injectable()
export class EnrollmentConsumer implements IConsumer {
  // Queue Name
  private readonly consumerEnrollmentHistoryOperation = '{domain}:enrollment_history';
  private readonly consumerAutoApproveEnrollmentOperation = '{domain}:auto-approve-enrollment';
  private readonly queueNameSummaryTSIQuizScore = 'summary_tsi_quiz_score';

  constructor(
    @Inject(OrganizationDIToken.GetOrganizationDomainsUseCase)
    private readonly getOrganizationDomainsUseCase: IGetOrganizationDomainsUseCase,
    @Inject(EnrollmentDIToken.BulkEnrollmentHistoryUseCase)
    private readonly bulkEnrollmentHistoryUseCase: IBulkEnrollmentHistoryUseCase,
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
    @Inject(EnrollmentDIToken.CancelEnrollmentHistoryUseCase)
    private readonly cancelEnrollmentHistoryUseCase: ICancelEnrollmentHistoryUseCase,
    @Inject(EnrollmentDIToken.AutoApproveEnrollmentUseCase)
    private readonly autoApproveEnrollmentUseCase: IAutoApproveEnrollmentUseCase,
    @Inject(EnrollmentDIToken.CalculateCumulativeTSIQuizScoreUseCase)
    private readonly calculateCumulativeTSIQuizScoreUseCase: ICalculateCumulativeTSIQuizScoreUseCase,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    await this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const organizations = await this.getOrganizationDomainsUseCase.execute(null);

    for (const organization of organizations) {
      const queueNameEnrollmentHistory = this.consumerEnrollmentHistoryOperation.replace(
        '{domain}',
        organization.domain,
      );
      this.logger.log(`consume: ${queueNameEnrollmentHistory}`);
      this.messageBrokerServiceAdaptor.consumers(
        queueNameEnrollmentHistory,
        async (msg, channel) => {
          this.bulkEnrollmentHistoryConsumerListener.bind(this)(msg, channel);
          this.cancelEnrollmentHistoryConsumerListener.bind(this)(msg, channel);
        },
        {
          noAck: false,
        },
      );
      const queueNameAutoApproveEnrollment = this.consumerAutoApproveEnrollmentOperation.replace(
        '{domain}',
        organization.domain,
      );
      this.logger.log(`consume: ${queueNameAutoApproveEnrollment}`);
      this.messageBrokerServiceAdaptor.consumers(
        queueNameAutoApproveEnrollment,
        this.autoApproveEnrollmentConsumerListener.bind(this),
        {
          noAck: false,
        },
      );
    }

    this.logger.log(`consume: ${this.queueNameSummaryTSIQuizScore}`);
    this.messageBrokerServiceAdaptor.consumers(
      this.queueNameSummaryTSIQuizScore,
      this.calculateSummaryTSIQuizScoreConsumerListener.bind(this),
      {
        noAck: false,
      },
    );
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.ENROLLMENT_HISTORY,
    onValidateHeader: validateHeaderBulkOperationDomainQueue,
  })
  async bulkEnrollmentHistoryConsumerListener(
    @MessageChannel() channel: Channel,
    @MessageBody(BulkEnrollmentHistorySchema) content: BulkEnrollmentHistorySchema,
    @MessageHeader() headers: Record<string, unknown>,
  ) {
    this.logger.log('consume: bulk enrollment history');
    const data = content.toJson();
    const { createdByUserId, organizationId, jobId, raw } = headers;

    const payload = {
      channel,
      payload: {
        params: data,
        createdByUserId: createdByUserId as string,
        organizationId: organizationId as string,
        jobId: jobId as string,
        raw: raw as string[],
      },
    };

    await this.bulkEnrollmentHistoryUseCase.execute(payload);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.CANCEL_ENROLLMENT_HISTORY,
    onValidateHeader: validateHeaderBulkOperationDomainQueue,
  })
  async cancelEnrollmentHistoryConsumerListener(
    @MessageBody(CancelEnrollmentHistorySchema) content: CancelEnrollmentHistorySchema,
  ) {
    this.logger.log('consume: cancel enrollment history');
    const payload = content.toJson();
    await this.cancelEnrollmentHistoryUseCase.execute(payload);
  }

  @ConsumerHandler()
  async autoApproveEnrollmentConsumerListener(
    @MessageBody(AutoApproveEnrollmentSchema) content: AutoApproveEnrollmentSchema,
  ): Promise<void> {
    this.logger.log('consume: approval enrollment');
    const payload = content.toJson();
    await this.autoApproveEnrollmentUseCase.execute(payload);
  }

  @ConsumerHandler({ isAck: true, onError: summaryTSIQuizScoreExceptionMiddleware })
  async calculateSummaryTSIQuizScoreConsumerListener(
    @MessageBody(CalculateSummaryTSIQuizScoreSchema) content: CalculateSummaryTSIQuizScoreSchema,
    @MessageHeader('key') key: string,
  ): Promise<void> {
    const data = content.toJson();
    await this.calculateCumulativeTSIQuizScoreUseCase.execute({ ...data, key });
  }
}
