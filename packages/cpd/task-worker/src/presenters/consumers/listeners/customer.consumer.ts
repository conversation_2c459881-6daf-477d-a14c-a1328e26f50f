import crypto from 'node:crypto';

import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { DataType } from '@core/constants/enums';

import { CreditDIToken } from '@applications/di/domain/credit.di';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import {
  ICreateCustomerChannelUseCase,
  IGetCustomerCodesUseCase,
  IUploadCustomerUserValidationUseCase,
} from '@interfaces/usecases/customer.interface';

import { UploadCustomerUserValidationSchema } from '@schemas/consumers/customer/uploadCustomerUserValidation.schema';

import { ConsumerHeaderOperationTypeHandler, MessageBody } from '@presenters/consumers/decorator';
import {
  customerNewCustomerBulkOperation,
  customerUpdateBulkOperation,
  customerUploadUserBulkOperation,
} from '@presenters/consumers/middleware/validator.middleware';

@Injectable()
export class CustomerConsumer implements IConsumer {
  // Queue Name
  private readonly userValidateQueueName = 'customers/{customerCode}:user_validate';
  private readonly uploadUserValidateQueueName = 'customers/{customerCode}:upload_user_validate';

  private readonly workerClientIdQueueName = 'worker/{clientId}';
  private readonly newCustomerExchangeName = 'new_customer';
  private readonly routeKeyCustomerExchange = '';

  constructor(
    // Infrastructure
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
    // UseCase
    @Inject(CreditDIToken.GetCustomerCodesUseCase) private readonly getCustomerCodesUseCase: IGetCustomerCodesUseCase,
    @Inject(CreditDIToken.UploadCustomerUserValidationUseCase)
    private readonly uploadCustomerUserValidationUseCase: IUploadCustomerUserValidationUseCase,
    @Inject(CreditDIToken.CreateNewCustomerChannelUseCase)
    private readonly customerChannelUseCase: ICreateCustomerChannelUseCase,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    await this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const customerCodes = await this.getCustomerCodesUseCase.execute(null);
    for (const customerCode of customerCodes) {
      const customerUploadUserValidateQueueName = this.uploadUserValidateQueueName.replace(
        '{customerCode}',
        customerCode,
      );
      this.logger.log(`consume: ${customerUploadUserValidateQueueName}`);
      this.messageBrokerServiceAdaptor.consumers(
        customerUploadUserValidateQueueName,
        this.uploadUserValidationConsumerListener.bind(this),
        { noAck: false },
      );
    }

    const clientId = crypto.createHash('md5').update(new Date().toString()).digest('hex').slice(1, 16);
    const workerQueueName = this.workerClientIdQueueName.replace('{clientId}', clientId);
    this.logger.log(`consume: ${workerQueueName}`);
    this.messageBrokerServiceAdaptor.subscribe(
      workerQueueName,
      this.newCustomerExchangeName,
      this.routeKeyCustomerExchange,
      this.newCustomerUpdateCustomerConsumerListener.bind(this),
      { noAck: false },
    );
  }

  @OnEvent(MessageBrokerEventEnum.CreateNewChannelCustomer, { async: true })
  async onCreateConsumerChannelMessageBroker(customerCode: string) {
    const customerUploadUserValidateQueueName = this.uploadUserValidateQueueName.replace(
      '{customerCode}',
      customerCode,
    );
    this.logger.log(`consume: ${customerUploadUserValidateQueueName}`);
    this.messageBrokerServiceAdaptor.subscribe(
      customerUploadUserValidateQueueName,
      this.newCustomerExchangeName,
      '',
      this.uploadUserValidationConsumerListener.bind(this),
      { noAck: false },
    );
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.USER_VALIDATE,
    onValidateHeader: customerUpdateBulkOperation,
  })
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async userValidationConsumerListener(@MessageBody(DataType.StringType) content: string): Promise<void> {
    this.logger.log('userValidationConsumerListener: ');
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.UPLOAD_USER_VALIDATION,
    onValidateHeader: customerUploadUserBulkOperation,
  })
  async uploadUserValidationConsumerListener(
    @MessageBody(UploadCustomerUserValidationSchema) content: UploadCustomerUserValidationSchema,
  ): Promise<void> {
    const data = content.toJson();
    await this.uploadCustomerUserValidationUseCase.execute(data);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.UPDATE_CUSTOMER,
    onValidateHeader: customerNewCustomerBulkOperation,
  })
  async newCustomerUpdateCustomerConsumerListener(@MessageBody(DataType.StringType) customerCode: string) {
    await this.customerChannelUseCase.execute(customerCode);
  }
}
