import { GenericID } from '@iso/constants/commonTypes';
import { LoginProvider } from '@iso/lms/models/loginProvider.model';
import { LoginProviderParams } from '@iso/lms/types/loginProvider.type';

import { IBaseRepository } from '@interfaces/repositories/base.repository.interface';

export interface ILoginProviderRepository extends IBaseRepository<LoginProvider, LoginProviderParams> {
  findOrganizationLoginProviderSkilllane(organizationId: GenericID);
}
