export { IAchievementRepository } from '@interfaces/repositories/achievement.repository.interface';
export { IBadgeRepository } from '@interfaces/repositories/badge.repository.interface';
export { IAnnouncementRepository } from '@interfaces/repositories/announcement.repository.interface';
export { IAuthenticationMemoryCachedRepository } from '@interfaces/repositories/authenticationMemoryCached.repository.interface';
export { IBaseMemoryCachedRepository } from '@interfaces/repositories/baseMemoryCached.repository.interface';
export { ICertificateRepository } from '@interfaces/repositories/certificate.repository.interface';
export { IClassroomLocationEnrollmentRepository } from '@interfaces/repositories/classroomLocationEnrollment.repository.interface';
export { IClassroomLocationRepository } from '@interfaces/repositories/classroomLocation.repository.interface';
export { IClassroomRepository } from '@interfaces/repositories/classroom.repository.interface';
export { IClassroomRoundRepository } from '@interfaces/repositories/classroomRound.repository.interface';
export { IColumnSettingRepository } from '@interfaces/repositories/columnSetting.repository.interface';
export { ICourseItemCriteriaConfigRepository } from '@interfaces/repositories/courseItemCriteriaConfig.interface';
export { ICourseItemRepository } from '@interfaces/repositories/courseItem.repository.interface';
export { ICourseMarketplaceRepository } from '@interfaces/repositories/courseMarketplace.repository.interface';
export { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
export { ICourseVersionCertificateRepository } from '@interfaces/repositories/courseVersionCertificate.repository.interface';
export { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
export { ICustomerPartnerRepository } from '@interfaces/repositories/customerPartner.repository.interface';
export { ICustomerRepository } from '@interfaces/repositories/customer.repository.interface';
export { IDepartmentRepository } from '@interfaces/repositories/department.repository.interface';
export { IEnrollmentAttachmentRepository } from '@interfaces/repositories/enrollmentAttachment.repository.interface';
export { IEnrollmentAchievementRepository } from '@interfaces/repositories/enrollmentAchievement.repository.interface';
export { IEnrollmentBadgeRepository } from '@interfaces/repositories/enrollmentBadge.repository.interface';
export { IEnrollmentCertificateRepository } from '@interfaces/repositories/enrollmentCertificate.repository.interface';
export { IEnrollmentRegulatorReportRepository } from '@interfaces/repositories/enrollmentRegulatorReport.repository.interface';
export { IEnrollmentPlanPackageLicenseRepository } from '@interfaces/repositories/enrollmentPlanPackageLicense.repository.interface';
export { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
export { IIdentificationCardRepository } from '@interfaces/repositories/identificationCard.repository.interface';
export { IInstructorRepository } from '@interfaces/repositories/instructor.repository.interface';
export { IJobRepository } from '@interfaces/repositories/job.repository.interface';
export { IJobTransactionRepository } from '@interfaces/repositories/jobTransaction.repository.interface';
export { IKnowledgeContentItemRepository } from '@interfaces/repositories/knowledgeContentItem.repository.interface';
export { ILearningPathEnrollmentCertificateRepository } from '@interfaces/repositories/learningPathEnrollmentCertificate.repository.interface';
export { ILearningPathEnrollmentRepository } from '@interfaces/repositories/learningPathEnrollment.repository.interface';
export { ILearningPathRepository } from '@interfaces/repositories/learningPath.repository.interface';
export { ILearningPathVersionRepository } from '@interfaces/repositories/learningPathVersion.repository.interface';
export { ILicenseRepository } from '@interfaces/repositories/license.repository.interface';
export { ILogActivityDetectRepository } from '@interfaces/repositories/logActivityDetect.repository.interface';
export { ILogFaceComparisonRepository } from '@interfaces/repositories/logFaceComparison.repository.interface';
export { ILoginProviderRepository } from '@interfaces/repositories/loginProvider.repository.interface';
export { IMediaRepository } from '@interfaces/repositories/media.repository.interface';
export { IOrganizationCertificateRepository } from '@interfaces/repositories/organizationCertificate.repositoty.interface';
export { IOrganizationColumnSettingRepository } from '@interfaces/repositories/organizationColumnSetting.repository.interface';
export { IOrganizationLoginProviderRepository } from '@interfaces/repositories/organizationLoginProvider.repository.interface';
export { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
export { IOrganizationSchedulerRepository } from '@interfaces/repositories/organizationScheduler.repository.interface';
export { IOrganizationStorageRepository } from '@interfaces/repositories/organizationStorage.repository.interface';
export { IPackageRepository } from '@interfaces/repositories/package.repository.interface';
export { IPartRepository } from '@interfaces/repositories/part.repository.interface';
export { IPlanPackageLicenseHistoryRepository } from '@interfaces/repositories/planPackageLicenseHistory.repository.interface';
export { IPlanPackageLicenseRepository } from '@interfaces/repositories/planPackageLicense.repository.interface';
export { IPlanPackageRepository } from '@interfaces/repositories/planPackage.repository.interface';
export { IPlanRepository } from '@interfaces/repositories/plan.repository.interface';
export { IPreEnrollmentReservationRepository } from '@interfaces/repositories/preEnrollmentReservation.repository.interface';
export { IPreEnrollmentTransactionRepository } from '@interfaces/repositories/preEnrollmentTransaction.repository.interface';
export { IProductSKUBundleRepository } from '@interfaces/repositories/productSKUBundle.repository.interface';
export { IProductSKUCourseRepository } from '@interfaces/repositories/productSKUCourse.repository.interface';
export { IProductSKURepository } from '@interfaces/repositories/productSKU.repository.interface';
export { IPromoteNotificationRepository } from '@interfaces/repositories/promoteNotification.repository.interface';
export { IPurchaseOrderRepository } from '@interfaces/repositories/purchaseOrder.repository.interface';
export { IQuizAnswerRepository } from '@interfaces/repositories/quiz.repository.interface';
export { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
export { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
export { ISummaryTSIQuizScoreRepository } from '@interfaces/repositories/summaryTSIQuizScore.repository.interface';
export { IUserGroupRepository } from '@interfaces/repositories/userGroup.repository.interface';
export { IUserLoginRepository } from '@interfaces/repositories/userLogin.repository.interface';
export { IUserNotificationRepository } from '@interfaces/repositories/userNotification.repository.interface';
export { IUserRepository } from '@interfaces/repositories/user.repository.interface';
