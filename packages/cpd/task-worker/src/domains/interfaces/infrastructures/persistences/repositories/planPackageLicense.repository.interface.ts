import { GenericID } from '@iso/constants/commonTypes';
import { PlanPackageLicense } from '@iso/lms/models/planPackageLicense.model';
import { PlanPackageLicenseParams } from '@iso/lms/types/planPackageLicense.type';

import { IBaseRepository } from '@interfaces/repositories/base.repository.interface';

export interface IPlanPackageLicenseRepository extends IBaseRepository<PlanPackageLicense, PlanPackageLicenseParams> {
  findPlanPackageLicenseSOPendingActive(
    userId: GenericID,
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]>;
  findPlanPackageLicenseSOApprovedActive(
    userId: GenericID,
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]>;
  findPlanPackageLicenseMultipleUserSOApprovedActive(
    userIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]>;
  findPlanPackageLicenseMultipleUserSOPendingActive(
    userIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]>;
  findActiveLicenseUserIdsByPlanPackageIds(planPackageIds: GenericID[]): Promise<GenericID[]>;
}
