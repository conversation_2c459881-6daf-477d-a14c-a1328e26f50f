import { GenericID } from '@iso/constants/commonTypes';
import { PreEnrollmentTransactionStatusEnum } from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { PreEnrollmentTransaction } from '@iso/lms/models/preEnrollmentTransaction.model';
import { PreEnrollmentTransactionParams } from '@iso/lms/types/preEnrollmentTransaction.type';

import { OutputFindPreEnrollmentTransactionPayloadOfOicReportParams } from '@constants/types/preEnrollmentTransaction.type';

import { IBaseRepository } from '@interfaces/repositories/base.repository.interface';

export interface IPreEnrollmentTransactionRepository
  extends IBaseRepository<PreEnrollmentTransaction, PreEnrollmentTransactionParams> {
  findPayloadForOicReportByStatusAndRoundIds(
    roundIds: GenericID[],
    status: PreEnrollmentTransactionStatusEnum[],
    organizationId: GenericID,
  ): Promise<OutputFindPreEnrollmentTransactionPayloadOfOicReportParams[]>;
  findPayloadsByIds(ids: GenericID[]): Promise<Pick<PreEnrollmentTransactionParams, 'id' | 'payload'>[]>;
  findDuplicateInPayload(organizationId: GenericID, email: string, citizenId: string);
}
