import { OrganizationParams } from '@iso/lms/types/organization.type';

import { WebhookNotificationChannelEnum } from '@constants/enums/infrastructures/notification.enum';
import { WebHookPayloadParams } from '@constants/types/infrastructures/notification.type';
import {
  MailPayloadAchievementAndBadgeCompleteParams,
  MailPayloadAssignEnrollmentCompleteParams,
  MailPayloadAssignPlanPackageLicenseExistingUserParams,
  MailPayloadEnrollmentApproveParams,
  MailPayloadEnrollmentCompleteAndCertificateParams,
  MailPayloadEnrollmentRejectedParams,
  MailPayloadFirstPasswordParams,
  MailPayloadLearningPathEnrollmentCertificateParams,
  MailPayloadLearningPathEnrollmentCompleteAndCertificateParams,
  MailPayloadLearningPathEnrollmentCompleteParams,
  MailPayloadPlanPackageLicenseExpiredParams,
  MailPayloadPromoteContentParams,
} from '@constants/types/mailer.type';

export interface INotificationService {
  sendInApplicationNotification(payload: object): Promise<void>;
  sendEmailNotification(payload: object): Promise<void>;
  notifyRejected(
    email: string,
    payload: MailPayloadEnrollmentRejectedParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyAssignEnrollmentComplete(
    email: string,
    payload: MailPayloadAssignEnrollmentCompleteParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyApproved(
    email: string,
    payload: MailPayloadEnrollmentApproveParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyEnrollmentCompleteWithCertificate(
    email: string,
    payload: MailPayloadEnrollmentCompleteAndCertificateParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyLearningPathEnrollmentComplete(
    email: string,
    payload: MailPayloadLearningPathEnrollmentCompleteParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyLearningPathEnrollmentCertificate(
    email: string,
    payload: MailPayloadLearningPathEnrollmentCertificateParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyLearningPathEnrollmentCompleteAndCertificate(
    email: string,
    payload: MailPayloadLearningPathEnrollmentCompleteAndCertificateParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyPromoteContent(
    email: string,
    payload: MailPayloadPromoteContentParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyFirstSetPassword(
    email: string,
    payload: MailPayloadFirstPasswordParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyAssignPlanPackageLicenseExistingUser(
    email: string,
    payload: MailPayloadAssignPlanPackageLicenseExistingUserParams,
    organization: OrganizationParams,
  ): Promise<void>;
  notifyPlanPackageLicenseExpired(
    email: string,
    payload: MailPayloadPlanPackageLicenseExpiredParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void>;
  notifyAchievementComplete(
    email: string,
    payload: MailPayloadAchievementAndBadgeCompleteParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void>;
  notifyAchievementAdditionalComplete(
    email: string,
    payload: MailPayloadAchievementAndBadgeCompleteParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void>;
  notifyBadgeAdditionalComplete(
    email: string,
    payload: MailPayloadAchievementAndBadgeCompleteParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void>;
}

export interface IWebHookNotificationService {
  sendNotification(payload: WebHookPayloadParams, channel: WebhookNotificationChannelEnum): Promise<void>;
  sendNotChangeRemoveCourseItemProgressHistory(): Promise<void>;
  sendUpdateLearningPathEnrollmentExpired(total: number): Promise<void>;
  sendUpdateStatusClassroom(headerText: string, total: number, totalError: number): Promise<void>;
  sendEnrollmentExpired(total: number): Promise<void>;
  sendCertificateEmail(total: number, totalSuccess: number): Promise<void>;
  sendNotChangeEnrollmentExpired(): Promise<void>;
  sendNotChangeBulkPreEnrollmentTransactionUpdateRetailOrder(): Promise<void>;
  sendBulkPreEnrollmentTransactionUpdateRetailOrder(total: number, totalSuccess: number): Promise<void>;
  sendOperationExecute(
    domain: string,
    operationType: string,
    total: number,
    totalError: number,
    userId: string,
    urlAction: string,
  ): Promise<void>;
  sendAutoOperationEmptyExecute(domain: string): Promise<void>;
}
