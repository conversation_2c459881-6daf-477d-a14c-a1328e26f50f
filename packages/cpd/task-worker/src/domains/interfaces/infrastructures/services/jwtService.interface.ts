import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Secret, SignOptions } from 'jsonwebtoken';

export interface IJwtService {
  createFirstPasswordToken(params: { guid: GenericID; email: string; organizationId: GenericID }): Promise<string>;
  createJwt(data: Record<string, any>, tokenSecret: Secret, jwtOptions: SignOptions): Promise<string>;
  decodeJwt(token: string | null | undefined): Nullable<Record<string, any>>;
  jwtVerify(token: string, publicKey: string, tokenConfig?: Record<string, any>): Promise<Record<string, any>>;
  base64Decode(dataEncode: string): string;
  decodeBase64File(raw: string): Buffer;
}
