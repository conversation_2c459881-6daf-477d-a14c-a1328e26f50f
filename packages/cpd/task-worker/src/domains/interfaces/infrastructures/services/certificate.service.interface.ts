import { GenericID } from '@iso/constants/commonTypes';
import { DomainMetaDataEnum } from '@iso/lms/enums/certificate.enum';
import {
  BuildCertificatePayloadParams,
  CertificatePropertyParams,
  GenerateCertificateParams,
  GenerateCertificateResult,
} from '@iso/lms/types/certificate.type';
import {
  OrganizationCertificatePropertyParams,
  OrganizationCertificatePropertyWithNameParams,
} from '@iso/lms/types/organizationCertificate.type';

import { MailAttachmentParams } from '@constants/types/infrastructures/notification.type';

export interface ICertificateService {
  create(body: GenerateCertificateParams): Promise<GenerateCertificateResult>;
  fetchThumbnail(slugName: string): Promise<string>;
  pretestGenerateCertificate(body: GenerateCertificateParams): Promise<void>;
  buildCertificatePayload(payload: BuildCertificatePayloadParams): Promise<GenerateCertificateParams>;
  genMetaDataUrl(id: GenericID, domain: DomainMetaDataEnum): string;
  mergeProperties(
    certificateProperties: CertificatePropertyParams[],
    organizationCertificateProperties: OrganizationCertificatePropertyParams[],
  ): OrganizationCertificatePropertyWithNameParams[];
  getCertificateAttachmentBuffers(certificates?: GenerateCertificateResult[]): Promise<MailAttachmentParams[]>;
}

export type GenerateTestCertificateParams = {
  id: GenericID;
  userId: string;
  organizationId: GenericID;
  version?: number;
};

export interface IGenerateTestCertificateService {
  testCourseCertificate(params: GenerateTestCertificateParams): Promise<void>;
}

export interface IValidateCertificateService {
  checkMatchRefCode(refCode: string, organizationId: GenericID, courseId?: GenericID): Promise<boolean>;
  checkMatchType(courseVersionId: GenericID, type: string, id?: GenericID): Promise<boolean>;
}
