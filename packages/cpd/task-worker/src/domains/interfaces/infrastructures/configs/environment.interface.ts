export interface IEnvironments {
  appName: string;
  port: number;

  // Database Env
  dbURI: string;
  dbName: string;

  // Message Broker Env
  amqpURI: string;

  // File Storage
  awsAccessKeyId: string;
  awsSecretAccessKey: string;
  awsRegion: string;
  awsUploadBucketName: string;

  clientProtocol: string;
  certificateEndpoint: string;
  lmsApiEndpoint: string;

  redisURL: string;

  s3BucketUrl: string;

  asURI: string;
  asClientId: string;
  asSecret: string;
  asScope: string;
  advanceTokenRequestTime: number;
  renewTokenSecondsBeforeExpires: number;
  firstPasswordTokenExpires: string;

  tokenAlgorithm: string;
  tokenAudience: string;
  tokenIssuer: string;
  managePasswordTokenPassphrase: string;
  managePasswordTokenPrivatekey: string;
  managePasswordTokenPublickey: string;

  retryQueueMsDuration: number;

  encryptDataAlgorithm: string;
  encryptDataSecretKey: string;
  encryptDataSecretIV: string;

  // Slack
  webhookBotNotificationUrl: string;
  webhookAutoNotificationUrl: string;
}
