import { Channel } from 'amqplib';

import { BulkTransferPlanPackageLicenseParams } from '@constants/types/planPackage.type';

import { IBaseUseCase } from '@interfaces/usecases/base.interface';

export interface IBulkAssignPlanPackageLicenseUseCase extends IBaseUseCase<any, void> {}
export interface IBulkTransferPlanPackageLicenseUseCase
  extends IBaseUseCase<
    { channel: Channel; content: BulkTransferPlanPackageLicenseParams; headers: Record<string, any> },
    void
  > {}
