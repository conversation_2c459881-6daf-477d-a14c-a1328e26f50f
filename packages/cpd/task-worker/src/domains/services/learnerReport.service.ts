import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum } from '@iso/lms/enums/course.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { MaterialMediaTypeEnum } from '@iso/lms/enums/materialMedia.enum';
import { CourseCategoryParams } from '@iso/lms/types/courseCategory.type';
import { CourseItemProgressParams } from '@iso/lms/types/courseItemProgress.type';
import { CourseVersionParams } from '@iso/lms/types/courseVersion.type';
import { LicenseParams } from '@iso/lms/types/license.type';
import { Injectable } from '@nestjs/common';
import { isNumber } from 'lodash';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';

import { CourseObjectiveTypeTH } from '@constants/enums/course.enum';
import { UserLicenseTypeCodeEnum } from '@constants/enums/license.enum';
import { KeyColumnLearnerReportHeaderEnum } from '@constants/enums/report.enum';
import { OutputCertificateUrl, OutputEnrollmentReportTestScoreParams } from '@constants/types/customer.type';
import { LearnerReportRowDataParams, RelationEnrollmentParams } from '@constants/types/reportHistory.type';

import { ILearnerReportService } from '@interfaces/services/report.service.interface';

import { date, DateFormat } from '@domains/utils/date.util';

@Injectable()
export class LearnerReportService implements ILearnerReportService {
  buildLearnerReportRowData(
    relationEnrollment: RelationEnrollmentParams & {
      firstPreTestResultData: OutputEnrollmentReportTestScoreParams;
      postTestResultData: OutputEnrollmentReportTestScoreParams;
      certificateData: OutputCertificateUrl;
      enrollmentStatusTH: string;
      evaluateResultTH: string;
      attachmentDeductStatusTH: string;
      rejectedReason: string;
      organizationMainUrl: string;
    },
  ): Nullable<LearnerReportRowDataParams> {
    const {
      status: enrollmentStatus,
      completedCourseItem,
      startedAt,
      finishedAt,
      requestedApprovalAt,
      expiredAt,
      courseData,
      courseCategoryData,
      roundData,
      userData,
      licenseData,
      courseVersionData,
      firstPreTestResultData,
      postTestResultData,
      certificateData,
      enrollmentStatusTH,
      evaluateResultTH,
      attachmentDeductStatusTH,
      rejectedReason,
      learningProgress,
      organizationMainUrl,
    } = relationEnrollment;

    const {
      userPoint: preTestUserPoint,
      totalPoint: preTestTotalPoint,
      minPassPoint: preTestMinPassPoint,
      PCTUserPoint: preTestPCTUserPoint,
      testResult: preTestResult,
    } = firstPreTestResultData;

    const {
      userPoint: postTestUserPoint,
      totalPoint: postTestTotalPoint,
      minPassPoint: postTestMinPassPoint,
      PCTUserPoint: postTestPCTUserPoint,
      testResult: postTestResult,
    } = postTestResultData;

    const { tsiCertificateUrl, oicNonlifeCertificateUrl, oicLifeCertificateUrl, regularCertificateUrl } =
      certificateData;

    const courseObjectiveType = courseData?.objectiveType as CourseObjectiveTypeEnum;
    const courseObjectiveTypeTH = CourseObjectiveTypeTH[courseObjectiveType];
    const roundDate = roundData?.roundDate
      ? date(roundData.roundDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)
      : '';
    const courseCategoryName = courseCategoryData.map((item: CourseCategoryParams) => item.name).join(', ');
    const isUserActivated = userData?.firstActiveDate ? '1' : '0';
    const totalCourseItems = courseVersionData?.totalCourseItems ?? 0;
    const learningProgressPercentText = this.getLearningProgressPercentText(totalCourseItems, completedCourseItem);
    const totalDuration = this.getTotalDuration(courseVersionData);
    const actualTimeSpent = this.getActualTimeSpent(learningProgress);
    const percentageTimeSpentText = this.getPercentageTimeSpentText(actualTimeSpent, totalDuration);
    const startedDate = this.toBuddhistDateOrEmpty(startedAt);
    const passedDate = this.toBuddhistDateOrEmpty(finishedAt);
    let submittedDate = '';
    if (requestedApprovalAt) {
      submittedDate = this.toBuddhistDateOrEmpty(requestedApprovalAt);
    } else if (
      [
        EnrollmentStatusEnum.PASSED,
        EnrollmentStatusEnum.PENDING_APPROVAL,
        EnrollmentStatusEnum.VERIFIED,
        EnrollmentStatusEnum.APPROVED,
        EnrollmentStatusEnum.REJECTED,
      ].includes(enrollmentStatus) &&
      expiredAt &&
      date(expiredAt).toDate() < date().toDate()
    ) {
      submittedDate = this.toBuddhistDateOrEmpty(expiredAt);
    }

    const data: LearnerReportRowDataParams = {
      courseObjectiveType: courseObjectiveTypeTH,
      roundDate,
      courseCode: courseData?.code,
      courseCategory: courseCategoryName,
      courseName: courseVersionData?.name,
      citizenId: userData?.citizenId,
      email: userData?.email,
      title: userData?.profile?.salute,
      firstName: userData?.profile?.firstname,
      middleName: userData?.profile?.middlename,
      lastName: userData?.profile?.lastname,
      phone: userData?.profile?.mobilePhoneNumber,
      isUserActivated,
      tsiLicenseNoEnroll: this.getLicenseByType(licenseData, UserLicenseTypeCodeEnum.TSI),
      oicLifeLicenseNoEnroll: this.getLicenseByType(licenseData, UserLicenseTypeCodeEnum.OIC_LIFE),
      oicNonlifeLicenseNoEnroll: this.getLicenseByType(licenseData, UserLicenseTypeCodeEnum.OIC_NON_LIFE),
      totalCourseItem: totalCourseItems,
      completedCourseItem,
      learningProgressPercentText,
      preTestTotalPoint,
      preTestUserPoint,
      preTestPctUserPoint: preTestPCTUserPoint,
      preTestResult,
      preTestMinPassScore: preTestMinPassPoint,
      postTestTotalPoint,
      postTestUserPoint,
      postTestPctUserPoint: postTestPCTUserPoint,
      postTestResult,
      postTestMinPassScore: postTestMinPassPoint,
      startedDate,
      passedDate,
      submittedDate,
      learningStatus: enrollmentStatusTH,
      evaluateResult: evaluateResultTH,
      attachmentDeductStatus: attachmentDeductStatusTH,
      rejectedReason,
      tsiCertificateUrl,
      oicLifeCertificateUrl,
      oicNonlifeCertificateUrl,
      regularCertificateUrl,
      courseVersion: courseVersionData?.version,
      courseUpdatedDate: date(courseVersionData?.updatedAt).format(
        DateFormat.buddhistDayMonthYearHourMinuteWithLeadingZero,
      ),
      totalDuration,
      actualTimeSpent,
      percentageTimeSpent: percentageTimeSpentText,
      urlEnrollment: this.getReturnEnrollmentUrlForAdmin(
        organizationMainUrl,
        relationEnrollment.id,
        courseData.objectiveType,
      ),
    };

    return data;
  }

  async buildLearnerReportFile(props: LearnerReportRowDataParams[]): Promise<ArrayBuffer> {
    const headerNamesColumns = [
      KeyColumnLearnerReportHeaderEnum.COURSE_OBJECTIVE_TYPE,
      KeyColumnLearnerReportHeaderEnum.ROUND_DATE,
      KeyColumnLearnerReportHeaderEnum.COURSE_CODE,
      KeyColumnLearnerReportHeaderEnum.COURSE_CATEGORY,
      KeyColumnLearnerReportHeaderEnum.COURSE_NAME,
      KeyColumnLearnerReportHeaderEnum.COURSE_UPDATED_DATE,
      KeyColumnLearnerReportHeaderEnum.COURSE_VERSION,
      KeyColumnLearnerReportHeaderEnum.CITIZEN_ID,
      KeyColumnLearnerReportHeaderEnum.EMAIL,
      KeyColumnLearnerReportHeaderEnum.TITLE,
      KeyColumnLearnerReportHeaderEnum.FIRSTNAME,
      KeyColumnLearnerReportHeaderEnum.MIDDLENAME,
      KeyColumnLearnerReportHeaderEnum.LASTNAME,
      KeyColumnLearnerReportHeaderEnum.PHONE,
      KeyColumnLearnerReportHeaderEnum.IS_USER_ACTIVATED,
      KeyColumnLearnerReportHeaderEnum.TSI_LICENSE_NO_ENROLL,
      KeyColumnLearnerReportHeaderEnum.OIC_LIFE_LICENSE_NO_ENROLL,
      KeyColumnLearnerReportHeaderEnum.OIC_NONLIFE_LICENSE_NO_ENROLL,
      KeyColumnLearnerReportHeaderEnum.TOTAL_COURSE_ITEM,
      KeyColumnLearnerReportHeaderEnum.COMPLETED_COURSE_ITEM,
      KeyColumnLearnerReportHeaderEnum.LEARNING_PROGRESS,
      KeyColumnLearnerReportHeaderEnum.PRE_TEST_TOTAL_POINT,
      KeyColumnLearnerReportHeaderEnum.PRE_TEST_USER_POINT,
      KeyColumnLearnerReportHeaderEnum.PRE_TEST_PCT_USER_POINT,
      KeyColumnLearnerReportHeaderEnum.PRE_TEST_RESULT,
      KeyColumnLearnerReportHeaderEnum.PRE_TEST_MIN_PASS_SCORE,
      KeyColumnLearnerReportHeaderEnum.POST_TEST_TOTAL_POINT,
      KeyColumnLearnerReportHeaderEnum.POST_TEST_USER_POINT,
      KeyColumnLearnerReportHeaderEnum.POST_TEST_PCT_USER_POINT,
      KeyColumnLearnerReportHeaderEnum.POST_TEST_RESULT,
      KeyColumnLearnerReportHeaderEnum.POST_TEST_MIN_PASS_SCORE,
      KeyColumnLearnerReportHeaderEnum.STARTED_DATE,
      KeyColumnLearnerReportHeaderEnum.PASSED_DATE,
      KeyColumnLearnerReportHeaderEnum.SUBMITTED_DATE,
      KeyColumnLearnerReportHeaderEnum.LEARNING_STATUS,
      KeyColumnLearnerReportHeaderEnum.EVALUATE_RESULT,
      KeyColumnLearnerReportHeaderEnum.ATTACHMENT_DEDUCT_STATUS,
      KeyColumnLearnerReportHeaderEnum.REJECTED_REASON,
      KeyColumnLearnerReportHeaderEnum.TSI_CERTIFICATE_URL,
      KeyColumnLearnerReportHeaderEnum.OIC_LIFE_CERTIFICATE_URL,
      KeyColumnLearnerReportHeaderEnum.OIC_NONLIFE_CERTIFICATE_URL,
      KeyColumnLearnerReportHeaderEnum.REGULAR_CERTIFICATE_URL,
      KeyColumnLearnerReportHeaderEnum.TOTAL_DURATION,
      KeyColumnLearnerReportHeaderEnum.ACTUAL_TIME_SPENT,
      KeyColumnLearnerReportHeaderEnum.PERCENTAGE_TIME_SPENT,
      KeyColumnLearnerReportHeaderEnum.URL_ENROLLMENT,
    ].filter(Boolean);

    const rawData = props.map((data) => {
      return [
        data.courseObjectiveType,
        data.roundDate,
        data.courseCode,
        data.courseCategory,
        data.courseName,
        data.courseUpdatedDate,
        data.courseVersion,
        data.citizenId,
        data.email,
        data.title,
        data.firstName,
        data.middleName,
        data.lastName,
        data.phone,
        data.isUserActivated,
        data.tsiLicenseNoEnroll,
        data.oicLifeLicenseNoEnroll,
        data.oicNonlifeLicenseNoEnroll,
        data.totalCourseItem,
        data.completedCourseItem,
        data.learningProgressPercentText,
        data.preTestTotalPoint,
        data.preTestUserPoint,
        data.preTestPctUserPoint,
        data.preTestResult,
        data.preTestMinPassScore,
        data.postTestTotalPoint,
        data.postTestUserPoint,
        data.postTestPctUserPoint,
        data.postTestResult,
        data.postTestMinPassScore,
        data.startedDate,
        data.passedDate,
        data.submittedDate,
        data.learningStatus,
        data.evaluateResult,
        data.attachmentDeductStatus,
        data.rejectedReason,
        data.tsiCertificateUrl,
        data.oicLifeCertificateUrl,
        data.oicNonlifeCertificateUrl,
        data.regularCertificateUrl,
        data.totalDuration,
        data.actualTimeSpent,
        data.percentageTimeSpent,
        data.urlEnrollment,
      ];
    });

    const WORKSHEET_NAME = 'Sheet1';
    const excelBuilderService = new ExcelBuilderService();
    const rowsData = [headerNamesColumns, ...rawData];

    const startRowBorder = 1;
    const startColumnBorder = 1;

    excelBuilderService
      .addWorksheet(WORKSHEET_NAME)
      .addRows(WORKSHEET_NAME, rowsData)
      .setCellOuterBorder(
        WORKSHEET_NAME,
        { row: startRowBorder, col: startColumnBorder },
        { row: startRowBorder + rawData.length, col: headerNamesColumns.length },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowBorder + 1, col: startColumnBorder },
        { row: startRowBorder + rawData.length, col: headerNamesColumns.length },
        { horizontal: 'left' },
      )
      .adjustAutoWidth(WORKSHEET_NAME, { offset: 3 });

    const buffer = await excelBuilderService.getFileBuffer();
    return buffer;
  }

  getLearningProgressPercentText(totalCourseItems: number, completedCourseItem: number): string {
    if (!totalCourseItems) {
      return '0.00%';
    }

    const leaningProgressPercent = ((completedCourseItem || 0) / (totalCourseItems || 0)) * 100;
    return `${leaningProgressPercent.toFixed(2)}%`;
  }

  private getTotalDuration(courseVersion: CourseVersionParams): number {
    const { totalDurationSec, totalDurationArticleSec, isCountdownArticle } = courseVersion;
    let totalDuration = 0;

    if (totalDurationSec > 0) {
      totalDuration = totalDuration + totalDurationSec;
    }

    if (isCountdownArticle && totalDurationArticleSec > 0) {
      totalDuration = totalDuration + totalDurationArticleSec;
    }

    return totalDuration;
  }

  private getActualTimeSpent(learningProgress: CourseItemProgressParams[]) {
    return learningProgress
      .filter(({ type }) => [MaterialMediaTypeEnum.ARTICLE, MaterialMediaTypeEnum.VIDEO].includes(type))
      .reduce((acc, cur) => {
        const { timeSpent } = cur;
        if (!timeSpent) return acc;
        return acc + timeSpent;
      }, 0);
  }

  private getPercentageTimeSpentText(actualTimeSpent: number, totalDuration: number): string {
    if (!totalDuration || !isNumber(totalDuration)) return '0.00%';

    if (actualTimeSpent > totalDuration) {
      actualTimeSpent = totalDuration;
    }

    const percentage = ((actualTimeSpent || 0) / totalDuration) * 100;
    return `${percentage.toFixed(2)}%`;
  }

  private getReturnEnrollmentUrlForAdmin(
    organizationMainUrl: string,
    enrollmentId: GenericID,
    objectiveType: CourseObjectiveTypeEnum,
  ): string {
    switch (objectiveType) {
      case CourseObjectiveTypeEnum.REGULAR: {
        return `${organizationMainUrl}/admin/enrollments/${enrollmentId}?redirectTo=/admin/regularEnrollments?activeTab=learning`;
      }
      case CourseObjectiveTypeEnum.TRAINING: {
        return `${organizationMainUrl}/admin/enrollments/${enrollmentId}?redirectTo=/admin/trainingEnrollments?activeTab=learning`;
      }
      default: {
        return '/admin';
      }
    }
  }

  private getLicenseByType = (licenseData: LicenseParams[], type: UserLicenseTypeCodeEnum) => {
    const license = licenseData?.find((item) => item.licenseTypeCode === type);
    return license ? license.licenseNo : '';
  };

  private toBuddhistDateOrEmpty(currentDate?: Date | null) {
    return currentDate ? date(currentDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero) : '';
  }
}
