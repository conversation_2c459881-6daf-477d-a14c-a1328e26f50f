import { parseExpression } from 'cron-parser';
import dayjs from 'dayjs';
import 'dayjs/locale/th';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import isSameOrAfterPlugin from 'dayjs/plugin/isSameOrAfter';
import isSameOrBeforePlugin from 'dayjs/plugin/isSameOrBefore';
import timezone from 'dayjs/plugin/timezone'; // dependent on utc plugin
import utc from 'dayjs/plugin/utc';

dayjs.extend(buddhistEra);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfterPlugin);
dayjs.extend(isSameOrBeforePlugin);
dayjs.extend(duration);
dayjs.tz.setDefault('UTC');

/**
 * @description format date of target time zone in the Thai locale according to the template passed in
 * @param date The input of date to formatting.
 * @param targetTimeZone The target time zone e.g. UTC, Asia/Bangkok. The default value is UTC
 * @param format The format to formatting the date to. The default value is Buddhist short date(DD MMM BBBB)
 * @returns the formatted date according to the format passed in. e.g. "26 ม.ค. 2557"
 */
export const formatDateInThaiLocale = (
  date: Date,
  targetTimeZone = TimeZoneEnum.UTC,
  format = DateFormat.buddhistShortDate,
): string => dayjs.tz(date).tz(targetTimeZone).locale('th').format(format);

/**
 * @param date The input of date to formatting.
 * @returns string of date formatted. ex "26/1/2557"
 */
export const formatSlashDateTH = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: '2-digit', day: '2-digit' };
  return new Date(date).toLocaleDateString('th-TH', options);
};

export const formatDash = (date: Date): string =>
  `${date.getFullYear()}-${`0${date.getUTCMonth() + 1}`.slice(-2)}-${`0${date.getUTCDate()}`.slice(-2)}`;

export const TimeZoneEnum = {
  Bangkok: 'Asia/Bangkok',
  UTC: 'UTC',
};

export const DateFormat = {
  iso: 'YYYY-MM-DDTHH:mm:ss.SSS[Z]',
  yearMonthDayDash: 'YYYY-MM-DD',
  yearMonthDay: 'YYYYMMDD',
  yearMonth: 'YYYYMM',
  monthDayYearWithLeadingZero: 'MM/DD/YYYY',
  yearMonthDayHourMinuteSecond: 'YYYYMMDD-HHmmss',
  yearMonthDayHourMinuteWithLeadingZero: 'YYYY/MM/DD HH:mm',
  yearMonthDayWithLeadingZero: 'YYYY/MM/DD',
  dayMonthYearHourMinuteWithLeadingZero: 'DD/MM/YYYY HH:mm',
  dayMonthYearWithLeadingZero: 'DD/MM/YYYY',
  buddhistShortDate: 'DD MMM BBBB',
  buddhistDayMonthYearWithLeadingZero: 'DD/MM/BBBB',
  buddhistDayMonthYearHourMinuteWithLeadingZero: 'DD/MM/BBBB HH:mm',
  buddhistDayMonthYearHourMinuteWithLocale: 'DD MMM BBBB HH:mm น.',
  buddhistDayMonthYearHourMinuteWithTimeLocale: 'DD MMM BBBB เวลา HH:mm น.',
  buddhistFullDateWithLocale: 'D เดือน MMMM พ.ศ. BBBB',
  hourMinute: 'HH:mm',
};

/**

 * @param val The input of date text to parsing.
 * @param timeZone The input time zone e.g. UTC, Asia/Bangkok. The default value is UTC
 * @param formatter The format of input date string. default value is YYYY-MM-DDTHH:mm:ss:SSS[Z]
 * @returns the UTC date
 */
export const parseDate = (
  val: Date | string,
  timeZone: string = TimeZoneEnum.UTC,
  formatter: string = DateFormat.iso,
): Date => dayjs.tz(val, formatter, timeZone).toDate();

/**
 * @param val The input of date text
 * @param formatter The format of input date string
 * @returns Return true when the input match with the format otherwise false
 */
export const isValidDateFormat = (val: string, formatter: string): boolean => dayjs(val, formatter, true).isValid();

export const buddhistOffset = 543;
/**
 * @description Convert buddhist date to christian date by subtract 543 years
 * @param date The input date
 * @returns The christian date
 */
export const toChristianDate = (date: Date): Date => dayjs(date).subtract(buddhistOffset, 'year').toDate();

export const getChristianYear = (year: string): number => {
  return parseInt(year) - buddhistOffset;
};

export const strToISO = (date: string): string => dayjs(date).toISOString();

export const date = (
  val?: string | number | dayjs.Dayjs | Date | null | undefined,
  format?: dayjs.OptionType | undefined,
): dayjs.Dayjs => dayjs(val ?? undefined, format).tz('Asia/Bangkok');

export const addDuration = (
  val?: string | number | dayjs.Dayjs | Date | null | undefined,
  format?: dayjs.OptionType | undefined,
): dayjs.Dayjs => dayjs(val ?? undefined, format).tz('Asia/Bangkok');

export const formatDate = (val: Date, format = 'DD/MM/YYYY', locale = 'th'): string => {
  return dayjs.tz(val, TimeZoneEnum.Bangkok).locale(locale).format(format);
};

/**
 * The function converts seconds to milliseconds.
 * @param {number} seconds - The parameter `seconds` is a number representing a duration in seconds.
 * @returns This function takes in a number of seconds and returns the equivalent number of milliseconds.
 */
export const secondsToMilliseconds = (seconds: number): number => {
  return seconds * 1000;
};

/**
 * @param dateStr format "DD/MM/YYYY"
 * @returns boolean
 */
export const validateDayMonthYear = (dateStr: string): boolean => {
  const [day, month, year] = dateStr.split('/');
  if (parseInt(month) > 12 || parseInt(month) < 1) return false;
  if (parseInt(day) > 31 || parseInt(day) < 1) return false;
  if (parseInt(year) < 1000 || parseInt(year) > 9999) return false;

  return true;
};

/**
 * @param dateStr format "HH:mm"
 * @returns boolean
 */
export const validateTime = (timeStr: string): boolean => {
  const [hour, minute] = timeStr.split(':');
  if (parseInt(hour) > 23 || parseInt(hour) < 0) return false;
  if (parseInt(minute) > 59 || parseInt(minute) < 0) return false;
  return true;
};

/**
 * @param dateTimeStr format "DD/MM/YYYY HH:mm" or "DD/MM/YYYY"
 * @returns boolean
 */
export const validateFormatRoundDateTime = (dateTimeStr: string, isValidateTime = true): boolean => {
  const [dateStr, timeStr] = dateTimeStr.split(' ');

  const regexDate = /^\d{2}\/\d{2}\/\d{4}$/;
  const regexTime = /^\d{2}:\d{2}$/;

  if (!isValidateTime) {
    if (timeStr) return false;
    if (!validateDayMonthYear(dateStr)) return false;

    return regexDate.test(dateStr);
  }

  if (!timeStr) return false;
  if (!validateDayMonthYear(dateStr)) return false;
  if (!validateTime(timeStr)) return false;

  return regexDate.test(dateStr) && regexTime.test(timeStr);
};

/**
 * @param dateTimeStr format "DD/MM/YYYY HH:mm" or "DD/MM/YYYY"
 * @returns string
 */
export const convertFormatDateToYearMonthDay = (dateTimeStr: string): string => {
  const [dateStr, timeStr] = dateTimeStr.split(' ');

  let splitSyntax = '-';
  if (dateStr.includes('/')) {
    splitSyntax = '/';
  }
  const [day, month, year] = dateStr.split(splitSyntax);
  const newDate = [year, month, day].join(splitSyntax);

  return timeStr ? `${newDate} ${timeStr}` : newDate;
};

/**
 * The function converts time to seconds.
 * @param {number} timeValue - The time value to be converted.
 * @param {duration.DurationUnitType} unit - The unit of the time value, such as "minutes", "hours", "days", etc.
 * @returns {number} The equivalent time in seconds.
 */

export const convertTimeToSeconds = (timeValue: number, unit?: duration.DurationUnitType): number => {
  const seconds = dayjs.duration(timeValue, unit).asSeconds();
  return seconds;
};

export const formatToShortThaiDate = (
  val: Date,
  targetTimeZone = TimeZoneEnum.UTC,
  format = DateFormat.buddhistShortDate,
): string => dayjs.tz(val).tz(targetTimeZone).locale('th').format(format);

export const getDateLocale = (value: string, dateTime: Date): string => {
  const pattern = /BBBB/;
  const isMatchThaiLocale = pattern.exec(value);
  const result = isMatchThaiLocale ? date(dateTime).locale('th').format(value) : date(dateTime).format(value);
  return result;
};

export const getPublishDateTimeOfNotification = (cron: string): Date => {
  if (!cron) return null;
  const interval = parseExpression(cron, {
    currentDate: date().startOf('day').toDate(),
    endDate: date().endOf('day').toDate(),
    tz: TimeZoneEnum.Bangkok,
    iterator: false,
  });

  const dateTime = interval.next().toISOString();
  return date(dateTime).toDate();
};
