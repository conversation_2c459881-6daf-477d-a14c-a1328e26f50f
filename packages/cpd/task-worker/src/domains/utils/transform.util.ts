import { stringFormat } from '@iso/helpers/utility';

import { date } from '@domains/utils/date.util';
import { isNumeric } from '@domains/utils/validate.util';

export const transformStringToDataType = (value: string) => {
  if (typeof value === 'boolean') {
    return value;
  }

  if (date(value).isValid()) {
    return date(value).toDate();
  }

  if (['true', 'false'].includes(String(value).toLocaleLowerCase())) {
    return !!value;
  }

  if (isNumeric(value)) {
    return parseInt(value, 10);
  }
  return value;
};

export function ternaryGetValueOrDefault<T, E>(isValidCondition: boolean, value: T, defaultValue: E): T | E {
  return isValidCondition ? value : defaultValue;
}

export function getValueOrDefault<T, E>(value: T, defaultValue: E): T | E {
  return value ?? defaultValue;
}

export function getErrorMessage(code: string, text?: string | string[]) {
  if (Array.isArray(text)) {
    return stringFormat(code, ...text);
  } else {
    return stringFormat(code, text);
  }
}
