import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { buildAssignPlanPackageLicenseNotificationMessage } from '@iso/helpers/userNotification';
import { BulkOpTypeEnum, JobStatusEnum } from '@iso/lms/enums/job.enum';
import { JobTransactionStatusCodeEnum, JobTransactionStatusEnum } from '@iso/lms/enums/jobTransaction.enum';
import { LoginEmailTypeEnum } from '@iso/lms/enums/loginProvider.enum';
import { PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import { SaleOrderStatusEnum } from '@iso/lms/enums/plan.enum';
import { PlanPackageHistoryStatusEnum } from '@iso/lms/enums/planPackageLicenseHistory.enum';
import { JobTransaction } from '@iso/lms/models/jobTransaction.model';
import { License } from '@iso/lms/models/license.model';
import { Plan } from '@iso/lms/models/plan.model';
import { PlanPackage } from '@iso/lms/models/planPackage.model';
import { PlanPackageLicense } from '@iso/lms/models/planPackageLicense.model';
import { PlanPackageLicenseHistory } from '@iso/lms/models/planPackageLicenseHistory.model';
import { User } from '@iso/lms/models/user.model';
import { UserLogin } from '@iso/lms/models/userLogin.model';
import { UserNotification } from '@iso/lms/models/userNotification.model';
import { getPlanPackageLicenseTextList } from '@iso/lms/services/planPackageLicense.service';
import {
  BulkPlanPackageLicenseTransactionParams,
  JobTransactionBulkPlanPackageLicensePayloadParams,
} from '@iso/lms/types/jobTransaction.type';
import { LicenseParams } from '@iso/lms/types/license.type';
import { OrganizationParams } from '@iso/lms/types/organization.type';
import { PlanPackageTypeContentParams, PlanPackageTypePlatformParams } from '@iso/lms/types/planPackage.type';
import { PlanPackageLicenseParams } from '@iso/lms/types/planPackageLicense.type';
import { UserParams } from '@iso/lms/types/user.type';
import { Inject, Injectable } from '@nestjs/common';
import { Channel } from 'amqplib';
import { compact, isArray } from 'lodash';
import { ClientSession } from 'mongodb';
import * as rs from 'randomstring';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import {
  JobDIToken,
  LicenseDIToken,
  OrganizationDIToken,
  PlanDIToken,
  PreEnrollmentDIToken,
  UserDIToken,
} from '@applications/di/domain';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { JobErrorMessageEnum } from '@constants/enums/job.enum';
import {
  MailPayloadAssignPlanPackageLicenseExistingUserParams,
  MailPayloadFirstPasswordParams,
} from '@constants/types/mailer.type';
import { BulkAssignPlanPackageLicenseParams } from '@constants/types/planPackage.type';

import { IEnvironments } from '@interfaces/configs/environment.interface';
import {
  IJobRepository,
  IJobTransactionRepository,
  ILicenseRepository,
  ILoginProviderRepository,
  IOrganizationRepository,
  IPlanPackageLicenseHistoryRepository,
  IPlanPackageLicenseRepository,
  IPlanPackageRepository,
  IPlanRepository,
  IPreEnrollmentTransactionRepository,
  IUserLoginRepository,
  IUserNotificationRepository,
  IUserRepository,
} from '@interfaces/repositories';
import { IAESCipherService } from '@interfaces/services/aes.service.interface';
import { IIDSService } from '@interfaces/services/ids.service.interface';
import { IJwtService } from '@interfaces/services/jwtService.interface';
import { INotificationService, IWebHookNotificationService } from '@interfaces/services/notification.service.interface';
import { IDatabaseTransactionService } from '@interfaces/transaction/databaseTransaction.interface';
import { IBulkAssignPlanPackageLicenseUseCase } from '@interfaces/usecases/planPackage.interface';

import { date } from '@domains/utils/date.util';
import { getErrorMessage } from '@domains/utils/transform.util';
import { cleanTrim } from '@domains/utils/cleanTrim.util';

const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

@Injectable()
export class BulkAssignPlanPackageLicenseUseCase implements IBulkAssignPlanPackageLicenseUseCase {
  constructor(
    @Inject(JobDIToken.JobRepository)
    private readonly jobRepository: IJobRepository,
    @Inject(JobDIToken.JobTransactionRepository)
    private readonly jobTransactionRepository: IJobTransactionRepository,
    @Inject(LicenseDIToken.LicenseRepository)
    private readonly licenseRepository: ILicenseRepository,
    @Inject(OrganizationDIToken.LoginProviderRepository)
    private readonly loginProviderRepository: ILoginProviderRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(PlanDIToken.PlanPackageLicenseHistoryRepository)
    private readonly planPackageLicenseHistoryRepository: IPlanPackageLicenseHistoryRepository,
    @Inject(PlanDIToken.PlanPackageLicenseRepository)
    private readonly planPackageLicenseRepository: IPlanPackageLicenseRepository,
    @Inject(PlanDIToken.PlanPackageRepository)
    private readonly planPackageRepository: IPlanPackageRepository,
    @Inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @Inject(PreEnrollmentDIToken.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(UserDIToken.UserNotificationRepository)
    private readonly userNotificationRepository: IUserNotificationRepository,
    @Inject(UserDIToken.UserLoginRepository)
    private readonly userLoginRepository: IUserLoginRepository,
    // Service
    @Inject(InfrastructuresConfigDIToken.Environment)
    private readonly environment: IEnvironments,
    @Inject(InfrastructuresPersistenceDIToken.DatabaseTransaction)
    private readonly databaseTransaction: IDatabaseTransactionService,
    @Inject(InfrastructuresServiceDIToken.IDSService)
    private readonly idsService: IIDSService,
    @Inject(InfrastructuresServiceDIToken.AESCipherService)
    private readonly aesCipherService: IAESCipherService,
    @Inject(InfrastructuresServiceDIToken.JwtService)
    private readonly jwtService: IJwtService,
    @Inject(InfrastructuresServiceDIToken.NotificationService)
    private readonly notificationService: INotificationService,
    @Inject(InfrastructuresServiceDIToken.WebHookNotificationService)
    private readonly webHookNotificationService: IWebHookNotificationService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: {
    channel: Channel;
    content: BulkAssignPlanPackageLicenseParams;
    headers: Record<string, any>;
  }): Promise<void> {
    await delay(300);
    const { channel, content, headers } = params;
    const { jobId } = headers;
    const { payload, originalPayload, raw } = content;
    const { preEnrollmentTransactionId } = payload;

    this.logger.log(`Start processing the job ID ${jobId} of type ${BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE}`);
    let job = await this.jobRepository.findById(jobId);
    const organization = await this.organizationRepository.findById(job?.organizationId);
    const { domain, fqdn } = organization;
    const queueName = `${domain}:v2`;
    const { messageCount } = await channel.assertQueue(queueName);

    try {
      await this.databaseTransaction.runTransaction(async (session) => {
        await this.bulkTransaction(params, organization, session);
      });
    } catch (error) {
      this.logger.error(`| Something wrong, the job ID ${jobId} found error, errorMessage: ${error.message}`);

      const updateJob: any = {
        $push: {
          rawPayloads: [...(isArray(raw) ? raw : []), error.message],
          errorList: {
            originalPayload: {
              ...originalPayload,
              preEnrollmentId: preEnrollmentTransactionId || '',
            },
            message: error.message,
          },
        },
      };

      await this.jobRepository.updateOne(
        {
          guid: jobId,
        },
        updateJob,
      );
    } finally {
      if (preEnrollmentTransactionId) {
        await this.preEnrollmentTransactionRepository.updateOne(
          {
            id: preEnrollmentTransactionId,
          },
          {
            $set: {
              'operationExecute.isAssginPlanPackageLicense': true,
            },
          },
        );
      }
      const isQueueFinished = messageCount === 0;
      if (isQueueFinished) {
        job = await this.jobRepository.findById(jobId);
        const { errorList } = job;
        await this.jobRepository.updateOne(
          {
            guid: jobId,
          },
          {
            $set: {
              status: errorList && errorList.length ? JobStatusEnum.ERROR : JobStatusEnum.COMPLETED,
              totalError: errorList.length,
              updatedAt: date().toDate(),
            },
          },
        );

        const url = `${this.environment.clientProtocol}://${domain}.${fqdn}/admin/setting/jobs/${job.guid}`;
        this.webHookNotificationService.sendOperationExecute(
          domain,
          job.operationType,
          job.total,
          job.errorList.length,
          job.userId,
          url,
        );
      }
      this.logger.log(`End processing the job ID ${jobId} of type ${BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE}`);
    }
  }

  private async bulkTransaction(
    params: {
      channel: Channel;
      content: BulkAssignPlanPackageLicenseParams;
      headers: Record<string, any>;
    },
    organization: OrganizationParams,
    session: ClientSession,
  ) {
    const { content, headers } = params;
    const { jobId } = headers;
    const { payload } = content;
    const { user, preEnrollmentTransactionId, plans, isAutoAssignOnly, isAssginNewAlways } = payload;
    const { id: organizationId } = organization;
    const errorMessageList = [];
    const statusCodeList: BulkPlanPackageLicenseTransactionParams[] = [];

    const { userId, username } = user;
    let userExist = null;
    let welcomeEmail: MailPayloadFirstPasswordParams = null;
    let isNewUser = false;

    if (userId) {
      userExist = await this.userRepository.findOne({ guid: userId, organizationId });
    }

    if (!userExist && username) {
      userExist = await this.userRepository.findOne({ username, organizationId });
    }

    if (organization.featureConfig.isCreateUser && !isAutoAssignOnly && !userExist) {
      // Create user
      const activateUser = await this.processActivateUserBulkTransaction(
        payload,
        errorMessageList,
        statusCodeList,
        organization,
        session,
      );
      userExist = activateUser?.newUser;
      const organizationLoginProviderId = activateUser?.organizationLoginProviderId;

      const localEmailData = this.prepareSendFirstPasswordEmail(userExist, organization);
      const skilllaneSSOEmailData = await this.prepareSendFirstPasswordSkilllaneSSOEmail(
        userExist,
        organization,
        organizationLoginProviderId,
        session,
      );

      welcomeEmail = localEmailData ?? skilllaneSSOEmailData;
      isNewUser = true;
      statusCodeList.push({
        planPackageLicenseId: null,
        statusCode: JobTransactionStatusCodeEnum.SUCCESS_CREATE_USER,
      });
    } else if (!userExist) {
      errorMessageList.push(getErrorMessage(JobErrorMessageEnum.USER_NOT_FOUND));
      statusCodeList.push({
        planPackageLicenseId: null,
        statusCode: JobTransactionStatusCodeEnum.ERROR_USER_NOT_FOUND,
      });
    }

    const userExistId = userExist ? userExist.guid : null;

    let planList: Plan[] = [];
    let updatePlanPackageList: PlanPackage[] = [];
    let updatePlanPackageLicenseList: PlanPackageLicense[] = [];
    let updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[] = [];

    if (userExist) {
      const result = await this.processPlanPackageAssignment({
        userId: userExistId,
        organizationId,
        isAutoAssignOnly,
        isAssginNewAlways,
        plansFromPayload: plans,
        errorMessageList,
        statusCodeList,
      });
      planList = result.planList;
      updatePlanPackageList = result.updatePlanPackageList;
      updatePlanPackageLicenseList = result.updatePlanPackageLicenseList;
      updatePlanPackageLicenseHistorieList = result.updatePlanPackageLicenseHistorieList;
    }

    if (errorMessageList.length > 0) {
      // Create jobTransaction
      const jobTransaction = await this.prepareJobTransactionData({
        jobId,
        preEnrollmentTransactionId,
        userId: userExistId,
        payload: { transactions: statusCodeList },
        status: JobTransactionStatusEnum.ERROR,
        errorMessages: errorMessageList,
      });
      await this.jobTransactionRepository.save(jobTransaction);

      throw Exception.new({
        code: Code.INTERNAL_SERVER_ERROR,
        message: errorMessageList.join(','),
      });
    } else {
      // update planPackage
      if (updatePlanPackageList.length > 0) {
        await this.planPackageRepository.saveMany(updatePlanPackageList, { session });
      }

      // create or update planPackageLicense
      if (updatePlanPackageLicenseList.length > 0) {
        await this.planPackageLicenseRepository.saveMany(updatePlanPackageLicenseList, { session });
      }

      // create planPackageLicenseHistory
      if (updatePlanPackageLicenseHistorieList.length > 0) {
        await this.planPackageLicenseHistoryRepository.saveMany(updatePlanPackageLicenseHistorieList, { session });
      }

      // create jobTransaction
      const jobTransaction = await this.prepareJobTransactionData({
        jobId,
        preEnrollmentTransactionId,
        userId: userExistId,
        payload: { transactions: statusCodeList },
        status: JobTransactionStatusEnum.PASSED,
        errorMessages: errorMessageList,
      });
      await this.jobTransactionRepository.save(jobTransaction, { session });

      const planPackageLicenseTextList = getPlanPackageLicenseTextList(
        planList,
        updatePlanPackageList,
        updatePlanPackageLicenseList,
      );

      const isSendNotify = planPackageLicenseTextList.length > 0;

      if (isNewUser) {
        // send email new user welcome and assgin license
        welcomeEmail.planPackageLicenseTextList = planPackageLicenseTextList;
        await this.notificationService.notifyFirstSetPassword(userExist.email, welcomeEmail, organization);
      } else {
        if (isSendNotify) {
          // send email old user assgin license
          const emailData: MailPayloadAssignPlanPackageLicenseExistingUserParams = {
            fullName: `${userExist.profile.firstname} ${userExist.profile.lastname}`,
            planPackageLicenseTextList,
          };

          await this.notificationService.notifyAssignPlanPackageLicenseExistingUser(
            userExist.email,
            emailData,
            organization,
          );
        }
      }

      // send notification
      if (isSendNotify) {
        const notificationMessage = buildAssignPlanPackageLicenseNotificationMessage(planPackageLicenseTextList);
        const userNotification = await UserNotification.new({
          userId: userExist.guid,
          organizationId: organization.id,
          type: UserNotificationTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE,
          payload: {
            message: notificationMessage,
          },
        });

        await this.userNotificationRepository.save(userNotification, { session });
      }
    }
  }

  private async processPlanPackageAssignment(params: {
    userId: GenericID;
    organizationId: GenericID;
    isAutoAssignOnly: boolean;
    isAssginNewAlways: boolean;
    plansFromPayload: Record<string, any>;
    errorMessageList: string[];
    statusCodeList: BulkPlanPackageLicenseTransactionParams[];
  }): Promise<{
    planList: Plan[];
    updatePlanPackageList: PlanPackage[];
    updatePlanPackageLicenseList: PlanPackageLicense[];
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }> {
    const {
      userId,
      organizationId,
      isAutoAssignOnly,
      isAssginNewAlways,
      plansFromPayload,
      errorMessageList,
      statusCodeList,
    } = params;

    const updatePlanPackageList: PlanPackage[] = [];
    const updatePlanPackageLicenseList: PlanPackageLicense[] = [];
    const updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[] = [];

    const planIds = plansFromPayload.map((val) => val.id);
    const selectPlanPackageIds = plansFromPayload.flatMap((val) => val.planPackages.map((item) => item.id));

    const planList = await this.planRepository.find({
      id: { $in: planIds },
    });

    // planPackageLicense expired Date >= Date Now
    const currentUserPlanPackageLicenseSOPendingActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOPendingActive(
        [userId],
        selectPlanPackageIds,
      );

    // planPackageLicense expired Date > Date Now
    const currentUserPlanPackageLicenseSOApprovedActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOApprovedActive(
        [userId],
        selectPlanPackageIds,
      );

    const currentUserPlanPackageLicenseActive = currentUserPlanPackageLicenseSOPendingActive.concat(
      currentUserPlanPackageLicenseSOApprovedActive,
    );

    const planPackageList = await this.planPackageRepository.find({
      id: { $in: selectPlanPackageIds },
    });

    const planPackageLicenseActiveIds = currentUserPlanPackageLicenseActive.map((val) => val.planPackageId);

    const userNoPlanPackageLicenseActiveIds = selectPlanPackageIds.filter(
      (val) => !planPackageLicenseActiveIds.includes(val),
    );

    if (userNoPlanPackageLicenseActiveIds.length === 0) {
      statusCodeList.push({
        planPackageLicenseId: null,
        statusCode: JobTransactionStatusCodeEnum.SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_NOTHING,
      });
    } else if (isAutoAssignOnly) {
      await this.handleAutoAssignOnly({
        userId,
        organizationId,
        isAssginNewAlways,
        selectPlanPackageIds,
        errorMessageList,
        statusCodeList,
        planList,
        planPackageList,
        updatePlanPackageList,
        updatePlanPackageLicenseList,
        updatePlanPackageLicenseHistorieList,
      });
    } else {
      await this.handleManualAssign({
        userId,
        organizationId,
        isAssginNewAlways,
        errorMessageList,
        statusCodeList,
        selectPlanPackageIds,
        planPackageLicenseActiveIds,
        planList,
        planPackageList,
        updatePlanPackageList,
        updatePlanPackageLicenseList,
        updatePlanPackageLicenseHistorieList,
      });
    }
    return {
      planList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    };
  }

  private async handleAutoAssignOnly(params: {
    userId: GenericID;
    organizationId: GenericID;
    isAssginNewAlways: boolean;
    selectPlanPackageIds: GenericID[];
    errorMessageList: string[];
    statusCodeList: BulkPlanPackageLicenseTransactionParams[];
    planList: Plan[];
    planPackageList: PlanPackage[];
    updatePlanPackageList: PlanPackage[];
    updatePlanPackageLicenseList: PlanPackageLicense[];
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }) {
    const {
      userId,
      organizationId,
      isAssginNewAlways,
      selectPlanPackageIds,
      errorMessageList,
      statusCodeList,
      planList,
      planPackageList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    } = params;
    const currentDate = date().toDate();

    const isRemainAvailable = planPackageList.some(
      (val) => (val.content as PlanPackageTypePlatformParams | PlanPackageTypeContentParams).remainLicense > 0,
    );
    if (!isRemainAvailable) {
      errorMessageList.push(
        getErrorMessage(JobErrorMessageEnum.PLAN_PACKAGE_PLATFORM_NOT_ENOUGH_REMAIN_LICENSE, planPackageList[0].name),
      );
      statusCodeList.push({
        planPackageLicenseId: null,
        statusCode: JobTransactionStatusCodeEnum.ERROR_ASSIGN_PLAN_PACKAGE_LICENSE,
      });
      return;
    }

    const planPackageHaveRemainLicense = this.getPlanPackageLicenseHaveRemainToAssign(planPackageList);

    if (!isAssginNewAlways) {
      await this.autoAssignOldOrNewLicense({
        userId,
        organizationId,
        currentDate,
        selectPlanPackageIds,
        statusCodeList,
        planList,
        planPackageHaveRemainLicense,
        planPackageList,
        updatePlanPackageList,
        updatePlanPackageLicenseList,
        updatePlanPackageLicenseHistorieList,
      });
    }
  }

  private async handleManualAssign(params: {
    userId: GenericID;
    organizationId: GenericID;
    isAssginNewAlways: boolean;
    selectPlanPackageIds: GenericID[];
    planPackageLicenseActiveIds: GenericID[];
    errorMessageList: string[];
    statusCodeList: BulkPlanPackageLicenseTransactionParams[];
    planList: Plan[];
    planPackageList: PlanPackage[];
    updatePlanPackageList: PlanPackage[];
    updatePlanPackageLicenseList: PlanPackageLicense[];
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }) {
    const {
      userId,
      organizationId,
      isAssginNewAlways,
      selectPlanPackageIds,
      planPackageLicenseActiveIds,
      errorMessageList,
      statusCodeList,
      planList,
      planPackageList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    } = params;
    const currentDate = date().toDate();

    // assign ตามที่เลือกมา ใบไหนมีอยู่แล้วไม่ต้อง assign เพิ่ม
    for (const selectPlanPackageId of selectPlanPackageIds) {
      const isPlanPackageLicenseActive = planPackageLicenseActiveIds.includes(selectPlanPackageId);
      if (isPlanPackageLicenseActive) {
        continue;
      }

      const planPackageSelect = planPackageList.find((val) => val.id === selectPlanPackageId);
      const isExpired = this.checkPlanPackageExpired(planPackageSelect, errorMessageList);
      if (!isExpired) {
        continue;
      }

      const totalIgnore = 0;
      const isHaveRemainLicense = this.checkPlanPackageRemainLicense(planPackageSelect, totalIgnore, errorMessageList);
      if (!isHaveRemainLicense) {
        continue;
      }

      if (isAssginNewAlways) {
        await this.manuelAssignNewAlways({
          userId,
          organizationId,
          currentDate,
          planPackageToAssign: planPackageSelect,
          statusCodeList,
          selectPlanPackageIds: [selectPlanPackageId],
          planList,
          errorMessageList,
          updatePlanPackageList,
          updatePlanPackageLicenseList,
          updatePlanPackageLicenseHistorieList,
        });
      } else {
        await this.manuelAssignOldOrNewLicense({
          userId,
          organizationId,
          currentDate,
          selectPlanPackageIds: [selectPlanPackageId],
          statusCodeList,
          planPackageHaveRemainLicense: planPackageSelect,
          planList,
          planPackageList,
          updatePlanPackageList,
          updatePlanPackageLicenseList,
          updatePlanPackageLicenseHistorieList,
        });
      }
    }
  }

  private async assignNewLicense(params: {
    userId: GenericID;
    organizationId: GenericID;
    currentDate: Date;
    statusCodeList: BulkPlanPackageLicenseTransactionParams[];
    planPackageHaveRemainLicense: PlanPackage;
    planList: Plan[];
    updatePlanPackageList: PlanPackage[];
    updatePlanPackageLicenseList: PlanPackageLicense[];
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }) {
    const {
      userId,
      organizationId,
      currentDate,
      statusCodeList,
      planPackageHaveRemainLicense,
      planList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    } = params;

    const plan = planList.find((val) => val.id === planPackageHaveRemainLicense.planId);
    const isSaleOrderStatusApprove = plan.saleOrderStatus === SaleOrderStatusEnum.APPROVED;

    const newPlanPackageLicense = await this.prepareAssignPlanPackageLicenseData({
      userId,
      organizationId,
      currentDate,
      planPackage: planPackageHaveRemainLicense,
      isSaleOrderStatusApprove,
    });
    updatePlanPackageLicenseList.push(newPlanPackageLicense);

    await this.prepareAssignPlanPackageLicenseHistoryData({
      organizationId,
      currentDate,
      isSaleOrderStatusApprove,
      planPackageLicense: newPlanPackageLicense,
      updatePlanPackageLicenseHistorieList,
    });

    (
      planPackageHaveRemainLicense.content as PlanPackageTypePlatformParams | PlanPackageTypeContentParams
    ).remainLicense -= 1;
    updatePlanPackageList.push(planPackageHaveRemainLicense);
    statusCodeList.push({
      planPackageLicenseId: newPlanPackageLicense.id,
      statusCode: JobTransactionStatusCodeEnum.SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_NEW,
    });
  }

  private async autoAssignOldOrNewLicense(params: {
    userId: GenericID;
    organizationId: GenericID;
    currentDate: Date;
    selectPlanPackageIds: GenericID[];
    statusCodeList: BulkPlanPackageLicenseTransactionParams[];
    planPackageHaveRemainLicense: PlanPackage;
    planList: Plan[];
    planPackageList: PlanPackage[];
    updatePlanPackageList: PlanPackage[];
    updatePlanPackageLicenseList: PlanPackageLicense[];
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }) {
    const {
      userId,
      organizationId,
      currentDate,
      selectPlanPackageIds,
      statusCodeList,
      planPackageHaveRemainLicense,
      planList,
      planPackageList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    } = params;

    const userRevokeIds = [null];
    const oldPlanPackageLicenseSOPendingActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOPendingActive(
        userRevokeIds,
        selectPlanPackageIds,
      );
    const oldPlanPackageLicenseSOApprovedActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOApprovedActive(
        userRevokeIds,
        selectPlanPackageIds,
      );
    const oldUserPlanPackageLicenseActive = oldPlanPackageLicenseSOPendingActive.concat(
      oldPlanPackageLicenseSOApprovedActive,
    );

    // oldestLicense ต้องมี remain พอด้วย ถ้าไม่พอจะใช้ของ plan package ตัวต่อไปเลย
    const planPackageLicenseHaveRemainList = this.getPlanPackageLicenseHaveRemainList(planPackageList);
    const planPackageLicenseHaveRemainIds = planPackageLicenseHaveRemainList.map((val) => val.id);
    const oldestLicenseToAssign = this.getOldestLicenseToAutoAssign(
      oldUserPlanPackageLicenseActive,
      planPackageLicenseHaveRemainIds,
    );

    if (!oldestLicenseToAssign) {
      // Re-use assignNewLicense logic
      await this.assignNewLicense({
        userId,
        organizationId,
        currentDate,
        statusCodeList,
        planPackageHaveRemainLicense,
        planList,
        updatePlanPackageList,
        updatePlanPackageLicenseList,
        updatePlanPackageLicenseHistorieList,
      });
    } else {
      const planPackageUsed = planPackageList.find((val) => val.id === oldestLicenseToAssign.planPackageId);
      const plan = planList.find((val) => val.id === planPackageUsed.planId);
      const isSaleOrderStatusApprove = plan.saleOrderStatus === SaleOrderStatusEnum.APPROVED;

      const oldPlanPackageLicense = await this.planPackageLicenseRepository.findOne({ id: oldestLicenseToAssign.id });
      oldPlanPackageLicense.userId = userId;
      updatePlanPackageLicenseList.push(oldPlanPackageLicense);

      await this.prepareAssignPlanPackageLicenseHistoryData({
        organizationId,
        currentDate,
        isSaleOrderStatusApprove,
        planPackageLicense: oldPlanPackageLicense,
        updatePlanPackageLicenseHistorieList,
      });

      (planPackageUsed.content as PlanPackageTypePlatformParams | PlanPackageTypeContentParams).remainLicense -= 1;
      updatePlanPackageList.push(planPackageUsed);
      statusCodeList.push({
        planPackageLicenseId: oldPlanPackageLicense.id,
        statusCode: JobTransactionStatusCodeEnum.SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_OLD,
      });
    }
  }

  private async manuelAssignNewAlways(params: {
    userId: GenericID;
    organizationId: GenericID;
    currentDate: Date;
    planPackageToAssign: PlanPackage;
    selectPlanPackageIds: GenericID[];
    planList: Plan[];
    errorMessageList: string[];
    statusCodeList: BulkPlanPackageLicenseTransactionParams[];
    updatePlanPackageList: PlanPackage[];
    updatePlanPackageLicenseList: PlanPackageLicense[];
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }): Promise<void> {
    const {
      userId,
      organizationId,
      currentDate,
      planPackageToAssign,
      selectPlanPackageIds,
      planList,
      errorMessageList,
      statusCodeList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    } = params;
    // Note: The logic for `totalOldUserPlanPackageLicenseActive` and its subtraction from `remainLicense`
    // might need review. It compares a specific package's `remainLicense` with a global count
    // of unassigned licenses across all requested packages.
    // If `planPackageToAssign.content.remainLicense` is the true count of available new slots for *this specific package*,
    // then a simpler check `if (planPackageToAssign.content.remainLicense > 0)` might be more appropriate.
    // However, this refactoring preserves the original logic.
    const userRevokeIds = [null];
    const oldPlanPackageLicenseSOPendingActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOPendingActive(
        userRevokeIds,
        selectPlanPackageIds,
      );
    const oldPlanPackageLicenseSOApprovedActive =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOApprovedActive(
        userRevokeIds,
        selectPlanPackageIds,
      );
    const oldUserPlanPackageLicenseActive = oldPlanPackageLicenseSOPendingActive.concat(
      oldPlanPackageLicenseSOApprovedActive,
    );
    const totalOldUserPlanPackageLicenseActive = oldUserPlanPackageLicenseActive.length;

    const isHaveRemainLicense = this.checkPlanPackageRemainLicense(
      planPackageToAssign,
      totalOldUserPlanPackageLicenseActive,
      errorMessageList,
    );
    if (!isHaveRemainLicense) {
      return;
    }

    await this.assignNewLicense({
      userId,
      organizationId,
      currentDate,
      statusCodeList,
      planPackageHaveRemainLicense: planPackageToAssign,
      planList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    });
  }

  private async manuelAssignOldOrNewLicense(params: {
    userId: GenericID;
    organizationId: GenericID;
    currentDate: Date;
    selectPlanPackageIds: GenericID[];
    statusCodeList: BulkPlanPackageLicenseTransactionParams[];
    planPackageHaveRemainLicense: PlanPackage;
    planList: Plan[];
    planPackageList: PlanPackage[];
    updatePlanPackageList: PlanPackage[];
    updatePlanPackageLicenseList: PlanPackageLicense[];
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }) {
    const {
      userId,
      organizationId,
      currentDate,
      selectPlanPackageIds,
      statusCodeList,
      planPackageHaveRemainLicense,
      planList,
      updatePlanPackageList,
      updatePlanPackageLicenseList,
      updatePlanPackageLicenseHistorieList,
    } = params;

    const userRevokeIds = [null];
    const oldPlanPackageLicenseSOPending =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOPendingActive(
        userRevokeIds,
        selectPlanPackageIds,
      );
    const oldPlanPackageLicenseSOApproved =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOApprovedActive(
        userRevokeIds,
        selectPlanPackageIds,
      );
    const oldUserPlanPackageLicense = oldPlanPackageLicenseSOPending.concat(oldPlanPackageLicenseSOApproved);

    const oldestLicenseToAssign = this.getOldestLicenseToAutoAssign(oldUserPlanPackageLicense, selectPlanPackageIds);

    if (!oldestLicenseToAssign) {
      // Re-use assignNewLicense logic
      await this.assignNewLicense({
        userId,
        organizationId,
        currentDate,
        statusCodeList,
        planPackageHaveRemainLicense,
        planList,
        updatePlanPackageList,
        updatePlanPackageLicenseList,
        updatePlanPackageLicenseHistorieList,
      });
    } else {
      const plan = planList.find((val) => val.id === planPackageHaveRemainLicense.planId);
      const isSaleOrderStatusApprove = plan.saleOrderStatus === SaleOrderStatusEnum.APPROVED;

      const oldPlanPackageLicense = await this.planPackageLicenseRepository.findOne({
        id: oldestLicenseToAssign.id,
      });
      oldPlanPackageLicense.userId = userId;
      updatePlanPackageLicenseList.push(oldPlanPackageLicense);

      await this.prepareAssignPlanPackageLicenseHistoryData({
        organizationId,
        currentDate,
        isSaleOrderStatusApprove,
        planPackageLicense: oldPlanPackageLicense,
        updatePlanPackageLicenseHistorieList,
      });

      (
        planPackageHaveRemainLicense.content as PlanPackageTypePlatformParams | PlanPackageTypeContentParams
      ).remainLicense -= 1;
      updatePlanPackageList.push(planPackageHaveRemainLicense);
      statusCodeList.push({
        planPackageLicenseId: oldPlanPackageLicense.id,
        statusCode: JobTransactionStatusCodeEnum.SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_OLD,
      });
    }
  }

  private checkPlanPackageRemainLicense(
    planPackage: PlanPackage,
    totalOldUserPlanPackageLicenseActive: number,
    errorMessageList: string[],
  ): boolean {
    const { content, type, name: planPackageName } = planPackage;
    const currentRemainLicense = (content as PlanPackageTypePlatformParams | PlanPackageTypeContentParams)
      .remainLicense;
    const effectiveRemainLicense = currentRemainLicense - totalOldUserPlanPackageLicenseActive;

    console.log('totalOldUserPlanPackageLicenseActive :>> ', totalOldUserPlanPackageLicenseActive);
    if (effectiveRemainLicense <= 0) {
      const errorCode =
        type === PackageTypeEnum.PLATFORM
          ? JobErrorMessageEnum.PLAN_PACKAGE_PLATFORM_NOT_ENOUGH_REMAIN_LICENSE
          : JobErrorMessageEnum.PLAN_PACKAGE_CONTENT_NOT_ENOUGH_REMAIN_LICENSE;
      errorMessageList.push(getErrorMessage(errorCode, planPackageName));
      return false;
    }

    return true;
  }

  private checkPlanPackageExpired(planPackage: PlanPackage, errorMessageList: string[]) {
    const { type, name: planPackageName, endDate } = planPackage;

    if (endDate < date().toDate()) {
      const errorCode =
        type === PackageTypeEnum.PLATFORM
          ? JobErrorMessageEnum.PLAN_PACKAGE_PLATFORM_EXPIRED
          : JobErrorMessageEnum.PLAN_PACKAGE_CONTENT_EXPIRED;
      errorMessageList.push(getErrorMessage(errorCode, planPackageName));
      return false;
    }

    return true;
  }

  private getPlanPackageLicenseHaveRemainList(planPackageList: PlanPackage[]) {
    const planPackageHaveRemainLicense = planPackageList.filter(
      (val) => (val.content as PlanPackageTypePlatformParams | PlanPackageTypeContentParams).remainLicense > 0,
    );

    return planPackageHaveRemainLicense;
  }

  private getPlanPackageLicenseHaveRemainToAssign(planPackageList: PlanPackage[]) {
    const planPackageHaveRemainLicense = this.getPlanPackageLicenseHaveRemainList(planPackageList);

    const filterAndSortPlanPackages = planPackageHaveRemainLicense.slice().sort((a, b) => {
      // Ensure expiredAt and createdAt are valid dates
      const aExpiredAt = date(a.endDate).toDate().getTime();
      const bExpiredAt = date(b.endDate).toDate().getTime();
      const aCreatedAt = date(a.createdAt).toDate().getTime();
      const bCreatedAt = date(b.createdAt).toDate().getTime();

      // 1. Sort by expiredAt (ascending - closer to current date first)
      if (aExpiredAt !== bExpiredAt) {
        return aExpiredAt - bExpiredAt;
      }
      // 2. If expiredAt is the same, sort by createdAt (ascending - older first)
      return aCreatedAt - bCreatedAt;
    });

    return filterAndSortPlanPackages[0];
  }

  private getOldestLicenseToAutoAssign(
    oldLicenseList: PlanPackageLicenseParams[],
    planPackageLicenseHaveRemainIds: GenericID[],
  ): Nullable<PlanPackageLicenseParams> {
    if (oldLicenseList.length === 0) {
      return null;
    }

    const oldLicenseHaveRemainList = oldLicenseList.filter((val) =>
      planPackageLicenseHaveRemainIds.includes(val.planPackageId),
    );

    // Slice sort non-mutating
    const filterAndSortLicenses = oldLicenseHaveRemainList.slice().sort((a, b) => {
      // Ensure expiredAt and createdAt are valid dates
      const aExpiredAt = a.expiredAt ? date(a.expiredAt).toDate().getTime() : Infinity;
      const bExpiredAt = b.expiredAt ? date(b.expiredAt).toDate().getTime() : Infinity;
      const aCreatedAt = date(a.createdAt).toDate().getTime();
      const bCreatedAt = date(b.createdAt).toDate().getTime();

      // 1. Sort by expiredAt (ascending - closer to current date first)
      if (aExpiredAt !== bExpiredAt) {
        return aExpiredAt - bExpiredAt;
      }
      // 2. If expiredAt is the same, sort by createdAt (ascending - older first)
      return aCreatedAt - bCreatedAt;
    });

    return filterAndSortLicenses[0];
  }

  private async prepareAssignPlanPackageLicenseData(params: {
    userId: GenericID;
    organizationId: GenericID;
    currentDate: Date;
    isSaleOrderStatusApprove: boolean;
    planPackage: PlanPackage;
  }) {
    const { userId, currentDate, isSaleOrderStatusApprove, planPackage } = params;
    let startedAt: Nullable<Date> = null;
    let expiredAt: Nullable<Date> = null;
    if (isSaleOrderStatusApprove) {
      const isPlanPackageStarted = date(planPackage.startDate).isSameOrBefore(currentDate);
      const startedLicense = isPlanPackageStarted ? currentDate : planPackage.startDate;
      const calExpiredLicense = date(startedLicense).add(planPackage.totalUsageDay, 'day').endOf('day');
      startedAt = startedLicense;
      expiredAt = calExpiredLicense.isAfter(planPackage.endDate) ? planPackage.endDate : calExpiredLicense.toDate();
    }

    const planPackageLicense = await PlanPackageLicense.new({
      planPackageId: planPackage.id,
      userId,
      startedAt,
      expiredAt,
    });

    return planPackageLicense;
  }

  private async prepareAssignPlanPackageLicenseHistoryData(params: {
    organizationId: GenericID;
    currentDate: Date;
    isSaleOrderStatusApprove: boolean;
    planPackageLicense: PlanPackageLicenseParams;
    updatePlanPackageLicenseHistorieList: PlanPackageLicenseHistory[];
  }): Promise<void> {
    const {
      organizationId,
      currentDate,
      isSaleOrderStatusApprove,
      planPackageLicense,
      updatePlanPackageLicenseHistorieList,
    } = params;
    if (isSaleOrderStatusApprove) {
      const planPackageLicenseHistory = await PlanPackageLicenseHistory.new({
        organizationId,
        planPackageLicenseId: planPackageLicense.id,
        userId: planPackageLicense.userId,
        status: PlanPackageHistoryStatusEnum.ASSIGNED,
        startedAt: currentDate,
        expiredAt: planPackageLicense.expiredAt,
      });
      updatePlanPackageLicenseHistorieList.push(planPackageLicenseHistory);
    }
  }

  private async prepareJobTransactionData(params: {
    jobId: GenericID;
    preEnrollmentTransactionId: Nullable<GenericID>;
    userId: Nullable<GenericID>;
    status: JobTransactionStatusEnum;
    payload: JobTransactionBulkPlanPackageLicensePayloadParams;
    errorMessages: string[];
  }) {
    const { jobId, preEnrollmentTransactionId, userId, status, payload, errorMessages } = params;

    const jobTransaction = await JobTransaction.new({
      jobId,
      preEnrollmentTransactionId,
      userId,
      status,
      payload,
      errorMessages,
    });

    return jobTransaction;
  }

  private async processActivateUserBulkTransaction(
    payload: Record<string, any>,
    errorMessageList: string[],
    statusCodeList: BulkPlanPackageLicenseTransactionParams[],
    organization: OrganizationParams,
    session: ClientSession,
  ): Promise<Nullable<{ newUser: User; organizationLoginProviderId: GenericID }>> {
    const { user, preEnrollmentTransactionId } = payload;
    const { organizationId, email, citizenId, profile, isPassedUlSaleQualify, isTerminated } = user;

    const { salute, firstname, lastname } = profile;

    const userCreationPayload: any = { ...user };

    const { dateOfBirth } = profile;

    let userExist = null;

    const loginSkilllaneProviders = await this.loginProviderRepository.findOrganizationLoginProviderSkilllane(
      organization.id,
    );
    const [loginProvider] = loginSkilllaneProviders;

    const initialValidationRequireMessages = compact([
      this.validateRequireNewUser(email, JobErrorMessageEnum.REQUIRED_EMAIL),
      this.validateRequireNewUser(salute, JobErrorMessageEnum.REQUIRED_SALUTE),
      this.validateRequireNewUser(firstname, JobErrorMessageEnum.REQUIRED_FIRSTNAME),
      this.validateRequireNewUser(lastname, JobErrorMessageEnum.REQUIRED_LASTNAME),
      loginProvider?.organizationLoginProviderId &&
        this.validateRequireNewUser(citizenId, JobErrorMessageEnum.REQUIRED_CITIZEN_ID),
    ]);

    if (initialValidationRequireMessages.length > 0) {
      errorMessageList.push(...initialValidationRequireMessages);
      statusCodeList.push({ planPackageLicenseId: null, statusCode: JobTransactionStatusCodeEnum.ERROR_CREATE_USER });
      return null;
    }

    if (citizenId) {
      userExist = await this.userRepository.findOne({ citizenId, organizationId });
      if (userExist) {
        errorMessageList.push(getErrorMessage(JobErrorMessageEnum.DUPLICATED_CITIZEN_ID));
        statusCodeList.push({ planPackageLicenseId: null, statusCode: JobTransactionStatusCodeEnum.ERROR_CREATE_USER });
        return null;
      }
    }

    if (email) {
      userExist = await this.userRepository.findOne({ email, organizationId });
      if (userExist) {
        errorMessageList.push(getErrorMessage(JobErrorMessageEnum.DUPLICATED_EMAIL));
        statusCodeList.push({ planPackageLicenseId: null, statusCode: JobTransactionStatusCodeEnum.ERROR_CREATE_USER });
        return null;
      }
    }

    const isManualActivateUser = !preEnrollmentTransactionId;
    if (isManualActivateUser) {
      const isPreEnrollmentExists = await this.checkDuplicateInPreEnrollment(
        email,
        citizenId,
        organizationId,
        errorMessageList,
      );
      if (isPreEnrollmentExists) {
        statusCodeList.push({ planPackageLicenseId: null, statusCode: JobTransactionStatusCodeEnum.ERROR_CREATE_USER });
        return null;
      }
    }

    const plainPassword = rs.generate(8);
    userCreationPayload.passwordHash = await this.aesCipherService.hashPassword(plainPassword);
    userCreationPayload.profile.dateOfBirth = dateOfBirth ? date(dateOfBirth).toDate() : null;
    userCreationPayload.isPassedUlSaleQualify = isPassedUlSaleQualify ?? false;
    userCreationPayload.isTerminated = isTerminated ?? false;
    userCreationPayload.createdAt = date(payload.createdAt).toDate();
    userCreationPayload.updatedAt = date(payload.updatedAt).toDate();

    const firstPasswordToken = await this.jwtService.createFirstPasswordToken({
      guid: userCreationPayload.guid,
      email: userCreationPayload.email,
      organizationId: userCreationPayload.organizationId,
    });

    if (organization.isEnableLocalLogin) {
      userCreationPayload.firstPasswordToken = firstPasswordToken;
    }

    const userModel = await User.new(userCreationPayload);

    this.logger.log('Create User');
    await this.userRepository.save(userModel, { session });

    const licenseList = await this.prepareUserLicenses(
      payload.licenses,
      userModel.guid,
      organizationId,
      errorMessageList,
    );
    // if error return null
    if (!licenseList) {
      statusCodeList.push({ planPackageLicenseId: null, statusCode: JobTransactionStatusCodeEnum.ERROR_CREATE_USER });
      return null;
    }

    if (licenseList.length > 0) {
      this.logger.log('Create licenses');
      await this.licenseRepository.saveMany(licenseList, { session });
    }

    return { newUser: userModel, organizationLoginProviderId: loginProvider?.organizationLoginProviderId };
  }

  private validateRequireNewUser(val: string, errorText: JobErrorMessageEnum) {
    if (!val) {
      return getErrorMessage(errorText);
    }

    return '';
  }

  private async checkDuplicateInPreEnrollment(
    email: string,
    citizenId: string,
    organizationId: GenericID,
    errorMessageList: string[],
  ): Promise<boolean> {
    const preEnrollmentList = await this.preEnrollmentTransactionRepository.findDuplicateInPayload(
      organizationId,
      email,
      citizenId,
    );
    const [preEnrollment] = preEnrollmentList;
    if (preEnrollment?.payload?.email === email) {
      errorMessageList.push(getErrorMessage(JobErrorMessageEnum.DUPLICATED_EMAIL_IN_PRE_ENROLLMENT));
      return true;
    }
    if (preEnrollment?.payload?.citizenId === citizenId) {
      errorMessageList.push(getErrorMessage(JobErrorMessageEnum.DUPLICATED_CITIZEN_ID_IN_PRE_ENROLLMENT));
      return true;
    }
    return false;
  }

  private async prepareUserLicenses(
    userLicenses: LicenseParams[],
    userId: GenericID,
    organizationId: GenericID,
    errorMessageList: string[],
  ) {
    if (userLicenses && userLicenses.length > 0) {
      const licenseList = [];
      for (const license of userLicenses) {
        const { licenseTypeCode, licenseNo, type, startedAt, expiredAt } = license;
        const licenseExist = await this.licenseRepository.findOne({ licenseNo, licenseTypeCode, organizationId });
        if (licenseExist) {
          errorMessageList.push(getErrorMessage(JobErrorMessageEnum.DUPLICATED_LICENSE_NO, licenseExist.licenseNo));
          return null; // Stop processing if a duplicate license is found
        }
        const licenseDataModel = await License.new({
          userId,
          licenseTypeCode,
          licenseNo,
          type,
          organizationId,
          startedAt: startedAt ? date(startedAt).toDate() : null,
          expiredAt: expiredAt ? date(expiredAt).toDate() : null,
        });

        if (licenseNo) {
          licenseList.push(licenseDataModel);
        }
      }

      return licenseList;
    }

    return [];
  }

  private prepareSendFirstPasswordEmail(
    user: UserParams,
    organization: OrganizationParams,
  ): MailPayloadFirstPasswordParams {
    if (!organization.isEnableLocalLogin) {
      return null;
    }

    if (!user) {
      return null;
    }

    this.logger.log('prepare send FirstPasswordEmail');
    const { username, firstPasswordToken } = user;
    const urlEmail = `${this.environment.clientProtocol}://${organization.domain}.${organization.fqdn}/firstPassword?token=${firstPasswordToken}`;
    const emailData: MailPayloadFirstPasswordParams = {
      username,
      fullName: cleanTrim(`${user.profile.firstname} ${user.profile.lastname}`),
      url: urlEmail,
      loginType: LoginEmailTypeEnum.LOCAL,
    };

    return emailData;
  }

  private async prepareSendFirstPasswordSkilllaneSSOEmail(
    user: UserParams,
    organization: OrganizationParams,
    organizationLoginProviderId: GenericID,
    session: ClientSession,
  ) {
    const isEnableSkilllaneSSOLogin = organizationLoginProviderId;
    if (!isEnableSkilllaneSSOLogin) {
      return null;
    }

    if (!user) {
      return null;
    }

    const userLogin = {
      organizationLoginProviderId,
      loginProviderKey: null,
      userId: user.guid,
    };

    const userLoginModel = await UserLogin.new(userLogin);

    await this.userLoginRepository.save(userLoginModel, {
      session,
    });

    // ถ้าเปิด SSOLogin แต่ไม่เปิด LocalLogin ต้องส่งเมล์ SSO
    if (!organization.isEnableLocalLogin) {
      const idsProfile = {
        Email: user.email,
        CitizenId: this.aesCipherService.encrypt(user.citizenId),
        Salute: user.profile.salute,
        FirstName: user.profile.firstname,
        LastName: user.profile.lastname,
        Phone: user.profile.mobilePhoneNumber,
        Guid: user.guid.toString(),
      };

      const createUserUrl = this.idsService.generateCreateUserUrl(
        organizationLoginProviderId,
        `${organization.domain}.${organization.fqdn}`,
        idsProfile,
      );

      this.logger.log('prepare send FirstPasswordSkilllaneSSOEmail');
      const emailData: MailPayloadFirstPasswordParams = {
        username: 'เลขประจำตัวประชาชนของคุณ',
        fullName: cleanTrim(`${user.profile.firstname} ${user.profile.lastname}`),
        url: createUserUrl,
        loginType: LoginEmailTypeEnum.SSO,
      };

      return emailData;
    }

    return null;
  }
}
