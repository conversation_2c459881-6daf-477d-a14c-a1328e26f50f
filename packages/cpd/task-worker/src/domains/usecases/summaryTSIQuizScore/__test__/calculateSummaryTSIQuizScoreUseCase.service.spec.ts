import { ContentProviderTypeEnum } from '@iso/lms/enums/course.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { Test, TestingModule } from '@nestjs/testing';
import { CalculateCumulativeTSIQuizScoreUseCase } from '@usecases/summaryTSIQuizScore/calculateSummaryTSIQuizScore.usecase';

import { SummaryTSIQuizScoreDataMapper } from '@infrastructures/dataMappers';
import { MockLoggerModule } from '@infrastructures/services/logger/__mocks__/mockLoggerInstance.spec';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { MaterialMediaDIToken } from '@applications/di/domain/materialMedia.di';
import { ProductSKUDIToken } from '@applications/di/domain/productSKU.di';

import { CalculateSummaryTSIQuizScoreParams } from '@constants/types/summaryTSIQuizScore.type';

import { ICalculateCumulativeTSIQuizScoreUseCase } from '@interfaces/usecases/summaryTSIQuizScore.interface';

import { SummaryTSIQuizScoreService } from '@domains/services/summaryTSIQuizScore.service';
import { date } from '@domains/utils/date.util';

describe('Test Case: CalculateCumulativeTSIQuizScore Use Case', () => {
  let calculateCumulativeTSIQuizScoreUseCase: ICalculateCumulativeTSIQuizScoreUseCase;

  const mockDatabase = {
    summaryTSIQuizScores: [],
    courses: [
      {
        id: 'mock-course-id',
        contentType: 'E_LEARNING',
        contentProviderType: 'SELF',
        objectiveType: 'TSI',
        deletedAt: null,
      },
      {
        id: 'mock-retail-course-id',
        contentType: 'E_LEARNING',
        contentProviderType: 'SKILLLANE',
        objectiveType: 'TSI',
        deletedAt: null,
      },
    ],
    quizzes: [
      {
        id: 'mock-quiz-id',
        materialMediaId: 'mock-material-media-id',
      },
    ],
  };

  beforeEach(async () => {
    (global as any).mockDatabase = JSON.parse(JSON.stringify(mockDatabase));

    const module: TestingModule = await Test.createTestingModule({
      imports: [MockLoggerModule],
      providers: [
        {
          provide: EnrollmentDIToken.EnrollmentRepository,
          useValue: {
            aggregate: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: EnrollmentDIToken.SummaryTSIQuizScoreRepository,
          useValue: {
            aggregate: jest.fn().mockReturnValue([]),
            save: jest.fn().mockImplementation((newDocument) => {
              const dataMapper = new SummaryTSIQuizScoreDataMapper();
              mockDatabase.summaryTSIQuizScores.push(dataMapper.toDAL(newDocument));
            }),
          },
        },
        {
          provide: EnrollmentDIToken.QuizAnswerRepository,
          useValue: {
            aggregate: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: MaterialMediaDIToken.QuizRepository,
          useValue: {
            findOne: jest.fn().mockImplementation(({ materialMediaId }) => {
              return mockDatabase.quizzes.find((item) => item.materialMediaId === materialMediaId);
            }),
            aggregate: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: EnrollmentDIToken.SummaryTSIQuizScoreService,
          useClass: SummaryTSIQuizScoreService,
        },
        {
          provide: CourseDIToken.CourseRepository,
          useValue: {
            findOne: jest.fn().mockImplementation(({ id }) => {
              return mockDatabase.courses.find((item) => item.id === id);
            }),
            aggregate: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: CourseDIToken.CourseVersionRepository,
          useValue: {
            aggregate: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: CourseDIToken.CourseItemCriteriaConfigRepository,
          useValue: {
            find: jest.fn().mockReturnValue([]),
            aggregate: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: ProductSKUDIToken.ProductSKUCourseRepository,
          useValue: {
            aggregate: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: EnrollmentDIToken.CalculateCumulativeTSIQuizScoreUseCase,
          useClass: CalculateCumulativeTSIQuizScoreUseCase,
        },
      ],
    }).compile();

    calculateCumulativeTSIQuizScoreUseCase = module.get<ICalculateCumulativeTSIQuizScoreUseCase>(
      EnrollmentDIToken.CalculateCumulativeTSIQuizScoreUseCase,
    );
  });

  describe('Positive Case', () => {
    test.each([
      {
        testName: 'Course SELF: no have enrollments',
        input: {
          params: {
            key: 'tsiCode:courseId:materialMediaId:itemId',
            courseId: 'mock-course-id',
            tsiCode: 'mock-tsi-code',
            materialMediaId: 'mock-material-media-id',
            itemId: null,
            count: 0,
            min: 0,
            max: 0,
            avg: 0,
            std: 0,
            sumSquare: 0,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams,
        },
        mockResolvedFunctionValues: {
          findEnrollments: [],
        },
        get expectedResult() {
          return this.input.params;
        },
      },
      {
        testName: 'Course SELF: have enrollments but not match any groupCourseTSICodeQuiz',
        input: {
          params: {
            key: 'tsiCode:courseId:materialMediaId:itemId',
            courseId: 'mock-course-id',
            tsiCode: 'mock-tsi-code',
            materialMediaId: 'mock-material-media-id',
            itemId: null,
            count: 0,
            min: 0,
            max: 0,
            avg: 0,
            std: 0,
            sumSquare: 0,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams,
        },
        mockResolvedFunctionValues: {
          findEnrollments: [
            {
              id: 'mock-enrollment-id',
              courseVersionId: 'mock-courseVersion-id',
              courseId: 'mock-course-id',
              status: EnrollmentStatusEnum.PASSED,
            },
          ],
          findPartCourseItemWithCriteriaConfig: [
            {
              id: 'mock-courseVersion-id',
              courseId: 'mock-course-id',
              tsiCode: 'mock-tsi-code',
              parts: [
                {
                  id: 'part-1',
                  courseVersionId: 'mock-courseVersion-id',
                  courseItems: [
                    {
                      id: 'course-item-1',
                      partId: 'part-1',
                      materialMediaId: 'material-media-1',
                      type: 'quiz',
                      isEnabled: true,
                      courseItemCriteriaConfig: {
                        id: 'mock-courseItemCriteriaConfig-id',
                        courseVersionId: 'mock-courseVersion-id',
                        courseItemId: 'mock-courseItem-id',
                        courseItemType: 'quiz',
                        isEnabled: true,
                        config: {
                          passScore: 1,
                        },
                      },
                    },
                  ],
                },
                {
                  id: 'part-1-2',
                  courseVersionId: 'mock-courseVersion-id',
                  courseItems: [
                    {
                      id: 'course-item-1-2',
                      partId: 'part-1-2',
                      materialMediaId: 'material-media-2',
                      type: 'quiz',
                      isEnabled: true,
                    },
                  ],
                },
                {
                  id: 'part-3',
                  courseVersionId: 'mock-courseVersion-id',
                  courseItems: [
                    {
                      id: 'course-item-3-1',
                      partId: 'part-3',
                      materialMediaId: 'material-media-3',
                      type: 'quiz',
                      isEnabled: false,
                    },
                  ],
                },
              ],
              contentProviderType: ContentProviderTypeEnum.SELF,
            },
          ],
        },
        get expectedResult() {
          return this.input.params;
        },
      },
      {
        testName: 'Course SELF: have enrollments and match groupCourseTSICodeQuiz',
        input: {
          params: {
            key: 'tsiCode:courseId:materialMediaId:itemId',
            courseId: 'mock-course-id',
            tsiCode: 'mock-tsi-code',
            materialMediaId: 'mock-material-media-id',
            itemId: null,
            count: 0,
            min: 0,
            max: 0,
            avg: 0,
            std: 0,
            sumSquare: 0,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams,
        },
        mockResolvedFunctionValues: {
          findEnrollments: [
            {
              id: 'mock-enrollment-id',
              courseVersionId: 'mock-courseVersion-id',
              courseId: 'mock-course-id',
              status: EnrollmentStatusEnum.PASSED,
            },
          ],
          findPartCourseItemWithCriteriaConfig: [
            {
              id: 'mock-courseVersion-id',
              courseId: 'mock-course-id',
              tsiCode: 'mock-tsi-code',
              contentProviderType: ContentProviderTypeEnum.SELF,
              parts: [
                {
                  id: 'part-1',
                  courseVersionId: 'mock-courseVersion-id',
                  courseItems: [
                    {
                      id: 'course-item-1',
                      partId: 'part-1',
                      materialMediaId: 'mock-material-media-id',
                      type: 'quiz',
                      isEnabled: true,
                      courseItemCriteriaConfig: {
                        id: 'mock-courseItemCriteriaConfig-id',
                        courseVersionId: 'mock-courseVersion-id',
                        courseItemId: 'mock-courseItem-id',
                        courseItemType: 'quiz',
                        isEnabled: true,
                        config: {
                          passScore: 1,
                        },
                      },
                    },
                  ],
                },
                {
                  id: 'part-1-2',
                  courseVersionId: 'mock-courseVersion-id',
                  courseItems: [
                    {
                      id: 'course-item-1-2',
                      partId: 'part-1-2',
                      materialMediaId: 'material-media-2',
                      type: 'quiz',
                      isEnabled: true,
                    },
                  ],
                },
                {
                  id: 'part-3',
                  courseVersionId: 'mock-courseVersion-id',
                  courseItems: [
                    {
                      id: 'course-item-3-1',
                      partId: 'part-3',
                      materialMediaId: 'material-media-3',
                      type: 'quiz',
                      isEnabled: false,
                    },
                  ],
                },
              ],
            },
          ],
          findLatestQuizAnswers: [
            {
              quizAnswerId: 'mock-quizAnswer-id1',
              enrollmentId: 'mock-enrollment-id1',
              quizId: 'mock-quiz-id',
              userPoint: 2,
              totalPoint: 10,
            },
            {
              quizAnswerId: 'mock-quizAnswer-id2',
              enrollmentId: 'mock-enrollment-id2',
              quizId: 'mock-quiz-id',
              userPoint: 5,
              totalPoint: 10,
            },
          ],
        },
        get expectedResult() {
          return {
            courseId: 'mock-course-id',
            tsiCode: 'mock-tsi-code',
            materialMediaId: 'mock-material-media-id',
            itemId: null,
            count: 2,
            min: 2,
            max: 5,
            avg: 3.5,
            std: 2.1213,
            sumSquare: 28.9999,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams;
        },
      },
      {
        testName: 'Course SkillLane: have enrollments and match groupCourseTSICodeQuiz',
        input: {
          params: {
            key: 'tsiCode:courseId:materialMediaId:itemId',
            courseId: 'mock-retail-course-id',
            tsiCode: 'mock-tsi-code',
            materialMediaId: null,
            itemId: 1111111,
            count: 0,
            min: 0,
            max: 0,
            avg: 0,
            std: 0,
            sumSquare: 0,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams,
        },
        mockResolvedFunctionValues: {
          findEnrollments: [
            {
              id: 'mock-enrollment-id1',
              courseVersionId: 'mock-courseVersion-id',
              courseId: 'mock-retail-course-id',
              status: EnrollmentStatusEnum.VERIFIED,
            },
            {
              id: 'mock-enrollment-id2',
              courseVersionId: 'mock-courseVersion-id',
              courseId: 'mock-retail-course-id',
              status: EnrollmentStatusEnum.APPROVED,
            },
          ],
          findPartCourseItemWithCriteriaConfig: [
            {
              id: 'mock-courseVersion-id',
              courseId: 'mock-retail-course-id',
              tsiCode: 'mock-tsi-code',
              contentProviderType: ContentProviderTypeEnum.EXTERNAL,
              parts: [
                {
                  id: 'part-1',
                  courseItems: [
                    {
                      id: 'mock-quiz-id',
                      itemId: 1111111,
                      type: 'quiz',
                      courseItemCriteriaConfig: {
                        id: 'mock-courseItemCriteriaConfig-id',
                        courseVersionId: 'mock-courseVersion-id',
                        courseItemId: 1111111,
                        courseItemType: 'quiz',
                        isEnabled: true,
                        config: {
                          passScore: 5,
                        },
                      },
                    },
                  ],
                },
                {
                  id: 'part-1-2',
                  courseItems: [
                    {
                      id: 'course-item-1-2',
                      itemId: 9999990,
                      type: 'quiz',
                    },
                  ],
                },
                {
                  id: 'part-3',
                  courseItems: [
                    {
                      id: 'course-item-3-1',
                      itemId: 9999991,
                      type: 'quiz',
                    },
                  ],
                },
              ],
            },
          ],
          findLatestQuizAnswers: [
            {
              quizAnswerId: 'mock-quizAnswer-id1',
              enrollmentId: 'mock-enrollment-id1',
              quizId: 'mock-quiz-id',
              userPoint: 2,
              totalPoint: 10,
            },
            {
              quizAnswerId: 'mock-quizAnswer-id2',
              enrollmentId: 'mock-enrollment-id2',
              quizId: 'mock-quiz-id',
              userPoint: 5,
              totalPoint: 10,
            },
          ],
          findOneQuizIdFromProductSKUCourse: 'mock-quiz-id',
        },
        get expectedResult() {
          return {
            courseId: 'mock-retail-course-id',
            tsiCode: 'mock-tsi-code',
            materialMediaId: null,
            itemId: 1111111,
            count: 2,
            min: 2,
            max: 5,
            avg: 3.5,
            std: 2.1213,
            sumSquare: 28.9999,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams;
        },
      },
    ])('#$# when $testName', async ({ input, mockResolvedFunctionValues, expectedResult }) => {
      const { params } = input;
      jest
        .spyOn(calculateCumulativeTSIQuizScoreUseCase, 'findEnrollments' as any)
        .mockResolvedValue(mockResolvedFunctionValues.findEnrollments);

      jest
        .spyOn(calculateCumulativeTSIQuizScoreUseCase, 'findPartCourseItemWithCriteriaConfig' as any)
        .mockResolvedValue(mockResolvedFunctionValues.findPartCourseItemWithCriteriaConfig);

      jest
        .spyOn(calculateCumulativeTSIQuizScoreUseCase, 'findOneQuizIdFromProductSKUCourse' as any)
        .mockResolvedValue(mockResolvedFunctionValues.findOneQuizIdFromProductSKUCourse);

      jest
        .spyOn(calculateCumulativeTSIQuizScoreUseCase, 'findLatestQuizAnswers' as any)
        .mockResolvedValue(mockResolvedFunctionValues.findLatestQuizAnswers);

      await calculateCumulativeTSIQuizScoreUseCase.execute(params);

      const savedDocument = mockDatabase.summaryTSIQuizScores[mockDatabase.summaryTSIQuizScores.length - 1];

      expect(savedDocument).toEqual({
        id: expect.any(String),
        courseId: expectedResult.courseId,
        tsiCode: expectedResult.tsiCode,
        materialMediaId: expectedResult.materialMediaId,
        itemId: expectedResult.itemId,
        count: expectedResult.count,
        min: expectedResult.min,
        max: expectedResult.max,
        avg: expectedResult.avg,
        std: expectedResult.std,
        sumSquare: expectedResult.sumSquare,
        periodCutOffAt: expect.any(Date),
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        deletedAt: null,
      });
    });
  });

  describe('Negative Case', () => {
    test.each([
      {
        testName: 'course not found',
        input: {
          params: {
            key: 'tsiCode:courseId:materialMediaId:itemId',
            courseId: null,
            tsiCode: 'mock-tsi-code',
            materialMediaId: 'mock-material-media-id',
            itemId: null,
            count: 0,
            min: 0,
            max: 0,
            avg: 0,
            std: 0,
            sumSquare: 0,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams,
        },
        mockResolvedAggregates: {
          enrollments: [],
        },
      },
      {
        testName: 'have enrollment but data was wrong',
        input: {
          params: {
            key: 'tsiCode:courseId:materialMediaId:itemId',
            courseId: 'mock-course-id',
            tsiCode: 'mock-tsi-code',
            materialMediaId: 'mock-material-media-id',
            itemId: null,
            count: 0,
            min: 0,
            max: 0,
            avg: 0,
            std: 0,
            sumSquare: 0,
            periodCutOffAt: date('2025-01-23T16:59:59.999Z').toDate(),
          } as CalculateSummaryTSIQuizScoreParams,
        },
        mockResolvedFunctionValues: {
          findEnrollments: [{ id: 'mock-enrollment-id', courseVersionId: 'mock-courseVersion-id' }],
        },
      },
    ])('#$# when $testName', async ({ input, mockResolvedFunctionValues }) => {
      expect(async () => {
        const { params } = input;
        jest
          .spyOn(calculateCumulativeTSIQuizScoreUseCase, 'findEnrollments' as any)
          .mockResolvedValue(mockResolvedFunctionValues.findEnrollments);

        await calculateCumulativeTSIQuizScoreUseCase.execute(params);
      }).rejects.toThrow();
    });
  });
});
