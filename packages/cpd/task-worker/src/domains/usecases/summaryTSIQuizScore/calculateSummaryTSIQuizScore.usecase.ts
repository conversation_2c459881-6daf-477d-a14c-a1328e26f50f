import { GenericID } from '@iso/constants/commonTypes';
import { ContentProviderTypeEnum } from '@iso/lms/enums/course.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { SummaryTSIQuizScore } from '@iso/lms/models/summaryTSIQuizScore.model';
import { EnrollmentParams } from '@iso/lms/types/enrollment.type';
import { SummaryTSIQuizScoreParams } from '@iso/lms/types/summaryTSIQuizScore.type';
import { Inject, Injectable } from '@nestjs/common';
import { uniqBy } from 'lodash';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { MaterialMediaDIToken } from '@applications/di/domain/materialMedia.di';
import { ProductSKUDIToken } from '@applications/di/domain/productSKU.di';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { CourseTSICodePartCourseItemParams } from '@constants/types/courseVersion.type';
import { QuizAnswerEnrollmentParams } from '@constants/types/enrollment.type';
import {
  CalculateCumulativeCourseTSICodeQuizScoresParams,
  CalculateSummaryTSIQuizScoreParams,
} from '@constants/types/summaryTSIQuizScore.type';

import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseItemCriteriaConfigRepository } from '@interfaces/repositories/courseItemCriteriaConfig.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IProductSKUCourseRepository } from '@interfaces/repositories/productSKUCourse.repository.interface';
import { IQuizAnswerRepository, IQuizRepository } from '@interfaces/repositories/quiz.repository.interface';
import { ISummaryTSIQuizScoreRepository } from '@interfaces/repositories/summaryTSIQuizScore.repository.interface';
import { ISummaryTSIQuizScoreService } from '@interfaces/services/summaryTSIQuizScore.service.interface';
import { ICalculateCumulativeTSIQuizScoreUseCase } from '@interfaces/usecases/summaryTSIQuizScore.interface';

import { date } from '@domains/utils/date.util';

@Injectable()
export class CalculateCumulativeTSIQuizScoreUseCase implements ICalculateCumulativeTSIQuizScoreUseCase {
  constructor(
    @Inject(EnrollmentDIToken.EnrollmentRepository) private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(EnrollmentDIToken.SummaryTSIQuizScoreRepository)
    private readonly summaryTSIQuizScoreRepository: ISummaryTSIQuizScoreRepository,
    @Inject(EnrollmentDIToken.QuizAnswerRepository)
    private readonly quizAnswerRepository: IQuizAnswerRepository,
    @Inject(EnrollmentDIToken.SummaryTSIQuizScoreService)
    private readonly summaryTSIQuizScoreService: ISummaryTSIQuizScoreService,
    @Inject(MaterialMediaDIToken.QuizRepository)
    private readonly quizRepository: IQuizRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(CourseDIToken.CourseItemCriteriaConfigRepository)
    private readonly courseItemCriteriaConfigRepository: ICourseItemCriteriaConfigRepository,
    @Inject(ProductSKUDIToken.ProductSKUCourseRepository)
    private readonly productSKUCourseRepository: IProductSKUCourseRepository,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}
  async execute(params: CalculateSummaryTSIQuizScoreParams): Promise<void> {
    const { key, tsiCode, courseId, materialMediaId, itemId, ...latestCumulativeScore } = params;

    const passedAtStart = latestCumulativeScore.periodCutOffAt;
    const passedAtEnd = date().subtract(1, 'day').endOf('day').toDate();

    try {
      const course = await this.courseRepository.findOne({ id: courseId });
      if (!course) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'course not found',
          data: params,
        });
      }

      const enrollments = await this.findEnrollments(courseId, passedAtStart, passedAtEnd);
      if (!enrollments.length) {
        await this.saveOldValue({
          tsiCode,
          courseId,
          materialMediaId,
          itemId,
          count: latestCumulativeScore.count,
          max: latestCumulativeScore.max,
          min: latestCumulativeScore.min,
          avg: latestCumulativeScore.avg,
          std: latestCumulativeScore.std,
          sumSquare: latestCumulativeScore.sumSquare,
          periodCutOffAt: passedAtEnd,
        });

        return;
      }

      const enrollmentCourseVersionIds = uniqBy(enrollments, 'courseVersionId').map(
        (enrollment) => enrollment.courseVersionId,
      );

      const partCourseItemWithCriteriaConfigList = await this.findPartCourseItemWithCriteriaConfig(
        courseId,
        enrollmentCourseVersionIds,
        tsiCode,
        course.contentProviderType,
      );

      const courseTSICodeLastQuizList = this.summaryTSIQuizScoreService.getCourseTSICodeLastQuizList(
        partCourseItemWithCriteriaConfigList,
      );

      const groupCourseTSICodeQuiz = courseTSICodeLastQuizList.find(
        (item) =>
          item.courseId === courseId &&
          item.tsiCode === tsiCode &&
          item.materialMediaId === materialMediaId &&
          item.itemId === itemId,
      );

      if (!groupCourseTSICodeQuiz) {
        await this.saveOldValue({
          tsiCode,
          courseId,
          materialMediaId,
          itemId,
          count: latestCumulativeScore.count,
          max: latestCumulativeScore.max,
          min: latestCumulativeScore.min,
          avg: latestCumulativeScore.avg,
          std: latestCumulativeScore.std,
          sumSquare: latestCumulativeScore.sumSquare,
          periodCutOffAt: passedAtEnd,
        });

        return;
      }

      const targetCourseVersionIds = groupCourseTSICodeQuiz.courseVersionIds;
      const targetEnrollments = enrollments.filter((item) => targetCourseVersionIds.includes(item.courseVersionId));

      if (!targetEnrollments.length) {
        await this.saveOldValue({
          tsiCode,
          courseId,
          materialMediaId,
          itemId,
          count: latestCumulativeScore.count,
          max: latestCumulativeScore.max,
          min: latestCumulativeScore.min,
          avg: latestCumulativeScore.avg,
          std: latestCumulativeScore.std,
          sumSquare: latestCumulativeScore.sumSquare,
          periodCutOffAt: passedAtEnd,
        });

        return;
      }

      let quizId;
      if (materialMediaId) {
        quizId = await this.findOneQuizIdByMaterialMedia(materialMediaId);
      } else if (itemId) {
        quizId = await this.findOneQuizIdFromProductSKUCourse(itemId);
      }

      if (!quizId) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'quiz not found',
          data: params,
        });
      }

      const targetEnrollmentIds = targetEnrollments.map((item) => item.id);
      const quizAnswerEnrollments = await this.findLatestQuizAnswers(quizId, targetEnrollmentIds);

      if (quizAnswerEnrollments.length) {
        const prepareCumulativeScores: CalculateCumulativeCourseTSICodeQuizScoresParams = {
          count: latestCumulativeScore.count,
          max: latestCumulativeScore.max,
          min: latestCumulativeScore.min,
          avg: latestCumulativeScore.avg,
          std: latestCumulativeScore.std,
          sumSquare: latestCumulativeScore.sumSquare,
        };

        const newCumulativeScore = this.summaryTSIQuizScoreService.calculateCumulativeCourseTSICodeQuizScores(
          prepareCumulativeScores,
          quizAnswerEnrollments,
        );

        await this.saveNewValue({
          tsiCode,
          courseId,
          materialMediaId,
          itemId,
          count: newCumulativeScore.count,
          max: newCumulativeScore.max,
          min: newCumulativeScore.min,
          avg: newCumulativeScore.avg,
          std: newCumulativeScore.std,
          sumSquare: newCumulativeScore.sumSquare,
          periodCutOffAt: passedAtEnd,
        });
      } else {
        await this.saveOldValue({
          tsiCode,
          courseId,
          materialMediaId,
          itemId,
          count: latestCumulativeScore.count,
          max: latestCumulativeScore.max,
          min: latestCumulativeScore.min,
          avg: latestCumulativeScore.avg,
          std: latestCumulativeScore.std,
          sumSquare: latestCumulativeScore.sumSquare,
          periodCutOffAt: passedAtEnd,
        });

        return;
      }
    } catch (error) {
      const errorMessage = `Failed key: ${key}`;
      this.logger.error(errorMessage);
      throw error;
    }
  }

  async findOneQuizIdFromProductSKUCourse(itemId: GenericID) {
    const [result] = await this.productSKUCourseRepository.aggregate([
      {
        $match: {
          'parts.courseItems.itemId': itemId,
        },
      },
      {
        $unwind: '$parts',
      },
      {
        $unwind: '$parts.courseItems',
      },
      {
        $match: {
          'parts.courseItems.itemId': itemId,
        },
      },
      {
        $project: {
          _id: 0,
          quizId: '$parts.courseItems.id',
        },
      },
    ]);

    return result.quizId || null;
  }

  async findOneQuizIdByMaterialMedia(materialMediaId: GenericID) {
    const quiz = await this.quizRepository.findOne({ materialMediaId });
    return quiz.id || null;
  }

  async findEnrollments(courseId: GenericID, passedAtStart: Date, passedAtEnd: Date) {
    return this.enrollmentRepository.aggregate<EnrollmentParams>([
      {
        $match: {
          status: {
            $in: [
              EnrollmentStatusEnum.PASSED,
              EnrollmentStatusEnum.PENDING_APPROVAL,
              EnrollmentStatusEnum.VERIFIED,
              EnrollmentStatusEnum.APPROVED,
              EnrollmentStatusEnum.REJECTED,
            ],
          },
          passedAt: {
            $gt: passedAtStart,
            $lte: passedAtEnd,
          },
          courseId,
        },
      },
    ]);
  }

  async findLatestQuizAnswers(quizId: GenericID, enrollmentIds: GenericID[]) {
    const pipeline = [
      {
        $match: {
          quizId,
          enrollmentId: {
            $in: enrollmentIds,
          },
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $group: {
          _id: {
            enrollmentId: '$enrollmentId',
          },
          quizAnswer: {
            $first: '$$ROOT',
          },
        },
      },
      {
        $replaceRoot: {
          newRoot: '$quizAnswer',
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          enrollmentId: 1,
          quizId: 1,
          userPoint: 1,
          totalPoint: 1,
        },
      },
    ];

    return this.quizAnswerRepository.aggregate<QuizAnswerEnrollmentParams>(pipeline);
  }

  async findPartCourseItemWithCriteriaConfig(courseId, courseVersionIds, tsiCode, contentProviderType) {
    let coursePartCourseItemList: CourseTSICodePartCourseItemParams[];

    const courseItemCriteriaConfigs = await this.courseItemCriteriaConfigRepository.find({
      courseVersionId: { $in: courseVersionIds },
    });

    if (contentProviderType === ContentProviderTypeEnum.SELF) {
      coursePartCourseItemList = await this.courseVersionRepository.findSelfCourseTSIQuiz({ courseId, tsiCode });
    }

    if (contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      coursePartCourseItemList = await this.courseVersionRepository.findSkillLaneCourseTSIQuiz({
        courseId,
        tsiCode,
      });
    }

    return coursePartCourseItemList.map((courseVersion) =>
      this.summaryTSIQuizScoreService.mergePartCourseItemWithCriteriaConfig(courseVersion, courseItemCriteriaConfigs),
    );
  }

  async saveValue(params: SummaryTSIQuizScoreParams) {
    const { tsiCode, courseId, materialMediaId, itemId, count, max, min, avg, std, sumSquare, periodCutOffAt } = params;

    const newSummaryTSIQuizScore = await SummaryTSIQuizScore.new({
      tsiCode,
      courseId,
      materialMediaId,
      itemId,
      count,
      max,
      min,
      avg,
      std,
      sumSquare,
      periodCutOffAt,
    });

    await this.summaryTSIQuizScoreRepository.save(newSummaryTSIQuizScore);
  }

  async saveOldValue(params: SummaryTSIQuizScoreParams) {
    await this.saveValue(params);
  }

  async saveNewValue(params: SummaryTSIQuizScoreParams) {
    await this.saveValue(params);
  }
}
