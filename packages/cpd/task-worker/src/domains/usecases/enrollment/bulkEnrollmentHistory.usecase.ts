import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { ClassroomLocationEnrollmentStatusEnum } from '@iso/lms/enums/classroomLocationEnrollment.enum';
import { CourseEnrollTypeEnum, CourseObjectiveTypeEnum } from '@iso/lms/enums/course.enum';
import { CourseItemStatusCodeEnum, CourseItemStatusTypeEnum } from '@iso/lms/enums/courseItemProgress.enum';
import { BusinessTypeEnum, EnrollmentStatusEnum, EnrollTypeEnum } from '@iso/lms/enums/enrollment.enum';
import { JobStatusEnum } from '@iso/lms/enums/job.enum';
import { JobTransactionStatusEnum } from '@iso/lms/enums/jobTransaction.enum';
import { MaterialMediaTypeEnum } from '@iso/lms/enums/materialMedia.enum';
import { VerifyEnrollmentActionEnum, VerifyEnrollmentStatusEnum } from '@iso/lms/enums/verifyEnrollment.enum';
import { ClassroomLocationEnrollment } from '@iso/lms/models/classroomLocationEnrollment.model';
import { Enrollment } from '@iso/lms/models/enrollment.model';
import { JobTransaction } from '@iso/lms/models/jobTransaction.model';
import { QuizAnswer } from '@iso/lms/models/quizAnswer.model';
import { Round } from '@iso/lms/models/round.model';
import { VerifyEnrollment } from '@iso/lms/models/verifyEnrollment.model';
import { ClassroomLocationParams } from '@iso/lms/types/classroomLocation.type';
import { ClassroomLocationEnrollmentParams } from '@iso/lms/types/classroomLocationEnrollment.type';
import { CourseItemParams, CourseItemParamsWithType } from '@iso/lms/types/courseItem.type';
import { CourseItemProgressParams } from '@iso/lms/types/courseItemProgress.type';
import { QuestionParams } from '@iso/lms/types/quiz.type';
import { QuizAnswerProgressParams } from '@iso/lms/types/quizAnswer.type';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Channel } from 'amqplib';
import _ from 'lodash';
import { ClientSession } from 'mongodb';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { JobDIToken } from '@applications/di/domain/job.di';
import { MaterialMediaDIToken } from '@applications/di/domain/materialMedia.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { UserDIToken } from '@applications/di/domain/user.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { ClassroomLocationEnrollmentStatusTextEnum } from '@constants/enums/classroomLocationEnrollment.enum';
import { GetCourseQuizConfigParams } from '@constants/types/courseItemCriteriaConfig.type';
import { BulkEnrollmentHistoryParams } from '@constants/types/enrollment.type';

import { IClassroomLocationRepository } from '@interfaces/repositories/classroomLocation.repository.interface';
import { IClassroomLocationEnrollmentRepository } from '@interfaces/repositories/classroomLocationEnrollment.repository.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IJobRepository } from '@interfaces/repositories/job.repository.interface';
import { IJobTransactionRepository } from '@interfaces/repositories/jobTransaction.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IPartRepository } from '@interfaces/repositories/part.repository.interface';
import { IQuizAnswerRepository } from '@interfaces/repositories/quiz.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { IVerifyEnrollmentRepository } from '@interfaces/repositories/verifyEnrollment.repository.interface';
import { IDatabaseTransactionService } from '@interfaces/transaction/databaseTransaction.interface';
import { IBulkEnrollmentHistoryUseCase } from '@interfaces/usecases/enrollment.interface';

import { date, DateFormat, parseDate, TimeZoneEnum } from '@domains/utils/date.util';

const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

@Injectable()
export class BulkEnrollmentHistoryUseCase implements IBulkEnrollmentHistoryUseCase {
  constructor(
    @Inject(InfrastructuresPersistenceDIToken.DatabaseTransaction)
    private readonly databaseTransaction: IDatabaseTransactionService,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(EnrollmentDIToken.VerifyEnrollmentRepository)
    private readonly verifyEnrollmentRepository: IVerifyEnrollmentRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(RoundDIToken.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(EnrollmentDIToken.QuizAnswerRepository)
    private readonly quizAnswerRepository: IQuizAnswerRepository,
    @Inject(MaterialMediaDIToken.ClassroomLocationRepository)
    private readonly classroomLocationRepository: IClassroomLocationRepository,
    @Inject(MaterialMediaDIToken.ClassroomLocationEnrollmentRepository)
    private readonly classroomLocationEnrollmentRepository: IClassroomLocationEnrollmentRepository,
    @Inject(CourseDIToken.PartRepository)
    private readonly partRepository: IPartRepository,
    @Inject(JobDIToken.JobRepository)
    private readonly jobRepository: IJobRepository,
    @Inject(JobDIToken.JobTransactionRepository)
    private readonly jobTransactionRepository: IJobTransactionRepository,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: {
    channel: Channel;
    payload: {
      params: BulkEnrollmentHistoryParams;
      organizationId: GenericID;
      createdByUserId: GenericID;
      jobId: GenericID;
      raw: string[];
    };
  }): Promise<void> {
    const { channel } = params;
    const { organizationId, jobId } = params.payload;

    await delay(300);

    this.logger.log(`Start processing the job ID ${jobId} of type ENROLLMENT_HISTORY`);
    const organization = await this.organizationRepository.findOne({ id: organizationId });
    const { domain } = organization;
    const queueName = `${domain}:enrollment_history`;

    await this.databaseTransaction.runTransaction(async (session, commit, reverse) => {
      try {
        await this.bulkEnrollmentHistoryTransaction(params.payload, session);
        await commit();
      } catch (error) {
        this.logger.error(`| Something wrong, the job ID ${jobId} found error, errorMessage: ${error.message}`);
        await reverse();
      } finally {
        const { messageCount } = await channel.checkQueue(queueName);
        const isQueueFinished = messageCount === 0;
        if (isQueueFinished) {
          const job = await this.jobRepository.findOne({
            guid: jobId,
          });
          const { errorList } = job;
          await this.jobRepository.updateOne(
            {
              guid: jobId,
            },
            {
              $set: {
                status: errorList && errorList.length ? JobStatusEnum.ERROR : JobStatusEnum.COMPLETED,
                totalError: errorList.length,
              },
            },
          );
        }
      }
    });
  }

  async bulkEnrollmentHistoryTransaction(
    payload: {
      params: BulkEnrollmentHistoryParams;
      organizationId: GenericID;
      createdByUserId: GenericID;
      jobId: GenericID;
      raw: string[];
    },
    session: ClientSession,
  ) {
    const {
      username,
      courseCode,
      status,
      startedDate,
      endedDate,
      registerType,
      approvedDate,
      approvedBy,
      reason,
      resultQuizScore,
      classLocationCode,
      classroomStartedDate,
      classroomStatus,
      resultClassroomAttend,
      resultClassroomHomework,
      note,
    } = payload.params;

    const { organizationId, jobId, raw } = payload;
    const errorMessageList: string[] = [];
    let enrollType: Nullable<EnrollTypeEnum> = null;

    const user = await this.userRepository.findOne({ username, organizationId });
    if (!user) {
      errorMessageList.push('ไม่พบบัญชีผู้ใช้งานบนระบบ');
    }

    const approveUser = await this.userRepository.findOne({
      username: approvedBy,
      organizationId,
    });

    if (!approveUser && approvedBy) {
      errorMessageList.push('ไม่พบบัญชีผู้ใช้งานของผู้อนุมัติบนระบบ');
    }

    const approvedByUserId: GenericID = approveUser ? approveUser.guid : '';

    const courseList = await this.courseRepository.getCourseListWithVersionByCode(courseCode, organizationId);

    const roundDateYearMonthDayFormat = this.convertFormatDateToYearMonthDay(startedDate);
    const roundDateToChrist = this.convertFormatDateBuddistToChrist(roundDateYearMonthDayFormat);

    const endDateYearMonthDayFormat = this.convertFormatDateToYearMonthDay(endedDate);
    const endDateToChrist = this.convertFormatDateBuddistToChrist(endDateYearMonthDayFormat);

    const course =
      courseList.length === 1
        ? courseList[0]
        : courseList.find(
            (item, index) =>
              !date(date(item.courseVersion.publishedAt).startOf('day').toDate()).isAfter(
                date(roundDateToChrist).startOf('day').toDate(),
              ) || courseList.length === index + 1,
          );

    if (!course) {
      errorMessageList.push('ไม่พบหลักสูตรนี้ในระบบ หรือ ไม่เผยแพร่');
    }
    if (endedDate && !this.isDateFormatValid(endedDate)) {
      errorMessageList.push('วันที่จบการเรียนไม่ถูกต้อง');
    }

    if (startedDate && !this.isDateFormatValid(startedDate)) {
      errorMessageList.push('วันที่เริ่มเรียนไม่ถูกต้อง');
    }

    const startDate = date(roundDateToChrist).startOf('day').toDate();
    const endDate = date(endDateToChrist).endOf('day').toDate();
    const today = date().startOf('day').toDate();

    if (date(endDate).isBefore(startDate)) {
      errorMessageList.push('วันที่จบต้องเท่ากับหรือมากกว่าวันเริ่มเรียน');
    }

    if (date(startDate).isAfter(today)) {
      errorMessageList.push('ไม่สามารถสร้างประวัติการเรียนที่วันเริ่มเรียนในอนาคต');
    }

    if (approvedDate) {
      const approvedDateYearMonthDayFormat = this.convertFormatDateToYearMonthDay(approvedDate);
      const approvedDateToChrist = this.convertFormatDateBuddistToChrist(approvedDateYearMonthDayFormat);
      const approveDate = date(approvedDateToChrist).startOf('day').toDate();
      if (date(approveDate).isAfter(today)) {
        errorMessageList.push('ไม่สามารถสร้างประวัติการเรียนที่วันอนุมัติการเรียนในอนาคต');
      }
    }

    if (registerType) {
      enrollType = this.convertRegisterTypeToEnrollType(registerType);
      if (!enrollType) {
        errorMessageList.push('ประเภทการลงทะเบียนไม่ถูกต้อง กรุณาระบุประเภทการเรียน Compulsory หรือ Voluntary');
      }
    }

    if (note.trim().length > 500) {
      errorMessageList.push('ไม่สามารถใส่ note ได้เกิน 500 ตัวอักษร');
    }

    let courseItems: CourseItemParams[] = [];
    let quizConfigList: GetCourseQuizConfigParams[] = [];
    let classroomConfigCriterias = [];
    let numberQuizResultScores: number[] = [];
    let numberHomeworkScores: number[] = [];
    let numberAttendanceScores: number[] = [];
    let classroomStatusArray = [];
    let classLocationCodeArray = [];
    let sortedClassroomConfigCriterias = [];
    if (course) {
      const expireDate = date(roundDateToChrist).add(course.courseVersion?.expiryDay, 'day').endOf('day').toDate();
      // startDate + course.expiryDay ต้องไม่เกิน endDate
      if (course.courseVersion?.expiryDay && date(endDate).isAfter(expireDate)) {
        errorMessageList.push('วันที่เริ่มเรียนจบเกินวันหมดอายุ');
      }
      const isStatusColumnValid =
        this.validateStatusByObjective(course.objectiveType, status) &&
        Object.values(EnrollmentStatusEnum).includes(status);

      if (!isStatusColumnValid) {
        errorMessageList.push('สถานะไม่ถูกต้อง');
      }

      if (status === EnrollmentStatusEnum.REJECTED && !reason) {
        errorMessageList.push('จำเป็นต้องใส่เหตุผล กรณีที่กรอกสถานะหลักสูตรเป็น Rejected');
      }

      const parts = await this.partRepository.getPartByCourseId(course.courseVersion?.id);
      courseItems = parts.flatMap((item) => item.courseItems);

      const courseItemQuizzes = courseItems?.filter(
        (courseItem: CourseItemParamsWithType) =>
          courseItem.type === MaterialMediaTypeEnum.QUIZ && courseItem.isEnabled,
      );

      // Check result score length match to course item quiz length
      const resultQuizScoreArray = resultQuizScore ? resultQuizScore.split(',') : [];

      const resultQuizScoreLength = resultQuizScoreArray.length || 0;

      // Check result score should not greater than max score
      const [courseItemCriteria] = await this.courseVersionRepository.getCourseItemCriteriaConfigList(
        course.courseVersion?.id,
      );

      quizConfigList = courseItemCriteria?.quizzes || [];

      const validateQuizScoreResult = this.validateQuizScore(resultQuizScoreArray);
      const { isValidQuizScoreFormat } = validateQuizScoreResult;
      ({ numberQuizResultScores } = validateQuizScoreResult);

      const { isPassQuizCriteria, isQuizResultOverMaxScore } = this.validateQuizCriteria(
        quizConfigList,
        numberQuizResultScores,
      );

      const courseItemClassrooms = courseItems?.filter(
        (courseItem: CourseItemParamsWithType) =>
          courseItem.type === MaterialMediaTypeEnum.CLASSROOM && courseItem.isEnabled,
      );

      classLocationCodeArray = this.covertStringToArray(classLocationCode);

      const classroomLocations =
        await this.classroomLocationRepository.getClassroomLocationWithRound(classLocationCodeArray);

      classroomConfigCriterias = courseItemCriteria?.classrooms;

      // sorting classroom location by input location code
      const sortedClassroomLocations = _.sortBy(classroomLocations, (classroomLocation) =>
        _.indexOf(classLocationCodeArray, classroomLocation.code),
      );

      // sorting classroom criterias by input location code
      sortedClassroomConfigCriterias = _.sortBy(classroomConfigCriterias, (criteria) => {
        return _.findIndex(
          sortedClassroomLocations,
          (classroomLocation) => classroomLocation.classroomRound.materialMediaId === criteria.materialMediaId,
        );
      }).filter((index) => index !== -1);

      // input classroom location code ต้องเท่ากับ classroom ใน course ที่เปิด
      const isClassLocationCodeLengthValid = classLocationCodeArray.length === courseItemClassrooms.length;
      // input classroom location code ต้องเจอในระบบทั่้งหมด
      const isAllClassLocationCodeFound = classLocationCodeArray.length === classroomLocations.length;

      // input classroom location code ต้องอยู่ใน course นั้นจริงๆ
      let isAllClassroomLocationExistedInCourse = true;
      for (const classroomLocation of classroomLocations) {
        const { classroomRound } = classroomLocation;
        if (isAllClassroomLocationExistedInCourse) {
          isAllClassroomLocationExistedInCourse = classroomRound.courseIds.includes(course.id);
        }
      }

      // ห้ามลง classroom เดียวกัน มากกว่า 1 รอบ (materaialMedia id ต้องไม่ซ้ำกัน)
      const registerSameClassroom = _.chain(classroomLocations)
        .groupBy((item) => item.classroomRound.materialMediaId)
        .filter((group) => group.length > 1)
        .map((group) => group[0].classroomRound.materialMediaId)
        .value();

      const isRegisterSameClassroom = registerSameClassroom.length > 0;
      const classroomStartDateArray = this.covertStringToArray(classroomStartedDate);
      const isClassroomStartDateLengthValid = classroomStartDateArray.length === courseItemClassrooms.length;

      // classroom location code กับ รอบต้องตรงกัน
      const { isAllClassroomLocationHasRound, isAllClassroomDateValidFormat, isClassroomRoundOverStartDate } =
        this.validateClassroomDate(sortedClassroomLocations, classroomStartDateArray, roundDateToChrist);

      classroomStatusArray = this.covertStringToArray(classroomStatus);
      const classroomAttendanceScoreArray = this.covertStringToArray(resultClassroomAttend);
      const classroomHomeWorkScoreArray = this.covertStringToArray(resultClassroomHomework);

      let isClassroomHomeworkOverMaxScore = false;
      let isClassroomAttendanceOverMaxScore = false;
      let isClassroomStatusRelatedToScore = true;
      let isPassAllClassroomCriteria = true;

      let isValidClassroomHomeWorkScoreFormat = true;
      let isValidClassroomAttendanceScoreFormat = true;

      ({ numberHomeworkScores, isValidClassroomHomeWorkScoreFormat } =
        this.validateNumberHomeworkScores(classroomHomeWorkScoreArray));

      ({ numberAttendanceScores, isValidClassroomAttendanceScoreFormat } =
        this.validateClassroomAttendanceScores(classroomAttendanceScoreArray));

      for (const [index, classroomConfigCriteria] of sortedClassroomConfigCriterias.entries()) {
        const homeworkScoreValue = numberHomeworkScores[index];
        const attendanceScoreValue = numberAttendanceScores[index];
        const classroomStatusValue = classroomStatusArray[index];

        ({
          isClassroomHomeworkOverMaxScore,
          isClassroomAttendanceOverMaxScore,
          isClassroomStatusRelatedToScore,
          isPassAllClassroomCriteria,
        } = this.checkClassroomPassCriteria(
          homeworkScoreValue,
          attendanceScoreValue,
          classroomStatusValue,
          classroomConfigCriteria,
        ));
      }

      const isResultQuizScoreLengthValid = resultQuizScoreLength !== courseItemQuizzes.length;

      const isClassLocationCodeColumnValid =
        isClassLocationCodeLengthValid &&
        isAllClassLocationCodeFound &&
        isAllClassroomLocationExistedInCourse &&
        !isRegisterSameClassroom;

      const isApprovable =
        status === EnrollmentStatusEnum.APPROVED || status === EnrollmentStatusEnum.COMPLETED
          ? isPassQuizCriteria
          : true;

      const isApprovableClassroom =
        status === EnrollmentStatusEnum.APPROVED || status === EnrollmentStatusEnum.COMPLETED
          ? isPassAllClassroomCriteria
          : true;
      // input classroom status ต้องเท่ากับ classroom ใน course ที่เปิด
      const isClassroomStatusLengthValid = classroomStatusArray.length === courseItemClassrooms.length;

      // input classroom attendance ต้องเท่ากับ classroom ใน course ที่เปิด
      const isClassroomAttendanceScoreLengthValid =
        classroomAttendanceScoreArray.length === courseItemClassrooms.length;

      // input classroom homework ต้องเท่ากับ classroom ใน course ที่เปิด
      const isClassroomHomeworkScoreLengthValid = classroomHomeWorkScoreArray.length === courseItemClassrooms.length;

      let isClassroomStatusFormatValid = true;
      for (const classroomStatusValue of classroomStatusArray) {
        if (isClassroomStatusFormatValid) {
          isClassroomStatusFormatValid = Object.values(ClassroomLocationEnrollmentStatusTextEnum).includes(
            classroomStatusValue,
          );
        }
      }

      const isClassroomStatusColumnValid =
        isClassroomStatusLengthValid && isClassroomStatusRelatedToScore && isClassroomStatusFormatValid;

      const isClassroomStartedDateColumnValid =
        isClassroomStartDateLengthValid && isAllClassroomLocationHasRound && isAllClassroomDateValidFormat;

      const errorMessageQuizAndClassroomList = this.getErrorMessageListQuizAndClassroom({
        isValidQuizScoreFormat,
        isResultQuizScoreLengthValid,
        isQuizResultOverMaxScore,
        isApprovable,
        isApprovableClassroom,
        isClassLocationCodeColumnValid,
        isClassroomStatusColumnValid,
        isClassroomStartedDateColumnValid,
        isClassroomAttendanceScoreLengthValid,
        isClassroomHomeworkScoreLengthValid,
        isValidClassroomHomeWorkScoreFormat,
        isValidClassroomAttendanceScoreFormat,
        isClassroomAttendanceOverMaxScore,
        isClassroomHomeworkOverMaxScore,
        isClassroomRoundOverStartDate,
      });

      errorMessageList.push(...errorMessageQuizAndClassroomList);
    }

    if (errorMessageList.length > 0) {
      await this.jobRepository.updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...raw, errorMessageList],
            errorList: {
              originalPayload: { ...payload.params, email: user ? user.email : '' },
              message: errorMessageList,
            },
          },
        },
        { session },
      );

      const jobTransaction = await JobTransaction.new({
        jobId,
        status: JobTransactionStatusEnum.ERROR,
        preEnrollmentTransactionId: null,
        learningPathEnrollmentId: null,
        enrollmentId: null,
        payload: payload.params,
        errorMessages: [],
        warningMessages: [],
      });

      await this.jobTransactionRepository.save(jobTransaction, { session });
    } else {
      const round = await this.roundRepository.findOne({
        courseIds: {
          $in: [course.id],
        },
        roundDate: {
          $gte: date(roundDateToChrist).startOf('day').toDate(),
          $lte: date(roundDateToChrist).endOf('day').toDate(),
        },
      });
      // create round if round not existed
      if (!round && course.enrollType !== CourseEnrollTypeEnum.IMMEDIATE) {
        const roundData = await this.roundRepository.findOne({
          roundDate: {
            $gte: date(roundDateToChrist).startOf('day').toDate(),
            $lte: date(roundDateToChrist).endOf('day').toDate(),
          },
        });
        if (roundData) {
          const roundCourseIds = roundData.courseIds;
          roundCourseIds.push(course.id);
          roundData.courseIds = roundCourseIds;
          await this.roundRepository.save(roundData, {
            session,
          });
        } else {
          const newRound = await Round.new({
            roundDate: date(roundDateToChrist).startOf('day').toDate(),
            firstRegistrationDate: date(roundDateToChrist).add(-2, 'day').toDate(),
            lastRegistrationDate: date(roundDateToChrist).add(-1, 'day').toDate(),
            courseIds: [course.id],
            learningPathIds: [],
            organizationId,
          });
          await this.roundRepository.save(newRound, {
            session,
          });
        }
      }

      const expireDate = date(roundDateToChrist).add(course.courseVersion.expiryDay, 'day').endOf('day').toDate();
      const completeDate = date(endDateToChrist).toDate();

      let approveDateToChrist = '';
      if (approvedDate) {
        const approveDateYearMonthDayFormat = this.convertFormatDateToYearMonthDay(approvedDate);
        approveDateToChrist = this.convertFormatDateBuddistToChrist(approveDateYearMonthDayFormat);
      }
      const isStampApprovedDate =
        course.objectiveType !== CourseObjectiveTypeEnum.REGULAR &&
        status !== EnrollmentStatusEnum.EXPIRED &&
        approvedDate;

      // create enrollment
      const enrollmentModel = await Enrollment.new({
        courseId: course.id,
        courseVersionId: course.courseVersion?.id,
        organizationId,
        userId: user.guid,
        roundId: round ? round.id : '',
        business: BusinessTypeEnum.B2B,
        customerCode: '',
        status,
        isCountdownArticle: course.courseVersion?.isCountdownArticle,
        isIdentityVerificationEnabled: course.courseVersion?.isIdentityVerificationEnabled,
        imageIdCardPath: '',
        imageFacePath: '',
        completedCourseItem: course.courseVersion?.totalCourseItems,
        lastAccessItemId: '',
        learningProgress: [],
        startedAt: startDate,
        expiredAt:
          course.objectiveType === CourseObjectiveTypeEnum.REGULAR && course.courseVersion.expiryDay === 0
            ? null
            : expireDate,
        acceptedAt: startDate,
        finishedAt: status !== EnrollmentStatusEnum.EXPIRED ? completeDate : null,
        passedAt: status !== EnrollmentStatusEnum.EXPIRED ? completeDate : null,
        requestedApprovalAt: isStampApprovedDate ? date(approveDateToChrist).toDate() : null,
        approvalAt: isStampApprovedDate ? date(approveDateToChrist).toDate() : null,
        approvalReason: status === EnrollmentStatusEnum.APPROVED ? reason : '',
        snapshot: null,
        isUploadHistory: true,
        remark: note,
        enrollType,
      });

      await this.enrollmentRepository.save(enrollmentModel, { session });

      // create verify enrollment
      if (status === EnrollmentStatusEnum.APPROVED || status === EnrollmentStatusEnum.REJECTED) {
        const verifyEnrollment = await VerifyEnrollment.new({
          enrollmentId: enrollmentModel.id,
          status: this.mapEnrollmentStatusToVerifyEnrollmentStatus(status),
          action: this.mapEnrollmentStatusToVerifyEnrollmentAction(status),
          reason,
          actionByUserId: approvedByUserId,
        });
        await this.verifyEnrollmentRepository.save(verifyEnrollment, { session });
      }

      // create quiz answer
      const quizAnswerList: QuizAnswer[] = [];
      const classroomLocationEnrollmentList: ClassroomLocationEnrollment[] = [];

      const learningProgressList: CourseItemProgressParams[] = [];
      const classroomLocations =
        await this.classroomLocationRepository.getClassroomLocationWithRound(classLocationCodeArray);

      const enabledCourseItems = courseItems.filter((courseItem) => courseItem.isEnabled);

      for (const courseItem of enabledCourseItems) {
        const lastLearningProgress =
          learningProgressList.length && learningProgressList[learningProgressList.length - 1];

        const isNotPassPreviousContentItem =
          course.objectiveType === CourseObjectiveTypeEnum.TRAINING &&
          (status === EnrollmentStatusEnum.REJECTED || status === EnrollmentStatusEnum.EXPIRED) &&
          lastLearningProgress?.type === MaterialMediaTypeEnum.QUIZ &&
          lastLearningProgress?.quizAnswer.criteriaCertificate?.isPass === false;

        if (!isNotPassPreviousContentItem) {
          let quizAnswerLearningProgress: QuizAnswerProgressParams = null;
          let classroomLocationEnrollmentData: ClassroomLocationEnrollmentParams = null;

          if (courseItem.materialMedia.type === MaterialMediaTypeEnum.QUIZ) {
            const quizConfigCriteria = quizConfigList.find(
              (quizCriteria) => quizCriteria.courseItemId === courseItem.id,
            );

            const indexOfQuizSequence = quizConfigList.findIndex(
              (quizCriteria) => quizCriteria.courseItemId === courseItem.id,
            );
            const userScore = numberQuizResultScores[indexOfQuizSequence];
            const scorePercents = Math.floor((userScore / quizConfigCriteria.maxScore) * 100);
            const isPass = userScore >= quizConfigCriteria?.passScore;
            const {
              id: quizId,
              name: quizName,
              type: quizType,
              retest,
              limitTimeDuration,
              showAnswer,
              questions,
            } = courseItem.materialMedia.quiz;

            const questionsAndPassScore = this.getQuestionsByPassScore(questions, userScore, completeDate);

            const criteriaCertificate = quizConfigCriteria?.isEnabled
              ? {
                  passScore: quizConfigCriteria.passScore,
                  isPass,
                }
              : null;

            const quizAnswerModel = await QuizAnswer.new({
              enrollmentId: enrollmentModel.id,
              quizId,
              quizName,
              testType: quizType,
              quizConfig: {
                compulsory: false,
                retest,
                limitTimeDuration,
                showAnswer,
              },
              quizPublishedAt: date(courseItem.publishedAt).toDate(),
              timeSpent: 0,
              questions: questionsAndPassScore,
              userPoint: userScore,
              totalPoint: quizConfigCriteria.maxScore,
              scorePercents,
              quizStartDate: completeDate,
              finishedAt: completeDate,
            });
            quizAnswerList.push(quizAnswerModel);

            const quizAnswerId = quizAnswerModel.id;

            quizAnswerLearningProgress = {
              id: quizAnswerId,
              criteriaCertificate,
              finishedAt: completeDate,
            };
          }

          if (courseItem.materialMedia.type === MaterialMediaTypeEnum.CLASSROOM) {
            const classroomLocationWithRound = classroomLocations.find(
              (classroomLocation) => classroomLocation.classroomRound.materialMediaId === courseItem.materialMediaId,
            );

            const indexOfClassroomSequence = sortedClassroomConfigCriterias.findIndex(
              (classroomCriteria) => classroomCriteria.courseItemId === courseItem.id,
            );

            const homeworkScore = numberHomeworkScores[indexOfClassroomSequence];
            const attendanceScore = numberAttendanceScores[indexOfClassroomSequence];
            const classroomLocationEnrollmentStatus = classroomStatusArray[indexOfClassroomSequence];

            const statusParams =
              classroomLocationEnrollmentStatus === ClassroomLocationEnrollmentStatusTextEnum.PASS
                ? ClassroomLocationEnrollmentStatusEnum.PASSED
                : ClassroomLocationEnrollmentStatusEnum.NOT_PASS;

            const classroomLocationEnrollment = await ClassroomLocationEnrollment.new({
              classroomRoundId: classroomLocationWithRound.classroomRound.id,
              classroomLocationId: classroomLocationWithRound.id,
              enrollmentId: enrollmentModel.id,
              userId: user.guid,
              status: statusParams,
              totalAttended: attendanceScore,
              scoreHomework: homeworkScore,
              remark: '',
            });

            classroomLocationEnrollmentData = classroomLocationEnrollment;
            classroomLocationEnrollmentList.push(classroomLocationEnrollment);
          }

          // check last learning progress

          const learningProgressData = this.buildLearningProgressHistory(
            courseItem,
            completeDate,
            quizAnswerLearningProgress,
            course.courseVersion?.isCountdownArticle,
            classroomLocationEnrollmentData,
          );

          learningProgressList.push(learningProgressData);
        }
      }
      if (quizAnswerList.length > 0) {
        await this.quizAnswerRepository.saveMany(quizAnswerList, { session });
      }

      if (classroomLocationEnrollmentList.length > 0) {
        await this.classroomLocationEnrollmentRepository.saveMany(classroomLocationEnrollmentList, { session });
      }
      await this.enrollmentRepository.updateOne(
        { id: enrollmentModel.id },
        {
          $set: {
            learningProgress: [...learningProgressList],
            completedCourseItem: this.countCompletedCourseItem(learningProgressList),
          },
        },
        { session },
      );
      // create raw
      await this.jobRepository.updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...raw, errorMessageList],
          },
        },
        { session },
      );

      // create job transaction
      const jobTransaction = await JobTransaction.new({
        jobId,
        status: JobTransactionStatusEnum.PASSED,
        preEnrollmentTransactionId: null,
        learningPathEnrollmentId: null,
        enrollmentId: enrollmentModel.id,
        payload: { originalPayload: payload.params },
        errorMessages: [],
        warningMessages: [],
      });

      await this.jobTransactionRepository.save(jobTransaction, { session });
    }
  }

  private isDateFormatValid(str: string) {
    return /^([0-9]{2})\/([0-9]{2})\/([0-9]{4})$/.test(str);
  }

  private isClassroomDateFormatValid(str: string) {
    return /^([0-2]\d|3[0-1])\/(0\d|1[0-2])\/(\d{4})\s([0-9]|[01]\d|2[0-3]):[0-5]\d$/.test(str);
  }

  getQuestionsByPassScore(questions: QuestionParams[], passScore: number, completeDate: Date) {
    return questions.map((question, index) => {
      const isCorrect = index < passScore;
      const correctIndex = question.answers.findIndex((val) => val.isCorrect === isCorrect);

      return {
        ...question,
        correct_index: correctIndex,
        answerId: question.answers[correctIndex].id,
        answerIndex: correctIndex,
        isCorrect,
        point: isCorrect ? 1 : 0,
        createdAt: completeDate,
        updatedAt: completeDate,
      };
    });
  }

  buildLearningProgressHistory(
    courseItem: CourseItemParams,
    completeDate: Date,
    quizAnswerLearningProgress: QuizAnswerProgressParams,
    isCountdownArticle: boolean,
    classroomLocationEnrollment: ClassroomLocationEnrollmentParams,
  ): CourseItemProgressParams {
    const result: CourseItemProgressParams = {
      courseItemId: courseItem.id,
      type: courseItem.materialMedia.type,
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      statusText: CourseItemStatusTypeEnum.COMPLETE,
      timeSpent: null,
      updatedAt: completeDate,
      createdAt: completeDate,
    };

    if (courseItem.materialMedia.type === MaterialMediaTypeEnum.VIDEO) {
      result.currentTime = 0;
      result.maxSeekTime = courseItem.materialMedia.video.duration;
    }

    if (courseItem.materialMedia.type === MaterialMediaTypeEnum.QUIZ) {
      result.quizAnswer = quizAnswerLearningProgress;

      if (quizAnswerLearningProgress.criteriaCertificate?.isPass === false) {
        result.statusCode = CourseItemStatusCodeEnum.IN_PROGRESS;
        result.statusText = CourseItemStatusTypeEnum.IN_PROGRESS;
      }
    }

    if (courseItem.materialMedia.type === MaterialMediaTypeEnum.ARTICLE) {
      result.maxSeekTime = isCountdownArticle ? courseItem.materialMedia.article.duration : 0;
    }

    if (courseItem.materialMedia.type === MaterialMediaTypeEnum.CLASSROOM) {
      result.currentTime = null;
      result.maxSeekTime = null;
      result.classroomLocationEnrollmentId = classroomLocationEnrollment?.id;
      if (classroomLocationEnrollment?.status === ClassroomLocationEnrollmentStatusEnum.NOT_PASS) {
        result.statusCode = CourseItemStatusCodeEnum.IN_PROGRESS;
        result.statusText = CourseItemStatusTypeEnum.IN_PROGRESS;
      }
    }

    if (courseItem.materialMedia.type === MaterialMediaTypeEnum.SURVEY) {
      result.surveySubmissionId = null;
    }

    return result;
  }

  convertFormatDateToYearMonthDay(dateTimeStr: string) {
    const [dateStr, timeStr] = dateTimeStr.split(' ');

    let splitSyntax = '-';
    if (dateStr.includes('/')) {
      splitSyntax = '/';
    }
    const [day, month, year] = dateStr.split(splitSyntax);
    const newDate = [year, month, day].join(splitSyntax);

    return timeStr ? `${newDate} ${timeStr}` : newDate;
  }

  convertFormatDateBuddistToChrist(str: string) {
    const [dateStr, timeStr] = str.split(' ');

    let splitSyntax = '-';
    if (dateStr.includes('/')) {
      splitSyntax = '/';
    }
    const [year, month, day] = dateStr.split(splitSyntax);
    const newDate = [parseInt(year) - 543, month, day].join(splitSyntax);
    return timeStr ? `${newDate} ${timeStr}` : newDate;
  }

  covertStringToArray(str: string): string[] {
    return str ? str.split(',').map((item) => item.trim()) : [];
  }

  convertRegisterTypeToEnrollType(enrollType: string): Nullable<EnrollTypeEnum> {
    const isValid = Object.values(EnrollTypeEnum).includes(enrollType as EnrollTypeEnum);
    return isValid ? EnrollTypeEnum[enrollType] : null;
  }

  mapEnrollmentStatusToVerifyEnrollmentStatus(status: EnrollmentStatusEnum): VerifyEnrollmentStatusEnum {
    switch (status) {
      case EnrollmentStatusEnum.APPROVED:
        return VerifyEnrollmentStatusEnum.APPROVED;
      case EnrollmentStatusEnum.REJECTED:
        return VerifyEnrollmentStatusEnum.REJECTED;
      default:
        return VerifyEnrollmentStatusEnum.REJECTED;
    }
  }

  mapEnrollmentStatusToVerifyEnrollmentAction(status: EnrollmentStatusEnum): VerifyEnrollmentActionEnum {
    switch (status) {
      case EnrollmentStatusEnum.APPROVED:
        return VerifyEnrollmentActionEnum.APPROVE;
      case EnrollmentStatusEnum.REJECTED:
        return VerifyEnrollmentActionEnum.REJECT;
      default:
        return VerifyEnrollmentActionEnum.REJECT;
    }
  }

  private validateQuizScore(resultQuizScoreArray: string[]): {
    numberQuizResultScores: number[];
    isValidQuizScoreFormat: boolean;
  } {
    let isValidQuizScoreFormat = true;

    const numberQuizResultScores = resultQuizScoreArray.map((score) => {
      const num = Number(score);
      if (isValidQuizScoreFormat && isNaN(num)) {
        isValidQuizScoreFormat = false;
      }
      return num;
    });

    return { numberQuizResultScores, isValidQuizScoreFormat };
  }

  private validateQuizCriteria(
    quizConfigList: GetCourseQuizConfigParams[],
    numberQuizResultScores: number[],
  ): {
    isQuizResultOverMaxScore: boolean;
    isPassQuizCriteria: boolean;
  } {
    let isQuizResultOverMaxScore = false;
    let isPassQuizCriteria = true;
    for (const [index, quizConfigCriteria] of quizConfigList.entries()) {
      const userScore = numberQuizResultScores[index];
      const { maxScore, passScore, isEnabled } = quizConfigCriteria;
      if (!isQuizResultOverMaxScore && userScore > maxScore) {
        isQuizResultOverMaxScore = true;
      }

      if (isPassQuizCriteria && isEnabled && userScore < passScore) {
        isPassQuizCriteria = false;
      }
    }
    return { isQuizResultOverMaxScore, isPassQuizCriteria };
  }

  private validateClassroomDate(
    sortedClassroomLocations: ClassroomLocationParams[],
    classroomStartDateArray: string[],
    roundDateToChrist: string,
  ): {
    isAllClassroomLocationHasRound: boolean;
    isAllClassroomDateValidFormat: boolean;
    isClassroomRoundOverStartDate: boolean;
  } {
    let isAllClassroomLocationHasRound = true;
    let isAllClassroomDateValidFormat = true;
    let isClassroomRoundOverStartDate = false;

    for (const [index, classroomLocation] of sortedClassroomLocations.entries()) {
      if (this.isClassroomDateFormatValid(classroomStartDateArray[index])) {
        const classroomStartDate = this.convertFormatDateBuddistToChrist(
          this.convertFormatDateToYearMonthDay(classroomStartDateArray[index]),
        );

        const formattedDate = parseDate(
          classroomStartDate,
          TimeZoneEnum.Bangkok,
          DateFormat.yearMonthDayHourMinuteWithLeadingZero,
        );

        if (isAllClassroomLocationHasRound) {
          isAllClassroomLocationHasRound = date(formattedDate).isSame(
            date(classroomLocation.classroomRound.startedAt).toDate(),
          );
        }

        if (!isClassroomRoundOverStartDate) {
          isClassroomRoundOverStartDate = date(formattedDate).isBefore(date(roundDateToChrist).startOf('day').toDate());
        }
      } else {
        isAllClassroomDateValidFormat = false;
      }
    }

    return { isAllClassroomLocationHasRound, isAllClassroomDateValidFormat, isClassroomRoundOverStartDate };
  }

  private validateNumberHomeworkScores(classroomHomeWorkScoreArray: string[]): {
    numberHomeworkScores: number[];
    isValidClassroomHomeWorkScoreFormat: boolean;
  } {
    let isValidClassroomHomeWorkScoreFormat = true;
    const numberHomeworkScores = classroomHomeWorkScoreArray.map((score) => {
      const num = Number(score);
      if (isValidClassroomHomeWorkScoreFormat && isNaN(num)) {
        isValidClassroomHomeWorkScoreFormat = false;
      }
      return num;
    });

    return { isValidClassroomHomeWorkScoreFormat, numberHomeworkScores };
  }

  private validateClassroomAttendanceScores(classroomAttendanceScoreArray: string[]): {
    numberAttendanceScores: number[];
    isValidClassroomAttendanceScoreFormat: boolean;
  } {
    let isValidClassroomAttendanceScoreFormat = true;
    const numberAttendanceScores = classroomAttendanceScoreArray.map((score) => {
      const num = Number(score);
      if (isValidClassroomAttendanceScoreFormat && isNaN(num)) {
        isValidClassroomAttendanceScoreFormat = false;
      }
      return num;
    });

    return { numberAttendanceScores, isValidClassroomAttendanceScoreFormat };
  }

  private checkClassroomPassCriteria(
    homeworkScore: number,
    attendanceScore: number,
    classroomStatus: string,
    classroomConfigCriteria: any,
  ): {
    isClassroomHomeworkOverMaxScore: boolean;
    isClassroomAttendanceOverMaxScore: boolean;
    isClassroomStatusRelatedToScore: boolean;
    isPassAllClassroomCriteria: boolean;
  } {
    let isClassroomHomeworkOverMaxScore = false;
    let isClassroomAttendanceOverMaxScore = false;
    let isClassroomStatusRelatedToScore = true;
    let isPassHomeworkScoreCriteria = true;
    let isPassAttendanceScoreCriteria = true;
    let isPassAllClassroomCriteria = true;

    const { maxScoreHomework, maxAttendance, passScoreHomework, passAttendance, isEnabled } = classroomConfigCriteria;
    if (isEnabled) {
      // input homework attendance ไม่เกิน max score
      if (!isClassroomHomeworkOverMaxScore && maxScoreHomework !== null && homeworkScore > maxScoreHomework) {
        isClassroomHomeworkOverMaxScore = true;
      }

      // input classroom attendance ไม่เกิน max score
      if (!isClassroomAttendanceOverMaxScore && maxAttendance !== null && attendanceScore > maxAttendance) {
        isClassroomAttendanceOverMaxScore = true;
      }

      // input classroom homework ต้องผ่าน criteria
      if (isPassHomeworkScoreCriteria && passScoreHomework !== null && homeworkScore < passScoreHomework) {
        isPassHomeworkScoreCriteria = false;
      }

      // input classroom attendance ต้องผ่าน criteria
      if (isPassAttendanceScoreCriteria && passAttendance !== null && attendanceScore < passAttendance) {
        isPassAttendanceScoreCriteria = false;
      }

      if (isPassAllClassroomCriteria && (!isPassHomeworkScoreCriteria || !isPassAttendanceScoreCriteria)) {
        isPassAllClassroomCriteria = false;
      }

      // input classroom status ต้องเท่ากับ ตามผลข้อด้านบน (คะแนน PASS/NOT_PASS)
      if (
        isClassroomStatusRelatedToScore &&
        classroomStatus === ClassroomLocationEnrollmentStatusTextEnum.PASS &&
        (!isPassAttendanceScoreCriteria || !isPassHomeworkScoreCriteria)
      ) {
        isClassroomStatusRelatedToScore = false;
      }

      if (
        isClassroomStatusRelatedToScore &&
        classroomStatus === ClassroomLocationEnrollmentStatusTextEnum.FAIL &&
        isPassAttendanceScoreCriteria &&
        isPassHomeworkScoreCriteria
      ) {
        isClassroomStatusRelatedToScore = false;
      }
    }

    return {
      isClassroomHomeworkOverMaxScore,
      isClassroomAttendanceOverMaxScore,
      isClassroomStatusRelatedToScore,
      isPassAllClassroomCriteria,
    };
  }

  private getErrorMessageListQuizAndClassroom(isValidateParams: {
    isValidQuizScoreFormat: boolean;
    isResultQuizScoreLengthValid: boolean;
    isQuizResultOverMaxScore: boolean;
    isApprovable: boolean;
    isApprovableClassroom: boolean;
    isClassLocationCodeColumnValid: boolean;
    isClassroomStatusColumnValid: boolean;
    isClassroomStartedDateColumnValid: boolean;
    isClassroomAttendanceScoreLengthValid: boolean;
    isClassroomHomeworkScoreLengthValid: boolean;
    isValidClassroomHomeWorkScoreFormat: boolean;
    isValidClassroomAttendanceScoreFormat: boolean;
    isClassroomAttendanceOverMaxScore: boolean;
    isClassroomHomeworkOverMaxScore: boolean;
    isClassroomRoundOverStartDate: boolean;
  }): string[] {
    const {
      isValidQuizScoreFormat,
      isResultQuizScoreLengthValid,
      isQuizResultOverMaxScore,
      isApprovable,
      isApprovableClassroom,
      isClassLocationCodeColumnValid,
      isClassroomStatusColumnValid,
      isClassroomStartedDateColumnValid,
      isClassroomAttendanceScoreLengthValid,
      isClassroomHomeworkScoreLengthValid,
      isValidClassroomHomeWorkScoreFormat,
      isValidClassroomAttendanceScoreFormat,
      isClassroomAttendanceOverMaxScore,
      isClassroomHomeworkOverMaxScore,
      isClassroomRoundOverStartDate,
    } = isValidateParams;

    const errorMessageList: string[] = [];

    if (!isValidQuizScoreFormat) {
      errorMessageList.push('รูปแบบคะแนนแบบทดสอบไม่ถูกต้อง');
    }

    if (isResultQuizScoreLengthValid) {
      errorMessageList.push('จำนวนคะแนนไม่เท่ากับคำถาม');
    }

    if (isQuizResultOverMaxScore) {
      errorMessageList.push('คะแนนไม่ถูกต้อง มากกว่า จำนวนเต็ม');
    }

    if (!isApprovable) {
      errorMessageList.push('แบบทดสอบไม่ผ่านเกณฑ์ไม่สามารถกำหนดสถานะสำเร็จหรืออนุมัติได้');
    }

    if (!isApprovableClassroom) {
      errorMessageList.push('ห้องเรียนไม่ผ่านเกณฑ์ไม่สามารถกำหนดสถานะสำเร็จหรืออนุมัติได้');
    }

    if (!isClassLocationCodeColumnValid) {
      errorMessageList.push('รหัสสถานที่ไม่ถูกต้อง');
    }

    if (!isClassroomStartedDateColumnValid) {
      errorMessageList.push('วันเวลาที่เริ่มห้องเรียนไม่ถูกต้อง หรือไม่มีบนระบบ');
    }

    if (!isClassroomAttendanceScoreLengthValid) {
      errorMessageList.push('จำนวนการเข้าห้องเรียนไม่ถูกต้อง');
    }

    if (!isClassroomHomeworkScoreLengthValid) {
      errorMessageList.push('จำนวนการบ้านไม่ถูกต้อง');
    }

    if (!isValidClassroomHomeWorkScoreFormat) {
      errorMessageList.push('รูปแบบคะแนนการบ้านไม่ถูกต้อง');
    }

    if (!isValidClassroomAttendanceScoreFormat) {
      errorMessageList.push('รูปแบบคะแนนเข้าห้องเรียนไม่ถูกต้อง');
    }

    if (isClassroomAttendanceOverMaxScore) {
      errorMessageList.push('คะแนนเข้าห้องเรียนไม่ถูกต้อง: คะแนนมากกว่า จำนวนเต็ม');
    }

    if (isClassroomHomeworkOverMaxScore) {
      errorMessageList.push('คะแนนการบ้านไม่ถูกต้อง: คะแนนมากกว่า จำนวนเต็ม');
    }

    if (!isClassroomStatusColumnValid) {
      errorMessageList.push('สถานะห้องเรียนไม่ถูกต้อง');
    }

    if (isClassroomRoundOverStartDate) {
      errorMessageList.push('วันเวลาที่เริ่มห้องเรียนต้องอยู่หลังวันเริ่มเรียน');
    }

    return errorMessageList;
  }

  validateStatusByObjective(courseObjective: CourseObjectiveTypeEnum, status: EnrollmentStatusEnum): boolean {
    const allowedStatus = {
      [CourseObjectiveTypeEnum.REGULAR]: [EnrollmentStatusEnum.COMPLETED, EnrollmentStatusEnum.EXPIRED],
      [CourseObjectiveTypeEnum.TRAINING]: [
        EnrollmentStatusEnum.APPROVED,
        EnrollmentStatusEnum.REJECTED,
        EnrollmentStatusEnum.EXPIRED,
      ],
    };
    const validStatus = allowedStatus[courseObjective];
    return validStatus ? validStatus.includes(status) : false;
  }

  countCompletedCourseItem(learningProgressList: CourseItemProgressParams[]): number {
    if (!Array.isArray(learningProgressList) || learningProgressList.length === 0) {
      return 0;
    }

    return learningProgressList.filter((item) => item.statusText === CourseItemStatusTypeEnum.COMPLETE).length;
  }
}
