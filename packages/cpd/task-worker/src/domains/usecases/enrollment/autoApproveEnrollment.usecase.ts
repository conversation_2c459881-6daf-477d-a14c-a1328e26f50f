import { GenericID, Nullable, ObjectValue } from '@iso/constants/commonTypes';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import {
  buildApprovedNotificationMessage,
  buildApprovedWithCertificateNotificationMessage,
  buildAssignEnrollmentCompletedNotificationMessage,
  buildLearningPathCompleteWithCertificateNotificationMessage,
} from '@iso/helpers/userNotification';
import {
  CertificatePropertyModuleEnum,
  CertificatePropertyTypeEnum,
  DomainMetaDataEnum,
} from '@iso/lms/enums/certificate.enum';
import { CourseObjectiveTypeEnum } from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { EnrollmentAttachmentStatusEnum, EnrollmentAttachmentTypeEnum } from '@iso/lms/enums/enrollmentAttachment.enum';
import {
  LearningPathContentProgressStatusEnum,
  LearningPathEnrollmentStatusEnum,
} from '@iso/lms/enums/learningPathEnrollment.enum';
import { LogActivityDetectTypeEnum } from '@iso/lms/enums/log.enum';
import { OrganizationStorageTypeEnum } from '@iso/lms/enums/organizationStorage.enum';
import { PreAssignContentTypeEnum } from '@iso/lms/enums/preAssignContent.enum';
import { UserNotificationApplicationEnum } from '@iso/lms/enums/userNotification.enum';
import { VerifyEnrollmentActionEnum, VerifyEnrollmentStatusEnum } from '@iso/lms/enums/verifyEnrollment.enum';
import { Course } from '@iso/lms/models/course.model';
import { CourseVersion } from '@iso/lms/models/courseVersion.model';
import { Enrollment } from '@iso/lms/models/enrollment.model';
import { LearningPath } from '@iso/lms/models/learningPath.model';
import { LearningPathEnrollment } from '@iso/lms/models/learningPathEnrollment.model';
import { LearningPathEnrollmentCertificate } from '@iso/lms/models/learningPathEnrollmentCertificate.model';
import { LearningPathVersion } from '@iso/lms/models/learningPathVersion.model';
import { Organization, OrganizationAutoApprovalEnrollmentCriteria } from '@iso/lms/models/organization.model';
import { PreAssignContent } from '@iso/lms/models/preAssignContent.model';
import { Round } from '@iso/lms/models/round.model';
import { User } from '@iso/lms/models/user.model';
import { UserNotification } from '@iso/lms/models/userNotification.model';
import { VerifyEnrollment } from '@iso/lms/models/verifyEnrollment.model';
import { GenerateCertificateParams, GenerateCertificateResult } from '@iso/lms/types/certificate.type';
import { EnrollmentParams } from '@iso/lms/types/enrollment.type';
import { OrganizationCertificatePropertyWithNameParams } from '@iso/lms/types/organizationCertificate.type';
import { VerifyEnrollmentParams } from '@iso/lms/types/verifyEnrollment.type';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { isBoolean, isNull, chain, groupBy, isEmpty, isNil, keyBy, map, partition } from 'lodash';
import { compareTwoStrings } from 'string-similarity';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import {
  CertificateDIToken,
  CourseDIToken,
  CreditDIToken,
  EnrollmentDIToken,
  LearningPathDIToken,
  LicenseDIToken,
  MaterialMediaDIToken,
  OrganizationDIToken,
  PreAssignContentDIToken,
  RoundDIToken,
  UserDIToken,
} from '@applications/di/domain';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { ColumnSettingNameCollectionEnum } from '@constants/enums/columnSetting.enum';
import { AutoApproveEnrollmentParams } from '@constants/types/enrollment.type';
import {
  MailPayloadAssignEnrollmentCompleteParams,
  MailPayloadEnrollmentApproveParams,
  MailPayloadEnrollmentCompleteAndCertificateParams,
  MailPayloadLearningPathEnrollmentCertificateParams,
  MailPayloadLearningPathEnrollmentCompleteAndCertificateParams,
  MailPayloadLearningPathEnrollmentCompleteParams,
} from '@constants/types/mailer.type';

import { IEnrollmentDataMapper } from '@interfaces/dataMapper/enrollment.dataMapper.interface';
import { IColumnSettingRepository } from '@interfaces/repositories/columnSetting.repository.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { ICustomerRepository } from '@interfaces/repositories/customer.repository.interface';
import { IDepartmentRepository } from '@interfaces/repositories/department.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IEnrollmentAttachmentRepository } from '@interfaces/repositories/enrollmentAttachment.repository.interface';
import { IEnrollmentCertificateRepository } from '@interfaces/repositories/enrollmentCertificate.repository.interface';
import { IIdentificationCardRepository } from '@interfaces/repositories/identificationCard.repository.interface';
import { ILearningPathRepository } from '@interfaces/repositories/learningPath.repository.interface';
import { ILearningPathEnrollmentRepository } from '@interfaces/repositories/learningPathEnrollment.repository.interface';
import { ILearningPathEnrollmentCertificateRepository } from '@interfaces/repositories/learningPathEnrollmentCertificate.repository.interface';
import { ILearningPathVersionRepository } from '@interfaces/repositories/learningPathVersion.repository.interface';
import { ILicenseRepository } from '@interfaces/repositories/license.repository.interface';
import { ILogActivityDetectRepository } from '@interfaces/repositories/logActivityDetect.repository.interface';
import { ILogFaceComparisonRepository } from '@interfaces/repositories/logFaceComparison.repository.interface';
import { IMediaRepository } from '@interfaces/repositories/media.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IOrganizationCertificateRepository } from '@interfaces/repositories/organizationCertificate.repositoty.interface';
import { IOrganizationColumnSettingRepository } from '@interfaces/repositories/organizationColumnSetting.repository.interface';
import { IOrganizationStorageRepository } from '@interfaces/repositories/organizationStorage.repository.interface';
import { IPreAssignContentRepository } from '@interfaces/repositories/preAssignContent.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { IUserDirectReportRepository } from '@interfaces/repositories/userDirectReport.repository.interface';
import { IUserNotificationRepository } from '@interfaces/repositories/userNotification.repository.interface';
import { IVerifyEnrollmentRepository } from '@interfaces/repositories/verifyEnrollment.repository.interface';
import { IBuildAggregateCertificateAdaptorService } from '@interfaces/services/buildAggregateCertificate.service.interface';
import { ICertificateService } from '@interfaces/services/certificate.service.interface';
import { IColumnSettingService } from '@interfaces/services/columnSetting.interface';
import { ILearningPathEnrollmentService } from '@interfaces/services/learningPathEnrollment.interface';
import { IMaterialMediaService } from '@interfaces/services/materialMedia.service.interface';
import { INotificationService } from '@interfaces/services/notification.service.interface';
import { IDatabaseTransactionService } from '@interfaces/transaction/databaseTransaction.interface';
import { IAutoApproveEnrollmentUseCase } from '@interfaces/usecases/enrollment.interface';

import { date, formatDateInThaiLocale, getDateLocale, TimeZoneEnum } from '@domains/utils/date.util';
import { convertSimilarSalute } from '@domains/utils/enrollment.util';

@Injectable()
export class AutoApproveEnrollmentUseCase implements IAutoApproveEnrollmentUseCase {
  constructor(
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(EnrollmentDIToken.VerifyEnrollmentRepository)
    private readonly verifyEnrollmentRepository: IVerifyEnrollmentRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(RoundDIToken.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(EnrollmentDIToken.IdentificationCardRepository)
    private readonly identificationCardRepository: IIdentificationCardRepository,
    @Inject(PreAssignContentDIToken.PreAssignContentRepository)
    private readonly preAssignContentRepository: IPreAssignContentRepository,
    @Inject(UserDIToken.UserNotificationRepository)
    private readonly userNotificationRepository: IUserNotificationRepository,
    @Inject(LicenseDIToken.LicenseRepository)
    private readonly licenseRepository: ILicenseRepository,
    @Inject(EnrollmentDIToken.EnrollmentAttachmentRepository)
    private readonly enrollmentAttachmentRepository: IEnrollmentAttachmentRepository,
    @Inject(EnrollmentDIToken.EnrollmentCertificateRepository)
    private readonly enrollmentCertificateRepository: IEnrollmentCertificateRepository,
    @Inject(EnrollmentDIToken.LogActivityDetectRepository)
    private readonly logActivityDetectRepository: ILogActivityDetectRepository,
    @Inject(EnrollmentDIToken.LogFaceComparisonRepository)
    private readonly logFaceComparisonRepository: ILogFaceComparisonRepository,
    @Inject(InfrastructuresPersistenceDIToken.DatabaseTransaction)
    private readonly databaseTransaction: IDatabaseTransactionService,
    @Inject(LearningPathDIToken.LearningPathEnrollmentRepository)
    private readonly learningPathEnrollmentRepository: ILearningPathEnrollmentRepository,
    @Inject(LearningPathDIToken.LearningPathVersionRepository)
    private readonly learningPathVersionRepository: ILearningPathVersionRepository,
    @Inject(LearningPathDIToken.LearningPathEnrollmentCertificateRepository)
    private readonly learningPathEnrollmentCertificateRepository: ILearningPathEnrollmentCertificateRepository,
    @Inject(LearningPathDIToken.LearningPathRepository)
    private readonly learningPathRepository: ILearningPathRepository,
    @Inject(OrganizationDIToken.OrganizationCertificateRepository)
    private readonly organizationCertificateRepository: IOrganizationCertificateRepository,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,

    // repository related to  build column setting query

    @Inject(OrganizationDIToken.ColumnSettingRepository)
    private readonly columnSettingRepository: IColumnSettingRepository,
    @Inject(OrganizationDIToken.OrganizationColumnSettingRepository)
    private readonly organizationColumnSettingRepository: IOrganizationColumnSettingRepository,
    @Inject(OrganizationDIToken.OrganizationStorageRepository)
    private readonly organizationStorageRepository: IOrganizationStorageRepository,
    @Inject(MaterialMediaDIToken.MediaRepository)
    private readonly mediaRepository: IMediaRepository,
    // repository factory
    @Inject(OrganizationDIToken.DepartmentRepository)
    private readonly departmentRepository: IDepartmentRepository,
    @Inject(UserDIToken.UserDirectReportRepository)
    private readonly userDirectReportRepository: IUserDirectReportRepository,
    @Inject(CreditDIToken.CustomerRepository)
    private readonly customerRepository: ICustomerRepository,

    @Inject(LearningPathDIToken.LearningPathEnrollmentService)
    private readonly learningPathEnrollmentService: ILearningPathEnrollmentService,
    @Inject(InfrastructuresServiceDIToken.NotificationService)
    private readonly notificationService: INotificationService,
    @Inject(InfrastructuresServiceDIToken.CertificateService)
    private readonly certificateService: ICertificateService,

    @Inject(EnrollmentDIToken.EnrollmentDataMapper)
    private readonly enrollmentDataMapper: IEnrollmentDataMapper,

    // service
    @Inject(OrganizationDIToken.ColumnSettingService)
    private readonly columnSettingService: IColumnSettingService,
    @Inject(MaterialMediaDIToken.MaterialMediaService)
    private readonly materialMediaService: IMaterialMediaService,
    @Inject(CertificateDIToken.BuildAggregateCertificateAdaptorService)
    private readonly buildAggregateCertificateAdaptorService: IBuildAggregateCertificateAdaptorService,
  ) {}

  async execute(params: AutoApproveEnrollmentParams): Promise<void> {
    const { enrollmentId, organizationId } = params;

    this.logger.log(`Start processing the auto approval enrollment ${enrollmentId}`);
    const organization = await this.organizationRepository.findById(organizationId);
    if (!organization) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
      });
    }

    const enrollment = await this.enrollmentRepository.findById(enrollmentId);

    if (!enrollment.expiredAt) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { enrollmentId },
      });
    }

    const isEnrollmentFinished = [
      EnrollmentStatusEnum.APPROVED,
      EnrollmentStatusEnum.REJECTED,
      EnrollmentStatusEnum.CANCELED,
    ].includes(enrollment.status);

    if (isEnrollmentFinished) {
      throw Exception.new({
        code: Code.BAD_REQUEST_ERROR,
        message: 'The enrollment state is finished, status cannot update',
        data: { enrollmentId, status: enrollment.status },
      });
    }

    const { courseVersionId } = enrollment;

    const courseVersion = await this.courseVersionRepository.findById(courseVersionId);
    if (!courseVersion?.isAutoApproveEnabled) return;

    const user = await this.userRepository.findById(enrollment.userId);
    if (!user) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: `user not found`,
      });
    }
    const { autoApprovalEnrollmentConfig } = organization;
    const { autoApproveCriteria, autoVerifyCriteria } = autoApprovalEnrollmentConfig ?? {};

    let { status } = enrollment;

    if (!autoVerifyCriteria?.isActive) return;

    try {
      if (status === EnrollmentStatusEnum.PASSED || status === EnrollmentStatusEnum.PENDING_APPROVAL) {
        const { updateEnrollmentStatus, updateEnrollmentAction, approvalReason } = await this.approvalProcess(
          enrollment,
          enrollment.userId,
          EnrollmentStatusEnum.VERIFIED,
          autoVerifyCriteria,
        );
        status = updateEnrollmentStatus;
        await this.updateStatusApproval(
          enrollment,
          organization,
          updateEnrollmentStatus,
          updateEnrollmentAction,
          `ตรวจสอบโดยระบบ\n${approvalReason}`.trim(),
          user,
          organizationId,
        );
      }

      if (autoApproveCriteria?.isActive && status === EnrollmentStatusEnum.VERIFIED) {
        const { updateEnrollmentStatus, updateEnrollmentAction, approvalReason } = await this.approvalProcess(
          enrollment,
          enrollment.userId,
          EnrollmentStatusEnum.APPROVED,
          autoApproveCriteria,
        );
        status = updateEnrollmentStatus;
        await this.updateStatusApproval(
          enrollment,
          organization,
          updateEnrollmentStatus,
          updateEnrollmentAction,
          `ตรวจสอบการอนุมัติโดยระบบ\n${approvalReason}`.trim(),
          user,
          organizationId,
        );
      }

      this.logger.log(`End processing the auto approval enrollment ${enrollmentId}`);
    } catch (error) {
      const event = status === EnrollmentStatusEnum.VERIFIED ? 'ตรวจสอบโดยระบบ' : 'ตรวจสอบการอนุมัติโดยระบบ';
      await this.updateStatusApproval(
        enrollment,
        organization,
        EnrollmentStatusEnum.PENDING_APPROVAL,
        VerifyEnrollmentActionEnum.NOT_VERIFY,
        `${event}\nพบข้อผิดพลาด`.trim(),
        user,
        organizationId,
      );
      this.logger.error(`Something wrong, auto approval enrollment id ${enrollmentId} found error: ${error}`);
    }
  }

  private async approvalProcess(
    enrollment: Enrollment,
    userId: GenericID,
    status: EnrollmentStatusEnum,
    criteria: OrganizationAutoApprovalEnrollmentCriteria,
  ): Promise<{
    updateEnrollmentStatus: EnrollmentStatusEnum;
    updateEnrollmentAction: VerifyEnrollmentActionEnum;
    approvalReason: string;
  }> {
    const enrollmentId = enrollment.id;

    const { citizenId, profile } = (await this.userRepository.findOne({ guid: userId })) ?? {};

    const { information } = (await this.identificationCardRepository.findOne({ enrollmentId })) ?? {};

    let approvalReason = ``;
    let isValid = true;

    if (criteria?.citizenIdMatchPercent?.minimum) {
      let percentSimilarity = 0;

      if (citizenId && information?.citizenId) {
        const userCitizenId = citizenId;
        const infoCitizenId = information.citizenId;
        percentSimilarity = compareTwoStrings(userCitizenId, infoCitizenId) * 100;
      } else {
        this.logger.error(`Invalid user('${userId}') information: ID card or user ID may be empty.`);
      }

      if (percentSimilarity >= criteria.citizenIdMatchPercent.minimum)
        approvalReason += `ข้อมูลเลขบัตรประชาชนที่อ่านได้จากบัตรประชาชนกับที่ลงทะเบียนไว้มีความตรงกัน ${criteria.citizenIdMatchPercent.minimum}%\n`;
      else {
        approvalReason += `**WARNING**ข้อมูลเลขบัตรประชาชนที่อ่านได้จากบัตรประชาชนกับระบบมีความตรงกันต่ำกว่า ${criteria.citizenIdMatchPercent.minimum}% ซึ่งไม่ผ่านตามเงื่อนไขที่กำหนด\n`;
        isValid = false;
      }
    }

    if (criteria?.fullNameMatchPercent?.minimum && profile) {
      const systemFullName = `${profile.salute} ${profile.firstname} ${profile.lastname}`;
      const userFullName = convertSimilarSalute(systemFullName);
      const ocrFullName = convertSimilarSalute(information?.fullname ?? '');
      const percentSimilarity = compareTwoStrings(userFullName, ocrFullName) * 100;
      if (percentSimilarity >= criteria.fullNameMatchPercent.minimum)
        approvalReason += `ข้อมูลชื่อและนามสกุลที่อ่านได้จากบัตรประชาชนกับระบบมีความตรงกันมากกว่า ${criteria.fullNameMatchPercent.minimum}%\n`;
      else {
        approvalReason += `**WARNING**ข้อมูลชื่อและนามสกุลที่อ่านได้จากบัตรประชาชนกับระบบมีความตรงกันต่ำกว่า ${criteria.fullNameMatchPercent.minimum}% ซึ่งไม่ผ่านตามเงื่อนไขที่กำหนด\n`;
        isValid = false;
      }
    }

    if (isBoolean(criteria?.isUploadIdCard) && !criteria?.isUploadIdCard) {
      const result = await this.identificationCardRepository.isUploadIdentificationCard({
        enrollmentId,
        isUpload: false,
      });
      if (result) approvalReason += `รูปภาพบัตรประชาชนถ่ายผ่านกล้อง/เว็บแคม\n`;
      else {
        approvalReason += `**WARNING**รูปภาพบัตรประชาชนไม่ได้ถ่ายผ่านกล้อง/เว็บแคม ซึ่งไม่ผ่านตามเงื่อนไขที่กำหนด\n`;
        isValid = false;
      }
    }

    if (criteria?.totalLivenessPercent) {
      const result = await this.totalLivenessPercent(enrollmentId);
      if (result >= criteria.totalLivenessPercent) approvalReason += `ผ่านการตรวจสอบการปลอมแปลงรูปภาพถ่ายใบหน้า\n`;
      else {
        approvalReason += `**WARNING**ไม่ผ่านการตรวจสอบการปลอมแปลงรูปภาพถ่ายใบหน้า\n`;
        isValid = false;
      }
    }

    if (criteria?.isAllDocsIsApprove) {
      const result = await this.enrollmentAttachmentRepository.isApprovedAllDocument(enrollmentId);
      if (result) approvalReason += `ผ่านการส่งเอกสารทั้งหมด\n`;
      else {
        approvalReason += `**WARNING**พบรายการเอกสารที่ยังไม่ได้รับการอนุมัติ\n`;
        isValid = false;
      }
    }

    let updateEnrollmentStatus: EnrollmentStatusEnum = status;
    let updateEnrollmentAction: VerifyEnrollmentActionEnum = VerifyEnrollmentActionEnum.NOT_VERIFY;

    switch (status) {
      case EnrollmentStatusEnum.VERIFIED:
        updateEnrollmentStatus = isValid ? status : EnrollmentStatusEnum.PENDING_APPROVAL;
        updateEnrollmentAction = isValid ? VerifyEnrollmentActionEnum.VERIFY : VerifyEnrollmentActionEnum.NOT_VERIFY;
        break;
      case EnrollmentStatusEnum.APPROVED:
        updateEnrollmentStatus = isValid ? status : EnrollmentStatusEnum.VERIFIED;
        updateEnrollmentAction = isValid ? VerifyEnrollmentActionEnum.APPROVE : VerifyEnrollmentActionEnum.NOT_VERIFY;
        break;
      default:
    }

    return { updateEnrollmentStatus, updateEnrollmentAction, approvalReason };
  }

  private async updateStatusApproval(
    enrollment: Enrollment,
    organization: Organization,
    status: EnrollmentStatusEnum,
    action: VerifyEnrollmentActionEnum,
    approvalReason: string,
    user: User,
    organizationId: GenericID,
  ) {
    this.logger.log(`Update enrollment ${enrollment.id} to ${status}`);

    const enrollmentId = enrollment.id;
    let round: Nullable<Round> = null;

    const course = await this.courseRepository.getOnePublishCourseDetailById(enrollment.courseId);
    if (!course) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'Course not found',
        data: { courseId: enrollment.courseId },
      });
    }

    if (enrollment?.roundId) {
      round = await this.roundRepository.findById(enrollment.roundId);
    }

    const dateNow = date().toDate();
    enrollment.status = status;

    let verifyEnrollmentStatus = VerifyEnrollmentStatusEnum.PENDING_APPROVAL;
    if (status === EnrollmentStatusEnum.VERIFIED) {
      verifyEnrollmentStatus = VerifyEnrollmentStatusEnum.VERIFIED;
    } else if (status === EnrollmentStatusEnum.PENDING_APPROVAL) {
      verifyEnrollmentStatus = VerifyEnrollmentStatusEnum.PENDING_APPROVAL;
    } else if (status === EnrollmentStatusEnum.REJECTED) {
      await this.requestRejected(enrollmentId, organization);
      enrollment.approvalAt = dateNow;
      verifyEnrollmentStatus = VerifyEnrollmentStatusEnum.REJECTED;
    } else {
      await this.requestCertificate(enrollmentId, organization);
      enrollment.approvalAt = dateNow;
      verifyEnrollmentStatus = VerifyEnrollmentStatusEnum.APPROVED;
    }

    await this.enrollmentRepository.save(enrollment);

    const verifyEnrollmentParams: VerifyEnrollmentParams = {
      enrollmentId: enrollment.id,
      status: verifyEnrollmentStatus,
      reason: approvalReason,
      action,
      actionByUserId: '',
    };

    const document = await VerifyEnrollment.new(verifyEnrollmentParams);
    await this.verifyEnrollmentRepository.save(document);

    if (verifyEnrollmentStatus === VerifyEnrollmentStatusEnum.APPROVED) {
      await this.updateContentEnrollmentProgress({
        enrollmentId,
        learningPathContentStatus: LearningPathContentProgressStatusEnum.COMPLETED,
        userId: user.guid,
        organizationId,
      });

      const preAssignContent = await this.preAssignContentRepository.findOne({
        id: enrollment.preAssignContentId,
        organizationId: enrollment.organizationId,
      });

      const assignee = isNull(preAssignContent)
        ? null
        : await this.userRepository.findById(preAssignContent.createdByUserId);

      if (assignee) {
        await this.requestSendAssignEnrollmentCompleteNotification({
          assignee,
          learner: user,
          enrollment,
          organization,
          course,
          round,
        });
      }
    }
  }

  private async totalLivenessPercent(enrollmentId: GenericID): Promise<number> {
    const getLogActivityDetects = this.logActivityDetectRepository.aggregate([
      {
        $match: {
          enrollmentId,
          isPass: true,
          type: LogActivityDetectTypeEnum.FACE_COMPARE,
        },
      },
      {
        $project: {
          enrollmentId: 1,
          isFaceLivenessMode: 1,
        },
      },
    ]);

    const getLogFaceComparisons = this.logFaceComparisonRepository.aggregate([
      {
        $match: {
          enrollmentId,
          isPass: true,
        },
      },
      {
        $project: {
          enrollmentId: 1,
          isFaceLivenessMode: 1,
        },
      },
    ]);

    const [logActivityDetects, logFaceComparisons] = await Promise.all([getLogActivityDetects, getLogFaceComparisons]);
    const livenesses = [...logActivityDetects, ...logFaceComparisons];
    const totalFaceCapture = livenesses?.length ?? 0;
    if (totalFaceCapture < 1) return 0;

    const totalLiveness =
      livenesses?.reduce((prev, curr) => {
        return curr.isFaceLivenessMode ? prev + 1 : prev;
      }, 0) ?? 0;

    const totalLivenessPercentage = (totalLiveness / totalFaceCapture) * 100;

    return Number(totalLivenessPercentage.toFixed(2));
  }

  private async updateContentEnrollmentProgress(params: {
    enrollmentId: GenericID;
    learningPathContentStatus: LearningPathContentProgressStatusEnum;
    userId: GenericID;
    organizationId: GenericID;
  }): Promise<void> {
    const { enrollmentId, learningPathContentStatus } = params;

    const enrollment = await this.enrollmentRepository.findOne({ id: enrollmentId });

    if (!enrollment) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'enrollment dose not exist',
      });
    }

    const learningPathEnrollments = await this.learningPathEnrollmentRepository.find({
      status: {
        $in: [LearningPathEnrollmentStatusEnum.ASSIGNED, LearningPathEnrollmentStatusEnum.IN_PROGRESS],
      },
      contentProgress: {
        $elemMatch: { enrollmentId, status: { $ne: LearningPathContentProgressStatusEnum.COMPLETED } },
      },
    });

    if (isEmpty(learningPathEnrollments)) return;

    this.logger.log(
      `Update learning path content enrollment progress of enrollment ${enrollmentId} to ${learningPathContentStatus}`,
    );

    const roundIds = chain(learningPathEnrollments).map('roundId').compact().uniq().value();
    const preAssignContentIds = chain(learningPathEnrollments).map('preAssignContentId').compact().uniq().value();

    const [user, organization, rounds, preAssignContents] = await Promise.all([
      this.userRepository.findById(enrollment.userId),
      this.organizationRepository.findById(enrollment.organizationId),
      this.roundRepository.find({ id: { $in: roundIds } }),
      this.preAssignContentRepository.find({
        id: { $in: preAssignContentIds },
        organizationId: enrollment.organizationId,
      }),
    ]);

    if (!user || !organization) return;

    const assigneeUserIds = chain(preAssignContents).map('createdByUserId').compact().uniq().value();

    const [assignee, learningPathVersions] = await Promise.all([
      this.findAssigneeByUserIds(assigneeUserIds),
      this.learningPathVersionRepository.find({
        $or: learningPathEnrollments.map((learningPathEnrollment) => ({
          learningPathId: learningPathEnrollment.learningPathId,
          version: learningPathEnrollment.version,
        })),
      }),
    ]);

    const learningPathVersionsMapper = learningPathVersions.reduce((acc, learningPathVersion) => {
      const key = `${learningPathVersion.learningPathId}_${learningPathVersion.version}`;
      acc.set(key, learningPathVersion);
      return acc;
    }, new Map<string, LearningPathVersion>());

    const learningPathIds = learningPathVersions.map((learningPathVersion) => learningPathVersion.learningPathId);
    const learningPaths = await this.learningPathRepository.find({ id: { $in: learningPathIds } });
    const learningPathMapWithId = learningPaths.reduce((acc, cur) => {
      const key = String(cur.id);
      acc.set(key, cur);
      return acc;
    }, new Map<string, LearningPath>());

    const roundMapWithId = rounds.reduce((acc, cur) => {
      const key = String(cur.id);
      acc.set(key, cur);
      return acc;
    }, new Map<string, Round>());

    const preAssignContentMapWithId = preAssignContents.reduce((acc, cur) => {
      const key = String(cur.id);
      acc.set(key, cur);
      return acc;
    }, new Map<string, PreAssignContent>());

    const assigneeMapWithId = assignee.reduce((acc, cur) => {
      const key = String(cur.guid);
      acc.set(key, cur);
      return acc;
    }, new Map<string, User>());

    const learningEnrollmentForSendCertifications: LearningPathEnrollment[] = [];

    // email template
    const completeLearningPathEmailTemplates = [];
    const completeLearningPathAndCertificateEmailTemplates = [];
    const completeCertificateEmailTemplates = [];
    const assignLearningPathEnrollmentCompleteEmailTemplates: {
      email: string;
      payload: MailPayloadAssignEnrollmentCompleteParams;
      organization: Organization;
    }[] = [];

    // in application
    const userNotificationCompleteLearningPathInApplication = [];

    learningPathVersions.splice(0, learningPathVersions.length);
    const currentDate = date().toDate();

    const learningPathEnrollmentCheckCertificateDataList = await Promise.all(
      learningPathEnrollments.map(async (learningPathEnrollment) => {
        this.logger.log(`Update content learning path enrollment progress ${learningPathEnrollment.id}`);
        learningPathEnrollment.updateContentProgressStatus(enrollmentId, learningPathContentStatus);
        const completedContentItems = learningPathEnrollment.contentProgress.filter(
          (contentProgressItem) => contentProgressItem.status === LearningPathContentProgressStatusEnum.COMPLETED,
        );
        learningPathEnrollment.completedContentItem = completedContentItems.length;

        const key = `${learningPathEnrollment.learningPathId}_${learningPathEnrollment.version}`;
        const learningPathOfLPEnrollment = learningPathMapWithId.get(String(learningPathEnrollment.learningPathId));
        const learningPathVersionOfLPEnrollment = learningPathVersionsMapper.get(key);
        const roundOfLPEnrollment = roundMapWithId.get(String(learningPathEnrollment?.roundId));
        const preAssignContentOfLPEnrollment = preAssignContentMapWithId.get(
          String(learningPathEnrollment?.preAssignContentId),
        );
        const assigneeOfLPEnrollment = assigneeMapWithId.get(String(preAssignContentOfLPEnrollment?.createdByUserId));

        if (!learningPathVersionOfLPEnrollment || !learningPathOfLPEnrollment)
          return {
            isVerifyCriteriaCertificate: false,
            learningPathVersion: learningPathVersionOfLPEnrollment,
            learningPathEnrollment,
          };

        // Check is complete all content in learning path
        const isCompleteLearningProgress =
          learningPathEnrollment.completedContentItem === learningPathVersionOfLPEnrollment.contents.length;
        if (isCompleteLearningProgress) {
          learningPathEnrollment.finishedAt = currentDate;
          learningPathEnrollment.status = LearningPathEnrollmentStatusEnum.COMPLETED;
          learningPathEnrollment.updatedAt = currentDate;

          if (assigneeOfLPEnrollment) {
            const payload: MailPayloadAssignEnrollmentCompleteParams = {
              learnerUserId: learningPathEnrollment.userId,
              contentName: learningPathVersionOfLPEnrollment.name,
              contentType: PreAssignContentTypeEnum.LEARNING_PATH,
              learnerFullName: user.fullName,
              fullName: assigneeOfLPEnrollment?.fullName,
              round: {
                roundDate: roundOfLPEnrollment?.roundDate,
                expiredDate: learningPathEnrollment?.expiredAt,
              },
            };

            assignLearningPathEnrollmentCompleteEmailTemplates.push({
              email: assigneeOfLPEnrollment.email,
              payload,
              organization,
            });

            const { userNotification } =
              await this.learningPathEnrollmentService.buildAssignLearningPathEnrollmentCompleteMessageAndInAppUserNotificationModel(
                {
                  learningPath: learningPathOfLPEnrollment,
                  learningPathVersion: learningPathVersionOfLPEnrollment,
                  learningPathEnrollment,
                  round: roundOfLPEnrollment || null,
                  assignee: assigneeOfLPEnrollment,
                  fullName: user.fullName,
                },
              );

            userNotificationCompleteLearningPathInApplication.push(userNotification);
          }
        }

        const isVerifyCriteriaCertificate =
          this.learningPathEnrollmentService.validateIsLearningPathEnrollmentNeedToVerifyCertificateCriteria(
            learningPathVersionOfLPEnrollment,
            learningPathEnrollment,
          );

        if (isCompleteLearningProgress && !learningPathVersionOfLPEnrollment.isCertificateEnabled) {
          learningPathEnrollment.passedAt = currentDate;
        }

        return {
          isVerifyCriteriaCertificate,
          learningPathVersion: learningPathVersionOfLPEnrollment,
          learningPathEnrollment,
        };
      }),
    );

    const [certificationLearningPathEnrollments, nonCertificationLearningPathEnrollments] = partition(
      learningPathEnrollmentCheckCertificateDataList,
      ({ isVerifyCriteriaCertificate }) => isVerifyCriteriaCertificate,
    );

    if (!isEmpty(certificationLearningPathEnrollments)) {
      const allCriteriaEnableContentIds = chain(certificationLearningPathEnrollments)
        .map('learningPathVersion')
        .map('contents')
        .flatten()
        .filter((content) => content.isCriteriaEnabled)
        .map('courseId')
        .value();

      const allEnrollmentContentIds = chain(certificationLearningPathEnrollments)
        .map('learningPathEnrollment')
        .map('contentProgress')
        .flatten()
        .map('enrollmentId')
        .filter((id) => !isNil(id))
        .value();

      const [courses, enrollments] = await Promise.all([
        this.courseRepository.aggregate<Course>([
          {
            $match: {
              id: { $in: allCriteriaEnableContentIds },
            },
          },
          {
            $project: {
              _id: 0,
              id: 1,
              objectiveType: 1,
            },
          },
        ]),
        this.enrollmentRepository
          .find({ id: { $in: allEnrollmentContentIds } })
          .then((res) => this.enrollmentDataMapper.toDTOs(res)),
      ]);

      const courseMappedById = courses.reduce((acc, course) => {
        const key = String(course.id);
        acc.set(key, course);
        return acc;
      }, new Map<string, Course>());

      const enrollmentMappedById = enrollments.reduce(
        (acc: Map<string, EnrollmentParams>, _enrollment: EnrollmentParams) => {
          const key = String(_enrollment.id);
          acc.set(key, _enrollment);
          return acc;
        },
        new Map<string, EnrollmentParams>(),
      );
      const courseIds = courses.map((course) => course.id);
      const enrollmentCourseIds = enrollments.map((enroll: EnrollmentParams) => enroll.courseId);
      const enrollmentCourseVersionIds = enrollments.map((enroll: EnrollmentParams) => enroll.courseVersionId);
      const notEnrollCourseIds = courseIds.filter(function (course) {
        return enrollmentCourseIds.indexOf(course) === -1;
      });
      const enrollmentCourseVersions = await this.courseVersionRepository.find({
        id: { $in: enrollmentCourseVersionIds },
      });
      const notEnrollCourseVersions = await this.courseVersionRepository.find({
        courseId: { $in: notEnrollCourseIds },
        status: CourseVersionStatusEnum.PUBLISHED,
      });
      const courseVersions = [...enrollmentCourseVersions, ...notEnrollCourseVersions];

      const courseVersionMappedByCourseId = courseVersions.reduce((acc, courseVersion) => {
        const key = String(courseVersion.courseId);
        acc.set(key, courseVersion);
        return acc;
      }, new Map<string, CourseVersion>());

      for (const [index, certificationLearningPathEnrollment] of certificationLearningPathEnrollments.entries()) {
        const { learningPathEnrollment } = certificationLearningPathEnrollment;
        const isPassedCriteria =
          this.learningPathEnrollmentService.validateIsLearningPathEnrollmentPassedCriteriaCertificate(
            certificationLearningPathEnrollment.learningPathEnrollment,
            certificationLearningPathEnrollment.learningPathVersion?.contents || [],
            courseMappedById,
            courseVersionMappedByCourseId,
            enrollmentMappedById,
          );

        if (!isPassedCriteria) continue;

        const isNotPassedAtExisting = isNil(learningPathEnrollment.passedAt);
        if (isNotPassedAtExisting) {
          certificationLearningPathEnrollments[index].learningPathEnrollment.passedAt = currentDate;
          learningEnrollmentForSendCertifications.push(
            certificationLearningPathEnrollments[index].learningPathEnrollment,
          );
        }
      }

      // Clean up memory
      courseMappedById.clear();
      enrollmentMappedById.clear();
    }

    const learningPathEnrollmentForSendCertificateIds = map(learningEnrollmentForSendCertifications, (v) => v.id);
    const newLearningPathEnrollmentCertificates: LearningPathEnrollmentCertificate[] = [];

    const groupLearningPathEnrollmentForSendCertificateById = groupBy(learningEnrollmentForSendCertifications, 'id');

    if (!isEmpty(learningPathEnrollmentForSendCertificateIds)) {
      const learningPathEnrollmentCertificates = await this.learningPathEnrollmentCertificateRepository.find({
        learningPathEnrollmentId: { $in: learningPathEnrollmentForSendCertificateIds },
        isSentEmail: false,
      });

      const learningPathEnrollmentCertificateMap = keyBy(
        learningPathEnrollmentCertificates,
        'learningPathEnrollmentId',
      );
      const certificatePayloadWithLearningPathEnrollmentId = new Map<string, GenerateCertificateParams>();
      for (const learningEnrollmentForSendCertification of learningEnrollmentForSendCertifications) {
        const key = `${learningEnrollmentForSendCertification.learningPathId}_${learningEnrollmentForSendCertification.version}`;
        const learningPathVersion = learningPathVersionsMapper.get(key);

        if (!learningPathVersion) continue;

        const { certificate } = learningPathVersion;
        const { guid: userId } = user;
        const learningPathEnrollmentCertificate =
          learningPathEnrollmentCertificateMap[learningEnrollmentForSendCertification.id as string];

        const organizationCertificate = await this.organizationCertificateRepository.findWithCertificateDetail(
          certificate.organizationCertificateId,
        );

        const { certificateDetail } = organizationCertificate;
        const certificateProperties = certificateDetail.properties;
        const organizationCertificateProperties = organizationCertificate.properties;
        const resultProperties = this.certificateService.mergeProperties(
          certificateProperties,
          organizationCertificateProperties,
        );
        const { slugName } = certificateDetail;

        const dynamicValue = await this.getCertificateDynamicValue({
          resultProperties,
          organizationId: organization.id,
          userId,
          courseId: null,
          courseVersionId: null,
          learningPathId: learningPathVersion.learningPathId,
          learningPathVersionId: learningPathVersion.id,
          course: null,
          enrollment: null,
        });

        const certificatePayload = {
          dynamic_value: dynamicValue,
          slug_name: slugName,
          metadata_url: this.certificateService.genMetaDataUrl(
            learningPathEnrollmentCertificate.learningPathEnrollmentId,
            DomainMetaDataEnum.LEARNING_PATH_ENROLLMENT,
          ),
        };

        certificatePayloadWithLearningPathEnrollmentId.set(
          String(learningEnrollmentForSendCertification.id),
          certificatePayload,
        );
      }

      // Generate Certificate
      const certificateResults = await Promise.all(
        learningPathEnrollmentCertificates.map(async (learningPathEnrollmentCertificate) => {
          const key = String(learningPathEnrollmentCertificate.learningPathEnrollmentId);
          const payloadCertificate = certificatePayloadWithLearningPathEnrollmentId.get(key);

          if (isNil(payloadCertificate)) return null;

          const certificateResult = await this.certificateService.create(payloadCertificate);
          return { id: learningPathEnrollmentCertificate.id, result: certificateResult };
        }),
      );

      const certificateResultMapWithLearningPathEnrollmentCertificateId = certificateResults
        .filter((v) => !isNil(v))
        .reduce((acc, cur) => {
          if (!cur) return acc;
          acc.set(String(cur.id), cur.result);
          return acc;
        }, new Map<string, GenerateCertificateResult>());

      for (const learningPathEnrollmentCertificate of learningPathEnrollmentCertificates) {
        const id = String(learningPathEnrollmentCertificate.id);
        const certificateResult = certificateResultMapWithLearningPathEnrollmentCertificateId.get(id);

        if (isNil(certificateResult)) continue;

        const { certificateCode, certificateUrl, certificatePDFUrl } = certificateResult;

        learningPathEnrollmentCertificate.certificateCode = certificateCode;
        learningPathEnrollmentCertificate.certificateUrl = certificateUrl;
        learningPathEnrollmentCertificate.isSentEmail = true;
        learningPathEnrollmentCertificate.updatedAt = currentDate;
        newLearningPathEnrollmentCertificates.push(learningPathEnrollmentCertificate);

        this.logger.log(`Update learning path enrollment certificate ${id}`);

        const [learningPathEnrollment] =
          groupLearningPathEnrollmentForSendCertificateById[
            String(learningPathEnrollmentCertificate.learningPathEnrollmentId)
          ] ?? [];
        if (!learningPathEnrollment) continue;

        const key = `${learningPathEnrollment.learningPathId}_${learningPathEnrollment.version}`;
        const learningPathVersion = learningPathVersionsMapper.get(key);

        if (!learningPathVersion) continue;

        const learningPath = learningPathMapWithId.get(String(learningPathVersion.learningPathId));
        const isCompleteLearningPath = learningPathEnrollment.status === LearningPathEnrollmentStatusEnum.COMPLETED;
        const { refName } = learningPathEnrollmentCertificate.payload;

        if (isCompleteLearningPath) {
          const payload: MailPayloadLearningPathEnrollmentCompleteAndCertificateParams = {
            learningPathName: learningPathVersion.name,
            fullName: user.fullName,
            certificateCode,
            certificateUrl,
            certificatePDFUrl,
            refName: refName || '',
          };

          completeLearningPathAndCertificateEmailTemplates.push({ email: user.email, payload, organization });

          const inApplicationMessage = buildLearningPathCompleteWithCertificateNotificationMessage({
            contentName: learningPathVersion.name,
            isCertificate: true,
            isComplete: true,
          });

          const userNotification = await UserNotification.new({
            userId: learningPathEnrollment.userId,
            organizationId: learningPathEnrollment.organizationId,
            type: UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE,
            payload: {
              mediaId: learningPath?.thumbnailId ?? null,
              message: inApplicationMessage,
              url: {
                code: learningPath?.code,
                learningPathEnrollmentId: learningPathEnrollment.id,
                certificateUrl,
              },
            },
          });
          userNotificationCompleteLearningPathInApplication.push(userNotification);
        } else {
          const payload: MailPayloadLearningPathEnrollmentCertificateParams = {
            learningPathName: learningPathVersion.name,
            fullName: user.fullName,
            certificateCode,
            certificateUrl,
            certificatePDFUrl,
            refName: refName || '',
          };
          completeCertificateEmailTemplates.push({ email: user.email, payload, organization });

          const inApplicationMessage = buildLearningPathCompleteWithCertificateNotificationMessage({
            contentName: learningPathVersion.name,
            isCertificate: true,
            isComplete: false,
          });
          const userNotification = await UserNotification.new({
            userId: learningPathEnrollment.userId,
            organizationId: learningPathEnrollment.organizationId,
            type: UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE,
            payload: {
              mediaId: learningPath?.thumbnailId ?? null,
              message: inApplicationMessage,
              url: {
                code: learningPath?.code,
                learningPathEnrollmentId: learningPathEnrollment.id,
                certificateUrl,
              },
            },
          });

          userNotificationCompleteLearningPathInApplication.push(userNotification);
        }
      }

      certificatePayloadWithLearningPathEnrollmentId.clear();
      certificateResultMapWithLearningPathEnrollmentCertificateId.clear();
    }

    const completeNonCertificationLearningPathEnrollments = nonCertificationLearningPathEnrollments.filter(
      (nonCertificationLearningPathEnrollment) =>
        !isNil(nonCertificationLearningPathEnrollment.learningPathEnrollment.finishedAt),
    );

    if (!isEmpty(completeNonCertificationLearningPathEnrollments)) {
      for (const completeNonCertificationLearningPathEnrollment of completeNonCertificationLearningPathEnrollments) {
        const { learningPathVersion, learningPathEnrollment } = completeNonCertificationLearningPathEnrollment;
        const learningPath = learningPathMapWithId.get(String(learningPathVersion?.learningPathId));
        if (!learningPath) continue;

        const payload: MailPayloadLearningPathEnrollmentCompleteParams = {
          learningPathName: learningPathVersion?.name ?? '',
          learningPathCode: learningPath.code,
          fullName: user.fullName,
        };
        completeLearningPathEmailTemplates.push({ email: user.email, payload, organization });

        const inApplicationMessage = buildLearningPathCompleteWithCertificateNotificationMessage({
          contentName: learningPathVersion?.name ?? '',
          isCertificate: false,
          isComplete: true,
        });

        const userNotification = await UserNotification.new({
          userId: learningPathEnrollment.userId,
          organizationId: learningPathEnrollment.organizationId,
          type: UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED,
          payload: {
            mediaId: learningPath.thumbnailId,
            message: inApplicationMessage,
            url: {
              code: learningPath.code,
              learningPathEnrollmentId: learningPathEnrollment.id,
            },
          },
        });
        userNotificationCompleteLearningPathInApplication.push(userNotification);
      }
    }

    const updatedLearningPathEnrollmentsContentProgress = [
      ...map(nonCertificationLearningPathEnrollments, 'learningPathEnrollment'),
      ...map(certificationLearningPathEnrollments, 'learningPathEnrollment'),
    ];

    await this.databaseTransaction.runTransaction(async (session, commit, reverse) => {
      try {
        await this.learningPathEnrollmentRepository.saveMany(updatedLearningPathEnrollmentsContentProgress, {
          session,
        });

        this.logger.log('Save learning path enrollment');

        if (!isEmpty(newLearningPathEnrollmentCertificates)) {
          await this.learningPathEnrollmentCertificateRepository.saveMany(newLearningPathEnrollmentCertificates, {
            session,
          });
          this.logger.log('Save learning path enrollment certificate');
        }

        if (!isEmpty(userNotificationCompleteLearningPathInApplication)) {
          await this.userNotificationRepository.saveMany(userNotificationCompleteLearningPathInApplication, {
            session,
          });
        }

        // send in application
        for (const userNotification of userNotificationCompleteLearningPathInApplication) {
          const payload = {
            payload: { message: userNotification.payload.message },
            application: UserNotificationApplicationEnum.LMS,
            groupId: organization.id,
          };
          this.notificationService.sendInApplicationNotification(payload);
        }

        // send email
        const emailTasks: Promise<void>[] = [];
        for (const completeLearningPathEmailTemplate of completeLearningPathEmailTemplates) {
          const { email, payload, organization: _organization } = completeLearningPathEmailTemplate;
          emailTasks.push(this.notificationService.notifyLearningPathEnrollmentComplete(email, payload, _organization));
        }

        for (const completeCertificateEmailTemplate of completeCertificateEmailTemplates) {
          const { email, payload, organization: _organization } = completeCertificateEmailTemplate;
          emailTasks.push(
            this.notificationService.notifyLearningPathEnrollmentCertificate(email, payload, _organization),
          );
        }

        for (const completeLearningPathAndCertificateEmailTemplate of completeLearningPathAndCertificateEmailTemplates) {
          const { email, payload, organization: _organization } = completeLearningPathAndCertificateEmailTemplate;
          emailTasks.push(
            this.notificationService.notifyLearningPathEnrollmentCompleteAndCertificate(email, payload, _organization),
          );
        }

        for (const assignLearningPathEnrollmentCompleteEmailTemplate of assignLearningPathEnrollmentCompleteEmailTemplates) {
          const { email, payload, organization: _organization } = assignLearningPathEnrollmentCompleteEmailTemplate;
          emailTasks.push(this.notificationService.notifyAssignEnrollmentComplete(email, payload, _organization));
        }

        await Promise.all(emailTasks);
        await commit();
      } catch (error) {
        await reverse();
      }
    });

    // clean up memory
    learningPathVersionsMapper.clear();
    learningPathMapWithId.clear();
    roundMapWithId.clear();
    preAssignContentMapWithId.clear();
    assigneeMapWithId.clear();

    updatedLearningPathEnrollmentsContentProgress.splice(0, updatedLearningPathEnrollmentsContentProgress.length);
  }

  private async findAssigneeByUserIds(userIds: GenericID[]): Promise<User[]> {
    const users = await this.userRepository.find({ guid: { $in: userIds } });
    if (users.length > 0) return users;
    return [];
  }

  private async requestRejected(id: GenericID, organization: Organization): Promise<void> {
    const datas = await this.enrollmentRepository.aggregate([
      { $match: { id } },
      {
        $lookup: {
          from: 'course-versions',
          let: { courseVersionId: '$courseVersionId' },
          pipeline: [
            { $match: { $expr: { $eq: ['$id', '$$courseVersionId'] } } },
            {
              $project: {
                id: 1,
                name: 1,
              },
            },
          ],
          as: 'courseVersion',
        },
      },
      { $unwind: '$courseVersion' },
      {
        $lookup: {
          from: 'users',
          let: { userId: '$userId' },
          pipeline: [
            { $match: { $expr: { $eq: ['$guid', '$$userId'] } } },
            {
              $project: {
                guid: '$guid',
                profile: '$profile',
                email: '$email',
              },
            },
          ],
          as: 'user',
        },
      },
      { $unwind: '$user' },
      {
        $project: {
          id: 1,
          courseVersion: 1,
          user: 1,
        },
      },
    ]);

    const data = datas[0];

    if (!data) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { id },
      });
    }

    const { courseVersion, user } = data;

    const {
      profile: { firstname, lastname },
      email,
    } = user as User;

    const { name: courseName } = courseVersion as CourseVersion;

    const fullName = `${firstname} ${lastname}`;

    const emailData = { courseName, fullName };

    await this.notificationService.notifyRejected(email, emailData, organization);
  }

  private async requestSendAssignEnrollmentCompleteNotification(params: {
    assignee: User;
    learner: User;
    enrollment: Enrollment;
    organization: Organization;
    course: Course;
    round: Nullable<Round>;
  }): Promise<void> {
    const { assignee, learner, enrollment, organization, course, round } = params;

    const emailPayload: MailPayloadAssignEnrollmentCompleteParams = {
      learnerUserId: learner.guid,
      contentName: course.courseVersion?.name || '',
      contentType: PreAssignContentTypeEnum.COURSE,
      fullName: assignee.fullName,
      learnerFullName: learner.fullName,
      round: {
        roundDate: round?.roundDate,
        expiredDate: enrollment.expiredAt,
      },
    };

    await this.notificationService.notifyAssignEnrollmentComplete(assignee.email, emailPayload, organization);

    const { message, userNotification } = await this.buildAssignEnrollmentCompleteMessageAndInAppUserNotificationModel({
      course,
      enrollment,
      round,
      assignee,
      fullName: learner.fullName,
    });

    const payloadInApplication = {
      payload: { message },
      application: UserNotificationApplicationEnum.LMS,
      groupId: organization.id,
    };

    await this.userNotificationRepository.save(userNotification);
    await this.notificationService.sendInApplicationNotification(payloadInApplication);
  }

  private async buildAssignEnrollmentCompleteMessageAndInAppUserNotificationModel(params: {
    course: Course;
    enrollment: Enrollment;
    round: Nullable<Round>;
    assignee: User;
    fullName: string;
  }): Promise<{ message: string; userNotification: Nullable<UserNotification> }> {
    const { course, enrollment, round, assignee, fullName } = params;

    const message = buildAssignEnrollmentCompletedNotificationMessage({
      fullName: fullName || '',
      contentName: course.courseVersion?.name || '',
      roundDate: round?.roundDate,
      expiryDay: course.courseVersion?.expiryDay || 0,
    });

    const userNotification = await UserNotification.new({
      userId: assignee.guid, //supervisor
      organizationId: enrollment.organizationId,
      type: UserNotificationTypeEnum.ASSIGN_ENROLLMENT_COMPLETED,
      payload: {
        mediaId: course.thumbnailId,
        message,
        url: {
          code: course.code,
          userId: enrollment.userId,
          enrollmentId: enrollment.id,
        },
      },
    });

    return { message, userNotification };
  }

  private async requestCertificate(enrollmentId: GenericID, organization: Organization): Promise<any[]> {
    const enrollment = await this.enrollmentRepository.findById(enrollmentId);

    if (!enrollment?.expiredAt) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { enrollmentId },
      });
    }

    const { courseId, courseVersionId, userId, expiredAt, roundId } = enrollment;

    const course = await this.courseRepository.findById(courseId);

    if (!course) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { courseVersionId },
      });
    }

    const courseVersion = await this.courseVersionRepository.findById(courseVersionId);

    if (!courseVersion) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { courseVersionId },
      });
    }

    const { url: courseUrl, regulatorInfo } = course;

    const { isCertificateEnabled, name: courseName } = courseVersion;

    if (!isCertificateEnabled) return [];

    const round = await this.roundRepository.findById(roundId);
    if (!round) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { roundId },
      });
    }

    if (!isCertificateEnabled) return [];

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { userId },
      });
    }

    const {
      email,
      profile: { firstname, lastname },
    } = user;
    const fullName = `${firstname} ${lastname}`;

    const listEnrollmentCertificate =
      await this.enrollmentCertificateRepository.findWithOrganizationCertificateDetail(enrollmentId);

    if (listEnrollmentCertificate.length === 0) return [];
    if (listEnrollmentCertificate.length > 0 && !listEnrollmentCertificate[0]?.certificateDetail.slugName) {
      return [];
    }

    const enrollmentAttachment = await this.enrollmentAttachmentRepository.findOne({
      enrollmentId,
      fileType: EnrollmentAttachmentTypeEnum.DEDUCT,
    });

    const promiseAllPretestGenerateCertificate: any[] = [];
    const promiseAllGenerateCertificate: any[] = [];

    for (const enrollmentCertificate of listEnrollmentCertificate) {
      const { certificateDetail, organizationCertificate } = enrollmentCertificate;
      const certificateProperties = certificateDetail.properties;
      const organizationCertificateProperties = organizationCertificate.properties;
      const resultProperties = this.certificateService.mergeProperties(
        certificateProperties,
        organizationCertificateProperties,
      );
      const { slugName } = certificateDetail;

      const dynamicValue = await this.getCertificateDynamicValue({
        resultProperties,
        organizationId: organization.id,
        userId: user.guid,
        courseId,
        courseVersionId,
        learningPathId: null,
        learningPathVersionId: null,
        course,
        enrollment,
      });

      const certificatePayload = {
        dynamic_value: dynamicValue,
        slug_name: slugName,
        metadata_url: this.certificateService.genMetaDataUrl(enrollmentId, DomainMetaDataEnum.ENROLLMENT),
      };

      promiseAllPretestGenerateCertificate.push(this.certificateService.pretestGenerateCertificate(certificatePayload));
      promiseAllGenerateCertificate.push(this.generateCertificate(enrollmentCertificate.id, certificatePayload));
    }

    // Generate certificate
    this.logger.log(`Process generate certificate for enrollment ${enrollmentId}`);
    await Promise.all(promiseAllPretestGenerateCertificate);
    const resGenerateCertList = await Promise.all(promiseAllGenerateCertificate);
    this.logger.log(`Success generate certificate for enrollment ${enrollmentId}`);

    const isDeduct = !!enrollmentAttachment;
    const isDeductApproved = enrollmentAttachment
      ? enrollmentAttachment.status === EnrollmentAttachmentStatusEnum.APPROVED
      : false;
    const operationExpiredDate = date(expiredAt).add(6, 'day').toDate();
    const operationExpiredDateTH = formatDateInThaiLocale(operationExpiredDate, TimeZoneEnum.Bangkok);

    const isEnabledSentCertificateEmailOnExpiredDate = !!listEnrollmentCertificate.find(
      (val) => val.isSentEmailOnExpiredDate,
    );
    const isEnrollmentNotExpired = date(date().toDate()).isBefore(date(expiredAt).toDate());

    if (isEnabledSentCertificateEmailOnExpiredDate && isEnrollmentNotExpired) {
      const startDateThaiDate = formatDateInThaiLocale(round.roundDate, TimeZoneEnum.Bangkok);
      const endDateThaiDate = formatDateInThaiLocale(expiredAt, TimeZoneEnum.Bangkok);
      const sendDate = date(expiredAt).add(1, 'day').toDate();
      const sentDateThaiDate = formatDateInThaiLocale(sendDate, TimeZoneEnum.Bangkok);

      const emailData: MailPayloadEnrollmentApproveParams = {
        fullName,
        courseName,
        isDeduct,
        isDeductApproved,
        objectiveType: course.objectiveType,
        startDate: startDateThaiDate,
        endDate: endDateThaiDate,
        sendDate: sentDateThaiDate,
        operationExpiredDate: operationExpiredDateTH,
        regulator: course.regulatorInfo?.regulator,
      };

      await this.notificationService.notifyApproved(email, emailData, organization);

      const message = buildApprovedNotificationMessage(courseName, isDeduct, isDeductApproved);

      const userNotification = await UserNotification.new({
        userId,
        organizationId: organization.id,
        type: UserNotificationTypeEnum.ENROLLMENT_APPROVED,
        payload: {
          message,
          url: {
            code: courseUrl,
            enrollmentId,
          },
        },
      });

      const payloadInApplication = {
        payload: { message },
        application: UserNotificationApplicationEnum.LMS,
        groupId: organization.id,
      };

      await this.userNotificationRepository.save(userNotification);

      await this.notificationService.sendInApplicationNotification(payloadInApplication);

      for (const item of resGenerateCertList) {
        const { id, certificateUrl, certificateCode } = item;

        const filterEnrollmentCert = listEnrollmentCertificate.find((val) => val.id === id);

        if (filterEnrollmentCert) {
          filterEnrollmentCert.certificateUrl = certificateUrl;
          filterEnrollmentCert.certificateCode = certificateCode;
          filterEnrollmentCert.isSentEmail = false;

          await this.enrollmentCertificateRepository.updateOne(
            {
              id: filterEnrollmentCert.id,
            },
            {
              $set: {
                certificateUrl,
                certificateCode,
                isSentEmail: false,
              },
            },
          );
          this.logger.log(`Save enrollment certificate ${filterEnrollmentCert.id}`);
        }
      }

      return resGenerateCertList;
    } else {
      for (const item of resGenerateCertList) {
        const { id, certificateUrl, certificatePDFUrl, certificateCode } = item;

        const foundEnrollmentCertificate = listEnrollmentCertificate.find((val) => val.id === id);
        const { courseVersionCertificate } = foundEnrollmentCertificate;
        const emailData: MailPayloadEnrollmentCompleteAndCertificateParams = {
          fullName,
          courseName,
          isDeduct,
          isDeductApproved,
          certificateUrl,
          certificatePDFUrl,
          certificateCode,
          operationExpiredDate: operationExpiredDateTH,
          refName: courseVersionCertificate?.refName,
          objectiveType: course.objectiveType,
          regulator: regulatorInfo?.regulator,
        };
        await this.notificationService.notifyEnrollmentCompleteWithCertificate(email, emailData, organization);

        const message = buildApprovedWithCertificateNotificationMessage(
          courseName,
          isDeduct,
          isDeductApproved,
          courseVersionCertificate?.refName,
        );

        const userNotification = await UserNotification.new({
          userId,
          organizationId: organization.id,
          type: UserNotificationTypeEnum.ENROLLMENT_APPROVED_WITH_CERTIFICATE,
          payload: {
            mediaId: course.thumbnailMediaId,
            message,
            url: {
              certificateUrl,
              enrollmentId,
            },
          },
        });

        await this.userNotificationRepository.save(userNotification);

        const payloadInApplication = {
          payload: { message },
          application: UserNotificationApplicationEnum.LMS,
          groupId: organization.id,
        };

        await this.notificationService.sendInApplicationNotification(payloadInApplication);

        if (foundEnrollmentCertificate) {
          foundEnrollmentCertificate.certificateUrl = certificateUrl;
          foundEnrollmentCertificate.certificateCode = certificateCode;
          foundEnrollmentCertificate.isSentEmail = true;
          await this.enrollmentCertificateRepository.updateOne(
            {
              id: foundEnrollmentCertificate.id,
            },
            {
              $set: {
                certificateUrl,
                certificateCode,
                isSentEmail: true,
              },
            },
          );
          this.logger.log(`Save enrollment certificate ${foundEnrollmentCertificate.id}`);
        }
      }
    }

    return resGenerateCertList;
  }

  private async generateCertificate(id: GenericID, payload: GenerateCertificateParams) {
    const resCert = await this.certificateService.create(payload);

    return {
      id,
      ...resCert,
    };
  }

  private async mainRepositoryFactory(collection: string, pipeline: Record<string, unknown>[]): Promise<ObjectValue[]> {
    switch (collection) {
      case ColumnSettingNameCollectionEnum.USERS: {
        return this.userRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.COURSES: {
        return this.courseRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.LEARNING_PATHS: {
        return this.learningPathRepository.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  private async repositoryFactory(collection: string, pipeline: ObjectValue[]): Promise<ObjectValue[]> {
    switch (collection) {
      case ColumnSettingNameCollectionEnum.USERS: {
        return this.userRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.LICENSES: {
        return this.licenseRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.DEPARTMENTS: {
        return this.departmentRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.USER_DIRECT_REPORTS: {
        return this.userDirectReportRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.CUSTOMERS: {
        return this.customerRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.COURSE_VERSIONS: {
        return this.courseVersionRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.LEARNING_PATH_VERSIONS: {
        return this.courseVersionRepository.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  private async getCertificateDynamicValue(params: {
    resultProperties: OrganizationCertificatePropertyWithNameParams[];
    organizationId: GenericID;
    userId: GenericID;
    courseId: GenericID;
    courseVersionId: GenericID;
    learningPathId: GenericID;
    learningPathVersionId: GenericID;
    course: Course;
    enrollment: Enrollment;
  }): Promise<Record<string, any>> {
    const {
      resultProperties,
      organizationId,
      userId,
      courseId,
      courseVersionId,
      learningPathId,
      learningPathVersionId,
      course,
      enrollment,
    } = params;

    const currentDate = date().toDate();
    const transformedCertificatePropertiesData = resultProperties.map((item) => {
      const key = ['t', 'i'].some((prefix) => item.key.startsWith(prefix)) ? item.key.slice(1) : item.key;
      const columnSettingParts = item.columnSettingKey ? item.columnSettingKey.split('.') : [null, null];

      return {
        key,
        columnSettingModule: columnSettingParts[0],
        columnSettingKey: item.columnSettingKey,
        type: item.type,
        value: item.value,
        mediaId: item.mediaId ? item.mediaId : null,
        name: item.name,
      };
    });

    const dynamicValue: Record<string, any> = {};
    for (const item of transformedCertificatePropertiesData) {
      if (item.type === CertificatePropertyTypeEnum.CURRENT_DATE) {
        const isIssuedDate = item.key === 'issueddate';
        const issuedDate =
          isIssuedDate && course?.objectiveType === CourseObjectiveTypeEnum.TRAINING
            ? enrollment?.expiredAt
            : currentDate;

        dynamicValue[item.key] = getDateLocale(item.value as string, issuedDate);
      } else if (item.type === CertificatePropertyTypeEnum.COLUMN_SETTING) {
        const columnSettings = await this.columnSettingRepository.findColumnSettingWithTemplate(item.columnSettingKey);

        const organizationColumnSettings = await this.organizationColumnSettingRepository.findColumnSettingWithTemplate(
          organizationId,
          item.columnSettingKey,
        );

        const columnSettingData = columnSettings.length > 0 ? columnSettings : organizationColumnSettings;

        if (item.columnSettingModule === CertificatePropertyModuleEnum.USER) {
          const userData = await this.buildAggregateCertificateAdaptorService.getUserColumnSettingData(
            organizationId,
            userId,
            columnSettings,
            (collection: string, pipeline: Record<string, unknown>[]) =>
              this.mainRepositoryFactory(collection, pipeline),
            (collection: string, pipeline: Record<string, unknown>[]) => this.repositoryFactory(collection, pipeline),
          );
          const userValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            userData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = userValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.LEARNING_PATH) {
          const learningPathData = await this.buildAggregateCertificateAdaptorService.getLearningPathColumnSettingData(
            organizationId,
            learningPathId,
            learningPathVersionId,
            columnSettings,
            (collection: string, pipeline: ObjectValue[]) => this.mainRepositoryFactory(collection, pipeline),
            (collection: string, pipeline: ObjectValue[]) => this.repositoryFactory(collection, pipeline),
          );
          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            learningPathData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.COURSE) {
          const courseData = await this.buildAggregateCertificateAdaptorService.getCourseColumnSettingData(
            organizationId,
            courseId,
            courseVersionId,
            columnSettings,
            (collection: string, pipeline: ObjectValue[]) => this.mainRepositoryFactory(collection, pipeline),
            (collection: string, pipeline: ObjectValue[]) => this.repositoryFactory(collection, pipeline),
          );

          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            courseData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        }
      } else if (item.type === CertificatePropertyTypeEnum.TEXT) {
        dynamicValue[item.key] = item.value;
      } else if (item.type === CertificatePropertyTypeEnum.IMAGE_URL) {
        const media = await this.mediaRepository.findOne({ id: item.mediaId });
        const organizationStorage = await this.organizationStorageRepository.findOne({
          organizationId: media.organizationId,
          storageType: OrganizationStorageTypeEnum.RESOURCE,
        });
        const mediaPath = this.materialMediaService.getMediaURL(media, organizationStorage);
        dynamicValue[item.key] = mediaPath;
      }
    }
    return dynamicValue;
  }
}
