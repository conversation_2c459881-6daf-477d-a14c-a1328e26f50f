import { RegulatorEnum } from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { BusinessTypeEnum, EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import {
  DeductionTypeCodeEnum,
  DeductionTypeEnum,
  DeductionTypeNameEnum,
  EnrollmentAttachmentStatusEnum,
  EnrollmentAttachmentTypeEnum,
} from '@iso/lms/enums/enrollmentAttachment.enum';
import { UserLicenseTypeCodeEnum } from '@iso/lms/enums/license.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { DeductAttachmentPayload, FormCriteriaEnrollmentAttachment } from '@iso/lms/types/enrollmentAttachment.type';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { orderBy, partition, sortBy } from 'lodash';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';
import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { LicenseDIToken } from '@applications/di/domain/license.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { EnrollmentStatusTextEnum } from '@constants/enums/enrollment.enum';
import {
  DeductionTypeFullNameEnum,
  EnrollmentAttachmentStatusTextEnum,
} from '@constants/enums/enrollmentAttachment.enum';
import { columnOICDeductReport } from '@constants/enums/excel.enum';
import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { OICExtendYearTypeTextEnum } from '@constants/enums/preEnrollmentTransaction.enum';
import { DeductAttachmentPriorityParams } from '@constants/types/enrollmentAttachment.type';
import { UploadFileParams } from '@constants/types/infrastructures/storage.type';
import {
  BuildOICDeductReportParams,
  ExportOICDeductReportExcelParams,
  FilterExportOICDeductReportParams,
} from '@constants/types/reportHistory.type';

import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { ILicenseRepository } from '@interfaces/repositories/license.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { IEnrollmentService } from '@interfaces/services/enrollment.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportOICDeductReportUseCase } from '@interfaces/usecases/report.interface';

import { buddhistOffset, date, DateFormat } from '@domains/utils/date.util';

@Injectable()
export class ExportOICDeductReportUseCase implements IExportOICDeductReportUseCase {
  constructor(
    @Inject(RoundDIToken.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(ReportDIToken.ReportHistoryRepository)
    private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(LicenseDIToken.LicenseRepository)
    private readonly licenseRepository: ILicenseRepository,
    @Inject(EnrollmentDIToken.EnrollmentService)
    private readonly enrollmentService: IEnrollmentService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(filter: FilterExportOICDeductReportParams): Promise<void> {
    const { reportHistoryId, roundDateFrom, roundDateTo } = filter;

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist.',
        data: { reportHistoryId },
      });
    }

    try {
      const deductReportDataList = await this.getDeductReportDataList(filter);
      const excelRecords = await this.buildDeductReportExcel(deductReportDataList);
      const excel = new ExcelBuilderService();
      const sheetName = 'Sheet1';
      excel
        .addWorksheet(sheetName)
        .setColumnHeader(columnOICDeductReport, sheetName)
        .buildRawFileExcel([{ workSheetName: 'Sheet1', rowsData: excelRecords }]);
      const buffer = await excel.getFileBuffer();

      const fileName = `approval_deduct_${date(roundDateFrom).format(DateFormat.yearMonthDay)}-${date(
        roundDateTo,
      ).format(DateFormat.yearMonthDay)}`;
      const systemFileName = `${Date.now()}_${fileName}.xlsx`;
      const targetKey = `xlsx/reports/${systemFileName}`;
      const uploadFileParams: UploadFileParams = {
        raw: buffer,
        path: targetKey,
        fileType: UploadFileTypeEnum.XLSX,
      };
      await this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS).upload(uploadFileParams);

      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      const reportHistoryFile = new ReportHistoryFile({
        fileName,
        filePath: targetKey,
      });
      reportHistory.files.push(reportHistoryFile);

      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  private async getDeductReportDataList(
    filter: FilterExportOICDeductReportParams,
  ): Promise<BuildOICDeductReportParams[]> {
    const { organizationId, roundDateFrom, roundDateTo, trainingCenter, enrollmentStatus, deductReportStatus } = filter;

    const trainingCenterQuery = [];
    const enrollmentQuery = [];

    if (trainingCenter) {
      trainingCenterQuery.push({
        $expr: {
          $eq: ['$regulatorInfo.trainingCenter', trainingCenter],
        },
      });
    }

    const deductReportStatusQuery = [];

    if (!deductReportStatus) {
      deductReportStatusQuery.push({
        $expr: {
          $in: [
            '$status',
            [
              EnrollmentAttachmentStatusEnum.NOT_SENT,
              EnrollmentAttachmentStatusEnum.PENDING_APPROVAL,
              EnrollmentAttachmentStatusEnum.APPROVED,
              EnrollmentAttachmentStatusEnum.REJECTED,
              EnrollmentAttachmentStatusEnum.CANCELED,
            ],
          ],
        },
      });
    } else {
      deductReportStatusQuery.push({
        $expr: {
          $eq: ['$status', deductReportStatus],
        },
      });
    }

    const rounds = await this.roundRepository.aggregate([
      {
        $match: {
          organizationId,
          roundDate: {
            $gte: date(roundDateFrom).startOf('day').toDate(),
            $lte: date(roundDateTo).endOf('day').toDate(),
          },
        },
      },
    ]);

    const roundIds = rounds.map((val) => val.id);

    if (!enrollmentStatus) {
      enrollmentQuery.push({
        status: {
          $in: [
            EnrollmentStatusEnum.IN_PROGRESS,
            EnrollmentStatusEnum.PASSED,
            EnrollmentStatusEnum.PENDING_RESULT,
            EnrollmentStatusEnum.EXPIRED,
            EnrollmentStatusEnum.CANCELED,
            EnrollmentStatusEnum.PENDING_APPROVAL,
            EnrollmentStatusEnum.VERIFIED,
            EnrollmentStatusEnum.APPROVED,
            EnrollmentStatusEnum.REJECTED,
          ],
        },
      });
    }

    if (enrollmentStatus === EnrollmentStatusEnum.IN_PROGRESS) {
      enrollmentQuery.push({
        $or: [
          {
            status: EnrollmentStatusEnum.IN_PROGRESS,
            expiredAt: {
              $gt: date().toDate(),
            },
          },
        ],
      });
    }

    if (enrollmentStatus === EnrollmentStatusEnum.PASSED) {
      enrollmentQuery.push({
        $or: [
          {
            status: EnrollmentStatusEnum.PASSED,
            expiredAt: {
              $gt: date().toDate(),
            },
          },
        ],
      });
    }

    if (enrollmentStatus === EnrollmentStatusEnum.PENDING_APPROVAL) {
      enrollmentQuery.push({
        $or: [
          {
            status: EnrollmentStatusEnum.PENDING_APPROVAL,
          },
          {
            status: EnrollmentStatusEnum.PASSED,
            expiredAt: {
              $lte: date().toDate(),
            },
          },
        ],
      });
    }

    if (enrollmentStatus === EnrollmentStatusEnum.EXPIRED) {
      enrollmentQuery.push({
        $or: [
          {
            status: EnrollmentStatusEnum.IN_PROGRESS,
            expiredAt: {
              $lte: date().toDate(),
            },
          },
          { status: EnrollmentStatusEnum.EXPIRED },
        ],
      });
    }

    if (
      enrollmentStatus === EnrollmentStatusEnum.PENDING_RESULT ||
      enrollmentStatus === EnrollmentStatusEnum.VERIFIED ||
      enrollmentStatus === EnrollmentStatusEnum.APPROVED ||
      enrollmentStatus === EnrollmentStatusEnum.REJECTED ||
      enrollmentStatus === EnrollmentStatusEnum.CANCELED
    ) {
      enrollmentQuery.push({
        $or: [
          {
            status: { $in: enrollmentStatus },
          },
        ],
      });
    }

    const aggregateParams = [
      {
        $match: {
          $and: [{ roundId: { $in: roundIds } }, ...enrollmentQuery],
        },
      },
      {
        $lookup: {
          from: 'rounds',
          localField: 'roundId',
          foreignField: 'id',
          as: 'round',
        },
      },
      { $unwind: '$round' },
      {
        $lookup: {
          from: 'enrollment-attachments',
          let: { enrollmentId: '$id' },
          pipeline: [
            {
              $match: {
                $and: [
                  {
                    $expr: {
                      $eq: ['$enrollmentId', '$$enrollmentId'],
                    },
                  },
                  {
                    $expr: {
                      $eq: ['$fileType', EnrollmentAttachmentTypeEnum.DEDUCT],
                    },
                  },
                  ...deductReportStatusQuery,
                ],
              },
            },
          ],
          as: 'enrollmentAttachment',
        },
      },
      { $unwind: '$enrollmentAttachment' },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: 'guid',
          as: 'user',
        },
      },
      { $unwind: '$user' },
      {
        $lookup: {
          from: 'courses',
          let: { courseId: '$courseId' },
          pipeline: [
            {
              $match: {
                $and: [
                  {
                    $expr: {
                      $eq: ['$id', '$$courseId'],
                    },
                  },
                  {
                    $expr: {
                      $eq: ['$regulatorInfo.regulator', RegulatorEnum.OIC],
                    },
                  },
                  ...trainingCenterQuery,
                ],
              },
            },
            {
              $lookup: {
                from: 'course-versions',
                let: { courseId: '$id' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          {
                            $eq: ['$courseId', '$$courseId'],
                          },
                          {
                            $eq: ['$status', CourseVersionStatusEnum.PUBLISHED],
                          },
                        ],
                      },
                    },
                  },
                  {
                    $limit: 1,
                  },
                ],
                as: 'courseVersion',
              },
            },
            { $unwind: '$courseVersion' },
          ],
          as: 'coursesWithCourseVersion',
        },
      },
      { $unwind: '$coursesWithCourseVersion' },
      {
        $lookup: {
          from: 'registrations',
          localField: 'id',
          foreignField: 'enrollmentId',
          as: 'registration',
        },
      },
      {
        $unwind: {
          path: '$registration',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'enrollment-regulator-reports',
          localField: 'registration.id',
          foreignField: 'registrationId',
          as: 'enrollmentRegulatorReport',
        },
      },
      {
        $unwind: {
          path: '$enrollmentRegulatorReport',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'pre-enrollment-transactions',
          localField: 'enrollmentRegulatorReport.preEnrollmentTransactionId',
          foreignField: 'id',
          as: 'preEnrollmentTransaction',
        },
      },
      {
        $unwind: {
          path: '$preEnrollmentTransaction',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $limit: 1000, // default limit records
      },
    ];

    const buildOICDeductReportList =
      await this.enrollmentRepository.aggregate<BuildOICDeductReportParams>(aggregateParams);

    const [dataWithEID, dataWithoutEID] = partition(
      buildOICDeductReportList,
      (item) => item?.enrollmentRegulatorReport?.id,
    );

    const sortedDataWithEID = sortBy(dataWithEID, ['enrollmentRegulatorReport.id', 'createdAt']);
    const sortedDataWithoutEID = sortBy(dataWithoutEID, 'createdAt');

    return [...sortedDataWithEID, ...sortedDataWithoutEID];
  }

  private async buildDeductReportExcel(
    dataList: BuildOICDeductReportParams[],
  ): Promise<ExportOICDeductReportExcelParams[]> {
    const result: ExportOICDeductReportExcelParams[] = [];
    const fileNames = [];
    for (const enrollment of dataList) {
      const {
        id: enrollmentId,
        customerCode,
        business,
        round,
        user,
        enrollmentRegulatorReport,
        preEnrollmentTransaction,
        coursesWithCourseVersion,
        enrollmentAttachment: deductAttachment,
      } = enrollment;

      const eid = enrollmentRegulatorReport?.id;
      const roundDate = date(round.roundDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero);
      const newEnrollmentStatus = this.enrollmentService.transformEnrollmentStatus(
        enrollment.status,
        enrollment.expiredAt,
      );

      const enrollmentStatus = EnrollmentStatusTextEnum[newEnrollmentStatus];
      const enrollmentAttachmentStatus = EnrollmentAttachmentStatusTextEnum[deductAttachment.status];
      const trainingCenter = coursesWithCourseVersion?.regulatorInfo?.trainingCenter;
      const courseCode = coursesWithCourseVersion.code;
      const courseName = coursesWithCourseVersion.courseVersion.name;
      const oicExtendYearType = OICExtendYearTypeTextEnum[preEnrollmentTransaction?.payload?.oicExtendYearType];
      const companyCode = business === BusinessTypeEnum.B2C ? BusinessTypeEnum.B2C : customerCode;

      // user
      const {
        citizenId,
        email,
        profile: { mobilePhoneNumber, salute: saluteText, firstname: firstName, lastname: lastName },
      } = user;
      const salute = this.convertSalute(saluteText);

      // license
      let oicLifeLicense: string;
      let oicNonLifeLicense: string;
      if (preEnrollmentTransaction) {
        oicLifeLicense = preEnrollmentTransaction?.payload?.oicLicenseLifeNo;
        oicNonLifeLicense = preEnrollmentTransaction?.payload?.oicLicenseNonLifeNo;
      } else {
        const license = await this.licenseRepository.find({ userId: user.guid });
        oicLifeLicense = license.find((item) => item.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_LIFE)?.licenseNo;
        oicNonLifeLicense = license.find(
          (item) => item.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_NON_LIFE,
        )?.licenseNo;
      }

      const prefix = user?.regulatorProfile?.prefix;
      const oicSalute = this.convertSalute(prefix);
      const oicFirstName = user?.regulatorProfile?.firstname;
      const oicLastName = user?.regulatorProfile?.lastname;

      // enrollment attachment
      const { criteria } = deductAttachment.payload as DeductAttachmentPayload;

      let deductions = this.getDeductAttachmentPriority(criteria);
      deductions = orderBy(deductions, ['isVerified', 'priority'], ['desc', 'asc']);
      const deduction = deductions.find(
        (val: DeductAttachmentPriorityParams) => val.data.checked && val.data.isVerified,
      );

      const deductionTypeCode = deduction?.deductionTypeCode;
      const deductionTypeName = DeductionTypeFullNameEnum[deduction?.deductionType];
      const life = deduction?.life;
      const nonLife = deduction?.nonLife;
      let deductHourAgent: string;
      let deductHourBroker: string;

      if (deduction && deductAttachment.status === EnrollmentAttachmentStatusEnum.APPROVED) {
        const isInstructor = deduction.deductionTypeCode === DeductionTypeCodeEnum.INSTRUCTOR;
        deductHourAgent = isInstructor ? '00.00' : '15.00';
        deductHourBroker = isInstructor ? '00.00' : '25.00';
      }

      let userFirstName = firstName;
      let userLastName = lastName;
      const { regulatorProfile } = user || {};

      if (regulatorProfile?.firstname || regulatorProfile?.lastname) {
        userFirstName = regulatorProfile.firstname?.trim() || '';
        userLastName = regulatorProfile.lastname?.trim() || '';
      }

      // educate mater degree
      let universityCode: string;
      let universityName: string;
      let universityNameInter: string;
      let universityMajor: string;

      // certificate
      let certificateType: string;
      let certificateEffectedYear: string;
      let certificateIssuedBy: string;
      let certificateEffectedDate: string;

      // instructor
      let instructorStartDate: string;
      let instructorEndDate: string;
      let instructorProgram: string;
      let instructorCourseRef: string;
      let instructorYear: string;
      let instructorMonth: string;
      let instructorHours: string;

      const deductionEducateMasterDegree = deductions.find(
        (val: DeductAttachmentPriorityParams) =>
          val.data.checked && val.data.isVerified && val.deductionTypeCode === DeductionTypeCodeEnum.MASTER_DEGREE,
      );

      const deductionCertificate = deductions.find(
        (val: DeductAttachmentPriorityParams) =>
          val.data.checked &&
          val.data.isVerified &&
          ![DeductionTypeCodeEnum.MASTER_DEGREE, DeductionTypeCodeEnum.INSTRUCTOR].includes(val.deductionTypeCode),
      );

      const deductionInstructor = deductions.find(
        (val: DeductAttachmentPriorityParams) =>
          val.data.checked && val.data.isVerified && val.deductionTypeCode === DeductionTypeCodeEnum.INSTRUCTOR,
      );

      if (deductionEducateMasterDegree) {
        const { data: educateMasterDegree } = deductionEducateMasterDegree;
        const foreignUniversityCode = '998';
        universityCode = educateMasterDegree.universityCode || '';
        universityName =
          educateMasterDegree.universityCode !== foreignUniversityCode ? educateMasterDegree.universityName : '';
        universityNameInter =
          educateMasterDegree.universityCode === foreignUniversityCode ? educateMasterDegree.universityName : '';
        universityMajor = educateMasterDegree.studyMajor;
      }

      if (deductionCertificate) {
        const { data: certificate, deductionType } = deductionCertificate;
        certificateType = deductionType;
        certificateEffectedYear = (date(certificate.effectedDate).year() + buddhistOffset)?.toString();

        certificateIssuedBy = certificate.issuedBy;
        certificateEffectedDate = certificate.effectedDate
          ? date(certificate.effectedDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)
          : '';
      }

      if (deductionInstructor) {
        const { data: instructor } = deductionInstructor;
        instructorStartDate = instructor.startedDate
          ? date(instructor.startedDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)
          : '';
        instructorEndDate = instructor.endedDate
          ? date(instructor.endedDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero)
          : '';
        instructorProgram = instructor.program;
        instructorCourseRef = instructor.courseRef;

        const instructorStartedDate = instructor.startedDate ? date(instructor.startedDate) : '';
        const instructorEndedDate = instructor.endedDate ? date(instructor.endedDate) : '';

        if (instructorStartedDate && instructorEndedDate) {
          const yearDiff = instructorEndedDate.diff(instructorStartedDate, 'year');
          const monthDiff = instructorEndedDate.diff(instructorStartedDate.add(yearDiff, 'year'), 'month');

          instructorYear = yearDiff.toString();
          instructorMonth = monthDiff.toString();
        }

        instructorHours = instructor.hours.toString();
      }

      let counter = 1;
      let fileName: string;
      if (
        deductAttachment.status === EnrollmentAttachmentStatusEnum.APPROVED &&
        deductAttachment?.attachmentApprovalFile?.filePath
      ) {
        fileName = `${citizenId}${userFirstName ? `_${userFirstName}` : ''}${
          userLastName ? `_${userLastName}` : ''
        }.pdf`;
        while (fileNames.includes(fileName)) {
          fileName = `${citizenId}${userFirstName ? `_${userFirstName}` : ''}${
            userLastName ? `_${userLastName}` : ''
          }(${counter++}).pdf`;
        }
        fileNames.push(fileName);
      }

      result.push({
        eid,
        enrollmentId,
        roundDate,
        enrollmentStatus,
        enrollmentAttachmentStatus,
        trainingCenter,
        courseCode,
        courseName,
        oicExtendYearType,
        companyCode,
        citizenId,
        oicLifeLicense,
        oicNonLifeLicense,
        salute,
        firstName,
        lastName,
        oicSalute,
        oicFirstName,
        oicLastName,
        mobilePhoneNumber,
        email,
        deductionTypeCode,
        deductionTypeName,
        life,
        nonLife,
        deductHourAgent,
        deductHourBroker,
        certificateType,
        certificateEffectedYear,
        certificateIssuedBy,
        certificateEffectedDate,
        universityCode,
        universityName,
        universityNameInter,
        universityMajor,
        instructorStartDate,
        instructorEndDate,
        instructorProgram,
        instructorCourseRef,
        instructorYear,
        instructorMonth,
        instructorHours,
        fileName,
      });
    }

    return result;
  }

  private getDeductAttachmentPriority(criteria: FormCriteriaEnrollmentAttachment): DeductAttachmentPriorityParams[] {
    const { educateMasterDegree, certificateType } = criteria;
    const deductions: DeductAttachmentPriorityParams[] = [];

    // MASTER_DEGREE
    deductions.push({
      deductionType: DeductionTypeEnum.MASTER_DEGREE,
      deductionTypeName: DeductionTypeNameEnum.MASTER_DEGREE,
      deductionTypeCode: DeductionTypeCodeEnum.MASTER_DEGREE,
      data: educateMasterDegree,
      isVerified: educateMasterDegree.isVerified,
      life: 'Y',
      nonLife: 'Y',
      priority: 1,
    });
    // CertificateType
    deductions.push({
      deductionType: DeductionTypeEnum.FCHFP,
      deductionTypeName: DeductionTypeNameEnum.FCHFP,
      deductionTypeCode: DeductionTypeCodeEnum.FCHFP,
      data: certificateType.FChFP,
      isVerified: certificateType.FChFP.isVerified,
      life: 'Y',
      nonLife: 'Y',
      priority: 2,
    });
    deductions.push({
      deductionType: DeductionTypeEnum.CFP,
      deductionTypeName: DeductionTypeNameEnum.CFP,
      deductionTypeCode: DeductionTypeCodeEnum.CFP,
      data: certificateType.CFP,
      isVerified: certificateType.CFP.isVerified,
      life: 'Y',
      nonLife: 'Y',
      priority: 3,
    });
    deductions.push({
      deductionType: DeductionTypeEnum.AFPT,
      deductionTypeName: DeductionTypeNameEnum.AFPT,
      deductionTypeCode: DeductionTypeCodeEnum.AFPT,
      data: certificateType.AFPT,
      isVerified: certificateType.AFPT.isVerified,
      life: 'Y',
      nonLife: 'Y',
      priority: 4,
    });
    deductions.push({
      deductionType: DeductionTypeEnum.DNLI,
      deductionTypeName: DeductionTypeNameEnum.DNLI,
      deductionTypeCode: DeductionTypeCodeEnum.DNLI,
      data: certificateType.DNLI,
      isVerified: certificateType.DNLI.isVerified,
      life: 'N',
      nonLife: 'Y',
      priority: 5,
    });
    deductions.push({
      deductionType: DeductionTypeEnum.CII,
      deductionTypeName: DeductionTypeNameEnum.CII,
      deductionTypeCode: DeductionTypeCodeEnum.CII,
      data: certificateType.CII,
      isVerified: certificateType.CII.isVerified,
      life: 'N',
      nonLife: 'Y',
      priority: 6,
    });
    // INSTRUCTOR
    deductions.push({
      deductionType: DeductionTypeEnum.INSTRUCTOR,
      deductionTypeName: DeductionTypeNameEnum.INSTRUCTOR,
      deductionTypeCode: DeductionTypeCodeEnum.INSTRUCTOR,
      data: criteria.instructor,
      isVerified: criteria.instructor.isVerified,
      life: 'Y',
      nonLife: 'Y',
      priority: 7,
    });

    return deductions;
  }

  private convertSalute(text: string) {
    const saluteTextMapping = {
      'น.ส.': 'นางสาว',
    };
    const salute = saluteTextMapping[text];

    return salute || text;
  }
}
