import { GenericID } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { EnrollmentAttachmentTypeEnum } from '@iso/lms/enums/enrollmentAttachment.enum';
import { QuizTestTypeEnum } from '@iso/lms/enums/quiz.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { QuizAnswerParams } from '@iso/lms/types/quizAnswer.type';
import { Inject } from '@nestjs/common';
import _ from 'lodash';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import {
  AttachmentDeductStatusTH,
  EnrollmentStatusTextEnum,
  EvaluateResultTH,
  LearningStatusTypeEnum,
} from '@constants/enums/enrollment.enum';
import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { FilterExportLearnerReportParams, RelationEnrollmentParams } from '@constants/types/reportHistory.type';

import { IOrganizationDataMapper } from '@interfaces/dataMapper/organization.dataMapper.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IEnrollmentService } from '@interfaces/services/enrollment.interface';
import { IOrganizationService } from '@interfaces/services/organization.interface';
import { ILearnerReportService } from '@interfaces/services/report.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportLearnerReportUseCase } from '@interfaces/usecases/report.interface';

import { date } from '@domains/utils/date.util';

export class ExportLearnerReportUseCase implements IExportLearnerReportUseCase {
  constructor(
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(ReportDIToken.ReportHistoryRepository)
    private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationDataMapper)
    private readonly organizationDataMapper: IOrganizationDataMapper,

    //Service
    @Inject(OrganizationDIToken.OrganizationService) private readonly organizationService: IOrganizationService,
    @Inject(ReportDIToken.LearnerReportService)
    private readonly learnerReportService: ILearnerReportService,
    @Inject(EnrollmentDIToken.EnrollmentService)
    private readonly enrollmentService: IEnrollmentService,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: FilterExportLearnerReportParams): Promise<void> {
    const {
      reportHistoryId,
      organizationId,
      startDateRange,
      endDateRange,
      objectiveType,
      categories: categoryIds,
      courses: courseIds,
      enrollmentStatus: enrollmentStatusList,
      evaluateResult: evaluateResultList,
      regulators,
    } = params;

    const reportHistory = await this.reportHistoryRepository.findOne({ id: reportHistoryId, organizationId });
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist ',
        data: { reportHistoryId },
      });
    }

    if (reportHistory.status !== ReportHistoryStatusEnum.PENDING) {
      throw Exception.new({
        code: Code.ENTITY_VALIDATION_ERROR,
        message: 'report History has been operate',
        data: { reportHistoryId },
      });
    }

    this.logger.log(`Start processing the reportHistory ID ${reportHistoryId} type OIC_POST_REPORT`);

    try {
      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'organization dose not exist',
          data: { organizationId },
        });
      }
      const organizationDto = this.organizationDataMapper.toDTO(organization);

      const enrollmentQuery = this.buildEnrollmentQuery(
        startDateRange,
        endDateRange,
        organizationId,
        objectiveType,
        courseIds,
        categoryIds,
        enrollmentStatusList,
        regulators,
        organizationDto.availableCourseObjectiveTypes,
      );

      const enrollments = await this.enrollmentRepository.aggregate(enrollmentQuery);

      const relationEnrollments: RelationEnrollmentParams[] = this.transformEnrollments(enrollments);

      const reportData: any[] = _.chain(relationEnrollments)
        .filter(
          (item: RelationEnrollmentParams) =>
            (!enrollmentStatusList.length || (item.status && enrollmentStatusList.includes(item.status))) &&
            (!evaluateResultList.length || (item.evaluateResult && evaluateResultList.includes(item.evaluateResult))),
        )
        .orderBy('startedAt', 'asc')
        .map((enrollment) => {
          const firstPreTestQuiz = enrollment.quizAnswersData.filter(
            (item: QuizAnswerParams) => item.testType === QuizTestTypeEnum.PRE_TEST,
          )?.[0];
          const postTestQuiz = enrollment.quizAnswersData.filter(
            (item: QuizAnswerParams) => item.testType === QuizTestTypeEnum.POST_TEST,
          );
          const lastPostTestQuiz = postTestQuiz[postTestQuiz.length - 1];

          const firstPreTestResultData = this.enrollmentService.buildTestScore(
            firstPreTestQuiz?.userPoint,
            firstPreTestQuiz?.totalPoint,
            !!firstPreTestQuiz?.finishedAt,
            firstPreTestQuiz?.criteriaCertificate?.passScore,
            firstPreTestQuiz?.criteriaCertificate?.isPass,
          );

          const postTestResultData = this.enrollmentService.buildTestScore(
            lastPostTestQuiz?.userPoint,
            lastPostTestQuiz?.totalPoint,
            !!lastPostTestQuiz?.finishedAt,
            lastPostTestQuiz?.criteriaCertificate?.passScore,
            lastPostTestQuiz?.criteriaCertificate?.isPass,
          );

          const enrollmentStatusTH = EnrollmentStatusTextEnum[enrollment.status];
          const evaluateResultTH = EvaluateResultTH[enrollment.evaluateResult];
          const enrollmentAttachmentDeduct = enrollment.enrollmentAttachData?.find(
            (item) => item.fileType === EnrollmentAttachmentTypeEnum.DEDUCT,
          );
          const attachmentDeductStatus = enrollmentAttachmentDeduct?.status;
          const newAttachmentDeductStatus = this.enrollmentService.buildAttachmentDeductStatus(
            attachmentDeductStatus,
            enrollment.courseData?.regulatorInfo,
          );
          const attachmentDeductStatusTH = AttachmentDeductStatusTH[newAttachmentDeductStatus];

          const rejectedReason = enrollment.status === EnrollmentStatusEnum.REJECTED ? enrollment.approvalReason : '';

          const certificateData = this.enrollmentService.buildCertificateUrl(
            enrollment.enrollmentCertData,
            enrollment.courseData?.objectiveType,
            enrollment.courseData?.regulatorInfo,
          );

          const organizationMainUrl = this.organizationService.getMainUrl(organization.domain, organization.fqdn);

          const result = this.learnerReportService.buildLearnerReportRowData({
            ...enrollment,
            firstPreTestResultData,
            postTestResultData,
            enrollmentStatusTH,
            evaluateResultTH,
            attachmentDeductStatusTH,
            rejectedReason,
            certificateData,
            organizationMainUrl,
          });

          return result;
        })
        .value();

      const buffer = await this.learnerReportService.buildLearnerReportFile(reportData);

      const filePath = `xlsx/reports/${reportHistory.fileName}.xlsx`;
      const reportHistoryFile = new ReportHistoryFile({
        fileName: reportHistory.fileName,
        filePath,
      });

      const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);
      await storageRepository.upload({
        raw: buffer,
        path: filePath,
        fileType: UploadFileTypeEnum.XLSX,
      });

      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      reportHistory.files.push(reportHistoryFile);

      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId} found error: ${error}`);

      reportHistory.status = ReportHistoryStatusEnum.ERROR;
    } finally {
      this.logger.log(`report history (${reportHistory.id}) status: ${reportHistory.status}`);
      await this.reportHistoryRepository.save(reportHistory);
    }
  }

  private transformEnrollments(enrollments: any): RelationEnrollmentParams[] {
    return enrollments.map((item: RelationEnrollmentParams) => {
      const courseObjectiveType = item.courseData?.objectiveType as CourseObjectiveTypeEnum;
      const learningStatus = this.enrollmentService.buildLearningStatus(
        item.status,
        item.expiredAt,
        item.acceptedAt,
        courseObjectiveType,
      );
      const evaluateResult = this.enrollmentService.buildEvaluateResult(
        item.status,
        item.expiredAt,
        learningStatus as LearningStatusTypeEnum,
        courseObjectiveType,
      );
      return {
        ...item,
        learningStatus,
        evaluateResult,
      };
    });
  }

  private buildEnrollmentQuery(
    startDateRange: Date[],
    endDateRange: Date[],
    organizationId: GenericID,
    objectiveType: CourseObjectiveTypeEnum[],
    courseIds: string[],
    categoryIds: string[],
    enrollmentStatus: string[],
    regulators: RegulatorEnum[],
    availableCoursesObjectiveTypes: CourseObjectiveTypeEnum[],
  ) {
    const queryableEnrollmentStatuses = [
      EnrollmentStatusEnum.PASSED,
      EnrollmentStatusEnum.IN_PROGRESS,
      EnrollmentStatusEnum.PENDING_RESULT,
      EnrollmentStatusEnum.PENDING_APPROVAL,
      EnrollmentStatusEnum.VERIFIED,
      EnrollmentStatusEnum.APPROVED,
      EnrollmentStatusEnum.REJECTED,
      EnrollmentStatusEnum.EXPIRED,
      EnrollmentStatusEnum.CANCELED,
      EnrollmentStatusEnum.COMPLETED,
    ];

    const hasFilterEnrollmentStatus = enrollmentStatus.every((item) =>
      queryableEnrollmentStatuses.includes(item as EnrollmentStatusEnum),
    );
    const filterEnrollmentStatus = hasFilterEnrollmentStatus ? enrollmentStatus : [];

    const enrollmentMatch: Record<string, unknown> = {
      startedAt: {
        $gte: date(startDateRange[0]).startOf('day').toDate(),
        $lte: date(startDateRange[1]).endOf('day').toDate(),
      },
    };
    if (endDateRange.length) {
      enrollmentMatch['finishedAt'] = {
        $gte: date(endDateRange[0]).startOf('day').toDate(),
        $lte: date(endDateRange[1]).endOf('day').toDate(),
      };
    }
    enrollmentMatch['organizationId'] = organizationId;
    enrollmentMatch['status'] = {
      $in: _.isEmpty(enrollmentStatus) ? queryableEnrollmentStatuses : filterEnrollmentStatus,
    };

    const enrollmentQuery = [
      { $match: enrollmentMatch },
      ...this.buildCourseVersionQuery(),
      ...this.buildCourseQuery(objectiveType, courseIds, regulators, availableCoursesObjectiveTypes),
      ...this.buildCourseCategoryQuery(categoryIds),
      ...this.buildLicenseQuery(),
      ...this.buildQuizAnswerQuery(),
      ...this.buildEnrollmentCertificatesQuery(),
      ...this.buildEnrollmentAttachmentQuery(),
      ...this.buildRoundsQuery(),
      ...this.buildUserQuery(),
      {
        $project: {
          id: 1,
          status: 1,
          userId: 1,
          courseId: 1,
          roundId: 1,
          learningProgress: 1,
          completedCourseItem: 1,
          startedAt: 1,
          finishedAt: 1,
          requestedApprovalAt: 1,
          acceptedAt: 1,
          expiredAt: 1,
          approvalReason: 1,
          courseVersionId: 1,
          courseVersionData: 1,
          licenseData: 1,
          courseData: 1,
          courseCategoryData: 1,
          quizAnswersData: 1,
          enrollmentCertData: 1,
          enrollmentAttachData: 1,
          roundData: 1,
          userData: 1,
        },
      },
    ];

    return enrollmentQuery;
  }

  private buildUserQuery() {
    return [
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: 'guid',
          as: 'userData',
        },
      },
      {
        $unwind: {
          path: '$userData',
          preserveNullAndEmptyArrays: true,
        },
      },
    ];
  }

  private buildRoundsQuery() {
    return [
      {
        $lookup: {
          from: 'rounds',
          localField: 'roundId',
          foreignField: 'id',
          as: 'roundData',
        },
      },
      {
        $unwind: {
          path: '$roundData',
          preserveNullAndEmptyArrays: true,
        },
      },
    ];
  }

  private buildEnrollmentAttachmentQuery() {
    return [
      {
        $lookup: {
          from: 'enrollment-attachments',
          localField: 'id',
          foreignField: 'enrollmentId',
          as: 'enrollmentAttachData',
        },
      },
    ];
  }

  private buildEnrollmentCertificatesQuery() {
    return [
      {
        $lookup: {
          from: 'enrollment-certificates',
          localField: 'id',
          foreignField: 'enrollmentId',
          as: 'enrollmentCertData',
        },
      },
    ];
  }

  private buildQuizAnswerQuery(): Record<string, unknown>[] {
    return [
      {
        $lookup: {
          from: 'quiz-answers',
          localField: 'id',
          foreignField: 'enrollmentId',
          as: 'quizAnswersData',
        },
      },
    ];
  }

  private buildLicenseQuery(): Record<string, unknown>[] {
    return [
      {
        $lookup: {
          from: 'licenses',
          localField: 'userId',
          foreignField: 'userId',
          as: 'licenseData',
        },
      },
    ];
  }

  private buildCourseCategoryQuery(categoryIds: string[]) {
    const courseCategoryQuery: Record<string, unknown>[] = [
      {
        $lookup: {
          from: 'course-categories',
          localField: 'courseData.id',
          foreignField: 'courseIds',
          as: 'courseCategoryData',
        },
      },
    ];
    const courseCategoryMatch: Record<string, unknown> = {};
    if (!_.isEmpty(categoryIds)) {
      courseCategoryMatch['courseCategoryData'] = {
        $elemMatch: {
          id: {
            $in: categoryIds,
          },
        },
      };
      courseCategoryQuery.push({ $match: courseCategoryMatch });
    }
    return courseCategoryQuery;
  }

  private buildCourseQuery(
    objectiveTypes: CourseObjectiveTypeEnum[],
    courseIds: string[],
    regulators: RegulatorEnum[],
    availableCoursesObjectiveTypes: CourseObjectiveTypeEnum[],
  ) {
    const courseMatch: Record<string, unknown>[] = [];
    const isEmptyObjectTypes = _.isEmpty(objectiveTypes);
    const isEmptyRegulators = _.isEmpty(regulators);
    if (!isEmptyObjectTypes && isEmptyRegulators) {
      courseMatch.push({ 'courseData.objectiveType': { $in: objectiveTypes } });
    }

    if (!isEmptyRegulators) {
      const selectObjectiveTypes = isEmptyObjectTypes ? availableCoursesObjectiveTypes : objectiveTypes;

      const isFilterTraining = selectObjectiveTypes.includes(CourseObjectiveTypeEnum.TRAINING);
      const nonTrainingObjectiveTypes = selectObjectiveTypes.filter(
        (_objectiveType) => _objectiveType !== CourseObjectiveTypeEnum.TRAINING,
      );

      const orCondition = [
        _.isEmpty(nonTrainingObjectiveTypes) ? {} : { 'courseData.objectiveType': { $in: nonTrainingObjectiveTypes } },
        isFilterTraining
          ? {
              $and: [
                { 'courseData.objectiveType': CourseObjectiveTypeEnum.TRAINING },
                { 'courseData.regulatorInfo.regulator': { $in: regulators } },
              ],
            }
          : {},
      ].filter((v) => !_.isEmpty(v));
      if (!_.isEmpty(orCondition)) {
        courseMatch.push({
          $or: orCondition,
        });
      }
    }

    if (!_.isEmpty(courseIds)) {
      courseMatch.push({ 'courseData.id': { $in: courseIds } });
    }

    const coursesQuery: Record<string, unknown>[] = [
      {
        $lookup: {
          from: 'courses',
          localField: 'courseVersionData.courseId',
          foreignField: 'id',
          as: 'courseData',
        },
      },
      {
        $unwind: '$courseData',
      },
    ];
    if (!_.isEmpty(courseMatch)) {
      coursesQuery.push({ $match: { $and: courseMatch } });
    }

    return coursesQuery;
  }

  private buildCourseVersionQuery() {
    return [
      {
        $lookup: {
          from: 'course-versions',
          localField: 'courseVersionId',
          foreignField: 'id',
          as: 'courseVersionData',
        },
      },
      {
        $unwind: '$courseVersionData',
      },
    ];
  }
}
