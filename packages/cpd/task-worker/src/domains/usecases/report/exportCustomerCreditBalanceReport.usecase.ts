import { GenericID } from '@iso/constants/commonTypes';
import { OperationPointHistoryEnum } from '@iso/lms/enums/pointHistory.enum';
import { PreEnrollmentReservationStatusEnum } from '@iso/lms/enums/preEnrollmentReservation.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { Inject } from '@nestjs/common';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';
import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CreditDIToken, OrganizationDIToken, PreEnrollmentDIToken, ReportDIToken } from '@applications/di/domain';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { CustomerDashboardActionTypeEnum } from '@constants/enums/customer.enum';
import { columnCustomerCreditBalanceReport } from '@constants/enums/excel.enum';
import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import {
  OutputCustomerDashboardInvoiceListParams,
  OutputCustomerPurchaseOrderCreditParams,
} from '@constants/types/customer.type';
import { UploadFileParams } from '@constants/types/infrastructures/storage.type';
import { FilterExportExportCustomerCreditBalanceReportParams } from '@constants/types/reportHistory.type';

import { ICustomerRepository } from '@interfaces/repositories/customer.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IPointHistoryRepository } from '@interfaces/repositories/pointHistory.repository.interface';
import { IPreEnrollmentReservationRepository } from '@interfaces/repositories/preEnrollmentReservation.repository.interface';
import { IPurchaseOrderRepository } from '@interfaces/repositories/purchaseOrder.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportCustomerCreditBalanceReportUseCase } from '@interfaces/usecases/report.interface';

import { accumulateUsedRefundedCreditTotal, calculateCustomerBalanceCredit } from '@domains/services/customer.service';
import { date } from '@domains/utils/date.util';
import { injectionRiskSyntaxProtector } from '@domains/utils/excel.util';

export class ExportCustomerCreditBalanceReportUseCase implements IExportCustomerCreditBalanceReportUseCase {
  constructor(
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(ReportDIToken.ReportHistoryRepository)
    private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(CreditDIToken.CustomerRepository)
    private readonly customerRepository: ICustomerRepository,
    @Inject(CreditDIToken.PointHistoryRepository)
    private readonly pointHistoryRepository: IPointHistoryRepository,
    @Inject(CreditDIToken.PurchaseOrderRepository)
    private readonly purchaseOrderRepository: IPurchaseOrderRepository,
    @Inject(PreEnrollmentDIToken.PreEnrollmentReservationRepository)
    private readonly preEnrollmentReservationRepository: IPreEnrollmentReservationRepository,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: FilterExportExportCustomerCreditBalanceReportParams): Promise<void> {
    const { reportHistoryId, organizationId, keyword } = params;

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist',
        data: { reportHistoryId },
      });
    }

    try {
      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'organization dose not exist',
          data: { organizationId },
        });
      }

      const { fileName } = reportHistory;

      const keywordOperator = { $regex: keyword, $options: 'i' };
      const aggregateParams = [
        {
          $match: {
            $or: [{ customerCode: keywordOperator }, { customerName: keywordOperator }],
          },
        },
        { $sort: { createdAt: 1 } },
      ];

      const customerList: any[] = await this.customerRepository.aggregate(aggregateParams);

      const customerCodeList = customerList.map((val) => val.customerCode);

      const purchaseOrderAvailableList = await this.getPurchaseOrderAvailable(customerCodeList);

      const [totalCreditList, notStartedCreditList, reservedCreditList, usedList, refundedList, reservedList] =
        await Promise.all([
          this.getTotalCredit(customerCodeList),
          this.getTotalNotStartedCredit(customerCodeList),
          this.getTotalReservedCredit(organizationId, customerCodeList),
          this.getUsedList(purchaseOrderAvailableList),
          this.getRefundedList(purchaseOrderAvailableList),
          this.getReservedList(organizationId, customerCodeList),
        ]);

      const datas = customerList.map((item) => {
        const totalCredit = totalCreditList.find((val) => val.customerCode === item.customerCode)?.totalCredit || 0;
        const totalNotStartedCredit =
          notStartedCreditList.find((val) => val.customerCode === item.customerCode)?.totalCredit || 0;
        const totalReservedCredit =
          reservedCreditList.find((val) => val.customerCode === item.customerCode)?.totalCredit || 0;
        const totalUsedList = usedList.filter((val) => val.customerCode === item.customerCode);
        const totalRefundedList = refundedList.filter((val) => val.customerCode === item.customerCode);
        const totalReservedList = reservedList.filter((val) => val.customerCode === item.customerCode);

        const { accUsed, accRefunded, lastAccRefunded } = accumulateUsedRefundedCreditTotal(
          totalUsedList.concat(totalRefundedList).concat(totalReservedList),
        );

        const creditOverall = calculateCustomerBalanceCredit(
          totalCredit,
          totalNotStartedCredit,
          totalReservedCredit,
          accUsed,
          accRefunded,
          lastAccRefunded,
        );

        return {
          id: item.id,
          customerName: item.customerName,
          customerCode: item.customerCode,
          availableCredit: creditOverall.availableCredit,
          reservedCredit: creditOverall.reservedCredit,
          notStartedCredit: creditOverall.notStartedCredit,
          usedCredit: creditOverall.usedCredit,
          unusedCredit: creditOverall.unusedCredit,
          refundedCredit: creditOverall.refundedCredit,
          totalCredit: creditOverall.totalCredit,
        };
      });

      const excelRecords = this.buildReportExcel(datas);
      const excel = new ExcelBuilderService();
      const sheetName = 'Sheet1';
      excel
        .addWorksheet(sheetName)
        .setColumnHeader(columnCustomerCreditBalanceReport, sheetName)
        .buildRawFileExcel([{ workSheetName: sheetName, rowsData: excelRecords }]);

      excel.setRowFill(1, sheetName, { type: 'pattern', pattern: 'solid', fgColor: { argb: 'C9D8BA' } });
      const buffer = await excel.getFileBuffer();
      const targetKey = `xlsx/reports/${fileName}.xlsx`;
      const uploadFileParams: UploadFileParams = {
        raw: buffer as Buffer,
        path: targetKey,
        fileType: UploadFileTypeEnum.XLSX,
      };
      await this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS).upload(uploadFileParams);
      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      const reportHistoryFile = new ReportHistoryFile({
        filePath: targetKey,
        fileName,
      });
      reportHistory.files.push(reportHistoryFile);

      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  private async getPurchaseOrderAvailable(customerCodes: string[]) {
    const aggregateParams = [
      {
        $match: {
          customerCode: { $in: customerCodes },
          startedAt: { $lte: date().toDate() },
          expiredAt: { $gte: date().toDate() },
        },
      },
      {
        $group: {
          _id: null,
          ids: { $push: '$id' },
        },
      },
    ];

    const result = await this.purchaseOrderRepository.aggregate(aggregateParams);

    return result;
  }

  private async getTotalCredit(customerCodes: string[]): Promise<OutputCustomerPurchaseOrderCreditParams[]> {
    const aggregateParams = [
      {
        $match: {
          customerCode: { $in: customerCodes },
          expiredAt: { $gte: date().toDate() },
        },
      },
      {
        $group: {
          _id: '$customerCode',
          totalPoint: { $sum: '$point' },
        },
      },
      {
        $project: {
          customerCode: '$_id',
          totalCredit: { $divide: ['$totalPoint', 100] },
        },
      },
    ];

    const result =
      await this.purchaseOrderRepository.aggregate<OutputCustomerPurchaseOrderCreditParams>(aggregateParams);

    return result;
  }

  private async getTotalReservedCredit(
    organizationId: GenericID,
    customerCodes: string[],
  ): Promise<OutputCustomerPurchaseOrderCreditParams[]> {
    const aggregateParams = [
      {
        $match: {
          organizationId,
          customerCode: { $in: customerCodes },
          status: PreEnrollmentReservationStatusEnum.PASSED,
          totalPoint: { $gt: 0 },
          roundDate: { $gte: date().startOf('day').toDate() },
        },
      },
      {
        $group: {
          _id: '$customerCode',
          totalPoint: { $sum: '$totalPoint' },
        },
      },
      {
        $project: {
          customerCode: '$_id',
          totalCredit: { $divide: ['$totalPoint', 100] },
        },
      },
    ];

    const result =
      await this.preEnrollmentReservationRepository.aggregate<OutputCustomerPurchaseOrderCreditParams>(aggregateParams);

    return result;
  }

  private async getTotalNotStartedCredit(customerCodes: string[]): Promise<OutputCustomerPurchaseOrderCreditParams[]> {
    const aggregateParams = [
      {
        $match: {
          customerCode: { $in: customerCodes },
          startedAt: { $gt: date().toDate() },
        },
      },
      {
        $group: {
          _id: '$customerCode',
          totalPoint: { $sum: '$point' },
        },
      },
      {
        $project: {
          customerCode: '$_id',
          totalCredit: { $divide: ['$totalPoint', 100] },
        },
      },
    ];

    const result =
      await this.purchaseOrderRepository.aggregate<OutputCustomerPurchaseOrderCreditParams>(aggregateParams);

    return result;
  }

  private async getUsedList(
    purchaseOrderAvailableList: Record<string, any>[],
  ): Promise<OutputCustomerDashboardInvoiceListParams[]> {
    const poIds = purchaseOrderAvailableList.map((val) => val.ids).flat();

    const aggregateParams = [
      {
        $match: {
          type: OperationPointHistoryEnum.DEDUCT,
          invoiceItemId: { $ne: '' },
          'purchaseOrders.purchaseOrderId': {
            $in: poIds,
          },
        },
      },
      {
        $unwind: '$purchaseOrders',
      },
      {
        $lookup: {
          from: 'purchase-orders',
          let: { purchaseOrderId: '$purchaseOrders.purchaseOrderId' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$id', '$$purchaseOrderId'] },
              },
            },
            {
              $project: {
                id: 1,
                customerCode: 1,
              },
            },
          ],
          as: 'purchaseOrderCustomer',
        },
      },
      {
        $unwind: '$purchaseOrderCustomer',
      },
      {
        $match: {
          'purchaseOrders.purchaseOrderId': {
            $in: poIds,
          },
        },
      },
      {
        $project: {
          id: 1,
          customerCode: '$purchaseOrderCustomer.customerCode',
          type: CustomerDashboardActionTypeEnum.USED,
          totalAmount: { $literal: 1 },
          totalCredit: { $divide: ['$purchaseOrders.point', 100] },
          createdAt: 1,
        },
      },
    ];

    const result =
      await this.pointHistoryRepository.aggregate<OutputCustomerDashboardInvoiceListParams>(aggregateParams);

    return result;
  }

  private async getRefundedList(
    purchaseOrderAvailableList: Record<string, any>[],
  ): Promise<OutputCustomerDashboardInvoiceListParams[]> {
    const poIds = purchaseOrderAvailableList.map((val) => val.ids).flat();

    const aggregateParams = [
      {
        $match: {
          type: OperationPointHistoryEnum.ADD,
          invoiceItemId: { $ne: '' },
          'purchaseOrders.purchaseOrderId': {
            $in: poIds,
          },
        },
      },
      {
        $unwind: '$purchaseOrders',
      },
      {
        $lookup: {
          from: 'purchase-orders',
          let: { purchaseOrderId: '$purchaseOrders.purchaseOrderId' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$id', '$$purchaseOrderId'] },
              },
            },
            {
              $project: {
                id: 1,
                customerCode: 1,
              },
            },
          ],
          as: 'purchaseOrderCustomer',
        },
      },
      {
        $unwind: '$purchaseOrderCustomer',
      },
      {
        $match: {
          'purchaseOrders.purchaseOrderId': {
            $in: poIds,
          },
        },
      },
      {
        $project: {
          id: 1,
          customerCode: '$purchaseOrderCustomer.customerCode',
          type: CustomerDashboardActionTypeEnum.REFUNDED,
          totalAmount: { $literal: 1 },
          totalCredit: { $divide: ['$purchaseOrders.point', 100] },
          createdAt: 1,
        },
      },
    ];

    const result =
      await this.pointHistoryRepository.aggregate<OutputCustomerDashboardInvoiceListParams>(aggregateParams);

    return result;
  }

  private async getReservedList(
    organizationId: GenericID,
    customerCodes: string[],
  ): Promise<OutputCustomerDashboardInvoiceListParams[]> {
    const aggregateParams = [
      {
        $match: {
          customerCode: { $in: customerCodes },
          organizationId,
          status: { $in: [PreEnrollmentReservationStatusEnum.PASSED] },
          totalPoint: { $gt: 0 },
          roundDate: { $gte: date().startOf('day').toDate() },
        },
      },
      {
        $project: {
          _id: 0,
          id: { $literal: '' },
          totalAmount: { $literal: 1 },
          totalCredit: { $divide: ['$totalPoint', 100] },
          customerCode: 1,
          createdAt: 1,
        },
      },
    ];

    const result =
      await this.preEnrollmentReservationRepository.aggregate<OutputCustomerDashboardInvoiceListParams>(
        aggregateParams,
      );

    return result;
  }

  private buildReportExcel(list: any[]) {
    const result = list.map((item: any, index: number) =>
      injectionRiskSyntaxProtector({
        noRow: index + 1,
        ...item,
      }),
    );

    return result;
  }
}
