import { GenericID, ObjectValue } from '@iso/constants/commonTypes';
import { getParentBeforeLastChild, getValueObject } from '@iso/helpers/columnSetting';
import {
  ColumnSettingDataTypeEnum,
  ColumnSettingKeyEnum,
  ColumnSettingTemplateEnum,
} from '@iso/lms/enums/columnSetting.enum';
import { CourseObjectiveTypeEnum } from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { PreEnrollmentReservationStatusEnum } from '@iso/lms/enums/preEnrollmentReservation.enum';
import {
  PreEnrollmentTransactionEnrollByEnum,
  PreEnrollmentTransactionStatusEnum,
} from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { PreEnrollmentTransaction } from '@iso/lms/models/preEnrollmentTransaction.model';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { ColumnSettingGroupFieldParams } from '@iso/lms/types/columnSetting.type';
import { ProductSKUParams } from '@iso/lms/types/productSKU.type';
import { ProductSKUBundleParams } from '@iso/lms/types/productSKUBundle.type';
import { Inject, Injectable } from '@nestjs/common';
import { has, isEmpty, isObject, partition, uniq } from 'lodash';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';
import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { CreditDIToken } from '@applications/di/domain/credit.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { PreEnrollmentDIToken } from '@applications/di/domain/preEnrollment.di';
import { ProductSKUDIToken } from '@applications/di/domain/productSKU.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { ColumnSettingNameCollectionEnum } from '@constants/enums/columnSetting.enum';
import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { ColumnSettingWithTemplateParams } from '@constants/types/columnSetting.type';
import { CourseInBundleParams } from '@constants/types/course.type';
import { UploadFileParams } from '@constants/types/infrastructures/storage.type';
import { OutputPreEnrollmentParams, PreEnrollmentListParams } from '@constants/types/preEnrollmentTransaction.type';
import { ExportPreEnrollmentReportPayload } from '@constants/types/reportHistory.type';

import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { ICustomerRepository } from '@interfaces/repositories/customer.repository.interface';
import { ICustomerPartnerRepository } from '@interfaces/repositories/customerPartner.repository.interface';
import { IEnrollmentRegulatorReportRepository } from '@interfaces/repositories/enrollmentRegulatorReport.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IPreEnrollmentReservationRepository } from '@interfaces/repositories/preEnrollmentReservation.repository.interface';
import { IPreEnrollmentTransactionRepository } from '@interfaces/repositories/preEnrollmentTransaction.repository.interface';
import { IProductSKURepository } from '@interfaces/repositories/productSKU.repository.interface';
import { IProductSKUBundleRepository } from '@interfaces/repositories/productSKUBundle.repository.interface';
import { IProductSKUCourseRepository } from '@interfaces/repositories/productSKUCourse.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { IColumnSettingService } from '@interfaces/services/columnSetting.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportRegularPreEnrollmentReportUseCase } from '@interfaces/usecases/report.interface';

import { date, DateFormat } from '@domains/utils/date.util';

@Injectable()
export class ExportRegularPreEnrollmentReportUseCase implements IExportRegularPreEnrollmentReportUseCase {
  constructor(
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(PreEnrollmentDIToken.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(RoundDIToken.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @Inject(PreEnrollmentDIToken.PreEnrollmentReservationRepository)
    private readonly preEnrollmentReservationRepository: IPreEnrollmentReservationRepository,
    @Inject(PreEnrollmentDIToken.EnrollmentRegulatorReportRepository)
    private readonly enrollmentRegulatorReportRepository: IEnrollmentRegulatorReportRepository,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(ReportDIToken.ReportHistoryRepository)
    private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(ProductSKUDIToken.ProductSKURepository)
    private readonly productSKURepository: IProductSKURepository,
    @Inject(ProductSKUDIToken.ProductSKUBundleRepository)
    private readonly productSKUBundleRepository: IProductSKUBundleRepository,
    @Inject(ProductSKUDIToken.ProductSKUCourseRepository)
    private readonly productSKUCourseRepository: IProductSKUCourseRepository,
    @Inject(CreditDIToken.CustomerRepository)
    private readonly customerRepository: ICustomerRepository,
    @Inject(CreditDIToken.CustomerPartnerRepository)
    private readonly customerPartnerRepository: ICustomerPartnerRepository,
    @Inject(OrganizationDIToken.ColumnSettingService)
    private readonly columnSettingService: IColumnSettingService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: ExportPreEnrollmentReportPayload): Promise<void> {
    const excel = new ExcelBuilderService();
    const WORKSHEET_NAME = 'Sheet1';

    const { organizationId, filters, exportCfg, fileName, reportHistoryId } = params;

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist',
        data: { reportHistoryId },
      });
    }

    try {
      const organization = await this.organizationRepository.findOne({ id: organizationId });
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'The organization does not exist',
        });
      }

      const columnSettings = await this.columnSettingService.getColumnSettingList(
        organizationId,
        ColumnSettingTemplateEnum.regularPreEnrollment,
      );

      const dataKey = exportCfg.columnSettings.map((val) => val.key);

      const allColumnsSetting = columnSettings
        ?.filter((col) => dataKey.includes(col.key))
        .sort((a, b) => dataKey.indexOf(a.key) - dataKey.indexOf(b.key));

      const columns = allColumnsSetting.map((item) => {
        return {
          header: item.columnBulkHeader,
          key: item.columnBulkHeader,
          width: 15,
        };
      });

      excel.addWorksheet(WORKSHEET_NAME).setColumnHeader(columns, WORKSHEET_NAME);

      const columnSettingKey = {
        roundDate: ColumnSettingKeyEnum.PRE_ENROLLMENT_ROUND_DATE,
        course: ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSES,
      };

      const courseCodeCustomFilters = filters.find((val) => val.id === columnSettingKey.course)?.data[0]
        .value as string[];

      const transformFilters = this.columnSettingService.transformColumnSettingFilterType({ filters, columnSettings });

      const {
        aggregateInMainCollection,
        aggregateJoinCollectionsWithNoneConditions,
        aggregateJoinCollectionWithFilterConditions,
      } = this.columnSettingService.buildAggregationFromColumnSetting({ filters: transformFilters, columnSettings });

      const { isNothingMatchInJoinCondition, matchAggregationJoinConditions } =
        await this.columnSettingService.buildAndQueryJoinAggregateCollectionWithFilterCondition(
          aggregateJoinCollectionWithFilterConditions,
          (collection, pipeline) => this.repositoryFactory(collection, pipeline, organizationId),
          (state, collection) => this.buildStateAggregationJoinCollection(state, collection),
        );

      if (isNothingMatchInJoinCondition) excel.buildStreamFileExcel([{ workSheetName: WORKSHEET_NAME, rowsData: [] }]);

      // * CPD: use productSKUCode for filtering
      // * LMS: use courseCode for filtering
      const matchCourseConditions = [];
      let allProductSKUBundles: ProductSKUBundleParams[] = [];
      let allCourseInBundles: CourseInBundleParams[] = [];
      const [allProductSKUs, allProductSKUCourses, productSkuBundles, courseInBundles] =
        await this.getAvailableProductSKUs(organizationId, []);

      const availableProductSkuCodes = allProductSKUs.map((val) => val.code);
      allProductSKUBundles = [...productSkuBundles];
      allCourseInBundles = [...courseInBundles];

      const availableRegularCourses = await this.courseRepository.aggregate([
        {
          $match: {
            organizationId,
            objectiveType: CourseObjectiveTypeEnum.REGULAR,
          },
        },
        {
          $project: {
            _id: 0,
            id: 1,
            code: 1,
            productSKUCourseId: 1,
            regulatorInfo: 1,
            objectiveType: 1,
          },
        },
      ]);

      let courseCodeFilters = [];
      if (availableRegularCourses.length > 0) {
        const availableCourseCodes = availableRegularCourses.map((val) => val.code);
        courseCodeFilters.push(...availableCourseCodes);
      }

      if (courseCodeCustomFilters?.length > 0) {
        courseCodeFilters = courseCodeFilters.filter((val) => courseCodeCustomFilters.includes(val));
      }

      if (courseCodeFilters.length > 0) {
        matchCourseConditions.push({ 'payload.courseCode': { $in: courseCodeFilters } });
      }

      if (!availableProductSkuCodes.length && !availableRegularCourses.length) {
        excel.buildStreamFileExcel([{ workSheetName: WORKSHEET_NAME, rowsData: [] }]);
      }

      const matchAggregationForMainCollection = this.buildMainConditionAggregation(aggregateInMainCollection.match);

      const filterStatusCondition = matchAggregationForMainCollection.find((match) => match.status);

      // * The Page should display the following statuses (excluding 'CANCELED'):
      const preEnrollmentTransactionStatusList = [
        PreEnrollmentTransactionStatusEnum.WAITING_VERIFY,
        PreEnrollmentTransactionStatusEnum.EDITED_AFTER_VERIFY,
        PreEnrollmentTransactionStatusEnum.PASSED,
        PreEnrollmentTransactionStatusEnum.APPLIED,
        PreEnrollmentTransactionStatusEnum.REJECTED,
        PreEnrollmentTransactionStatusEnum.FAILED_TO_APPLY,
      ];

      if (!filterStatusCondition) {
        matchAggregationForMainCollection.push({ status: { $in: preEnrollmentTransactionStatusList } });
      }

      // PRE-ENROLLMENT RESERVATION
      const roundDateColumnSettingKey = columnSettings.find(
        (columnSetting) => columnSetting.key === columnSettingKey.roundDate,
      )?.key;

      const roundDateFilterValue = transformFilters.find((filter) => filter.id === roundDateColumnSettingKey)?.data[0]
        .value as [Date, Date];
      const validPreEnrollmentReservationIds = await this.getAvailablePreEnrollmentReservationIds({
        organizationId,
        roundDateStart: roundDateFilterValue[0],
        roundDateEnd: roundDateFilterValue[1],
      });

      const preEnrollmentPipeline = [
        {
          $match: {
            $and: [
              { organizationId },
              { $or: [...matchCourseConditions] },
              ...matchAggregationJoinConditions,
              ...matchAggregationForMainCollection,
              {
                $or: [
                  {
                    $and: [
                      { enrollBy: PreEnrollmentTransactionEnrollByEnum.ADMIN },
                      { preEnrollmentReservationId: { $in: validPreEnrollmentReservationIds } },
                    ],
                  },
                  { enrollBy: PreEnrollmentTransactionEnrollByEnum.SELF },
                ],
              },
              {
                roundId: { $ne: null },
              },
            ],
          },
        },
        {
          $sort: {
            updatedAt: -1,
          },
        },
        {
          $facet: {
            items: [
              {
                $project: {
                  _id: 0,
                  ...aggregateInMainCollection.project,
                },
              },
            ],
          },
        },
      ];

      const [result]: PreEnrollmentListParams =
        await this.preEnrollmentTransactionRepository.aggregate(preEnrollmentPipeline);

      const mergedAggregateJoinCollectionsWithNoneCondition =
        this.columnSettingService.updateWhereConditionInAggregateJoinCollectionCollection(
          result.items,
          aggregateJoinCollectionsWithNoneConditions,
        );

      const queryJoinCollections = mergedAggregateJoinCollectionsWithNoneCondition.map((joinCollectionRelation) =>
        this.columnSettingService.queryJoinRelationalCollection(
          joinCollectionRelation,
          (collection: string, pipeline: ObjectValue[]) => this.repositoryFactory(collection, pipeline, organizationId),
        ),
      );

      const resultQueryJoinCollection = await Promise.all(queryJoinCollections);

      const mergeRelationalDataFromJoinCollectionToMainCollectionData =
        this.columnSettingService.mergeRelationalDataFromJoinCollectionToMainCollection(
          result.items,
          resultQueryJoinCollection,
        ) as OutputPreEnrollmentParams[];

      result.items = mergeRelationalDataFromJoinCollectionToMainCollectionData;

      // *MAP BUNDLE DATA FOR CPD SITE
      const uniqueResultBundleProductSKUIds = uniq(
        result.items
          .flatMap((item) => item?.productSkus ?? [])
          .flatMap((productSKU) => productSKU?.productSkuBundles)
          .map((productSkuBundle) => productSkuBundle.productSKUId),
      );

      const resultBundles = allProductSKUBundles.filter((bundle) =>
        uniqueResultBundleProductSKUIds.includes(bundle.productSKUId),
      );
      const resultBundleWithCourseInfo = this.mapBundleWithCourseData(resultBundles, allCourseInBundles);

      // *MERGE RESULT WITH BUNDLE AND COURSE INFO, THE PATTERN RELY ON THE COLUMN SETTING GROUP FIELDS
      result.items = result.items.map((item) => {
        if (item?.productSkus?.length > 0) {
          const productSkus = item.productSkus.map((productSKU) => {
            const bundle = resultBundleWithCourseInfo.find((_bundle) => _bundle.productSKUId === productSKU.id);
            if (bundle) {
              return {
                ...productSKU,
                productSkuBundles: [
                  {
                    ...productSKU.productSkuBundles[0],
                    name: [productSKU.productSkuBundles[0].name, ...bundle.productSKUCourses],
                  },
                ],
                productSkuCourses: [
                  {
                    courses: [
                      { regulatorInfo: { regulator: bundle.regulator, trainingCenter: bundle.trainingCenter } },
                    ],
                  },
                ],
              };
            }
            return productSKU;
          });
          return { ...item, productSkus } as OutputPreEnrollmentParams;
        } else {
          const courseIds = item.courses.map((val) => val.id);
          const productSKUCourseData = allProductSKUCourses.find((val) => courseIds.includes(val.course.id));
          const ProductSKUData = allProductSKUs.find((val) => val.id === productSKUCourseData?.productSKUId);
          const productSkus = [
            { id: ProductSKUData?.id, name: productSKUCourseData?.name, code: ProductSKUData?.code },
          ];
          return { ...item, productSkus } as OutputPreEnrollmentParams;
        }
      });

      const preparedExcelDataList = this.prepareExcelDataList(result.items, allColumnsSetting);

      excel
        .addWorksheet(WORKSHEET_NAME)
        .setColumnHeader(columns, WORKSHEET_NAME)
        .buildRawFileExcel([{ workSheetName: WORKSHEET_NAME, rowsData: preparedExcelDataList }]);

      const buffer = await excel.getFileBuffer();

      const targetKey = `xlsx/reports/${fileName}.xlsx`;
      const uploadFileParams: UploadFileParams = {
        raw: buffer as Buffer,
        path: targetKey,
        fileType: UploadFileTypeEnum.XLSX,
      };

      await this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS).upload(uploadFileParams);

      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      const reportHistoryFile = new ReportHistoryFile({ fileName, filePath: targetKey });
      reportHistory.files.push(reportHistoryFile);

      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  private async getAvailablePreEnrollmentReservationIds({
    organizationId,
    roundDateStart,
    roundDateEnd,
  }: {
    organizationId: GenericID;
    roundDateStart: Date;
    roundDateEnd: Date;
  }) {
    const [validPreEnrollmentReservations] = await this.preEnrollmentReservationRepository.aggregate([
      {
        $match: {
          organizationId,
          status: {
            $in: [PreEnrollmentReservationStatusEnum.PASSED, PreEnrollmentReservationStatusEnum.APPLIED],
          },
          roundDate: { $gte: roundDateStart, $lte: roundDateEnd },
        },
      },
      {
        $group: {
          _id: null,
          ids: { $push: '$id' },
        },
      },
    ]);
    return validPreEnrollmentReservations?.ids || [];
  }

  private async getAvailableProductSKUs(
    organizationId: GenericID,
    filters: Record<string, unknown>[] | null,
  ): Promise<[ProductSKUParams[], CourseInBundleParams[], ProductSKUBundleParams[], CourseInBundleParams[]]> {
    const courseFilters = [];
    // * bundle filters is using for in application filtering
    const bundleFilter: { regulator?: string; trainingCenters?: string[] } = {};
    const filterCondition = filters?.reduce((acc, obj) => ({ ...acc, ...obj }), {});

    if (filterCondition?.regulator) {
      courseFilters.push({ 'regulatorInfo.regulator': filterCondition.regulator });
      bundleFilter.regulator = filterCondition.regulator as string | undefined;
    }
    if (filterCondition?.trainingCenter) {
      courseFilters.push({ 'regulatorInfo.trainingCenter': { $in: filterCondition.trainingCenter } });
      bundleFilter.trainingCenters = filterCondition.trainingCenter as string[] | undefined;
    }

    const allProductSKUBundles = await this.productSKUBundleRepository.aggregate<ProductSKUBundleParams>([]);
    const uniqueCourseInBundleProductSKUIds = uniq(allProductSKUBundles.flatMap((item) => item.productSKUIds));
    const allProductSKUCourseInBundles: CourseInBundleParams[] = await this.productSKUCourseRepository.aggregate([
      {
        $match: {
          productSKUId: { $in: uniqueCourseInBundleProductSKUIds },
        },
      },
      {
        $lookup: {
          from: 'courses',
          let: { productSKUCourseId: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$productSKUCourseId', '$$productSKUCourseId'] },
                    { $eq: ['$organizationId', organizationId] },
                  ],
                },
              },
            },
          ],
          as: 'course',
        },
      },
      {
        $unwind: {
          path: '$course',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          productSKUId: 1,
          name: 1,
          'course.id': 1,
          'course.objectiveType': 1,
          'course.regulatorInfo': 1,
        },
      },
    ]);

    let filteredProductSKUCourses = [...allProductSKUCourseInBundles];
    if (bundleFilter.regulator) {
      filteredProductSKUCourses = filteredProductSKUCourses.filter(
        (item) => item.course.regulatorInfo.regulator === bundleFilter.regulator,
      );
    }
    if (bundleFilter.trainingCenters && bundleFilter.trainingCenters.length) {
      filteredProductSKUCourses = filteredProductSKUCourses.filter((item) =>
        bundleFilter?.trainingCenters?.includes(item.course.regulatorInfo.trainingCenter),
      );
    }
    const filteredBundles = allProductSKUBundles.filter((bundle) =>
      filteredProductSKUCourses.some((val) => bundle.productSKUIds.includes(val.productSKUId)),
    );
    const bundlesProductSKUIds = filteredBundles.map((item) => item.productSKUId);

    const allProductSKUCourses: CourseInBundleParams[] = await this.productSKUCourseRepository.aggregate([
      {
        $lookup: {
          from: 'courses',
          let: { productSKUCourseId: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$productSKUCourseId', '$$productSKUCourseId'] },
                    { $eq: ['$organizationId', organizationId] },
                  ],
                },
              },
            },
          ],
          as: 'course',
        },
      },
      {
        $unwind: '$course',
      },
      {
        $project: {
          _id: 0,
          id: 1,
          productSKUId: 1,
          name: 1,
          'course.id': 1,
          'course.objectiveType': 1,
          'course.regulatorInfo': 1,
        },
      },
    ]);

    const productSKUCourseIds = allProductSKUCourses.map((item) => item.id);
    const [enabledCourse]: Record<string, any>[] = await this.courseRepository.aggregate([
      {
        $match: {
          $and: [
            { organizationId },
            { objectiveType: CourseObjectiveTypeEnum.REGULAR },
            {
              productSKUCourseId: {
                $in: productSKUCourseIds,
              },
            },
            ...courseFilters,
          ],
        },
      },
      { $group: { _id: null, productSKU: { $push: { id: '$id', productSKUCourseId: '$productSKUCourseId' } } } },
    ]);

    const enabledCourseIds = enabledCourse?.productSKU?.flatMap((val) => val.productSKUCourseId) || [];
    const enabledProductSKUCourses = allProductSKUCourses.filter((item: { id: GenericID }) =>
      enabledCourseIds.some((val: GenericID) => val === item.id),
    );
    const enabledCourseProductSKUIds = enabledProductSKUCourses.map(
      (item: { productSKUId: GenericID }) => item.productSKUId,
    );

    const allProductSKUs = await this.productSKURepository.aggregate<ProductSKUParams>([
      {
        $match: {
          $and: [{ id: { $in: [...enabledCourseProductSKUIds, ...bundlesProductSKUIds] } }],
        },
      },
    ]);
    return [allProductSKUs, allProductSKUCourses, allProductSKUBundles, allProductSKUCourseInBundles];
  }

  private buildStateAggregationJoinCollection(
    state: { match: ObjectValue[]; project: ObjectValue },
    collection: string,
  ): { match: ObjectValue[] | ObjectValue; project: ObjectValue } {
    if (collection === ColumnSettingNameCollectionEnum.LICENSES) {
      return { ...state, match: { $or: state.match } };
    }
    return state;
  }

  private repositoryFactory(
    collection: string,
    pipeline: ObjectValue[],
    organizationId: GenericID,
  ): Promise<ObjectValue[]> {
    switch (collection) {
      case ColumnSettingNameCollectionEnum.CUSTOMER_PARTNERS: {
        return this.customerPartnerRepository.aggregate(pipeline);
      }

      case ColumnSettingNameCollectionEnum.PRODUCT_SKU: {
        return this.productSKURepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.PRODUCT_SKU_COURSE: {
        return this.productSKUCourseRepository.aggregate(pipeline);
      }

      case ColumnSettingNameCollectionEnum.PRODUCT_SKU_BUNDLE: {
        return this.productSKUBundleRepository.aggregate(pipeline);
      }

      case ColumnSettingNameCollectionEnum.PRE_ENROLLMENT_RESERVATIONS: {
        return this.preEnrollmentReservationRepository.aggregate(pipeline);
      }

      case ColumnSettingNameCollectionEnum.ENROLLMENT_REGULATOR_REPORT: {
        return this.enrollmentRegulatorReportRepository.aggregate(pipeline);
      }

      case ColumnSettingNameCollectionEnum.CUSTOMERS: {
        return this.customerRepository.aggregate(pipeline);
      }

      case ColumnSettingNameCollectionEnum.ROUNDS: {
        return this.roundRepository.aggregate(pipeline);
      }

      case ColumnSettingNameCollectionEnum.COURSES: {
        const match = pipeline.find((item) => has(item, '$match'));
        if (match && isObject(match.$match)) {
          match.$match = {
            ...match.$match,
            organizationId,
          };
        }
        return this.courseRepository.aggregate(pipeline);
      }
      case ColumnSettingNameCollectionEnum.COURSE_VERSIONS: {
        const match = pipeline.find((item) => has(item, '$match'));
        if (match && isObject(match.$match)) {
          match.$match = {
            ...match.$match,
            status: CourseVersionStatusEnum.PUBLISHED,
          };
        }

        return this.courseVersionRepository.aggregate(pipeline);
      }
      default: {
        return Promise.resolve([]);
      }
    }
  }

  private prepareExcelDataList(
    dataList: Record<string, unknown>[],
    columnSettings: ColumnSettingWithTemplateParams[],
  ): Record<string, unknown>[] {
    const preparedData = dataList.map((data) => {
      const object = columnSettings.reduce(
        (acc, col) => {
          const columnBulkHeader = col.columnBulkHeader ?? '';
          acc[columnBulkHeader] = this.getFieldValue(data, col);
          return acc;
        },
        {} as Record<string, unknown>,
      );
      return object;
    });

    return preparedData;
  }

  private getFieldValue(data: Record<string, unknown>, columnSetting: ColumnSettingWithTemplateParams): unknown {
    const { groupFields, dropdownValue } = columnSetting;
    const filteredFields = groupFields.filter((group) => group.isDisplay);

    const value = filteredFields.map((groupField) => {
      const { displayConditions } = groupField;
      let val: unknown = '';

      if (!displayConditions?.length) {
        val = getValueObject(groupField, data);
      } else {
        const parentNode = getParentBeforeLastChild(groupField, data);

        if (Array.isArray(parentNode)) {
          const obj = parentNode.find((node) => {
            const isAllDisplayConditionMatch = displayConditions.every(
              (condition) => node[condition.columnCode] === condition.value,
            );
            return isAllDisplayConditionMatch;
          });

          val = obj ? obj[groupField.columnCode] : '';
        } else if (parentNode !== null && typeof parentNode === 'object') {
          const isMatch = displayConditions.every((condition) => {
            const { columnCode, value: _value } = condition;
            return parentNode[columnCode as keyof typeof parentNode] === _value;
          });
          val = isMatch ? parentNode[groupField.columnCode as keyof typeof parentNode] : '';
        } else {
          const isMatch = displayConditions.every((condition) => {
            const { columnCode, value: _value } = condition;
            return data[columnCode as keyof PreEnrollmentTransaction] === _value;
          });
          val = isMatch ? data[groupField.columnCode as keyof PreEnrollmentTransaction] : '';
        }
      }
      return this.convertToDataTypes(val, groupField, dropdownValue);
    });

    if (value.length <= 1) {
      return value[0] || '';
    }

    return value.filter(Boolean).join(' ').trim() || '';
  }

  private convertToDataTypes(
    value: unknown,
    groupField: ColumnSettingGroupFieldParams,
    dropdownValue: Record<string, any>[],
  ): unknown {
    if (groupField.dataType === ColumnSettingDataTypeEnum.flag) {
      return value ? 'YES' : 'NO';
    }

    if (groupField.dataType === ColumnSettingDataTypeEnum.enum) {
      const matchValue = dropdownValue.find((dropdown) => dropdown.value === value);
      if (matchValue) {
        return matchValue.label;
      }

      const isExist = !!value;
      const matchValueBoolean = dropdownValue.find((dropdown) => dropdown.value === isExist);
      if (matchValueBoolean) {
        return matchValueBoolean.label;
      }

      return '';
    }

    if (groupField.dataType === ColumnSettingDataTypeEnum.date) {
      if (value) {
        return date(String(value)).format(DateFormat.buddhistDayMonthYearWithLeadingZero);
      } else {
        return '';
      }
    }

    if (!value || (Array.isArray(value) && value.length === 0)) {
      return '';
    }

    return value;
  }

  private buildMatchStateFullNamePipeline(conditions: ObjectValue[]): ObjectValue[] {
    const [fullNameCondition, otherConditions] = partition(
      conditions,
      (condition) => has(condition, 'payload.firstname') || has(condition, 'payload.lastname'),
    );
    if (isEmpty(fullNameCondition)) return otherConditions;

    if (fullNameCondition.length === 1) {
      const [firstNameCondition] = fullNameCondition;
      const lastNameConditionString = JSON.stringify(firstNameCondition).replace(
        'payload.firstname',
        'payload.lastname',
      );
      const lastNameCondition = JSON.parse(lastNameConditionString);
      fullNameCondition.push(lastNameCondition);
      return [{ $or: fullNameCondition }, ...otherConditions];
    }

    return [{ $and: fullNameCondition }, ...otherConditions];
  }

  private buildMainConditionAggregation(matchConditions: ObjectValue[]): ObjectValue[] {
    const matchFullName: any = this.buildMatchStateFullNamePipeline(matchConditions);
    let filterMatchConditions = [...matchFullName];
    filterMatchConditions = filterMatchConditions.filter((condition: any) => !has(condition, 'code'));

    const filterPreEnrollId = matchConditions.find((condition) => has(condition, 'id'));

    if (filterPreEnrollId) {
      const updateMatches = matchConditions.filter((condition: any) => !has(condition, 'id'));
      const queryValue = filterPreEnrollId.id as { $regex?: string };
      const query = queryValue.$regex;
      filterMatchConditions = query ? [...updateMatches, { id: Number(query) }] : [...updateMatches];
    }

    return filterMatchConditions;
  }

  private mapBundleWithCourseData(bundles: ProductSKUBundleParams[], courseInBundles: CourseInBundleParams[]) {
    const productSKUIdToCourseMapper = Object.fromEntries(
      courseInBundles.map((item) => [
        item.productSKUId,
        {
          name: item.name,
          course: item.course,
        },
      ]),
    );

    const result = bundles.map((bundle) => {
      const { productSKUIds } = bundle;
      const productSKUCourseNames = productSKUIds.map((productSKUId) => {
        return productSKUIdToCourseMapper[productSKUId].name;
      });
      const regulator = productSKUIds.map(
        (productSKUId) => productSKUIdToCourseMapper[productSKUId].course.regulatorInfo.regulator,
      );
      const trainingCenter = productSKUIds.map(
        (productSKUId) => productSKUIdToCourseMapper[productSKUId].course.regulatorInfo.trainingCenter,
      );
      return {
        ...bundle,
        productSKUCourses: productSKUCourseNames,
        regulator,
        trainingCenter,
      };
    });

    return result;
  }
}
