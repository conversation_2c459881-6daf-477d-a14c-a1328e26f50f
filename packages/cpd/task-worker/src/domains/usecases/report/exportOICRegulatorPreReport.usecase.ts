import { setFlagsFromString } from 'v8';
import { runInNewContext } from 'vm';

import { GenericID } from '@iso/constants/commonTypes';
import {
  ApplicantTypeEnum,
  CourseObjectiveTypeEnum,
  LicenseRenewalEnum,
  LicenseTypeEnum,
  RegulatorEnum,
} from '@iso/lms/enums/course.enum';
import { PreEnrollmentTransactionStatusEnum } from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { CourseVersionParams } from '@iso/lms/types/courseVersion.type';
import { Inject } from '@nestjs/common';
import _ from 'lodash';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { PreEnrollmentDIToken } from '@applications/di/domain/preEnrollment.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import {
  ExportReportHistoryApplicantTypeTextEnum,
  ExportReportHistoryLicenseTypeTextEnum,
  ExportReportHistorySuffixLicenseRenewalTextEnum,
} from '@constants/enums/report.enum';
import { OutputSubjectRegulatorCourseParams } from '@constants/types/course.type';
import {
  CreateReportFileNameParams,
  FilterOICPreReportParams,
  OICReportCourseSubjectCodeNameParams,
} from '@constants/types/reportHistory.type';
import { OutputFindIdAndRoundDateBetweenRoundDatesParams } from '@constants/types/round.type';

import { IOrganizationDataMapper } from '@interfaces/dataMapper/organization.dataMapper.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { IEnrollmentRegulatorReportRepository } from '@interfaces/repositories/enrollmentRegulatorReport.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IPreEnrollmentReservationRepository } from '@interfaces/repositories/preEnrollmentReservation.repository.interface';
import { IPreEnrollmentTransactionRepository } from '@interfaces/repositories/preEnrollmentTransaction.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { IOrganizationService } from '@interfaces/services/organization.interface';
import { IOICPreReportService } from '@interfaces/services/report.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IStorageTransaction } from '@interfaces/transaction/storage.transaction.interface';
import { IExportOICRegulatorPreReportUseCase } from '@interfaces/usecases/report.interface';

import { arrayToHashMapByKey } from '@domains/utils/collection.util';
import { date, DateFormat } from '@domains/utils/date.util';

setFlagsFromString('--expose_gc');
const runGarbageCollector = runInNewContext('gc');

export class ExportOICRegulatorPreReportUseCase implements IExportOICRegulatorPreReportUseCase {
  constructor(
    @Inject(CourseDIToken.CourseRepository) private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository) private readonly courseVersionRepository: ICourseVersionRepository,

    @Inject(PreEnrollmentDIToken.EnrollmentRegulatorReportRepository)
    private readonly enrollmentRegulatorReportRepository: IEnrollmentRegulatorReportRepository,

    @Inject(RoundDIToken.RoundRepository) private readonly roundRepository: IRoundRepository,
    @Inject(ReportDIToken.ReportHistoryRepository) private readonly reportHistoryRepository: IReportHistoryRepository,

    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationService) private readonly organizationService: IOrganizationService,
    @Inject(OrganizationDIToken.OrganizationDataMapper)
    private readonly organizationDataMapper: IOrganizationDataMapper,

    @Inject(PreEnrollmentDIToken.PreEnrollmentReservationRepository)
    private readonly preEnrollmentReservationRepository: IPreEnrollmentReservationRepository,
    @Inject(PreEnrollmentDIToken.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,

    @Inject(ReportDIToken.OICPreReportService) private readonly oicReportService: IOICPreReportService,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,

    @Inject(InfrastructuresPersistenceDIToken.StorageTransaction)
    private readonly storageTransaction: IStorageTransaction,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: FilterOICPreReportParams): Promise<void> {
    const { reportHistoryId, roundDateFrom, roundDateTo, preEnrollmentStatus, organizationId, ...regulatorCourse } =
      params;

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist ',
        data: { reportHistoryId },
      });
    }

    if (reportHistory.status !== ReportHistoryStatusEnum.PENDING) {
      throw Exception.new({
        code: Code.ENTITY_VALIDATION_ERROR,
        message: 'report History has been operate',
        data: { reportHistoryId },
      });
    }

    try {
      this.logger.log(`Start processing the reportHistory ID ${reportHistoryId} type OIC_REGULATOR_PRE_REPORT`);

      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'organization dose not exist',
          data: { organizationId },
        });
      }

      const roundDates: [Date, Date] = [roundDateFrom, roundDateTo];
      const rounds: OutputFindIdAndRoundDateBetweenRoundDatesParams[] =
        await this.roundRepository.findIdsAndRoundDateBetweenRoundDates(roundDates, params.organizationId);

      const roundIds = rounds.map((round) => round.id);
      const roundById = arrayToHashMapByKey(rounds, 'id');

      const organizationDto = this.organizationDataMapper.toDTO(organization);
      const courseTrainingObjectiveConfigs = organizationDto.courseObjectiveConfigs.find(
        (config) => config.objectiveType === CourseObjectiveTypeEnum.TRAINING,
      );
      const oicCourseRegulatorTrainingCenterConfigs = courseTrainingObjectiveConfigs.trainingCenters.filter(
        (config) => config.regulator === RegulatorEnum.OIC,
      );

      const organizationRegulatorConfigList = this.organizationService.getRegulatorConfigList(
        {
          trainingCenter: regulatorCourse.trainingCenter,
          licenseRenewal: regulatorCourse?.licenseRenewal,
          applicantType: regulatorCourse?.applicantType,
          licenseType: regulatorCourse?.licenseType,
        },
        oicCourseRegulatorTrainingCenterConfigs,
        { isDenormalizeBothLicense: true },
      );

      const resultGroupSubjectRegulatorCourses =
        await this.courseRepository.findGroupSubjectRegulatorOICPreReportCourseByRegulatorCourseProperty(
          { ...regulatorCourse, licenseType: regulatorCourse?.licenseType && [regulatorCourse?.licenseType] },
          organizationId,
        );

      const groupSubjectRegulatorCourses = organizationRegulatorConfigList.map((organizationRegulatorConfig) => {
        const resultGroupSubjectRegulatorCourse = resultGroupSubjectRegulatorCourses.find(
          ({ regulatorInfo }) =>
            regulatorInfo.trainingCenter === organizationRegulatorConfig.trainingCenter &&
            regulatorInfo.licenseRenewal === organizationRegulatorConfig.licenseRenewal &&
            regulatorInfo.applicantType === organizationRegulatorConfig.applicantType &&
            regulatorInfo.licenseType === organizationRegulatorConfig.licenseType,
        );

        if (_.isUndefined(resultGroupSubjectRegulatorCourse)) {
          return {
            regulatorInfo: {
              trainingCenter: organizationRegulatorConfig.trainingCenter,
              licenseRenewal: organizationRegulatorConfig.licenseRenewal,
              applicantType: organizationRegulatorConfig.applicantType,
              licenseType: organizationRegulatorConfig.licenseType,
            },
            courses: [] as OutputSubjectRegulatorCourseParams[],
          };
        }

        return resultGroupSubjectRegulatorCourse;
      });

      const status = _.isNil(preEnrollmentStatus)
        ? [
            PreEnrollmentTransactionStatusEnum.APPLIED,
            PreEnrollmentTransactionStatusEnum.PASSED,
            PreEnrollmentTransactionStatusEnum.EDITED_AFTER_VERIFY,
            PreEnrollmentTransactionStatusEnum.FAILED_TO_APPLY,
            PreEnrollmentTransactionStatusEnum.REJECTED,
          ]
        : [preEnrollmentStatus];
      const preEnrollmentTransactions =
        await this.preEnrollmentTransactionRepository.findPayloadForOicReportByStatusAndRoundIds(
          roundIds,
          status,
          organizationId,
        );

      const preEnrollmentTransactionIds = preEnrollmentTransactions.map(
        (preEnrollmentTransaction) => preEnrollmentTransaction.id,
      );

      const preEnrollmentReservationIds = _.chain(preEnrollmentTransactions)
        .map('preEnrollmentReservationId')
        .filter((id) => !!id)
        .uniq()
        .value();

      const preEnrollmentReservations =
        await this.preEnrollmentReservationRepository.findAppliedOrPassedIdAndCustomerCodeByIds(
          preEnrollmentReservationIds,
        );
      const preEnrollmentReservationById = arrayToHashMapByKey(preEnrollmentReservations, 'id');

      preEnrollmentReservations.splice(0, preEnrollmentReservations.length);
      rounds.splice(0, rounds.length);
      roundIds.splice(0, roundIds.length);

      const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);
      await this.storageTransaction.runTransaction(storageRepository, async (session) => {
        for (const groupSubjectRegulatorCourse of groupSubjectRegulatorCourses) {
          const { regulatorInfo, courses: subjectRegulatorCourses } = groupSubjectRegulatorCourse;

          const courseIds = subjectRegulatorCourses.map((course) => course.id);
          const subjectRegulatorCourseById = arrayToHashMapByKey<OutputSubjectRegulatorCourseParams, GenericID>(
            subjectRegulatorCourses,
            'id',
          );

          const latestPublishCourseVersions =
            await this.courseVersionRepository.findLatestPublishVersionByCourseId(courseIds);

          const latestPublishCourseVersionsByCourseId = arrayToHashMapByKey<CourseVersionParams, GenericID>(
            latestPublishCourseVersions,
            'courseId',
          );

          const enrollmentRegulatorReports =
            await this.enrollmentRegulatorReportRepository.findIdsAndCourseIdByCourseIdAndPreEnrollmentTransactionIds(
              preEnrollmentTransactionIds,
              courseIds,
            );

          const enrollmentRegulatorReportByPreEnrollmentTransactionId = arrayToHashMapByKey(
            enrollmentRegulatorReports,
            'preEnrollmentTransactionId',
          );

          const subjectCodeNames = _.chain(subjectRegulatorCourses)
            .flatMap((course) =>
              course.subjects.map((subject) => ({
                ...subject,
                courseId: course.id,
              })),
            )
            .map((subjects) => ({
              key: `${subjects.courseId}_${subjects.subjectCode}`,
              courseId: subjects.courseId,
              courseCode: subjects.courseCode,
              subjectCode: subjects.subjectCode,
              trainingDuration: subjects.trainingDuration,
            }))
            .filter((subject) => !_.isEmpty(subject?.subjectCode))
            .uniqBy('key')
            .value();

          const uniqueSubjectCodes = _.chain(subjectCodeNames)
            .map((item) => item.subjectCode)
            .uniq()
            .value();

          const subjectCodeNameMapByKey = arrayToHashMapByKey<OICReportCourseSubjectCodeNameParams, string>(
            subjectCodeNames,
            'key',
          );

          // clear data before get pre-enrollment transaction
          subjectRegulatorCourses.splice(0, subjectRegulatorCourses.length);
          latestPublishCourseVersions.splice(0, latestPublishCourseVersions.length);
          enrollmentRegulatorReports.splice(0, enrollmentRegulatorReports.length);
          runGarbageCollector();

          const organizationMainUrl = this.organizationService.getMainUrl(organization.domain, organization.fqdn);

          const reportData = _.chain(preEnrollmentTransactions)
            .filter((preEnrollmentTransaction) =>
              this.oicReportService.filterMatchingRelationalData(preEnrollmentTransaction, {
                preEnrollmentReservationById,
                enrollmentRegulatorReportByPreEnrollmentTransactionId,
              }),
            )
            .filter((preEnrollmentTransaction) =>
              this.oicReportService.filterRegistrationCourse(preEnrollmentTransaction, {
                enrollmentRegulatorReportByPreEnrollmentTransactionId,
                courseById: subjectRegulatorCourseById,
                reportLicenseType: regulatorInfo.licenseType,
                latestPublishCourseVersionsByCourseId,
              }),
            )
            .map((preEnrollmentTransaction) => {
              const enrollmentRegulatorReport = enrollmentRegulatorReportByPreEnrollmentTransactionId.get(
                preEnrollmentTransaction.id,
              );

              const subjectRegulatorCourse = subjectRegulatorCourseById.get(enrollmentRegulatorReport.courseId);

              const oicPreReportData = this.oicReportService.buildOicPreReportRowData(preEnrollmentTransaction, {
                organizationMainUrl,
                roundById,
                preEnrollmentReservationById,
                enrollmentRegulatorReportByPreEnrollmentTransactionId,
                subjectRegulatorCourseById,
                latestPublishCourseVersionsByCourseId,
                reportLicenseType:
                  regulatorInfo.licenseType === LicenseTypeEnum.NONLIFE
                    ? LicenseTypeEnum.NONLIFE
                    : LicenseTypeEnum.LIFE,
              });

              const oicPreReportSubjectDataMapByKey = this.oicReportService.buildOicSubjectRowDataMapByKey(
                arrayToHashMapByKey(subjectRegulatorCourse.subjects, 'subjectCode'),
                uniqueSubjectCodes,
                subjectCodeNameMapByKey,
                subjectRegulatorCourse.id,
              );
              const objectOicPreReportSubjectData = Object.fromEntries(oicPreReportSubjectDataMapByKey);

              return { courseId: subjectRegulatorCourse.id, ...oicPreReportData, ...objectOicPreReportSubjectData };
            })
            .orderBy('eid')
            .map((data, index) => ({ ...data, item: index + 1 }))
            .value();

          const trainingCenter = oicCourseRegulatorTrainingCenterConfigs.find(
            (config) => config.key === regulatorInfo.trainingCenter,
          );

          const isSupportBothLicenseType = trainingCenter.licenseTypes.some(
            (regulatorConfig) =>
              regulatorConfig.licenseRenewalKeys.includes(regulatorInfo.licenseRenewal as LicenseRenewalEnum) &&
              regulatorConfig.applicantTypeKeys.includes(regulatorInfo.applicantType as ApplicantTypeEnum) &&
              regulatorConfig.key === LicenseTypeEnum.BOTH,
          );

          const isCPD = this.organizationService.checkIsCPDOrganization(organization.domain);
          const isShowCustomer = isCPD;
          const isShowHaveCost = isCPD;
          const isShowLifeLicense =
            isSupportBothLicenseType ||
            [LicenseTypeEnum.LIFE.toString(), LicenseTypeEnum.UNIT_LINKED.toString()].includes(
              regulatorInfo.licenseType,
            );
          const isShowNonLifeLicense =
            isSupportBothLicenseType || regulatorInfo.licenseType === LicenseTypeEnum.NONLIFE;

          const reportOptions = {
            isShowCustomer,
            isShowHaveCost,
            isShowLifeLicense,
            isShowNonLifeLicense,
            subjectCodeNames,
          };

          let buffer = await this.oicReportService.buildOicRegulatorPreReportFile(reportData, reportOptions);

          reportData.splice(0, reportData.length);
          subjectRegulatorCourseById.clear();
          latestPublishCourseVersionsByCourseId.clear();
          enrollmentRegulatorReportByPreEnrollmentTransactionId.clear();
          runGarbageCollector();

          const reportFileName = this.createOicPreReportFileName({
            trainingCenter: regulatorInfo.trainingCenter,
            licenseRenewal: regulatorInfo.licenseRenewal,
            applicantType: regulatorInfo.applicantType,
            licenseType: regulatorInfo.licenseType,
            roundDateFrom,
            roundDateTo,
          });

          const filePath = `xlsx/reports/${reportFileName}.xlsx`;
          const reportHistoryFile = new ReportHistoryFile({
            fileName: reportFileName,
            filePath,
          });
          reportHistory.files.push(reportHistoryFile);
          await storageRepository.upload(
            {
              raw: buffer,
              path: filePath,
              fileType: UploadFileTypeEnum.XLSX,
            },
            { session },
          );

          buffer = undefined;
          runGarbageCollector();
        }
      });

      preEnrollmentReservationById.clear();
      preEnrollmentTransactions.splice(0, preEnrollmentTransactions.length);
      runGarbageCollector();

      reportHistory.files = reportHistory.files.sort((a, b) => b.fileName.localeCompare(a.fileName));
      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;

      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  private createOicPreReportFileName(params: CreateReportFileNameParams): string {
    const { trainingCenter } = params;
    const licenseType = ExportReportHistoryLicenseTypeTextEnum[params.licenseType] ?? '';
    const roundStart = date(params.roundDateFrom).format(DateFormat.yearMonthDay);
    const roundEnd = date(params.roundDateTo).format(DateFormat.yearMonthDay);
    const licenseRenewal = ExportReportHistorySuffixLicenseRenewalTextEnum[params.licenseRenewal] ?? '';
    const applicantType = ExportReportHistoryApplicantTypeTextEnum[params.applicantType];

    const bodyName = [licenseType, applicantType, licenseRenewal].filter((text) => !_.isEmpty(text)).join('');
    return ['pre', trainingCenter, bodyName, `${roundStart}-${roundEnd}`].filter((text) => !_.isEmpty(text)).join('_');
  }
}
