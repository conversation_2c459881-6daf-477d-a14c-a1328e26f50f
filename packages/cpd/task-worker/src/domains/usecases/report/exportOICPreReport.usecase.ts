import { setFlagsFromString } from 'v8';
import { runInNewContext } from 'vm';

import { GenericID } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum, LicenseTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { PreEnrollmentTransactionStatusEnum } from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { CourseVersionParams } from '@iso/lms/types/courseVersion.type';
import { Inject } from '@nestjs/common';
import _, { isNil } from 'lodash';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { PreEnrollmentDIToken } from '@applications/di/domain/preEnrollment.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { OutputSubjectRegulatorCourseParams } from '@constants/types/course.type';
import { FilterOICPreReportParams } from '@constants/types/reportHistory.type';
import { OutputFindIdAndRoundDateBetweenRoundDatesParams } from '@constants/types/round.type';

import { IOrganizationDataMapper } from '@interfaces/dataMapper/organization.dataMapper.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { IEnrollmentRegulatorReportRepository } from '@interfaces/repositories/enrollmentRegulatorReport.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IPreEnrollmentReservationRepository } from '@interfaces/repositories/preEnrollmentReservation.repository.interface';
import { IPreEnrollmentTransactionRepository } from '@interfaces/repositories/preEnrollmentTransaction.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { IOrganizationService } from '@interfaces/services/organization.interface';
import { IOICPreReportService } from '@interfaces/services/report.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportOICPreReportUseCase } from '@interfaces/usecases/report.interface';

import { arrayToHashMapByKey } from '@domains/utils/collection.util';

setFlagsFromString('--expose_gc');
const runGarbageCollector = runInNewContext('gc');
export class ExportOICPreReportUseCase implements IExportOICPreReportUseCase {
  constructor(
    @Inject(CourseDIToken.CourseRepository) private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository) private readonly courseVersionRepository: ICourseVersionRepository,

    @Inject(PreEnrollmentDIToken.EnrollmentRegulatorReportRepository)
    private readonly enrollmentRegulatorReportRepository: IEnrollmentRegulatorReportRepository,

    @Inject(RoundDIToken.RoundRepository) private readonly roundRepository: IRoundRepository,
    @Inject(ReportDIToken.ReportHistoryRepository) private readonly reportHistoryRepository: IReportHistoryRepository,

    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationService) private readonly organizationService: IOrganizationService,
    @Inject(OrganizationDIToken.OrganizationDataMapper)
    private readonly organizationDataMapper: IOrganizationDataMapper,

    @Inject(PreEnrollmentDIToken.PreEnrollmentReservationRepository)
    private readonly preEnrollmentReservationRepository: IPreEnrollmentReservationRepository,
    @Inject(PreEnrollmentDIToken.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,

    @Inject(ReportDIToken.OICPreReportService) private readonly oicReportService: IOICPreReportService,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: FilterOICPreReportParams): Promise<void> {
    const { reportHistoryId, roundDateFrom, roundDateTo, preEnrollmentStatus, organizationId, ...regulatorCourse } =
      params;

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist ',
        data: { reportHistoryId },
      });
    }

    if (reportHistory.status !== ReportHistoryStatusEnum.PENDING) {
      throw Exception.new({
        code: Code.ENTITY_VALIDATION_ERROR,
        message: 'report History has been operate',
        data: { reportHistoryId },
      });
    }

    try {
      this.logger.log(`Start processing the reportHistory ID ${reportHistoryId} type OIC_PRE_REPORT`);

      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'organization dose not exist',
          data: { organizationId },
        });
      }

      const roundDates: [Date, Date] = [roundDateFrom, roundDateTo];
      const rounds: OutputFindIdAndRoundDateBetweenRoundDatesParams[] =
        await this.roundRepository.findIdsAndRoundDateBetweenRoundDates(roundDates, params.organizationId);
      const roundIds = rounds.map((round) => round.id);

      const roundById = arrayToHashMapByKey(rounds, 'id');

      const subjectRegulatorCourses: OutputSubjectRegulatorCourseParams[] =
        await this.courseRepository.findSubjectRegulatorOICCourseByRegulatorCourseProperty(
          { ...regulatorCourse, licenseType: this.transformLicenseTypeToLicenseTypeArray(regulatorCourse.licenseType) },
          organizationId,
        );

      const courseIds = subjectRegulatorCourses.map((course) => course.id);
      const subjectRegulatorCourseById = arrayToHashMapByKey<OutputSubjectRegulatorCourseParams, GenericID>(
        subjectRegulatorCourses,
        'id',
      );

      const latestPublishCourseVersions =
        await this.courseVersionRepository.findLatestPublishVersionByCourseId(courseIds);

      const latestPublishCourseVersionsByCourseId = arrayToHashMapByKey<CourseVersionParams, GenericID>(
        latestPublishCourseVersions,
        'courseId',
      );

      // clear data before get pre-enrollment transaction
      subjectRegulatorCourses.splice(0, subjectRegulatorCourses.length);
      latestPublishCourseVersions.splice(0, latestPublishCourseVersions.length);
      runGarbageCollector();

      const status = _.isNil(preEnrollmentStatus)
        ? [
            PreEnrollmentTransactionStatusEnum.APPLIED,
            PreEnrollmentTransactionStatusEnum.PASSED,
            PreEnrollmentTransactionStatusEnum.EDITED_AFTER_VERIFY,
            PreEnrollmentTransactionStatusEnum.FAILED_TO_APPLY,
            PreEnrollmentTransactionStatusEnum.REJECTED,
          ]
        : [preEnrollmentStatus];
      const preEnrollmentTransactions =
        await this.preEnrollmentTransactionRepository.findPayloadForOicReportByStatusAndRoundIds(
          roundIds,
          status,
          organizationId,
        );
      const preEnrollmentTransactionIds = preEnrollmentTransactions.map(
        (preEnrollmentTransaction) => preEnrollmentTransaction.id,
      );

      const preEnrollmentReservationIds = _.chain(preEnrollmentTransactions)
        .map('preEnrollmentReservationId')
        .filter((id) => !!id)
        .uniq()
        .value();

      const preEnrollmentReservations =
        await this.preEnrollmentReservationRepository.findAppliedOrPassedIdAndCustomerCodeByIds(
          preEnrollmentReservationIds,
        );
      const preEnrollmentReservationById = arrayToHashMapByKey(preEnrollmentReservations, 'id');

      const enrollmentRegulatorReports =
        await this.enrollmentRegulatorReportRepository.findIdsAndCourseIdByCourseIdAndPreEnrollmentTransactionIds(
          preEnrollmentTransactionIds,
          courseIds,
        );
      const enrollmentRegulatorReportByPreEnrollmentTransactionId = arrayToHashMapByKey(
        enrollmentRegulatorReports,
        'preEnrollmentTransactionId',
      );

      rounds.splice(0, rounds.length);
      roundIds.splice(0, roundIds.length);
      enrollmentRegulatorReports.splice(0, enrollmentRegulatorReports.length);
      preEnrollmentReservations.splice(0, preEnrollmentReservations.length);
      runGarbageCollector();

      const organizationMainUrl = this.organizationService.getMainUrl(organization.domain, organization.fqdn);
      const reportData = _.chain(preEnrollmentTransactions)
        .filter((preEnrollmentTransaction) =>
          this.oicReportService.filterMatchingRelationalData(preEnrollmentTransaction, {
            preEnrollmentReservationById,
            enrollmentRegulatorReportByPreEnrollmentTransactionId,
          }),
        )
        .filter((preEnrollmentTransaction) =>
          this.oicReportService.filterRegistrationCourse(preEnrollmentTransaction, {
            enrollmentRegulatorReportByPreEnrollmentTransactionId,
            courseById: subjectRegulatorCourseById,
            reportLicenseType: regulatorCourse?.licenseType ?? null,
            latestPublishCourseVersionsByCourseId,
          }),
        )

        .map((preEnrollmentTransaction) =>
          this.oicReportService.buildOicPreReportRowData(preEnrollmentTransaction, {
            organizationMainUrl,
            roundById,
            preEnrollmentReservationById,
            enrollmentRegulatorReportByPreEnrollmentTransactionId,
            subjectRegulatorCourseById,
            latestPublishCourseVersionsByCourseId,
            reportLicenseType:
              regulatorCourse.licenseType === LicenseTypeEnum.NONLIFE ? LicenseTypeEnum.NONLIFE : LicenseTypeEnum.LIFE,
          }),
        )
        .orderBy('eid')
        .map((data, index) => ({ ...data, item: index + 1 }))
        .value();

      // clean pre-enrollment transaction
      preEnrollmentTransactions.splice(0, preEnrollmentTransactions.length);
      runGarbageCollector();

      const organizationDto = this.organizationDataMapper.toDTO(organization);

      const courseTrainingObjectiveConfigs = organizationDto.courseObjectiveConfigs.find(
        (config) => config.objectiveType === CourseObjectiveTypeEnum.TRAINING,
      );
      const oicCourseRegulatorTrainingCenterConfigs = courseTrainingObjectiveConfigs.trainingCenters.filter(
        (config) => config.regulator === RegulatorEnum.OIC,
      );

      const organizationRegulatorCourses = this.organizationService.getRegulatorConfigList(
        {
          trainingCenter: regulatorCourse.trainingCenter,
          licenseRenewal: regulatorCourse?.licenseRenewal,
          applicantType: regulatorCourse?.applicantType,
        },
        oicCourseRegulatorTrainingCenterConfigs,
        { isContainBothLicense: true },
      );

      const licenseTypes = _.chain(organizationRegulatorCourses).map('licenseType').uniq().value();

      const filteredLicenseType = isNil(regulatorCourse?.licenseType)
        ? licenseTypes
        : licenseTypes.filter((_licenseType) => _licenseType === regulatorCourse.licenseType);

      const lifeLicenseTypesSupported = [LicenseTypeEnum.LIFE.toString(), LicenseTypeEnum.UNIT_LINKED.toString()];
      const nonLifeLicenseTypesSupported = [LicenseTypeEnum.NONLIFE.toString()];

      const isSupportBothLicense = licenseTypes.includes(LicenseTypeEnum.BOTH);

      const isShowLifeLicense =
        isSupportBothLicense ||
        filteredLicenseType.some((_licenseType) => lifeLicenseTypesSupported.includes(_licenseType));

      const isShowNonLifeLicense =
        isSupportBothLicense ||
        filteredLicenseType.some((_licenseType) => nonLifeLicenseTypesSupported.includes(_licenseType));

      const isCPD = this.organizationService.checkIsCPDOrganization(organization.domain);
      const isShowCustomer = isCPD;
      const isShowHaveCost = isCPD;

      const buffer = await this.oicReportService.buildOICPreReportFile(reportData, {
        isShowCustomer,
        isShowHaveCost,
        isShowLifeLicense,
        isShowNonLifeLicense,
        subjectCodeNames: [],
      });

      const filePath = `xlsx/reports/${reportHistory.fileName}.xlsx`;
      const reportHistoryFile = new ReportHistoryFile({
        fileName: reportHistory.fileName,
        filePath,
      });

      reportHistory.files.push(reportHistoryFile);
      const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);
      await storageRepository.upload({
        raw: buffer,
        path: filePath,
        fileType: UploadFileTypeEnum.XLSX,
      });
      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  private transformLicenseTypeToLicenseTypeArray(licenseType?: string): string[] | undefined {
    if (_.isNil(licenseType)) {
      return undefined;
    }

    if (licenseType === LicenseTypeEnum.BOTH) {
      return [LicenseTypeEnum.LIFE, LicenseTypeEnum.NONLIFE];
    }

    return [licenseType];
  }
}
