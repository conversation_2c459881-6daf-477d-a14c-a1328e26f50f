import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { ContentProviderTypeEnum } from '@iso/lms/enums/course.enum';
import { BusinessTypeEnum, EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { SummaryTSIQuizScore } from '@iso/lms/models/summaryTSIQuizScore.model';
import { CourseParams } from '@iso/lms/types/course.type';
import { CourseVersionParams } from '@iso/lms/types/courseVersion.type';
import { SummaryTSIQuizScoreParams } from '@iso/lms/types/summaryTSIQuizScore.type';
import { Inject, Injectable } from '@nestjs/common';
import { compact, find, groupBy, isEmpty, lte, orderBy, uniq, uniqBy } from 'lodash';
import { max, mean, min, std } from 'mathjs';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';
import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { MaterialMediaDIToken } from '@applications/di/domain/materialMedia.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { ProductSKUDIToken } from '@applications/di/domain/productSKU.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { UserDIToken } from '@applications/di/domain/user.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { EnrollmentStatusTextEnum } from '@constants/enums/enrollment.enum';
import { columnApprovalTSIReport, KeyColumnApprovalTSIReportEnum } from '@constants/enums/excel.enum';
import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import {
  CourseTSICodeLastQuizParams,
  CourseTSICodePartCourseItemCriteriaConfigParams,
  CourseTSICodePartCourseItemParams,
} from '@constants/types/courseVersion.type';
import { EnrollmentReportHistoryParams, QuizAnswerEnrollmentParams } from '@constants/types/enrollment.type';
import { UploadFileParams } from '@constants/types/infrastructures/storage.type';
import { ResultFindQuizIdFromProductSKUCourseParams } from '@constants/types/productSKUCourse.type';
import { ResultFindQuizIdsByMaterialIdsParams } from '@constants/types/quiz.type';
import {
  ApprovalReportTSIExportExcelParams,
  BuildReportTSIParams,
  FilterExportApprovalTSIReportHistoryParams,
  ReportQuizScoreParams,
} from '@constants/types/reportHistory.type';
import { ResultFindUserProfileByIdsParams, UserReportHistoryParams } from '@constants/types/user.type';

import { ICourseDataMapper } from '@interfaces/dataMapper/course.dataMapper.interface';
import { ICourseVersionDataMapper } from '@interfaces/dataMapper/courseVersion.dataMapper.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseItemCriteriaConfigRepository } from '@interfaces/repositories/courseItemCriteriaConfig.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IProductSKUCourseRepository } from '@interfaces/repositories/productSKUCourse.repository.interface';
import { IQuizAnswerRepository, IQuizRepository } from '@interfaces/repositories/quiz.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { ISummaryTSIQuizScoreRepository } from '@interfaces/repositories/summaryTSIQuizScore.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { IEnrollmentService } from '@interfaces/services/enrollment.interface';
import { IOrganizationService } from '@interfaces/services/organization.interface';
import { ISummaryTSIQuizScoreService } from '@interfaces/services/summaryTSIQuizScore.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportApprovalTSIReportUseCase } from '@interfaces/usecases/report.interface';

import { arrayToHashMapByKey } from '@domains/utils/collection.util';
import { date, DateFormat } from '@domains/utils/date.util';

@Injectable()
export class ExportApprovalTSIReportUseCase implements IExportApprovalTSIReportUseCase {
  constructor(
    // Repository
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(ReportDIToken.ReportHistoryRepository)
    private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(EnrollmentDIToken.EnrollmentService)
    private readonly enrollmentService: IEnrollmentService,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(EnrollmentDIToken.SummaryTSIQuizScoreRepository)
    private readonly summaryTSIQuizScoreRepository: ISummaryTSIQuizScoreRepository,
    @Inject(RoundDIToken.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(CourseDIToken.CourseItemCriteriaConfigRepository)
    private readonly courseItemCriteriaConfigRepository: ICourseItemCriteriaConfigRepository,
    @Inject(ProductSKUDIToken.ProductSKUCourseRepository)
    private readonly productSKUCourseRepository: IProductSKUCourseRepository,
    @Inject(EnrollmentDIToken.SummaryTSIQuizScoreService)
    private readonly summaryTSIQuizScoreService: ISummaryTSIQuizScoreService,
    @Inject(MaterialMediaDIToken.QuizRepository)
    private readonly quizRepository: IQuizRepository,
    @Inject(EnrollmentDIToken.QuizAnswerRepository)
    private readonly quizAnswerRepository: IQuizAnswerRepository,
    @Inject(UserDIToken.UserRepository) private readonly userRepository: IUserRepository,

    // DataMapper
    @Inject(CourseDIToken.CourseDataMapper) private readonly courseDataMapper: ICourseDataMapper,
    @Inject(CourseDIToken.CourseVersionDataMapper) private readonly courseVersionDataMapper: ICourseVersionDataMapper,
    // Service
    @Inject(OrganizationDIToken.OrganizationService)
    private readonly organizationService: IOrganizationService,

    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: FilterExportApprovalTSIReportHistoryParams): Promise<any> {
    const {
      roundDateFrom,
      roundDateTo,
      passDateFrom,
      passDateTo,
      courseIds,
      businessType,
      status,
      fileName,
      reportHistoryId,
      organizationId,
    } = params;

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist',
        data: { reportHistoryId },
      });
    }

    try {
      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'organization dose not exist',
          data: { organizationId },
        });
      }

      const roundDates: [Date, Date] = [roundDateFrom, roundDateTo];
      const rounds = await this.roundRepository.findIdsAndRoundDateBetweenRoundDates(roundDates, organizationId);
      const roundIds = rounds.sort((a, b) => a.roundDate.getTime() - b.roundDate.getTime()).map((round) => round.id);

      const tsiCourses = await this.courseRepository.findTSICourseByOrganizationIdAndCourseIds(
        courseIds ?? [],
        organizationId,
      );
      const tsiCourseIds = tsiCourses.map((course) => course.id);

      const firstCalculatedTSIQuizScore = await this.summaryTSIQuizScoreRepository.findOne(
        {},
        { sort: { periodCutOffAt: 1 } },
      );

      const enrollments = await this.enrollmentRepository.findEnrollmentForTSIApprovalReportByStatusBusinessAndRoundIds(
        {
          organizationId,
          passedAtDateRange: passDateFrom ? [passDateFrom, passDateTo] : undefined,
          roundIds,
          courseIds: tsiCourseIds,
          business: businessType as BusinessTypeEnum,
          status,
        },
      );

      const userIds = enrollments.map((enrollment) => enrollment.userId);
      const enrollmentCourseIds = uniq(enrollments.map((enrollment) => enrollment.courseId));
      const courseVersionIds = enrollments.map((enrollment) => enrollment.courseVersionId);

      const [users, courseVersions] = await Promise.all([
        this.userRepository.findUserProfileByIds(userIds),
        this.courseVersionRepository.findByIds(courseVersionIds),
      ]);

      const tisCourseById = arrayToHashMapByKey(tsiCourses, 'id');
      const courses = enrollmentCourseIds.map((id) => tisCourseById.get(id)).filter(Boolean);

      const courseById = arrayToHashMapByKey<CourseParams, GenericID>(courses, 'id');

      const userById = arrayToHashMapByKey<ResultFindUserProfileByIdsParams, GenericID>(users, 'guid');

      const dtoCourseVersions = this.courseVersionDataMapper.toDTOs(courseVersions);
      const courseVersionById = arrayToHashMapByKey<CourseVersionParams, GenericID>(dtoCourseVersions, 'id');

      users.splice(0, users.length);
      courses.splice(0, courses.length);
      courseVersions.splice(0, courseVersions.length);
      tisCourseById.clear();
      enrollmentCourseIds.splice(0, enrollmentCourseIds.length);

      const tsiCourseEnrollment = enrollments.filter((enrollment) => {
        const isCourseExist = courseById.has(enrollment.courseId);
        const isCourseVersionExist = courseVersionById.has(enrollment.courseVersionId);
        const isUserExist = userById.has(enrollment.userId);
        return isCourseExist && isCourseVersionExist && isUserExist;
      });

      const dataList = await this.getBuildReportTSI(tsiCourseEnrollment, {
        userById,
        courseById,
        courseVersionById,
      });

      enrollments.splice(0, enrollments.length);
      tsiCourseEnrollment.splice(0, tsiCourseEnrollment.length);

      const sortedDataList = orderBy(
        dataList,
        [(data) => roundIds.indexOf(data.enrollment.roundId), (data) => data.enrollment.courseId],
        ['asc', 'asc'],
      );

      const excelRecords = await this.buildTSIReportExcel(sortedDataList, firstCalculatedTSIQuizScore);
      const excel = new ExcelBuilderService();
      const sheetName = 'Sheet1';

      const isCPD = this.organizationService.checkIsCPDOrganization(organization.domain);
      const newColumnApprovalTSIReport = isCPD
        ? columnApprovalTSIReport
        : columnApprovalTSIReport.filter((item) => item.key !== KeyColumnApprovalTSIReportEnum.COMPANY_CODE);

      excel
        .addWorksheet(sheetName)
        .setColumnHeader(newColumnApprovalTSIReport, sheetName)
        .buildRawFileExcel([{ workSheetName: 'Sheet1', rowsData: excelRecords }]);

      const buffer = await excel.getFileBuffer();

      const targetKey = `xlsx/reports/${fileName}.xlsx`;
      const uploadFileParams: UploadFileParams = {
        raw: buffer as Buffer,
        path: targetKey,
        fileType: UploadFileTypeEnum.XLSX,
      };
      await this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS).upload(uploadFileParams);

      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      const reportHistoryFile = new ReportHistoryFile({
        filePath: targetKey,
        fileName,
      });
      reportHistory.files.push(reportHistoryFile);

      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  private async buildTSIReportExcel(
    items: BuildReportTSIParams[],
    firstCalculatedTSIQuizScore: Nullable<SummaryTSIQuizScore>,
  ): Promise<ApprovalReportTSIExportExcelParams[]> {
    const passedAts = items
      .map((item) => item.enrollment.passedAt)
      .filter(Boolean)
      .map((item) => item.getTime());
    const minPassedAt = isEmpty(passedAts) ? null : date(min(passedAts)).toDate();
    const maxPassedAt = isEmpty(passedAts) ? null : date(max(passedAts)).toDate();
    const periodCutOffStart = minPassedAt
      ? await this.summaryTSIQuizScoreRepository.findMinLatestPeriodCusOffAtByLtePeriodCusOff(minPassedAt)
      : null;

    const periodCutOffStartAt = date(periodCutOffStart ?? (firstCalculatedTSIQuizScore ?? {})?.periodCutOffAt)
      .startOf('day')
      .toDate();
    const periodCutOffEndAt = date(maxPassedAt).endOf('day').toDate();
    const courseIds = items.map((item) => item.enrollment.courseId);

    const groupSummaryTSIScore = await this.summaryTSIQuizScoreRepository.findLatestGroupSummaryTSIQuizScore(
      periodCutOffStartAt,
      periodCutOffEndAt,
      courseIds,
    );

    const quizScoresGroupByQuizId = items
      .filter((item: BuildReportTSIParams) => item.latestQuizAnswer)
      .reduce(
        (acc, cur) => {
          const { latestQuizAnswer } = cur;
          const { quizId, userPoint } = latestQuizAnswer;
          const scores = acc[quizId] ?? [];
          acc[quizId] = [...scores, userPoint];
          return acc;
        },
        {} as Record<GenericID, number[]>,
      );

    const summaryQuizScoreByQuizId = Object.entries(quizScoresGroupByQuizId).reduce(
      (acc, [quizId, scores]) => {
        acc[quizId] = {
          quizId,
          maxScore: max(scores),
          minScore: min(scores),
          averageScore: Math.round(mean(scores) * 100) / 100,
          standardDeviationScore: Math.round(std(scores) * 100) / 100,
        };
        return acc;
      },
      {} as Record<GenericID, ReportQuizScoreParams>,
    );

    const result = items.map((item: BuildReportTSIParams) => {
      const { user, course, courseVersion, latestQuizAnswer, courseTSICodeLastQuiz, enrollment } = item;
      const { report } = courseVersion;
      const tsiCode: string = report?.tsiCode || '';

      const quizAnswer = latestQuizAnswer;

      const { courseId, materialMediaId, itemId } = courseTSICodeLastQuiz;

      const summaryTSIQuizScore = find(
        groupSummaryTSIScore,
        (_item) =>
          _item.courseId === courseId &&
          _item.tsiCode === tsiCode &&
          _item.itemId === itemId &&
          _item.materialMediaId === materialMediaId &&
          lte(_item.periodCutOffAt, enrollment.passedAt),
      );

      const trainingStartDate = enrollment?.expiredAt ? date(enrollment.expiredAt).format(DateFormat.yearMonthDay) : '';
      const trainingEndDate = enrollment?.expiredAt ? date(enrollment.expiredAt).format(DateFormat.yearMonth) : '';

      const salute = user?.profile?.salute;
      const firstname = `${salute} ${user?.profile?.firstname}`;
      const lastname = user?.profile?.lastname;
      const mobilePhoneNumber = user?.profile?.mobilePhoneNumber;
      const citizenId = user?.citizenId;
      const citizenType = '1';
      const email = user?.email;
      const newEnrollmentStatus = this.enrollmentService.transformEnrollmentStatus(
        enrollment.status,
        enrollment.expiredAt,
      );

      const enrollmentStatus = EnrollmentStatusTextEnum[newEnrollmentStatus];

      const quizId = quizAnswer?.quizId;
      const userPoint = quizAnswer?.userPoint;
      const totalPoint = quizAnswer?.totalPoint;

      const courseCode = course.code;
      const courseName = courseVersion.name;

      const startDate = enrollment?.startedAt
        ? date(enrollment.startedAt).format(DateFormat.buddhistDayMonthYearWithLeadingZero)
        : '';
      const endDate = enrollment?.passedAt
        ? date(enrollment.passedAt).format(DateFormat.buddhistDayMonthYearWithLeadingZero)
        : '';
      const approvalDate = enrollment?.approvalAt
        ? date(enrollment.approvalAt).format(DateFormat.buddhistDayMonthYearWithLeadingZero)
        : '';
      const companyCode = this.getCompanyCode(enrollment.business, enrollment.customerCode);

      const isEnrollmentPassed = !!enrollment.passedAt;
      const isSummaryCalculated = !!firstCalculatedTSIQuizScore.periodCutOffAt;

      const isPassedAfterFirstCalculated =
        isEnrollmentPassed &&
        isSummaryCalculated &&
        date(enrollment.passedAt).isAfter(date(firstCalculatedTSIQuizScore.periodCutOffAt));

      const { maxScore, minScore, averageScore, standardDeviationScore } = this.getSummaryCalculateScoreData({
        isDoQuiz: !!quizAnswer,
        isPassedAfterFirstCalculated,
        summaryTSIQuizScore,
        alreadyCalculatedQuizScore: summaryQuizScoreByQuizId[quizId],
      });

      return {
        tsiCode,
        trainingStartDate,
        trainingEndDate,
        citizenType,
        citizenId,
        email,
        firstname,
        lastname,
        mobilePhoneNumber,
        userPoint,
        totalPoint,
        maxScore,
        minScore,
        averageScore,
        standardDeviationScore,
        courseCode,
        courseName,
        startDate,
        endDate,
        approvalDate,
        companyCode,
        enrollmentStatus,
      };
    });

    return result;
  }

  private getCompanyCode(business: BusinessTypeEnum, customerCode: string): string {
    return business === BusinessTypeEnum.B2C ? BusinessTypeEnum.B2C : customerCode;
  }

  private getSummaryCalculateScoreData(payload: {
    isDoQuiz: boolean;
    isPassedAfterFirstCalculated: boolean;
    summaryTSIQuizScore: SummaryTSIQuizScoreParams;
    alreadyCalculatedQuizScore: ReportQuizScoreParams;
  }) {
    const { isDoQuiz, isPassedAfterFirstCalculated, summaryTSIQuizScore, alreadyCalculatedQuizScore } = payload;

    if (!isDoQuiz) {
      return {
        maxScore: undefined,
        minScore: undefined,
        averageScore: undefined,
        standardDeviationScore: undefined,
      };
    }

    if (isPassedAfterFirstCalculated && !summaryTSIQuizScore) {
      return {
        maxScore: 0,
        minScore: 0,
        averageScore: 0,
        standardDeviationScore: 0,
      };
    }

    if (summaryTSIQuizScore) {
      return {
        maxScore: summaryTSIQuizScore.max,
        minScore: summaryTSIQuizScore.min,
        averageScore: parseFloat(summaryTSIQuizScore.avg.toFixed(2)),
        standardDeviationScore: parseFloat(summaryTSIQuizScore.std.toFixed(2)),
      };
    }

    if (alreadyCalculatedQuizScore) {
      return {
        maxScore: alreadyCalculatedQuizScore.maxScore,
        minScore: alreadyCalculatedQuizScore.minScore,
        averageScore: alreadyCalculatedQuizScore.averageScore,
        standardDeviationScore: alreadyCalculatedQuizScore.standardDeviationScore,
      };
    }

    return {
      maxScore: undefined,
      minScore: undefined,
      averageScore: undefined,
      standardDeviationScore: undefined,
    };
  }

  private async getBuildReportTSI(
    enrollments: EnrollmentReportHistoryParams[],
    mapData: {
      userById: Map<GenericID, ResultFindUserProfileByIdsParams>;
      courseById: Map<GenericID, CourseParams>;
      courseVersionById: Map<GenericID, CourseVersionParams>;
    },
  ): Promise<BuildReportTSIParams[]> {
    const courseVersionIds = Array.from(mapData.courseVersionById.keys());
    const courses = Array.from(mapData.courseById.values());

    const partCourseItemWithCriteriaConfigList = await this.getPartCourseItemWithCriteriaConfig(
      courses,
      courseVersionIds,
    );

    const courseTSICodeLastQuizList = this.summaryTSIQuizScoreService.getCourseTSICodeLastQuizList(
      partCourseItemWithCriteriaConfigList,
    );

    const materialMediaIds = compact(
      uniqBy(courseTSICodeLastQuizList, 'materialMediaId').map((item) => item.materialMediaId),
    );

    const itemIds = compact(uniqBy(courseTSICodeLastQuizList, 'itemId').map((item) => item.itemId));

    const quizProductSKUCourses = await this.productSKUCourseRepository.findQuizIdByCourseItemIds(itemIds);

    const quizMaterialMedias = await this.quizRepository.findQuizIdByMaterialMediaIds(materialMediaIds);

    const quizIds = compact(
      uniqBy([...quizProductSKUCourses, ...quizMaterialMedias], 'quizId').map((item) => item.quizId),
    ) as GenericID[];

    const enrollmentIds = enrollments
      .filter((item) =>
        [
          EnrollmentStatusEnum.PASSED,
          EnrollmentStatusEnum.PENDING_APPROVAL,
          EnrollmentStatusEnum.VERIFIED,
          EnrollmentStatusEnum.APPROVED,
        ].includes(item.status),
      )
      .map((item) => item.id);

    const enrollmentQuizAnswers =
      await this.quizAnswerRepository.findLatestQuizAnswerOfQuizAndEnrollmentByQuizIdsAndEnrollmentIds(
        quizIds,
        enrollmentIds,
      );

    const quizMaterialMediaIdByMaterialMediaId = arrayToHashMapByKey(quizMaterialMedias, 'materialMediaId');
    const quizProductSKUCoursesByCourseItemId = arrayToHashMapByKey(quizProductSKUCourses, 'courseItemId');

    const payloadReportDataList: BuildReportTSIParams[] = [];

    for (const enrollment of enrollments) {
      const courseTSICodeLastQuiz = courseTSICodeLastQuizList.find(
        (item) =>
          item.courseId === enrollment.courseId &&
          item.courseVersionIds.includes(enrollment.courseVersionId as GenericID),
      );

      if (courseTSICodeLastQuiz) {
        const quizId = this.getQuizIdInCourseItem(
          courseTSICodeLastQuiz,
          quizMaterialMediaIdByMaterialMediaId,
          quizProductSKUCoursesByCourseItemId,
        );

        if (quizId) {
          const quizAnswer = enrollmentQuizAnswers.find(
            (item) => item.quizId === quizId && item.enrollmentId === enrollment.id,
          );

          const latestQuizAnswer: Nullable<QuizAnswerEnrollmentParams> = quizAnswer
            ? {
                quizAnswerId: quizAnswer.id,
                enrollmentId: quizAnswer.enrollmentId,
                quizId: quizAnswer.quizId,
                userPoint: quizAnswer.userPoint,
                totalPoint: quizAnswer.totalPoint,
              }
            : null;

          const user = mapData.userById.get(enrollment.userId) as UserReportHistoryParams;
          const course = mapData.courseById.get(enrollment.courseId);
          const courseVersion = mapData.courseVersionById.get(enrollment.courseVersionId);

          payloadReportDataList.push({
            enrollment,
            course,
            courseVersion,
            user,
            latestQuizAnswer,
            courseTSICodeLastQuiz,
          });
        }
      }
    }

    return payloadReportDataList;
  }

  private getQuizIdInCourseItem(
    courseTSICodeLastQuiz: CourseTSICodeLastQuizParams,
    quizMaterialMediaIdByMaterialMediaId: Map<GenericID, ResultFindQuizIdsByMaterialIdsParams>,
    quizProductSKUCoursesByCourseItemId: Map<GenericID, ResultFindQuizIdFromProductSKUCourseParams>,
  ): GenericID {
    const { materialMediaId, itemId: courseItemId } = courseTSICodeLastQuiz;
    if (courseTSICodeLastQuiz.materialMediaId) {
      const quiz = quizMaterialMediaIdByMaterialMediaId.get(materialMediaId);
      return quiz?.quizId;
    } else if (courseTSICodeLastQuiz.itemId) {
      const quiz = quizProductSKUCoursesByCourseItemId.get(courseItemId);
      return quiz?.quizId;
    }
    return null;
  }

  async getPartCourseItemWithCriteriaConfig(
    courses: CourseParams[],
    courseVersionIds: GenericID[],
  ): Promise<CourseTSICodePartCourseItemCriteriaConfigParams[]> {
    const courseGroupByContentProviderType = groupBy(courses, 'contentProviderType');

    const selfCourses = courseGroupByContentProviderType[ContentProviderTypeEnum.SELF] ?? [];
    const skilllaneCourses = courseGroupByContentProviderType[ContentProviderTypeEnum.EXTERNAL] ?? [];

    const courseItemCriteriaConfigs = await this.courseItemCriteriaConfigRepository.find({
      courseVersionId: { $in: courseVersionIds },
    });
    const selfCoursePartCourseItems = await Promise.all(
      selfCourses.map((course) => this.courseVersionRepository.findSelfCourseTSIQuiz({ courseId: course.id })),
    );
    const skilllaneCoursesPartCourseItems = await Promise.all(
      skilllaneCourses.map((course) =>
        this.courseVersionRepository.findSkillLaneCourseTSIQuiz({
          courseId: course.id,
        }),
      ),
    );
    const coursePartCourseItemList = [...selfCoursePartCourseItems, ...skilllaneCoursesPartCourseItems]
      .flat()
      .map((courseVersion) =>
        this.summaryTSIQuizScoreService.mergePartCourseItemWithCriteriaConfig(courseVersion, courseItemCriteriaConfigs),
      );

    return coursePartCourseItemList;
  }

  private async findPartCourseItemWithCriteriaConfig(courseIds: GenericID[], courseVersionIds: GenericID[]) {
    const result: CourseTSICodePartCourseItemCriteriaConfigParams[] = [];
    const courses = await this.courseRepository.find({ id: { $in: courseIds } });

    const courseItemCriteriaConfigs = await this.courseItemCriteriaConfigRepository.find({
      courseVersionId: { $in: courseVersionIds },
    });

    for (const course of courses) {
      let coursePartCourseItemList: CourseTSICodePartCourseItemParams[];

      if (course.contentProviderType === ContentProviderTypeEnum.SELF) {
        coursePartCourseItemList = await this.courseVersionRepository.findSelfCourseTSIQuiz({ courseId: course.id });
      }

      if (course.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
        coursePartCourseItemList = await this.courseVersionRepository.findSkillLaneCourseTSIQuiz({
          courseId: course.id,
        });
      }

      result.push(
        ...coursePartCourseItemList.map((courseVersion) =>
          this.summaryTSIQuizScoreService.mergePartCourseItemWithCriteriaConfig(
            courseVersion,
            courseItemCriteriaConfigs,
          ),
        ),
      );
    }

    return result;
  }

  private async findOneQuizIdByMaterialMedia(materialMediaIds: GenericID[]) {
    const result = await this.quizRepository.aggregate([
      {
        $match: { materialMediaId: { $in: materialMediaIds } },
      },
      {
        $project: {
          _id: 0,
          materialMediaId: 1,
          quizId: '$id',
        },
      },
    ]);

    return result;
  }

  private async findLatestGroupSummaryTSIQuizScore(
    minPassedAt: Date,
    maxPassedAt: Date,
    courseIds: GenericID[],
  ): Promise<SummaryTSIQuizScoreParams[]> {
    const pipeline: Record<string, unknown>[] = [
      {
        $match: {
          periodCutOffAt: {
            $gte: minPassedAt,
            $lt: maxPassedAt,
          },
          courseId: { $in: courseIds },
        },
      },
      { $sort: { periodCutOffAt: -1 } },
      {
        $group: {
          _id: {
            tsiCode: '$tsiCode',
            courseId: '$courseId',
            materialMediaId: '$materialMediaId',
            itemId: '$itemId',
          },
          cumulativeScores: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 0,
          tsiCode: '$_id.tsiCode',
          courseId: '$_id.courseId',
          materialMediaId: '$_id.materialMediaId',
          itemId: '$_id.itemId',
          cumulativeScores: '$cumulativeScores',
        },
      },
      {
        $unwind: {
          path: '$cumulativeScores',
        },
      },
      {
        $replaceRoot: {
          newRoot: '$cumulativeScores',
        },
      },
    ];

    const result = await this.summaryTSIQuizScoreRepository.aggregate<SummaryTSIQuizScoreParams>(pipeline);
    return result;
  }
}
