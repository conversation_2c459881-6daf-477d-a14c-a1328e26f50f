import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { CourseParams } from '@iso/lms/types/course.type';
import { EnrollmentParams } from '@iso/lms/types/enrollment.type';
import { RoundParams } from '@iso/lms/types/round.type';
import { SurveySubmissionParams } from '@iso/lms/types/surveySubmission.type';
import { UserParams } from '@iso/lms/types/user.type';
import { Inject, Logger } from '@nestjs/common';
import { uniqBy, intersection, isEmpty } from 'lodash';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';
import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { MaterialMediaDIToken } from '@applications/di/domain/materialMedia.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { UserDIToken } from '@applications/di/domain/user.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { ExportSurveySubmissionReportParams } from '@constants/types/reportHistory.type';

import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { ISurveyRepository } from '@interfaces/repositories/survey.repository.interface';
import { ISurveySubmissionRepository } from '@interfaces/repositories/surveySubmission.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { IEnrollmentService } from '@interfaces/services/enrollment.interface';
import { IMaterialMediaService } from '@interfaces/services/materialMedia.service.interface';
import { ISurveySubmissionReportService } from '@interfaces/services/surveySubmissionReport.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportSurveySubmissionReportUseCase } from '@interfaces/usecases/report.interface';

import { date } from '@domains/utils/date.util';

export class ExportSurveySubmissionReportUseCase implements IExportSurveySubmissionReportUseCase {
  constructor(
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(ReportDIToken.ReportHistoryRepository)
    private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(MaterialMediaDIToken.SurveySubmissionRepository)
    private readonly surveySubmissionRepository: ISurveySubmissionRepository,
    @Inject(MaterialMediaDIToken.SurveyRepository)
    private readonly surveyRepository: ISurveyRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(RoundDIToken.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @Inject(MaterialMediaDIToken.MaterialMediaService)
    private readonly materialMediaService: IMaterialMediaService,
    @Inject(ReportDIToken.SurveySubmissionReportService)
    private readonly surveySubmissionReportService: ISurveySubmissionReportService,
    @Inject(EnrollmentDIToken.EnrollmentService)
    private readonly enrollmentService: IEnrollmentService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: ExportSurveySubmissionReportParams): Promise<void> {
    const {
      reportHistoryId,
      organizationId,
      materialMediaId,
      submissionDateFrom,
      submissionDateTo,
      courseIds,
      enrollmentStatus,
    } = params;

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist ',
        data: { reportHistoryId },
      });
    }

    try {
      if (!submissionDateFrom || !submissionDateTo) {
        throw Exception.new({
          code: Code.ENTITY_VALIDATION_ERROR,
          message: 'Please provide both submissionDateFrom and submissionDateTo',
        });
      }

      if (reportHistory.status !== ReportHistoryStatusEnum.PENDING) {
        throw Exception.new({
          code: Code.ENTITY_VALIDATION_ERROR,
          message: 'report History has been operate',
          data: { reportHistoryId },
        });
      }

      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'organization dose not exist',
          data: { organizationId },
        });
      }

      const survey = await this.surveyRepository.findOne({ materialMediaId });
      if (!survey) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'survey dose not exist',
          data: { materialMediaId },
        });
      }

      const filterEnrollmentStatus = enrollmentStatus ? enrollmentStatus : [];
      const surveySubmissions = await this.getSurveySubmissions(submissionDateFrom, submissionDateTo, materialMediaId);
      const filterSurveySubmissionIds = surveySubmissions.map((item) => item.id);
      let enrollments = await this.getEnrollmentsByStatus(
        organizationId,
        filterEnrollmentStatus,
        filterSurveySubmissionIds,
      );
      enrollments = enrollments.map((val) => {
        val.status = this.enrollmentService.transformEnrollmentStatus(val.status, val.expiredAt);

        return val;
      });
      const enrollmentCourseIds = uniqBy(enrollments, 'courseId').map((item) => item.courseId);
      const enrollmentUserIds = uniqBy(enrollments, 'userId').map((item) => item.userId);
      const enrollmentRoundIds = uniqBy(enrollments, 'roundId').map((item) => item.roundId);
      const users = await this.getUsers(enrollmentUserIds);
      const filterCourseIds = courseIds ? intersection(enrollmentCourseIds, courseIds) : enrollmentCourseIds;
      const courses = await this.getCourseVersionsByIds(filterCourseIds);
      const surveyQuestions = this.materialMediaService.getSurveyQuestions(survey.formSchema);
      const rounds = await this.getRounds(enrollmentRoundIds);

      const { headerColumns, rowColumns } = this.surveySubmissionReportService.buildReportExcel(
        surveyQuestions,
        surveySubmissions,
        enrollments,
        courses,
        rounds,
        users,
      );

      const excel = new ExcelBuilderService();
      const sheetName = 'Sheet1';
      excel
        .addWorksheet(sheetName)
        .setColumnHeader(headerColumns, sheetName)
        .buildRawFileExcel([{ workSheetName: sheetName, rowsData: rowColumns }]);

      const buffer = await excel.getFileBuffer();
      const filePath = `xlsx/reports/${Date.now()}_${reportHistory.fileName}.xlsx`;
      const reportHistoryFile = new ReportHistoryFile({
        fileName: reportHistory.fileName,
        filePath,
      });

      reportHistory.files.push(reportHistoryFile);
      const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);
      await storageRepository.upload({
        raw: buffer,
        path: filePath,
        fileType: UploadFileTypeEnum.XLSX,
      });

      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;

      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  async getSurveySubmissions(
    dateForm: Date,
    dateTo: Date,
    materialMediaId: GenericID,
  ): Promise<SurveySubmissionParams[]> {
    const pipeline: Record<string, unknown>[] = [
      {
        $match: {
          $and: [{ materialMediaId }, { createdAt: { $gte: dateForm } }, { createdAt: { $lte: dateTo } }],
        },
      },
      { $sort: { createdAt: 1 } },
    ];

    return this.surveySubmissionRepository.aggregate(pipeline);
  }

  async getEnrollmentsByStatus(
    organizationId: GenericID,
    status: EnrollmentStatusEnum[],
    surveySubmissionIds: GenericID[],
  ): Promise<EnrollmentParams[]> {
    const enrollmentStatusDefaultList = [
      EnrollmentStatusEnum.IN_PROGRESS,
      EnrollmentStatusEnum.PENDING_RESULT,
      EnrollmentStatusEnum.PASSED,
      EnrollmentStatusEnum.COMPLETED,
      EnrollmentStatusEnum.PENDING_APPROVAL,
      EnrollmentStatusEnum.VERIFIED,
      EnrollmentStatusEnum.APPROVED,
      EnrollmentStatusEnum.REJECTED,
      EnrollmentStatusEnum.EXPIRED,
      EnrollmentStatusEnum.CANCELED,
    ];

    const matchings = [];
    if (isEmpty(status)) {
      matchings.push({
        $or: [
          {
            status: { $in: enrollmentStatusDefaultList },
          },
        ],
      });
    }

    if (status.includes(EnrollmentStatusEnum.IN_PROGRESS)) {
      matchings.push({
        $or: [
          {
            status: EnrollmentStatusEnum.IN_PROGRESS,
            $or: [{ expiredAt: { $gte: date().toDate() } }, { expiredAt: { $eq: null } }],
          },
        ],
      });
    }

    if (status.includes(EnrollmentStatusEnum.PASSED)) {
      matchings.push({
        $or: [
          {
            status: EnrollmentStatusEnum.PASSED,
            expiredAt: {
              $gt: date().toDate(),
            },
          },
        ],
      });
    }

    if (status.includes(EnrollmentStatusEnum.PENDING_APPROVAL)) {
      matchings.push({
        $or: [
          {
            status: EnrollmentStatusEnum.PENDING_APPROVAL,
          },
          {
            status: EnrollmentStatusEnum.PASSED,
            expiredAt: {
              $lte: date().toDate(),
            },
          },
        ],
      });
    }

    if (status.includes(EnrollmentStatusEnum.EXPIRED)) {
      matchings.push({
        $or: [
          {
            status: EnrollmentStatusEnum.IN_PROGRESS,
            expiredAt: {
              $lte: date().toDate(),
            },
          },
          { status: EnrollmentStatusEnum.EXPIRED },
        ],
      });
    }

    const newStatus = status.filter(
      (val) =>
        val === EnrollmentStatusEnum.PENDING_RESULT ||
        val === EnrollmentStatusEnum.COMPLETED ||
        val === EnrollmentStatusEnum.VERIFIED ||
        val === EnrollmentStatusEnum.APPROVED ||
        val === EnrollmentStatusEnum.REJECTED ||
        val === EnrollmentStatusEnum.CANCELED,
    );

    if (newStatus.length > 0) {
      matchings.push({
        $or: [
          {
            status: { $in: newStatus },
          },
        ],
      });
    }

    const pipeline: Record<string, unknown>[] = [
      {
        $match: {
          organizationId,
          $or: [...matchings],
          learningProgress: {
            $elemMatch: {
              surveySubmissionId: { $in: surveySubmissionIds },
            },
          },
        },
      },
    ];

    return this.enrollmentRepository.aggregate(pipeline);
  }

  async getCourseVersionsByIds(courseIds: Nullable<GenericID[]>): Promise<CourseParams[]> {
    if (!courseIds) return [];

    const pipeline: Record<string, unknown>[] = [
      {
        $match: {
          id: {
            $in: courseIds,
          },
        },
      },
      {
        $lookup: {
          from: 'course-versions',
          localField: 'id',
          foreignField: 'courseId',
          as: 'courseVersion',
        },
      },
      {
        $unwind: '$courseVersion',
      },
    ];

    return this.courseRepository.aggregate(pipeline);
  }

  async getUsers(userIds: GenericID[]): Promise<UserParams[]> {
    if (!userIds) return [];

    const pipeline: Record<string, unknown>[] = [
      {
        $match: {
          guid: {
            $in: userIds,
          },
        },
      },
    ];

    return this.userRepository.aggregate(pipeline);
  }

  async getRounds(roundIds: GenericID[]): Promise<RoundParams[]> {
    if (!roundIds) return [];

    return this.roundRepository.find({ id: { $in: roundIds } });
  }
}
