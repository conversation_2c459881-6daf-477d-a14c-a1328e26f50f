import { setFlagsFromString } from 'v8';
import { runInNewContext } from 'vm';

import { GenericID } from '@iso/constants/commonTypes';
import {
  ContentProviderTypeEnum,
  CourseObjectiveTypeEnum,
  LicenseTypeEnum,
  RegulatorEnum,
} from '@iso/lms/enums/course.enum';
import { CourseItemStatusCodeEnum } from '@iso/lms/enums/courseItemProgress.enum';
import { QuizTestTypeEnum } from '@iso/lms/enums/quiz.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { CourseItemProgressParams } from '@iso/lms/types/courseItemProgress.type';
import { CourseVersionParams } from '@iso/lms/types/courseVersion.type';
import { EnrollmentRegulatorReportParams } from '@iso/lms/types/enrollmentRegulatorReport.type';
import { PreEnrollmentTransactionParams } from '@iso/lms/types/preEnrollmentTransaction.type';
import { QuizAnswerParams } from '@iso/lms/types/quizAnswer.type';
import { Inject } from '@nestjs/common';
import _ from 'lodash';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { PreEnrollmentDIToken } from '@applications/di/domain/preEnrollment.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { RoundDIToken } from '@applications/di/domain/round.di';
import { UserDIToken } from '@applications/di/domain/user.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { OutputSubjectRegulatorCourseParams } from '@constants/types/course.type';
import {
  EnrollmentPayloadOfBuildOicPostReportParams,
  OutputFindEnrollmentPayloadOfOicReportParams,
} from '@constants/types/enrollment.type';
import { DetailPartParams } from '@constants/types/part.type';
import {
  CalculateAveragePostTestScoreCourse,
  FilterExportOICPostReportParams,
  OICPostReportDataParams,
  OutputGetPartsByCourseVersionIdsParams,
  OutputGetUserListByUserIdsParams,
} from '@constants/types/reportHistory.type';
import { OutputFindIdAndRoundDateBetweenRoundDatesParams } from '@constants/types/round.type';

import { IOrganizationDataMapper } from '@interfaces/dataMapper/organization.dataMapper.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { ICourseVersionRepository } from '@interfaces/repositories/courseVersion.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { IEnrollmentAttachmentRepository } from '@interfaces/repositories/enrollmentAttachment.repository.interface';
import { IEnrollmentCertificateRepository } from '@interfaces/repositories/enrollmentCertificate.repository.interface';
import { IEnrollmentRegulatorReportRepository } from '@interfaces/repositories/enrollmentRegulatorReport.repository.interface';
import { IOrganizationRepository } from '@interfaces/repositories/organization.repository.interface';
import { IPreEnrollmentTransactionRepository } from '@interfaces/repositories/preEnrollmentTransaction.repository.interface';
import { IQuizAnswerRepository } from '@interfaces/repositories/quiz.repository.interface';
import { IReportHistoryRepository } from '@interfaces/repositories/reportHistory.repository.interface';
import { IRoundRepository } from '@interfaces/repositories/round.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { ICourseService } from '@interfaces/services/course.interface';
import { IEnrollmentService } from '@interfaces/services/enrollment.interface';
import { IOrganizationService } from '@interfaces/services/organization.interface';
import { IOICPostReportService } from '@interfaces/services/report.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportOICPostReportUseCase } from '@interfaces/usecases/report.interface';

import { arrayToHashMapByKey } from '@domains/utils/collection.util';

setFlagsFromString('--expose_gc');
const runGarbageCollector = runInNewContext('gc');
export class ExportOICPostReportUseCase implements IExportOICPostReportUseCase {
  constructor(
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @Inject(RoundDIToken.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(EnrollmentDIToken.QuizAnswerRepository)
    private readonly quizAnswerRepository: IQuizAnswerRepository,
    @Inject(ReportDIToken.ReportHistoryRepository)
    private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(EnrollmentDIToken.EnrollmentAttachmentRepository)
    private readonly enrollmentAttachmentRepository: IEnrollmentAttachmentRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationDataMapper)
    private readonly organizationDataMapper: IOrganizationDataMapper,
    @Inject(PreEnrollmentDIToken.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,
    @Inject(PreEnrollmentDIToken.EnrollmentRegulatorReportRepository)
    private readonly enrollmentRegulatorReportRepository: IEnrollmentRegulatorReportRepository,
    @Inject(EnrollmentDIToken.EnrollmentCertificateRepository)
    private readonly enrollmentCertificateRepository: IEnrollmentCertificateRepository,

    //Service
    @Inject(CourseDIToken.CourseService)
    private readonly courseService: ICourseService,
    @Inject(OrganizationDIToken.OrganizationService)
    private readonly organizationService: IOrganizationService,
    @Inject(ReportDIToken.OICPostReportService)
    private readonly oicReportService: IOICPostReportService,
    @Inject(EnrollmentDIToken.EnrollmentService)
    private readonly enrollmentService: IEnrollmentService,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: FilterExportOICPostReportParams): Promise<void> {
    const {
      reportHistoryId,
      trainingCenter,
      applicantType,
      licenseRenewal,
      licenseType,
      organizationId,
      enrollmentStatus,
      roundDateFrom,
      roundDateTo,
    } = params;

    const regulatorCourse: Pick<
      FilterExportOICPostReportParams,
      'trainingCenter' | 'licenseRenewal' | 'licenseType' | 'applicantType'
    > = {
      trainingCenter,
      applicantType,
      licenseRenewal,
      licenseType,
    };

    const reportHistory = await this.reportHistoryRepository.findById(reportHistoryId);
    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'report history does not exist ',
        data: { reportHistoryId },
      });
    }

    if (reportHistory.status !== ReportHistoryStatusEnum.PENDING) {
      throw Exception.new({
        code: Code.ENTITY_VALIDATION_ERROR,
        message: 'report History has been operate',
        data: { reportHistoryId },
      });
    }

    this.logger.log(`Start processing the reportHistory ID ${reportHistoryId} type OIC_POST_REPORT`);

    try {
      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'organization dose not exist',
          data: { organizationId },
        });
      }
      const organizationDto = this.organizationDataMapper.toDTO(organization);

      const courseTrainingObjectiveConfigs = organizationDto.courseObjectiveConfigs.find(
        (config) => config.objectiveType === CourseObjectiveTypeEnum.TRAINING,
      );
      const oicCourseRegulatorTrainingCenterConfigs = courseTrainingObjectiveConfigs.trainingCenters.filter(
        (config) => config.regulator === RegulatorEnum.OIC,
      );

      const roundDates: [Date, Date] = [roundDateFrom, roundDateTo];
      const rounds: OutputFindIdAndRoundDateBetweenRoundDatesParams[] =
        await this.roundRepository.findIdsAndRoundDateBetweenRoundDates(roundDates, params.organizationId);
      const roundIds = rounds.map((round) => round.id);
      const roundById = arrayToHashMapByKey(rounds, 'id');

      const subjectRegulatorCourses: OutputSubjectRegulatorCourseParams[] =
        await this.courseRepository.findSubjectRegulatorOICCourseByRegulatorCourseProperty(
          {
            trainingCenter: regulatorCourse.trainingCenter,
            licenseRenewal: regulatorCourse?.licenseRenewal,
            applicantType: regulatorCourse?.applicantType,
            licenseType: this.transformLicenseTypeToLicenseTypeArray(regulatorCourse?.licenseType),
          },
          organizationId,
        );

      const courseIds = subjectRegulatorCourses.map((course) => course.id);
      const subjectRegulatorCourseById = arrayToHashMapByKey<OutputSubjectRegulatorCourseParams, GenericID>(
        subjectRegulatorCourses,
        'id',
      );

      const latestPublishCourseVersions =
        await this.courseVersionRepository.findLatestPublishVersionByCourseId(courseIds);
      const latestPublishCourseVersionsByCourseId = arrayToHashMapByKey<CourseVersionParams, GenericID>(
        latestPublishCourseVersions,
        'courseId',
      );

      latestPublishCourseVersions.splice(0, latestPublishCourseVersions.length);
      runGarbageCollector();

      const status = enrollmentStatus ? [enrollmentStatus] : [];

      const enrollmentsWithRegisterAndEnrollmentRegulatorReports =
        await this.enrollmentRepository.findEnrollmentsForOicReportByStatusAndRoundIds(
          roundIds,
          status,
          organizationId,
        );

      const registrationIds = _.chain(enrollmentsWithRegisterAndEnrollmentRegulatorReports)
        .map((enrollment) => enrollment?.registration?.id)
        .compact()
        .value();

      const enrollmentRegulatorReports = _.isEmpty(registrationIds)
        ? []
        : await this.enrollmentRegulatorReportRepository.findEnrollmentRegulatorReportForOicReport(registrationIds);

      const preEnrollmentTransactionIds = _.chain(enrollmentRegulatorReports)
        .map((enrollmentRegulatorReport) => enrollmentRegulatorReport.preEnrollmentTransactionId)
        .compact()
        .value();

      const preEnrollmentTransactions = _.isEmpty(preEnrollmentTransactionIds)
        ? []
        : await this.preEnrollmentTransactionRepository.findPayloadsByIds(preEnrollmentTransactionIds);

      const enrollmentRegulatorReportsByRegistrationId = arrayToHashMapByKey<
        EnrollmentRegulatorReportParams,
        GenericID
      >(enrollmentRegulatorReports, 'registrationId');

      const preEnrollmentTransactionsById = arrayToHashMapByKey<
        Pick<PreEnrollmentTransactionParams, 'id' | 'payload'>,
        GenericID
      >(preEnrollmentTransactions, 'id');

      const enrollments: OutputFindEnrollmentPayloadOfOicReportParams[] =
        enrollmentsWithRegisterAndEnrollmentRegulatorReports.map((enrollment) => {
          const enrollmentRegulatorReportOfEnrollment =
            enrollmentRegulatorReportsByRegistrationId.get(enrollment?.registration.id) ?? null;
          const preEnrollmentTransactionOfEnrollment =
            preEnrollmentTransactionsById.get(enrollmentRegulatorReportOfEnrollment?.preEnrollmentTransactionId) ??
            null;

          const newEnrollmentStatus = this.enrollmentService.transformEnrollmentStatus(
            enrollment.status,
            enrollment.expiredAt,
          );

          enrollment.status = newEnrollmentStatus;

          return {
            ...enrollment,
            enrollmentRegulatorReport: enrollmentRegulatorReportOfEnrollment,
            preEnrollmentTransaction: preEnrollmentTransactionOfEnrollment,
          };
        });

      const enrollmentIds = enrollments.map((enrollment) => enrollment.id);
      const manualEnrollmentIds = _.chain(enrollments)
        .filter((enrollment) => _.isNull(enrollment.preEnrollmentTransaction))
        .map((enrollment) => enrollment.id)
        .value();

      const [enrollmentCertificates, enrollmentAttachments] = await Promise.all([
        this.enrollmentCertificateRepository.find({ enrollmentId: { $in: manualEnrollmentIds } }),
        this.enrollmentAttachmentRepository.find(
          {
            enrollmentId: { $in: enrollmentIds },
          },
          { sort: { createdAt: 1 } },
        ),
      ]);

      const enrollmentCertificateGroupByEnrollmentId = _.groupBy(enrollmentCertificates, 'enrollmentId');
      const enrollmentAttachmentGroupByEnrollmentId = _.groupBy(enrollmentAttachments, 'enrollmentId');

      const userIds = _.uniq(enrollments.map((enrollment) => enrollment.userId));

      const [partsList, userList] = await Promise.all([
        this.getPartsByCourseVersionIds(enrollments, subjectRegulatorCourses),
        this.getUserList(userIds),
      ]);

      const partsMappingByCourseVersionId = arrayToHashMapByKey<OutputGetPartsByCourseVersionIdsParams, GenericID>(
        partsList,
        'courseVersionId',
      );

      const userByUserId = arrayToHashMapByKey<OutputGetUserListByUserIdsParams, GenericID>(userList, 'guid');

      const postTestQuizAnswers = await this.getPostTestQuizAnswers(enrollmentIds);
      const postTestQuizAnswerGroupByEnrollmentId = _.groupBy(postTestQuizAnswers, 'enrollmentId');

      rounds.splice(0, rounds.length);
      roundIds.splice(0, roundIds.length);
      enrollmentIds.splice(0, enrollmentIds.length);
      registrationIds.splice(0, registrationIds.length);
      preEnrollmentTransactionIds.splice(0, preEnrollmentTransactionIds.length);
      enrollmentAttachments.splice(0, enrollmentAttachments.length);
      enrollmentsWithRegisterAndEnrollmentRegulatorReports.splice(
        0,
        enrollmentsWithRegisterAndEnrollmentRegulatorReports.length,
      );
      enrollmentRegulatorReports.splice(0, enrollmentRegulatorReports.length);
      preEnrollmentTransactions.splice(0, preEnrollmentTransactions.length);
      enrollmentCertificates.splice(0, enrollmentCertificates.length);
      partsList.splice(0, partsList.length);
      userList.splice(0, userList.length);
      postTestQuizAnswers.splice(0, postTestQuizAnswers.length);
      runGarbageCollector();

      const organizationMainUrl = this.organizationService.getMainUrl(organization.domain, organization.fqdn);
      const reportData: OICPostReportDataParams[] = _.chain(enrollments)
        .filter((enrollment) =>
          this.oicReportService.filterEnrollmentRelationalData(enrollment, {
            courseById: subjectRegulatorCourseById,
            courseVersionByCourseId: latestPublishCourseVersionsByCourseId,
            enrollmentCertificateGroupByEnrollmentId,
            reportLicenseType: regulatorCourse?.licenseType ?? null,
          }),
        )
        .map((enrollment) => {
          let resultAveragePostTestScore: CalculateAveragePostTestScoreCourse = {
            isHavePostTestQuiz: false,
            percentPostTestScore: 0,
          };
          let percentCompleteCourseItems: number = 0;

          const subjectRegulatorCourse = subjectRegulatorCourseById.get(enrollment.courseId);
          const latestPublishCourseVersion = latestPublishCourseVersionsByCourseId.get(subjectRegulatorCourse.id);

          const enrollmentCertificateList = enrollmentCertificateGroupByEnrollmentId[enrollment.id] || [];
          const licenseTypeByEnrollmentCertificate = this.oicReportService.licenseTypeByEnrollmentCertificates(
            enrollmentCertificateList,
            subjectRegulatorCourse.regulatorInfo.licenseType,
            subjectRegulatorCourse.regulatorInfo.licenseRenewal,
          );

          const partsOfCoursesVersion = partsMappingByCourseVersionId.get(latestPublishCourseVersion.id);
          const postTestQuizAnswerOfEnrollment = postTestQuizAnswerGroupByEnrollmentId[enrollment.id];

          if (partsOfCoursesVersion) {
            //calculate percent post quiz score
            resultAveragePostTestScore = this.calculateAveragePostTestScoreCourse(
              partsOfCoursesVersion.parts,
              postTestQuizAnswerOfEnrollment,
            );

            //calculate percent complete course items
            percentCompleteCourseItems = this.calculatePercentCompleteCourseItems(
              enrollment,
              partsOfCoursesVersion.parts,
              latestPublishCourseVersion,
            );
          }

          return {
            ...enrollment,
            resultAveragePostTestScore,
            percentCompleteCourseItems,
            licenseTypeByEnrollmentCertificate,
          } as EnrollmentPayloadOfBuildOicPostReportParams;
        })
        .map((enrollment) => {
          return this.oicReportService.buildOicPostReportRowData(enrollment, {
            organizationMainUrl,
            roundById,
            subjectRegulatorCourseById,
            latestPublishCourseVersionsByCourseId,
            enrollmentAttachmentGroupByEnrollmentId,
            userByUserId,
          });
        })
        .orderBy('eid')
        .partition((data) => _.isEmpty(data.eid))
        .value()
        .reduce((acc, data) => [...data, ...acc], [])
        .map((data, index) => ({ ...data, item: index + 1 }));

      enrollments.splice(0, enrollments.length);
      runGarbageCollector();

      const isCPD = this.organizationService.checkIsCPDOrganization(organization.domain);
      const isShowCustomer = isCPD;

      const organizationRegulatorCourses = this.organizationService.getRegulatorConfigList(
        {
          trainingCenter: regulatorCourse.trainingCenter,
          licenseRenewal: regulatorCourse?.licenseRenewal,
          applicantType: regulatorCourse?.applicantType,
          licenseType: regulatorCourse?.licenseType,
        },
        oicCourseRegulatorTrainingCenterConfigs,
        { isContainBothLicense: true },
      );

      const organizationLicenseTypeList = _.chain(organizationRegulatorCourses).map('licenseType').uniq().value();
      const intersectLicenseTypeLife = _.intersection(organizationLicenseTypeList, [
        LicenseTypeEnum.LIFE,
        LicenseTypeEnum.BOTH,
        LicenseTypeEnum.UNIT_LINKED,
      ]);
      const intersectLicenseTypeNonLife = _.intersection(organizationLicenseTypeList, [
        LicenseTypeEnum.NONLIFE,
        LicenseTypeEnum.BOTH,
      ]);

      const isShowLifeLicense = !_.isEmpty(intersectLicenseTypeLife);
      const isShowNonLifeLicense = !_.isEmpty(intersectLicenseTypeNonLife);

      const buffer = await this.oicReportService.buildOICPostReportFile(reportData, {
        isShowCustomer,
        isShowLifeLicense,
        isShowNonLifeLicense,
        subjectCodeNames: [],
      });

      const filePath = `xlsx/reports/${reportHistory.fileName}.xlsx`;
      const reportHistoryFile = new ReportHistoryFile({
        fileName: reportHistory.fileName,
        filePath,
      });

      const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);
      await storageRepository.upload({
        raw: buffer,
        path: filePath,
        fileType: UploadFileTypeEnum.XLSX,
      });

      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      reportHistory.files.push(reportHistoryFile);

      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully reportHistory ID ${reportHistoryId}`);
    } catch (error) {
      this.logger.error(`| Something wrong, reportHistory ID ${reportHistoryId}`);
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);

      throw error;
    }
  }

  private transformLicenseTypeToLicenseTypeArray(licenseType?: string): string[] | undefined {
    if (_.isNil(licenseType)) {
      return undefined;
    }

    if (licenseType === LicenseTypeEnum.BOTH) {
      return [LicenseTypeEnum.LIFE, LicenseTypeEnum.NONLIFE];
    }

    return [licenseType];
  }

  private async getUserList(userIds: GenericID[]): Promise<OutputGetUserListByUserIdsParams[]> {
    const pipeline = [
      { $match: { guid: { $in: userIds } } },
      { $lookup: { from: 'licenses', localField: 'guid', foreignField: 'userId', as: 'licenses' } },
    ];

    return this.userRepository.aggregate<OutputGetUserListByUserIdsParams>(pipeline);
  }

  private async getPartsByCourseVersionIds(
    enrollments: OutputFindEnrollmentPayloadOfOicReportParams[],
    subjectRegulatorCourses: OutputSubjectRegulatorCourseParams[],
  ): Promise<OutputGetPartsByCourseVersionIdsParams[]> {
    const courseSelfContentProviderIds = new Set(
      subjectRegulatorCourses
        .filter((course) => course.contentProviderType === ContentProviderTypeEnum.SELF)
        .map(({ id }) => id),
    );
    const courseSkilllaneContentProviderIds = new Set(
      subjectRegulatorCourses
        .filter((course) => course.contentProviderType === ContentProviderTypeEnum.EXTERNAL)
        .map(({ id }) => id),
    );

    const courseVersionSelfContentProviderIds = enrollments
      .filter((enrollment) => courseSelfContentProviderIds.has(enrollment.courseId))
      .map((enrollment) => enrollment.courseVersionId);
    const courseVersionSkilllaneContentProviderIds = enrollments
      .filter((enrollment) => courseSkilllaneContentProviderIds.has(enrollment.courseId))
      .map((enrollment) => enrollment.courseVersionId);

    const [partSELF, partSKILLLANE] = await Promise.all([
      this.courseVersionRepository.findPartCourseVersionByIds(courseVersionSelfContentProviderIds),
      this.courseVersionRepository.findPartProductSKUCourseVersionByIds(courseVersionSkilllaneContentProviderIds),
    ]);

    const preparePartSkilllaneProvider = partSKILLLANE
      .map((partDetail) => {
        const { parts, courseRetailId, courseVersionId, contentProviderType } = partDetail;
        const prepare = this.courseService.preparePartSkilllaneProvider(parts, courseRetailId, courseVersionId);
        return { courseVersionId, contentProviderType, parts: prepare };
      })
      .flat();

    const preparePartSelfProvider = partSELF
      .map((partDetail) => {
        const { parts, courseVersionId, contentProviderType } = partDetail;
        const prepare = this.courseService.preparePartSelfProvider(parts);
        return { courseVersionId, contentProviderType, parts: prepare };
      })
      .flat();

    const parts = preparePartSkilllaneProvider.concat(preparePartSelfProvider);

    return parts;
  }

  private async getPostTestQuizAnswers(
    enrollmentIds: GenericID[],
  ): Promise<Pick<QuizAnswerParams, 'id' | 'enrollmentId' | 'quizId' | 'userPoint' | 'totalPoint' | 'createdAt'>[]> {
    const pipelineQueryQuizAnswer = [
      {
        $match: { enrollmentId: { $in: enrollmentIds }, testType: QuizTestTypeEnum.POST_TEST },
      },
      {
        $group: {
          _id: { quizId: '$quizId', enrollmentId: '$enrollmentId' },
          answers: { $push: '$$ROOT' },
        },
      },
      {
        $unwind: '$answers',
      },
      {
        $replaceRoot: { newRoot: '$answers' },
      },
      {
        $project: {
          _id: 0,
          quizId: 1,
          enrollmentId: 1,
          quizName: 1,
          testType: 1,
          userPoint: 1,
          totalPoint: 1,
          criteriaCertificate: 1,
          createdAt: 1,
        },
      },
      {
        $sort: {
          createdAt: 1,
        },
      },
    ];

    const resQuizAnswer: Pick<
      QuizAnswerParams,
      | 'id'
      | 'quizId'
      | 'quizName'
      | 'testType'
      | 'userPoint'
      | 'totalPoint'
      | 'criteriaCertificate'
      | 'createdAt'
      | 'enrollmentId'
    >[] = await this.quizAnswerRepository.aggregate(pipelineQueryQuizAnswer);

    const quizAnswerList: Pick<
      QuizAnswerParams,
      'id' | 'enrollmentId' | 'quizId' | 'userPoint' | 'totalPoint' | 'createdAt'
    >[] = resQuizAnswer.map((val) => {
      return {
        id: val.id,
        enrollmentId: val.enrollmentId,
        quizId: val.quizId,
        userPoint: val.userPoint,
        totalPoint: val.totalPoint,
        createdAt: val.createdAt,
      };
    });

    return quizAnswerList;
  }

  private calculatePercentCompleteCourseItems(
    enrollment: OutputFindEnrollmentPayloadOfOicReportParams,
    parts: DetailPartParams[],
    courseVersion: CourseVersionParams,
  ): number {
    let { completedCourseItem = 0 } = enrollment ?? {};
    const { totalCourseItems } = courseVersion;
    const courseItems = parts.flatMap((part) => part.courseItems);

    const validLearningProgress: CourseItemProgressParams[] = enrollment.learningProgress.filter((progress) =>
      courseItems.some((courseItem) => progress.courseItemId === courseItem.id),
    );

    const sumValidCompleteLearning = _.sumBy(validLearningProgress, (lp) =>
      lp.statusCode === CourseItemStatusCodeEnum.COMPLETE ? 1 : 0,
    );

    completedCourseItem = _.min([sumValidCompleteLearning, completedCourseItem]) || 0;

    const percentProgress = _.clamp(Math.round((completedCourseItem / (totalCourseItems || 1)) * 100), 0, 100);

    return percentProgress;
  }

  private calculateAveragePostTestScoreCourse(
    parts: DetailPartParams[],
    postTestQuizAnswerOfEnrollment: Pick<
      QuizAnswerParams,
      'id' | 'createdAt' | 'quizId' | 'userPoint' | 'totalPoint' | 'enrollmentId'
    >[],
  ): CalculateAveragePostTestScoreCourse {
    let isHavePostTestQuiz = false;
    const quizPostTestList = _.chain(parts)
      .map((part) => {
        const data = part.courseItems
          .filter((courseItem) => courseItem.testType === QuizTestTypeEnum.POST_TEST)
          .map((courseItem) => ({ quizId: courseItem.quizId, questions: courseItem.questions }))
          .filter(Boolean)
          .flat();
        return data;
      })
      .compact()
      .flatten()
      .value();

    if (quizPostTestList.length === 0) return { isHavePostTestQuiz, percentPostTestScore: 0 };

    const lastQuizPostTest = quizPostTestList.at(-1);

    const { quizId, questions } = lastQuizPostTest;
    const totalScore = questions.length;
    let userPoint = 0;

    const postTestQuizAnswer = _.chain(postTestQuizAnswerOfEnrollment)
      .filter((answer) => answer?.quizId === quizId)
      .orderBy('createdAt', 'asc')
      .value()
      .at(-1);

    if (postTestQuizAnswer) {
      userPoint = postTestQuizAnswer.userPoint || 0;
      isHavePostTestQuiz = true;
    }

    const percentPostTestScore = Math.round((userPoint / (totalScore || 1)) * 100 * 100) / 100;

    return { isHavePostTestQuiz, percentPostTestScore };
  }
}
