import { setFlagsFromString } from 'v8';
import { runInNewContext } from 'vm';

import { GenericID, Nullable } from '@iso/constants/commonTypes';
import {
  ColumnSettingKeyEnum,
  ColumnSettingModuleEnum,
  ColumnSettingTemplateEnum,
} from '@iso/lms/enums/columnSetting.enum';
import {
  UserGroupOperatorSubConditionTypeEnum,
  UserGroupParticipationTypeEnum,
  UserGroupSubConditionOperatorEnum,
} from '@iso/lms/enums/userGroup.enum';
import { ColumnSettingInTemplateParams } from '@iso/lms/types/templateColumnSetting.type';
import {
  UserGroupSubConditionMultipleCourseEnrollmentParams,
  UserGroupSubConditionMultipleEnrollmentParams,
  UserGroupSubConditionPassedDurationEnrollmentParams,
} from '@iso/lms/types/userGroup.type';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { intersection, isEmpty, isNull, keyBy, partition, union } from 'lodash';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { PlanDIToken } from '@applications/di/domain';
import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { LicenseDIToken } from '@applications/di/domain/license.di';
import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { UserDIToken } from '@applications/di/domain/user.di';
import { UserGroupDIToken } from '@applications/di/domain/userGroup.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import {
  UpdateUserInUserGroupParams,
  UserGroupSubConditionCategoryParams,
  UserGroupSubConditionColumnSettingParams,
} from '@constants/types/userGroup.type';

import { IColumnSettingDataMapper } from '@interfaces/dataMapper/columnSetting.dataMapper.interface';
import { IOrganizationColumnSettingDataMapper } from '@interfaces/dataMapper/organizationColumnSetting.dataMapper.interface';
import { IPlanPackageLicenseRepository, IPlanRepository } from '@interfaces/repositories';
import { IColumnSettingRepository } from '@interfaces/repositories/columnSetting.repository.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';
import { IDepartmentRepository } from '@interfaces/repositories/department.repository.interface';
import { IEnrollmentRepository } from '@interfaces/repositories/enrollment.repository.interface';
import { ILicenseRepository } from '@interfaces/repositories/license.repository.interface';
import { IOrganizationColumnSettingRepository } from '@interfaces/repositories/organizationColumnSetting.repository.interface';
import { ITemplateColumnSettingRepository } from '@interfaces/repositories/templateColumnSetting.repository.interface';
import { IUserRepository } from '@interfaces/repositories/user.repository.interface';
import { IUserDirectReportRepository } from '@interfaces/repositories/userDirectReport.repository.interface';
import { IUserGroupRepository } from '@interfaces/repositories/userGroup.repository.interface';
import { IUserGroupDbAdaptorService } from '@interfaces/services/userGroupDbAdaptor.service.interface';
import { IDatabaseTransactionService } from '@interfaces/transaction/databaseTransaction.interface';
import { IUpdateUsersInUserGroupUseCase } from '@interfaces/usecases/userGroup.usecase.interface';

import { binarySearch } from '@domains/utils/binarySearch.util';
import { date } from '@domains/utils/date.util';

setFlagsFromString('--expose_gc');
const runGarbageCollector = runInNewContext('gc');

@Injectable()
export class UpdateUsersInUserGroupUseCase implements IUpdateUsersInUserGroupUseCase {
  constructor(
    @Inject(OrganizationDIToken.ColumnSettingRepository)
    private readonly columnSettingRepository: IColumnSettingRepository,
    @Inject(OrganizationDIToken.OrganizationColumnSettingRepository)
    private readonly organizationColumnSettingRepository: IOrganizationColumnSettingRepository,
    @Inject(OrganizationDIToken.TemplateColumnSettingRepository)
    private readonly templateColumnSettingRepository: ITemplateColumnSettingRepository,
    @Inject(UserGroupDIToken.UserGroupRepository)
    private readonly userGroupRepository: IUserGroupRepository,
    @Inject(OrganizationDIToken.DepartmentRepository)
    private readonly departmentRepository: IDepartmentRepository,
    @Inject(UserDIToken.UserDirectReportRepository)
    private readonly userDirectReportRepository: IUserDirectReportRepository,
    @Inject(LicenseDIToken.LicenseRepository)
    private readonly licenseRepository: ILicenseRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(EnrollmentDIToken.EnrollmentRepository)
    private readonly enrollmentRepository: IEnrollmentRepository,
    @Inject(OrganizationDIToken.ColumnSettingDataMapper)
    private readonly columnSettingDataMapper: IColumnSettingDataMapper,
    @Inject(OrganizationDIToken.OrganizationColumnSettingDataMapper)
    private readonly organizationColumnSettingDataMapper: IOrganizationColumnSettingDataMapper,
    @Inject(PlanDIToken.PlanRepository) private readonly planRepository: IPlanRepository,
    @Inject(PlanDIToken.PlanPackageLicenseRepository)
    private readonly planPackageLicenseRepository: IPlanPackageLicenseRepository,

    @Inject(UserGroupDIToken.UserGroupDbAdaptorService)
    private readonly userGroupDbAdapterService: IUserGroupDbAdaptorService,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(InfrastructuresPersistenceDIToken.DatabaseTransaction)
    private readonly databaseTransaction: IDatabaseTransactionService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: UpdateUserInUserGroupParams): Promise<void> {
    const { organizationId, userGroup } = params;
    const { condition, id: userGroupId } = userGroup;

    const { operatorSubConditionType, subConditions } = condition;

    const templateColumnSettings = await this.templateColumnSettingRepository.aggregate<{
      columnSetting: ColumnSettingInTemplateParams;
    }>([
      {
        $match: { code: ColumnSettingTemplateEnum.userGroupConditionManagement, organizationId: params.organizationId },
      },
      { $project: { _id: 0, columnSetting: 1 } },
    ]);

    const columnSettingKeys = templateColumnSettings
      .flatMap((templateColumnSetting) => templateColumnSetting.columnSetting)
      .filter((col) => col.isFilter && col.isActive)
      .map((col) => col.key);

    const columnSettings = await this.columnSettingRepository.find({ key: { $in: columnSettingKeys } });
    const orgColumnSettings = await this.organizationColumnSettingRepository.find({
      key: { $in: columnSettingKeys },
      organizationId,
    });

    const columnSettingDTOs = this.columnSettingDataMapper.toDTOs(columnSettings);
    const organizationColumnSettingDTOs = this.organizationColumnSettingDataMapper.toDTOs(orgColumnSettings);
    const allColumnSettings = [...columnSettingDTOs, ...organizationColumnSettingDTOs];
    const allColumnSettingsMapByKey = keyBy(allColumnSettings, 'key');

    // Clean up memory
    templateColumnSettings.splice(0, templateColumnSettings.length);
    columnSettingKeys.splice(0, columnSettings.length);
    columnSettingDTOs.splice(0, columnSettingDTOs.length);
    columnSettings.splice(0, columnSettings.length);
    orgColumnSettings.splice(0, orgColumnSettings.length);
    organizationColumnSettingDTOs.splice(0, organizationColumnSettingDTOs.length);
    runGarbageCollector();

    const {
      userColumnSettingSubConditions,
      courseEnrollmentColumnSettingSubCondition,
      departmentColumnSettingSubCondition,
      licenseColumnSettingSubConditions,
      userDirectReportColumnSettingSubCondition,
    } = subConditions
      .filter((subCondition) => subCondition.columnSettingKey in allColumnSettingsMapByKey)
      .map((subCondition) => ({
        subCondition,
        columnSetting: allColumnSettingsMapByKey[subCondition.columnSettingKey],
      }))
      .reduce(
        (
          groupColumnSettingSubCondition: UserGroupSubConditionCategoryParams,
          columnSettingSubCondition: UserGroupSubConditionColumnSettingParams,
        ) => {
          const { columnSetting } = columnSettingSubCondition;
          switch (columnSetting.module) {
            case ColumnSettingModuleEnum.USER: {
              groupColumnSettingSubCondition.userColumnSettingSubConditions.push(columnSettingSubCondition);
              break;
            }
            case ColumnSettingModuleEnum.COURSE: {
              groupColumnSettingSubCondition.courseEnrollmentColumnSettingSubCondition = columnSettingSubCondition;
              break;
            }
            case ColumnSettingModuleEnum.DEPARTMENT: {
              groupColumnSettingSubCondition.departmentColumnSettingSubCondition = columnSettingSubCondition;
              break;
            }
            case ColumnSettingModuleEnum.LICENSE: {
              groupColumnSettingSubCondition.licenseColumnSettingSubConditions.push(columnSettingSubCondition);
              break;
            }
            case ColumnSettingModuleEnum.USER_DIRECT_REPORT: {
              groupColumnSettingSubCondition.userDirectReportColumnSettingSubCondition = columnSettingSubCondition;
              break;
            }
            default:
              break;
          }
          return groupColumnSettingSubCondition;
        },
        {
          userColumnSettingSubConditions: [],
          licenseColumnSettingSubConditions: [],
          courseEnrollmentColumnSettingSubCondition: null,
          departmentColumnSettingSubCondition: null,
          userDirectReportColumnSettingSubCondition: null,
        } as UserGroupSubConditionCategoryParams,
      );

    const accumulateAutoCondition: { accumulateUserIds: GenericID[]; isFirstInsertUserIdWithCondition: boolean } = {
      accumulateUserIds: [],
      isFirstInsertUserIdWithCondition: true,
    };

    const isAndCondition = operatorSubConditionType === UserGroupOperatorSubConditionTypeEnum.AND;

    const isUserSubConditionExisted = !isEmpty(userColumnSettingSubConditions);
    if (isUserSubConditionExisted) {
      this.logger.log(
        `[START][USER-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name}`,
      );
      const [statusIsActivePlatformLicenseUserConditions, userOtherColumnSettingSubConditions] = partition(
        userColumnSettingSubConditions,
        (userColumnSettingSubCondition) =>
          userColumnSettingSubCondition.subCondition.columnSettingKey === ColumnSettingKeyEnum.USER_ACTIVE,
      );

      if (!isEmpty(userOtherColumnSettingSubConditions)) {
        const conditions = userOtherColumnSettingSubConditions.map((userColumnSettingSubCondition) =>
          this.userGroupDbAdapterService.convertSubConditionOperatorToDbQuery(userColumnSettingSubCondition),
        );

        const userIds = await this.userRepository.findIdsByUserGroupConditionAndOrganizationId(
          conditions,
          params.organizationId,
          isAndCondition,
        );

        accumulateAutoCondition.accumulateUserIds = this.updateAccumulateUserId(
          accumulateAutoCondition.accumulateUserIds,
          userIds,
          isAndCondition,
          accumulateAutoCondition.isFirstInsertUserIdWithCondition,
        );
        accumulateAutoCondition.isFirstInsertUserIdWithCondition = false;
        userIds.splice(0, userIds.length);
        conditions.splice(0, conditions.length);
      }

      if (!isEmpty(statusIsActivePlatformLicenseUserConditions)) {
        const [{ subCondition }] = statusIsActivePlatformLicenseUserConditions;
        const [{ value: isActive }] = subCondition.operations;

        const activePlatformPlanPackageIds =
          await this.planRepository.findActivePlatformPlanPackageIdsByOrganizationId(organizationId);

        const activeLicenseUserId =
          await this.planPackageLicenseRepository.findActiveLicenseUserIdsByPlanPackageIds(
            activePlatformPlanPackageIds,
          );

        let userIds = activeLicenseUserId;

        if (!isActive) {
          userIds = await this.userRepository.findIdsByNotEqualIdsAndOrganizationId(
            activeLicenseUserId,
            organizationId,
          );
        }

        accumulateAutoCondition.accumulateUserIds = this.updateAccumulateUserId(
          accumulateAutoCondition.accumulateUserIds,
          userIds,
          isAndCondition,
          accumulateAutoCondition.isFirstInsertUserIdWithCondition,
        );
        accumulateAutoCondition.isFirstInsertUserIdWithCondition = false;

        userIds.splice(0, userIds.length);
        activeLicenseUserId.splice(0, activeLicenseUserId.length);
        activePlatformPlanPackageIds.splice(0, activePlatformPlanPackageIds.length);
      }

      this.logger.log(
        `[END][USER-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name} | user count: ${accumulateAutoCondition.accumulateUserIds.length}`,
      );

      runGarbageCollector();
    }

    const isLicenseSubConditionExisted = !isEmpty(licenseColumnSettingSubConditions);
    if (isLicenseSubConditionExisted) {
      this.logger.log(
        `[START][LICENSE-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name}`,
      );
      const conditions = licenseColumnSettingSubConditions.map((licenseColumnSettingSubCondition) =>
        this.userGroupDbAdapterService.convertSubConditionOperatorToDbQuery(licenseColumnSettingSubCondition),
      );

      const isIntersection = isAndCondition;
      const userIdsInLicense = await this.licenseRepository.findUnionOrIntersectUserIdsByOrganizationAndQuery(
        params.organizationId,
        conditions,
        isIntersection ? 'intersection' : 'union',
      );

      accumulateAutoCondition.accumulateUserIds = this.updateAccumulateUserId(
        accumulateAutoCondition.accumulateUserIds,
        userIdsInLicense,
        isAndCondition,
        accumulateAutoCondition.isFirstInsertUserIdWithCondition,
      );
      accumulateAutoCondition.isFirstInsertUserIdWithCondition = false;

      this.logger.log(
        `[END][LICENSE-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name} | user count: ${userIdsInLicense.length}`,
      );
      conditions.splice(0, conditions.length);
      runGarbageCollector();
    }

    const isDepartmentSubConditionExisted = !isNull(departmentColumnSettingSubCondition);
    if (isDepartmentSubConditionExisted) {
      this.logger.log(
        `[START][DEPARTMENT-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name}`,
      );
      const [{ operator, value: departmentIds }] = departmentColumnSettingSubCondition.subCondition.operations;
      const isIntersection = operator === UserGroupSubConditionOperatorEnum.ALL_EQ;

      const userIdsInDepartment = await this.departmentRepository.findUnionOrIntersectUserIdsByIds(
        departmentIds as GenericID[],
        isIntersection ? 'intersection' : 'union',
      );

      accumulateAutoCondition.accumulateUserIds = this.updateAccumulateUserId(
        accumulateAutoCondition.accumulateUserIds,
        userIdsInDepartment,
        isAndCondition,
        accumulateAutoCondition.isFirstInsertUserIdWithCondition,
      );
      accumulateAutoCondition.isFirstInsertUserIdWithCondition = false;

      this.logger.log(
        `[END][DEPARTMENT-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name} | user count: ${userIdsInDepartment.length}`,
      );
      userIdsInDepartment.splice(0, userIdsInDepartment.length);
      runGarbageCollector();
    }

    const isUserDirectReportSubConditionExisted = !isNull(userDirectReportColumnSettingSubCondition);
    if (isUserDirectReportSubConditionExisted) {
      this.logger.log(
        `[START][USER-DIRECT-REPORT-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name}`,
      );
      const [{ operator, value: userDirectReportIds }] =
        userDirectReportColumnSettingSubCondition.subCondition.operations;
      const isIntersection = operator === UserGroupSubConditionOperatorEnum.ALL_EQ;

      const userIdsInUserDirectReport =
        await this.userDirectReportRepository.findUnionOrIntersectUserIdsByUserDirectReportIds(
          userDirectReportIds as GenericID[],
          isIntersection ? 'intersection' : 'union',
        );

      accumulateAutoCondition.accumulateUserIds = this.updateAccumulateUserId(
        accumulateAutoCondition.accumulateUserIds,
        userIdsInUserDirectReport,
        isAndCondition,
        accumulateAutoCondition.isFirstInsertUserIdWithCondition,
      );
      accumulateAutoCondition.isFirstInsertUserIdWithCondition = false;
      this.logger.log(
        `[END][USER-DIRECT-REPORT-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name} | user count: ${userIdsInUserDirectReport.length}`,
      );
      userIdsInUserDirectReport.splice(0, userIdsInUserDirectReport.length);
      runGarbageCollector();
    }

    const isCourseEnrollmentSubConditionExisted = !isNull(courseEnrollmentColumnSettingSubCondition);
    if (isCourseEnrollmentSubConditionExisted) {
      this.logger.log(
        `[START][ENROLLMENT-COURSE-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name}`,
      );
      const userIdsInEnrollment = await this.getUserIdsFromCourseEnrollmentCondition(
        courseEnrollmentColumnSettingSubCondition.subCondition
          .operations as UserGroupSubConditionMultipleEnrollmentParams[],
        params.organizationId,
      );

      accumulateAutoCondition.accumulateUserIds = this.updateAccumulateUserId(
        accumulateAutoCondition.accumulateUserIds,
        userIdsInEnrollment,
        isAndCondition,
        accumulateAutoCondition.isFirstInsertUserIdWithCondition,
      );
      accumulateAutoCondition.isFirstInsertUserIdWithCondition = false;
      this.logger.log(
        `[END][ENROLLMENT-COURSE-CONDITION] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name} | user count: ${userIdsInEnrollment.length}`,
      );
      runGarbageCollector();
    }

    // update Users In User Group
    try {
      const totalUser = accumulateAutoCondition.accumulateUserIds.length;
      this.logger.log(
        `[START][UPDATE-USER-IN-USER-GROUP] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name}`,
      );
      await this.databaseTransaction.runTransaction(async (session, commit) => {
        await this.userGroupRepository.removeAutoUserIdsFromUserGroupById(userGroupId, {
          session,
        });

        const batchSize = 1000;
        const numberIterator = Math.ceil(totalUser / batchSize);
        for (let i = 0; i < numberIterator; i++) {
          const filter1000UserIds = accumulateAutoCondition.accumulateUserIds.splice(0, batchSize);

          const users = filter1000UserIds.map((id) => ({
            userId: id,
            participationType: UserGroupParticipationTypeEnum.AUTO,
          }));

          await this.userGroupRepository.addAutoUserIdsInUserGroupById(userGroupId, users, { session });
        }

        await commit();
      });
      this.logger.log(
        `[END][UPDATE-USER-IN-USER-GROUP] update user group id ${userGroupId} (org: ${organizationId}) ${userGroup.name}`,
      );

      this.logger.log(
        `[SUCCESS] update user group id ${userGroupId} user group: ${userGroup.name} | user count: ${totalUser}`,
      );
    } catch (error) {
      this.logger.error(
        `[FAIL] update user group id ${userGroupId} user group: ${userGroup.name}: error message: ${error.message} `,
      );
    }
  }

  private async getUserIdsFromCourseEnrollmentCondition(
    courseEnrollmentOperations: UserGroupSubConditionMultipleEnrollmentParams[],
    organizationId: GenericID,
  ): Promise<GenericID[]> {
    // date operator
    const { PASSED_DURATION_GTE_RANGE, PASSED_DURATION_LTE_RANGE } = UserGroupSubConditionOperatorEnum;
    const [passedDurationOperations, enrollmentOperations]: [
      UserGroupSubConditionPassedDurationEnrollmentParams[],
      UserGroupSubConditionMultipleCourseEnrollmentParams[],
    ] = partition(courseEnrollmentOperations, (v) =>
      [PASSED_DURATION_GTE_RANGE, PASSED_DURATION_LTE_RANGE].includes(v.operator),
    ) as [UserGroupSubConditionPassedDurationEnrollmentParams[], UserGroupSubConditionMultipleCourseEnrollmentParams[]];

    const courseIds = enrollmentOperations.map(({ value }) => value).flat();
    const { regularCourseIds, noneRegularCourseIds } =
      await this.courseRepository.findCourseRegularAndNoneRegularIdsByIds(courseIds, organizationId);

    if (isEmpty(regularCourseIds) && isEmpty(noneRegularCourseIds)) return [];

    const { passedDurationGteRangeOperationValue, passedDurationLteRangeOperationValue } =
      passedDurationOperations.reduce(
        (acc, operation) => {
          const { unit, number } = operation.value;
          if (operation.operator === PASSED_DURATION_GTE_RANGE) {
            acc.passedDurationGteRangeOperationValue = date().subtract(number, unit).toDate();
          } else {
            acc.passedDurationLteRangeOperationValue = date().subtract(number, unit).toDate();
          }
          return acc;
        },
        {
          passedDurationGteRangeOperationValue: null,
          passedDurationLteRangeOperationValue: null,
        } as {
          passedDurationGteRangeOperationValue: Nullable<Date>;
          passedDurationLteRangeOperationValue: Nullable<Date>;
        },
      );

    const { ALL_EQ, ANY_EQ, ALL_NOT_EQ, ANY_NOT_EQ } = UserGroupSubConditionOperatorEnum;
    const allOperationCourseIds = enrollmentOperations.map((operation) => {
      const { operator, value } = operation;
      const _regularCourseIds = value.filter((courseId) => binarySearch(regularCourseIds as string[], courseId) >= 0);
      const _noneRegularCourseIds = value.filter(
        (courseId) => binarySearch(noneRegularCourseIds as string[], courseId) >= 0,
      );
      const isPassedOperation = [ALL_EQ, ANY_EQ].includes(operator);
      return {
        operator,
        regularCourseIds: _regularCourseIds,
        noneRegularCourseIds: _noneRegularCourseIds,
        passedDurationGteRangeOperation: isPassedOperation && passedDurationGteRangeOperationValue,
        passedDurationLteRangeOperation: isPassedOperation && passedDurationLteRangeOperationValue,
      };
    });

    let allUserIds: GenericID[] = [];
    for (const [index, operation] of allOperationCourseIds.entries()) {
      const isFirstOperation = index === 0;

      const setType = [ALL_EQ, ALL_NOT_EQ].includes(operation.operator) ? 'intersect' : 'union';
      const userIdsFromEnrollments =
        await this.enrollmentRepository.findUnionOrIntersectUserIdsCompleteEnrollmentByCourseIdAndPassDuration(
          {
            organizationId,
            operator: operation.operator,
            regularCourseIds: operation.regularCourseIds,
            noneRegularCourseIds: operation.noneRegularCourseIds,
            passedGteDateDuration: operation.passedDurationGteRangeOperation,
            passedLteDateDuration: operation.passedDurationLteRangeOperation,
          },
          setType,
        );

      if ([ALL_NOT_EQ, ANY_NOT_EQ].includes(operation.operator)) {
        const userIds = await this.userRepository.findIdsByNotEqualIdsAndOrganizationId(
          userIdsFromEnrollments,
          organizationId,
        );

        allUserIds = isFirstOperation ? userIds : intersection(allUserIds, userIds);
        if (!isFirstOperation) {
          userIds.splice(0, userIds.length);
        }
      } else {
        allUserIds = isFirstOperation ? userIdsFromEnrollments : intersection(allUserIds, userIdsFromEnrollments);

        if (!isFirstOperation) {
          userIdsFromEnrollments.splice(0, userIdsFromEnrollments.length);
        }
      }
      runGarbageCollector();
    }

    return allUserIds;
  }

  private updateAccumulateUserId(
    accumulateUserIds: GenericID[],
    userIds: GenericID[],
    isAndCondition: boolean,
    isFirstInsertUserIdWithCondition: boolean,
  ): GenericID[] {
    if (isFirstInsertUserIdWithCondition) {
      return [...userIds];
    } else {
      return isAndCondition ? intersection(accumulateUserIds, userIds) : union(accumulateUserIds, userIds);
    }
  }
}
