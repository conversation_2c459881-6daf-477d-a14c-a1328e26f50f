import { GenericID } from '@iso/constants/commonTypes';

export type BulkAssignPlanPackageLicenseParams = {
  payload: BulkAssignPlanPackageLicensePayloadParams;
  originalPayload: Record<string, any>;
  raw: string[];
};

export type BulkAssignPlanPackageLicensePayloadParams = {
  preEnrollmentTransactionId: GenericID;
  user: Record<string, any>;
  plans: BulkPlanPackageLicensePayloadPlanParams[];
  isAutoAssignOnly: boolean;
  isAssginNewAlways: boolean;
};

export type BulkTransferPlanPackageLicenseParams = {
  payload: {
    organizationId: GenericID;
    plans: BulkPlanPackageLicensePayloadPlanParams[];
    usernameSender: string;
    usernameReceiver: string;
  };
  originalPayload: Record<string, any>;
  raw: string[];
};

export type BulkPlanPackageLicensePayloadPlanParams = {
  id: GenericID;
  name: string;
  planPackages: {
    id: GenericID;
    name: string;
  }[];
};
