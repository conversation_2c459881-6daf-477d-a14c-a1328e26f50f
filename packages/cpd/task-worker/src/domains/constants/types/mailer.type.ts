import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { BuildNotificationAchievementPayloadParams } from '@iso/email/constants/mailer';
import { CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { LoginEmailTypeEnum } from '@iso/lms/enums/loginProvider.enum';
import { PreAssignContentTypeEnum } from '@iso/lms/enums/preAssignContent.enum';
import { NotifyPlanPackageLicenseTextParams } from '@iso/lms/types/planPackageLicense.type';

import { MailAttachmentParams } from '@constants/types/infrastructures/notification.type';

export type TemplateParams = {
  name: string;
  enable: boolean;
  content?: any;
};

export type MailPayloadParams = {
  fullName: string;
  sendAt?: Date;
};

export type MailPayloadAssignEnrollmentCompleteParams = MailPayloadParams & {
  learnerUserId: GenericID;
  contentName: string;
  contentType: PreAssignContentTypeEnum;
  learnerFullName: string;
  round?: Nullable<{ roundDate?: Date; expiredDate: Nullable<Date> }>;
};

export type MailPayloadEnrollmentRejectedParams = MailPayloadParams & {
  courseName: string;
};

export type MailPayloadEnrollmentApproveParams = MailPayloadParams & {
  courseName: string;
  startDate: string; //round date
  endDate: string; //enrollment expiredAt
  sendDate: string;
  operationExpiredDate?: string; //enrollment expiredAt + operation date
  isDeduct?: boolean;
  isDeductApproved?: boolean;
  objectiveType: Nullable<CourseObjectiveTypeEnum>;
  regulator: RegulatorEnum;
};

export type MailPayloadEnrollmentCompleteAndCertificateParams = MailPayloadParams & {
  courseName: string;
  certificateUrl: string;
  certificatePDFUrl: string;
  certificateCode: string;
  isDeduct?: boolean;
  isDeductApproved?: boolean;
  refName?: string;
  operationExpiredDate?: string;
  objectiveType: Nullable<CourseObjectiveTypeEnum>;
  regulator: RegulatorEnum;
};

export type MailPayloadLearningPathEnrollmentCertificateParams = MailPayloadParams & {
  learningPathName: string;
  certificateUrl: string;
  certificatePDFUrl: string;
  certificateCode: string;
  refName: string;
};

export type MailPayloadLearningPathEnrollmentCompleteAndCertificateParams = MailPayloadParams & {
  learningPathName: string;
  certificateUrl: string;
  certificatePDFUrl: string;
  certificateCode: string;
  refName: string;
};

export type MailPayloadLearningPathEnrollmentCompleteParams = MailPayloadParams & {
  learningPathName: string;
  learningPathCode: string;
};

export type PromoteContentParams = {
  imageUrl: string;
  subtitle1: string[];
  subtitle2: string;
  url: string;
  title?: string;
};

export type MailPayloadPromoteContentParams = MailPayloadParams & {
  title: string;
  description: string;
  contents: PromoteContentParams[];
  url: string;
  textButton: string;
};

export type MailPayloadFirstPasswordParams = MailPayloadParams & {
  url: string;
  username: string;
  loginType: LoginEmailTypeEnum;
  planPackageLicenseTextList?: NotifyPlanPackageLicenseTextParams[];
};

export type MailPayloadAssignPlanPackageLicenseExistingUserParams = MailPayloadParams & {
  planPackageLicenseTextList: NotifyPlanPackageLicenseTextParams[];
};

export type MailPayloadPlanPackageLicenseExpiredParams = MailPayloadParams & {
  planPackageLicenseTextList: NotifyPlanPackageLicenseTextParams[];
};

export type MailPayloadAchievementAndBadgeCompleteParams = {
  fullName: string;
  achievementName: string;
  courseName: string;
  courseCode: string;
  enrollmentId: GenericID;
  achievementList: BuildNotificationAchievementPayloadParams[];
  attachments?: MailAttachmentParams[];
};
