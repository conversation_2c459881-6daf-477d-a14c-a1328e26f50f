import fastifyCsrf from '@fastify/csrf-protection';
import helmet from '@fastify/helmet';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';

import { ILogger } from '@infrastructures/services/logger/interfaces';
import Logger from '@infrastructures/services/logger/logger';

import { AppModule } from '@root/app.module';

import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { IEnvironments } from '@interfaces/configs/environment.interface';

export class ServerApplication {
  private static instance: ServerApplication;

  public static new() {
    if (!ServerApplication.instance) {
      ServerApplication.instance = new ServerApplication();
    }

    return ServerApplication.instance;
  }

  async run(): Promise<void> {
    const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter(), {
      logger: new Logger('Task-Worker'),
    });
    const environmentService = app.get<symbol, IEnvironments>(InfrastructuresConfigDIToken.Environment);

    this.initialApplication(app);

    await this.initialSecurity(app);

    const { port } = environmentService;
    await app.listen(port, '0.0.0.0');
  }

  private initialApplication(app: NestFastifyApplication): void {
    const LoggerApplication = app.get<symbol, ILogger>(InfrastructuresServiceDIToken.LoggerApplication);
    LoggerApplication.setContext('Task-Worker');
    app.useLogger(LoggerApplication);
    app.useGlobalPipes(new ValidationPipe());
  }

  private async initialSecurity(app: NestFastifyApplication) {
    app.enableCors({ origin: '*' });

    await app.register(helmet);
    await app.register(fastifyCsrf);
  }
}
