export class InfrastructuresServiceDIToken {
  static readonly HttpService: unique symbol = Symbol('HttpService');
  static readonly NotificationService: unique symbol = Symbol('NotificationService');
  static readonly WebHookNotificationService: unique symbol = Symbol('WebHookNotificationService');
  static readonly OAuthService: unique symbol = Symbol('OAuthService');
  static readonly AESCipherService: unique symbol = Symbol('AESCipherService');
  static readonly IDSService: unique symbol = Symbol('IDSService');
  static readonly JwtService: unique symbol = Symbol('JwtService');
  static readonly CertificateService: unique symbol = Symbol('CertificateService');
  static readonly UserValidateMessageBrokerService: unique symbol = Symbol('UserValidateMessageBrokerService');
  static readonly LoggerJobMessageService: unique symbol = Symbol('LoggerJobMessageService');
  static readonly TaskNotifyMessageBrokerService: unique symbol = Symbol('TaskNotifyMessageBrokerService');
  static readonly LoggerApplication: unique symbol = Symbol('LoggerApplication');
}
