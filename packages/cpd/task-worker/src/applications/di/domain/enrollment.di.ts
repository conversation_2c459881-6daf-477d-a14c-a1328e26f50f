export class EnrollmentDIToken {
  // Infrastructure
  static readonly EnrollmentRepository: unique symbol = Symbol('EnrollmentRepository');
  static readonly EnrollmentDataMapper: unique symbol = Symbol('EnrollmentDataMapper');

  static readonly EnrollmentAttachmentRepository: unique symbol = Symbol('EnrollmentAttachmentRepository');
  static readonly EnrollmentAttachmentDataMapper: unique symbol = Symbol('EnrollmentAttachmentDataMapper');

  static readonly VerifyEnrollmentRepository: unique symbol = Symbol('VerifyEnrollmentRepository');
  static readonly VerifyEnrollmentDataMapper: unique symbol = Symbol('VerifyEnrollmentDataMapper');

  static readonly EnrollmentCertificateRepository: unique symbol = Symbol('EnrollmentCertificateRepository');
  static readonly EnrollmentCertificateDataMapper: unique symbol = Symbol('EnrollmentCertificateDataMapper');

  static readonly LogFaceComparisonRepository: unique symbol = Symbol('LogFaceComparisonRepository');
  static readonly LogFaceComparisonDataMapper: unique symbol = Symbol('LogFaceComparisonDataMapper');

  static readonly IdentificationCardRepository: unique symbol = Symbol('IdentificationCardRepository');
  static readonly IdentificationCardDataMapper: unique symbol = Symbol('IdentificationCardDataMapper');

  static readonly LogActivityDetectRepository: unique symbol = Symbol('LogActivityDetectRepository');
  static readonly LogActivityDetectDataMapper: unique symbol = Symbol('LogActivityDetectDataMapper');

  static readonly QuizAnswerRepository: unique symbol = Symbol('QuizAnswerRepository');
  static readonly QuizAnswerDataMapper: unique symbol = Symbol('QuizAnswerDataMapper');

  static readonly SummaryTSIQuizScoreRepository: unique symbol = Symbol('SummaryTSIQuizScoreRepository');
  static readonly SummaryTSIQuizScoreDataMapper: unique symbol = Symbol('SummaryTSIQuizScoreDataMapper');

  static readonly EnrollmentPlanPackageLicenseRepository: unique symbol = Symbol(
    'EnrollmentPlanPackageLicenseRepository',
  );
  static readonly EnrollmentPlanPackageLicenseDataMapper: unique symbol = Symbol(
    'EnrollmentPlanPackageLicenseDataMapper',
  );

  // Service
  static readonly EnrollmentService: unique symbol = Symbol('EnrollmentService');
  static readonly SummaryTSIQuizScoreService: unique symbol = Symbol('SummaryTSIQuizScoreService');

  // Consumer
  static readonly EnrollmentConsumer: unique symbol = Symbol('EnrollmentConsumer');

  // UseCase
  static readonly BulkEnrollmentHistoryUseCase: unique symbol = Symbol('BulkEnrollmentHistoryUseCase');
  static readonly CancelEnrollmentHistoryUseCase: unique symbol = Symbol('CancelEnrollmentHistoryUseCase');
  static readonly AutoApproveEnrollmentUseCase: unique symbol = Symbol('AutoApproveEnrollmentUseCase');
  static readonly CalculateCumulativeTSIQuizScoreUseCase: unique symbol = Symbol(
    'CalculateCumulativeTSIQuizScoreUseCase',
  );
}
