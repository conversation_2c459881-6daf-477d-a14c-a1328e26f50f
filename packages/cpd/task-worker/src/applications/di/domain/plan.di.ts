export class PlanDIToken {
  // Data Mapper
  static readonly PackageDataMapper: unique symbol = Symbol('PackageDataMapper');
  static readonly PlanDataMapper: unique symbol = Symbol('PlanDataMapper');
  static readonly PlanPackageDataMapper: unique symbol = Symbol('PlanPackageDataMapper');
  static readonly PlanPackageLicenseDataMapper: unique symbol = Symbol('PlanPackageLicenseDataMapper');
  static readonly PlanPackageLicenseHistoryDataMapper: unique symbol = Symbol('PlanPackageLicenseHistoryDataMapper');

  // Repositories
  static readonly PackageRepository: unique symbol = Symbol('PackageRepository');
  static readonly PlanRepository: unique symbol = Symbol('PlanRepository');
  static readonly PlanPackageRepository: unique symbol = Symbol('PlanPackageRepository');
  static readonly PlanPackageLicenseRepository: unique symbol = Symbol('PlanPackageLicenseRepository');
  static readonly PlanPackageLicenseHistoryRepository: unique symbol = Symbol('PlanPackageLicenseHistoryRepository');

  // Service

  // Consumer
  static readonly PlanConsumer: unique symbol = Symbol('PlanConsumer');

  // Use Case
  static readonly BulkAssignPlanPackageLicenseUseCase: unique symbol = Symbol('BulkAssignPlanPackageLicenseUseCase');
  static readonly BulkTransferPlanPackageLicenseUseCase: unique symbol = Symbol(
    'BulkTransferPlanPackageLicenseUseCase',
  );
  static readonly PlanPackageLicenseExpiredUseCase: unique symbol = Symbol('PlanPackageLicenseExpiredUseCase');
}
