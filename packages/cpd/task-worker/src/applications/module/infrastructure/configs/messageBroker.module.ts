import { EnvironmentsModule } from '@modules/infrastructure/configs/environments.module';
import { LoggerServiceModule } from '@modules/infrastructure/services/logger.service.module';
import { Module } from '@nestjs/common';

import { MessageBrokerEventEmitter } from '@infrastructures/configs/messageBroker/messageBroker.event';
import { MessageBrokerInstance } from '@infrastructures/configs/messageBroker/messageBroker.instance';

import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
@Module({
  imports: [EnvironmentsModule, LoggerServiceModule],
  providers: [
    {
      provide: InfrastructuresConfigDIToken.MessageBrokerInstance,
      useClass: MessageBrokerInstance,
    },
    {
      provide: InfrastructuresConfigDIToken.MessageBrokerEventEmitter,
      useClass: MessageBrokerEventEmitter,
    },
  ],
  exports: [InfrastructuresConfigDIToken.MessageBrokerInstance],
})
export class MessageBrokerModule {}
