import { Module } from '@nestjs/common';

import {
  AchievementDataMapper,
  BadgeDataMapper,
  AnnouncementDataMapper,
  CertificateDataMapper,
  ClassroomDataMapper,
  ClassroomLocationDataMapper,
  ClassroomLocationEnrollmentDataMapper,
  ClassroomRoundDataMapper,
  ColumnSettingDataMapper,
  CourseCategoryDataMapper,
  CourseDataMapper,
  CourseItemCriteriaConfigDataMapper,
  CourseItemDataMapper,
  CourseMarketplaceDataMapper,
  CourseVersionDataMapper,
  CustomerDataMapper,
  CustomerPartnerDataMapper,
  DepartmentDataMapper,
  EnrollmentAchievementDataMapper,
  EnrollmentAttachmentDataMapper,
  EnrollmentBadgeDataMapper,
  EnrollmentCertificateDataMapper,
  EnrollmentDataMapper,
  EnrollmentPlanPackageLicenseDataMapper,
  EnrollmentRegulatorReportDataMapper,
  IdentificationCardDataMapper,
  InstructorDataMapper,
  JobDataMapper,
  JobTransactionDataMapper,
  KnowledgeContentItemDataMapper,
  LearningPathDataMapper,
  LearningPathEnrollmentCertificateDataMapper,
  LearningPathEnrollmentDataMapper,
  LearningPathVersionDataMapper,
  LicenseDataMapper,
  LogActivityDetectDataMapper,
  LogFaceComparisonDataMapper,
  LoginProviderDataMapper,
  MediaDataMapper,
  OrganizationCertificateDataMapper,
  OrganizationColumnSettingDataMapper,
  OrganizationDataMapper,
  OrganizationLoginProviderDataMapper,
  OrganizationSchedulerDataMapper,
  OrganizationStorageDataMapper,
  PackageDataMapper,
  PartDataMapper,
  PlanDataMapper,
  PlanPackageDataMapper,
  PlanPackageLicenseDataMapper,
  PlanPackageLicenseHistoryDataMapper,
  PointHistoryDataMapper,
  PreAssignContentDataMapper,
  PreEnrollmentReservationDataMapper,
  PreEnrollmentTransactionDataMapper,
  ProductSKUBundleDataMapper,
  ProductSKUCourseDataMapper,
  ProductSKUDataMapper,
  PromoteNotificationDataMapper,
  PurchaseOrderDataMapper,
  QuizAnswerDataMapper,
  QuizDataMapper,
  ReportHistoryDataMapper,
  RoundDataMapper,
  SummaryTSIQuizScoreDataMapper,
  SurveyDataMapper,
  SurveySubmissionDataMapper,
  TaskOperationDataMapper,
  TemplateColumnSettingDataMapper,
  UserDataMapper,
  UserDirectReportDataMapper,
  UserGroupDataMapper,
  UserLoginDataMapper,
  UserNotificationDataMapper,
  VerifyEnrollmentDataMapper,
} from '@infrastructures/dataMappers';

import {
  AchievementDIToken,
  AnnouncementDIToken,
  BadgeDIToken,
  CertificateDIToken,
  CourseCategoryDIToken,
  CourseDIToken,
  CreditDIToken,
  EnrollmentDIToken,
  InstructorDIToken,
  JobDIToken,
  KnowledgeContentDIToken,
  LearningPathDIToken,
  LicenseDIToken,
  MaterialMediaDIToken,
  NotificationDIToken,
  OrganizationDIToken,
  PlanDIToken,
  PreAssignContentDIToken,
  PreEnrollmentDIToken,
  ProductSKUDIToken,
  ReportDIToken,
  RoundDIToken,
  TaskOperationDIToken,
  UserDIToken,
  UserGroupDIToken,
} from '@applications/di/domain';

@Module({
  providers: [
    {
      provide: AchievementDIToken.AchievementDataMapper,
      useClass: AchievementDataMapper,
    },
    {
      provide: AchievementDIToken.EnrollmentAchievementDataMapper,
      useClass: EnrollmentAchievementDataMapper,
    },
    {
      provide: BadgeDIToken.BadgeDataMapper,
      useClass: BadgeDataMapper,
    },
    {
      provide: BadgeDIToken.EnrollmentBadgeDataMapper,
      useClass: EnrollmentBadgeDataMapper,
    },
    { provide: CertificateDIToken.CertificateDataMapper, useClass: CertificateDataMapper },
    {
      provide: CreditDIToken.CustomerDataMapper,
      useClass: CustomerDataMapper,
    },
    {
      provide: OrganizationDIToken.ColumnSettingDataMapper,
      useClass: ColumnSettingDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationColumnSettingDataMapper,
      useClass: OrganizationColumnSettingDataMapper,
    },
    {
      provide: CourseDIToken.CourseDataMapper,
      useClass: CourseDataMapper,
    },
    {
      provide: OrganizationDIToken.DepartmentDataMapper,
      useClass: DepartmentDataMapper,
    },
    {
      provide: LicenseDIToken.LicenseDataMapper,
      useClass: LicenseDataMapper,
    },
    {
      provide: RoundDIToken.RoundDataMapper,
      useClass: RoundDataMapper,
    },
    {
      provide: EnrollmentDIToken.EnrollmentDataMapper,
      useClass: EnrollmentDataMapper,
    },
    {
      provide: CourseDIToken.CourseVersionCertificateDataMapper,
      useClass: CourseVersionDataMapper,
    },
    {
      provide: ReportDIToken.ReportHistoryDataMapper,
      useClass: ReportHistoryDataMapper,
    },
    {
      provide: UserDIToken.UserDataMapper,
      useClass: UserDataMapper,
    },
    {
      provide: PreEnrollmentDIToken.PreEnrollmentTransactionDataMapper,
      useClass: PreEnrollmentTransactionDataMapper,
    },
    {
      provide: OrganizationDIToken.TemplateColumnSettingDataMapper,
      useClass: TemplateColumnSettingDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationDataMapper,
      useClass: OrganizationDataMapper,
    },

    {
      provide: UserDIToken.UserDirectReportDataMapper,
      useClass: UserDirectReportDataMapper,
    },
    {
      provide: UserGroupDIToken.UserGroupDataMapper,
      useClass: UserGroupDataMapper,
    },
    {
      provide: ProductSKUDIToken.ProductSKUDataMapper,
      useClass: ProductSKUDataMapper,
    },
    {
      provide: ProductSKUDIToken.ProductSKUBundleDataMapper,
      useClass: ProductSKUBundleDataMapper,
    },
    {
      provide: ProductSKUDIToken.ProductSKUCourseDataMapper,
      useClass: ProductSKUCourseDataMapper,
    },
    {
      provide: CourseDIToken.CourseDataMapper,
      useClass: CourseDataMapper,
    },
    {
      provide: CreditDIToken.CustomerPartnerDataMapper,
      useClass: CustomerPartnerDataMapper,
    },
    {
      provide: PreEnrollmentDIToken.PreEnrollmentReservationDataMapper,
      useClass: PreEnrollmentReservationDataMapper,
    },
    {
      provide: PreEnrollmentDIToken.EnrollmentRegulatorReportDataMapper,
      useClass: EnrollmentRegulatorReportDataMapper,
    },
    {
      provide: JobDIToken.JobDataMapper,
      useClass: JobDataMapper,
    },
    {
      provide: JobDIToken.JobTransactionDataMapper,
      useClass: JobTransactionDataMapper,
    },
    {
      provide: TaskOperationDIToken.TaskOperationDataMapper,
      useClass: TaskOperationDataMapper,
    },
    {
      provide: CourseDIToken.CourseDataMapper,
      useClass: CourseDataMapper,
    },
    {
      provide: CourseDIToken.CourseVersionDataMapper,
      useClass: CourseVersionDataMapper,
    },
    {
      provide: MaterialMediaDIToken.ClassroomDataMapper,
      useClass: ClassroomDataMapper,
    },
    {
      provide: MaterialMediaDIToken.ClassroomLocationDataMapper,
      useClass: ClassroomLocationDataMapper,
    },
    {
      provide: MaterialMediaDIToken.ClassroomLocationEnrollmentDataMapper,
      useClass: ClassroomLocationEnrollmentDataMapper,
    },
    {
      provide: MaterialMediaDIToken.ClassroomRoundDataMapper,
      useClass: ClassroomRoundDataMapper,
    },
    {
      provide: CourseDIToken.PartDataMapper,
      useClass: PartDataMapper,
    },
    {
      provide: CourseDIToken.CourseItemDataMapper,
      useClass: CourseItemDataMapper,
    },
    {
      provide: EnrollmentDIToken.QuizAnswerDataMapper,
      useClass: QuizAnswerDataMapper,
    },
    {
      provide: CourseDIToken.CourseItemCriteriaConfigDataMapper,
      useClass: CourseItemCriteriaConfigDataMapper,
    },
    {
      provide: EnrollmentDIToken.VerifyEnrollmentDataMapper,
      useClass: VerifyEnrollmentDataMapper,
    },
    {
      provide: MaterialMediaDIToken.QuizDataMapper,
      useClass: QuizDataMapper,
    },
    {
      provide: MaterialMediaDIToken.SurveySubmissionDataMapper,
      useClass: SurveySubmissionDataMapper,
    },
    {
      provide: MaterialMediaDIToken.SurveyDataMapper,
      useClass: SurveyDataMapper,
    },
    {
      provide: EnrollmentDIToken.IdentificationCardDataMapper,
      useClass: IdentificationCardDataMapper,
    },
    {
      provide: EnrollmentDIToken.LogActivityDetectDataMapper,
      useClass: LogActivityDetectDataMapper,
    },
    {
      provide: EnrollmentDIToken.LogFaceComparisonDataMapper,
      useClass: LogFaceComparisonDataMapper,
    },
    {
      provide: LearningPathDIToken.LearningPathDataMapper,
      useClass: LearningPathDataMapper,
    },
    {
      provide: LearningPathDIToken.LearningPathEnrollmentDataMapper,
      useClass: LearningPathEnrollmentDataMapper,
    },
    {
      provide: LearningPathDIToken.LearningPathEnrollmentCertificateDataMapper,
      useClass: LearningPathEnrollmentCertificateDataMapper,
    },
    {
      provide: LearningPathDIToken.LearningPathVersionDataMapper,
      useClass: LearningPathVersionDataMapper,
    },
    {
      provide: PreAssignContentDIToken.PreAssignContentDataMapper,
      useClass: PreAssignContentDataMapper,
    },
    {
      provide: UserDIToken.UserNotificationDataMapper,
      useClass: UserNotificationDataMapper,
    },
    {
      provide: EnrollmentDIToken.EnrollmentAttachmentDataMapper,
      useClass: EnrollmentAttachmentDataMapper,
    },
    {
      provide: EnrollmentDIToken.IdentificationCardDataMapper,
      useClass: IdentificationCardDataMapper,
    },
    {
      provide: EnrollmentDIToken.EnrollmentCertificateDataMapper,
      useClass: EnrollmentCertificateDataMapper,
    },
    {
      provide: CreditDIToken.PointHistoryDataMapper,
      useClass: PointHistoryDataMapper,
    },
    {
      provide: CreditDIToken.PurchaseOrderDataMapper,
      useClass: PurchaseOrderDataMapper,
    },
    {
      provide: NotificationDIToken.PromoteNotificationDataMapper,
      useClass: PromoteNotificationDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationStorageDataMapper,
      useClass: OrganizationStorageDataMapper,
    },
    {
      provide: KnowledgeContentDIToken.KnowledgeContentItemDataMapper,
      useClass: KnowledgeContentItemDataMapper,
    },
    {
      provide: AnnouncementDIToken.AnnouncementDataMapper,
      useClass: AnnouncementDataMapper,
    },
    {
      provide: InstructorDIToken.InstructorDataMapper,
      useClass: InstructorDataMapper,
    },
    {
      provide: MaterialMediaDIToken.MediaDataMapper,
      useClass: MediaDataMapper,
    },
    {
      provide: EnrollmentDIToken.SummaryTSIQuizScoreDataMapper,
      useClass: SummaryTSIQuizScoreDataMapper,
    },
    {
      provide: PlanDIToken.PackageDataMapper,
      useClass: PackageDataMapper,
    },
    {
      provide: PlanDIToken.PlanPackageLicenseHistoryDataMapper,
      useClass: PlanPackageLicenseHistoryDataMapper,
    },
    {
      provide: PlanDIToken.PlanPackageLicenseDataMapper,
      useClass: PlanPackageLicenseDataMapper,
    },
    {
      provide: PlanDIToken.PlanPackageDataMapper,
      useClass: PlanPackageDataMapper,
    },
    {
      provide: PlanDIToken.PlanDataMapper,
      useClass: PlanDataMapper,
    },
    {
      provide: ProductSKUDIToken.CourseMarketplaceDataMapper,
      useClass: CourseMarketplaceDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationLoginProviderDataMapper,
      useClass: OrganizationLoginProviderDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationSchedulerDataMapper,
      useClass: OrganizationSchedulerDataMapper,
    },
    {
      provide: OrganizationDIToken.OrganizationCertificateDataMapper,
      useClass: OrganizationCertificateDataMapper,
    },
    {
      provide: OrganizationDIToken.LoginProviderDataMapper,
      useClass: LoginProviderDataMapper,
    },
    {
      provide: UserDIToken.UserLoginDataMapper,
      useClass: UserLoginDataMapper,
    },
    {
      provide: CourseCategoryDIToken.CourseCategoryDataMapper,
      useClass: CourseCategoryDataMapper,
    },
    {
      provide: EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper,
      useClass: EnrollmentPlanPackageLicenseDataMapper,
    },
  ],
  exports: [
    AchievementDIToken.AchievementDataMapper,
    AchievementDIToken.EnrollmentAchievementDataMapper,
    AnnouncementDIToken.AnnouncementDataMapper,
    BadgeDIToken.BadgeDataMapper,
    BadgeDIToken.EnrollmentBadgeDataMapper,
    CertificateDIToken.CertificateDataMapper,
    CourseDIToken.CourseDataMapper,
    CourseDIToken.CourseItemCriteriaConfigDataMapper,
    CourseDIToken.CourseItemDataMapper,
    CourseDIToken.CourseVersionCertificateDataMapper,
    CourseDIToken.CourseVersionDataMapper,
    CourseDIToken.PartDataMapper,
    CreditDIToken.CustomerDataMapper,
    CreditDIToken.CustomerPartnerDataMapper,
    CreditDIToken.PointHistoryDataMapper,
    CreditDIToken.PurchaseOrderDataMapper,
    CourseCategoryDIToken.CourseCategoryDataMapper,
    EnrollmentDIToken.EnrollmentAttachmentDataMapper,
    EnrollmentDIToken.EnrollmentCertificateDataMapper,
    EnrollmentDIToken.EnrollmentDataMapper,
    EnrollmentDIToken.IdentificationCardDataMapper,
    EnrollmentDIToken.LogActivityDetectDataMapper,
    EnrollmentDIToken.LogFaceComparisonDataMapper,
    EnrollmentDIToken.QuizAnswerDataMapper,
    EnrollmentDIToken.SummaryTSIQuizScoreDataMapper,
    EnrollmentDIToken.VerifyEnrollmentDataMapper,
    EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper,
    InstructorDIToken.InstructorDataMapper,
    JobDIToken.JobDataMapper,
    JobDIToken.JobTransactionDataMapper,
    TaskOperationDIToken.TaskOperationDataMapper,
    KnowledgeContentDIToken.KnowledgeContentItemDataMapper,
    LearningPathDIToken.LearningPathDataMapper,
    LearningPathDIToken.LearningPathEnrollmentCertificateDataMapper,
    LearningPathDIToken.LearningPathEnrollmentDataMapper,
    LearningPathDIToken.LearningPathVersionDataMapper,
    LicenseDIToken.LicenseDataMapper,
    LicenseDIToken.LicenseDataMapper,
    MaterialMediaDIToken.ClassroomDataMapper,
    MaterialMediaDIToken.ClassroomLocationDataMapper,
    MaterialMediaDIToken.ClassroomLocationEnrollmentDataMapper,
    MaterialMediaDIToken.ClassroomRoundDataMapper,
    MaterialMediaDIToken.MediaDataMapper,
    MaterialMediaDIToken.QuizDataMapper,
    MaterialMediaDIToken.SurveyDataMapper,
    MaterialMediaDIToken.SurveySubmissionDataMapper,
    NotificationDIToken.PromoteNotificationDataMapper,
    OrganizationDIToken.ColumnSettingDataMapper,
    OrganizationDIToken.DepartmentDataMapper,
    OrganizationDIToken.LoginProviderDataMapper,
    OrganizationDIToken.OrganizationCertificateDataMapper,
    OrganizationDIToken.OrganizationColumnSettingDataMapper,
    OrganizationDIToken.OrganizationDataMapper,
    OrganizationDIToken.OrganizationLoginProviderDataMapper,
    OrganizationDIToken.OrganizationSchedulerDataMapper,
    OrganizationDIToken.OrganizationStorageDataMapper,
    OrganizationDIToken.TemplateColumnSettingDataMapper,
    PlanDIToken.PackageDataMapper,
    PlanDIToken.PlanDataMapper,
    PlanDIToken.PlanPackageDataMapper,
    PlanDIToken.PlanPackageLicenseDataMapper,
    PlanDIToken.PlanPackageLicenseHistoryDataMapper,
    PreAssignContentDIToken.PreAssignContentDataMapper,
    PreEnrollmentDIToken.EnrollmentRegulatorReportDataMapper,
    PreEnrollmentDIToken.PreEnrollmentReservationDataMapper,
    PreEnrollmentDIToken.PreEnrollmentTransactionDataMapper,
    ProductSKUDIToken.CourseMarketplaceDataMapper,
    ProductSKUDIToken.ProductSKUBundleDataMapper,
    ProductSKUDIToken.ProductSKUCourseDataMapper,
    ProductSKUDIToken.ProductSKUDataMapper,
    ReportDIToken.ReportHistoryDataMapper,
    RoundDIToken.RoundDataMapper,
    UserDIToken.UserDataMapper,
    UserDIToken.UserDirectReportDataMapper,
    UserDIToken.UserLoginDataMapper,
    UserDIToken.UserNotificationDataMapper,
    UserGroupDIToken.UserGroupDataMapper,
  ],
})
export class DataMapperModule {}
