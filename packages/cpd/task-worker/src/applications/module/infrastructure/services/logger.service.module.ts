import { Module } from '@nestjs/common';

import Logger from '@infrastructures/services/logger/logger';
import { LoggerJobMessageService } from '@infrastructures/services/logger/loggerJobMessage.service';

import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

@Module({
  providers: [
    {
      provide: InfrastructuresServiceDIToken.LoggerJobMessageService,
      useClass: LoggerJobMessageService,
    },
    {
      provide: InfrastructuresServiceDIToken.LoggerApplication,
      useClass: Logger,
    },
  ],
  exports: [InfrastructuresServiceDIToken.LoggerJobMessageService, InfrastructuresServiceDIToken.LoggerApplication],
})
export class LoggerServiceModule {}
