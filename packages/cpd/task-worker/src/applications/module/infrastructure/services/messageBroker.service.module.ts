import { EnvironmentsModule } from '@modules/infrastructure/configs/environments.module';
import { MessageBrokerModule } from '@modules/infrastructure/configs/messageBroker.module';
import { Module } from '@nestjs/common';

import { MessageBrokerService } from '@infrastructures/services/messageBroker/messageBroker.service';
import { TaskNotifyMessageBrokerService } from '@infrastructures/services/messageBroker/taskNotifyMessageBroker.service';
import { UserValidateMessageBrokerService } from '@infrastructures/services/messageBroker/userValidateMessageBroker.service';

import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { IMessageBrokerInstance } from '@interfaces/configs/messageBroker.interface';

@Module({
  imports: [MessageBrokerModule, EnvironmentsModule],
  providers: [
    {
      provide: InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) => new MessageBrokerService(brokerMessageInstance),
    },
    {
      provide: InfrastructuresServiceDIToken.UserValidateMessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) =>
        new UserValidateMessageBrokerService(brokerMessageInstance),
    },
    {
      provide: InfrastructuresServiceDIToken.TaskNotifyMessageBrokerService,
      inject: [InfrastructuresConfigDIToken.MessageBrokerInstance],
      useFactory: (brokerMessageInstance: IMessageBrokerInstance) =>
        new TaskNotifyMessageBrokerService(brokerMessageInstance),
    },
  ],
  exports: [
    InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService,
    InfrastructuresServiceDIToken.UserValidateMessageBrokerService,
    InfrastructuresServiceDIToken.TaskNotifyMessageBrokerService,
  ],
})
export class MessageBrokerServiceModule {}
