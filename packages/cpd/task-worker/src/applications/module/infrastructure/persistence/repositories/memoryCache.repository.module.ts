import { DataMapperModule } from '@modules/infrastructure/dataMapper.module';
import { Module, Provider } from '@nestjs/common';

import { AuthenticationMemoryCachedRepository } from '@infrastructures/persistence/repositories/memoryCache/authenticationMemoryCached.repository';
import { MemoryCacheRepository } from '@infrastructures/persistence/repositories/memoryCache/memoryCache.repository';
import { TaskMemoryCachedRepository } from '@infrastructures/persistence/repositories/memoryCache/taskMemoryCached.repository';

import { TaskOperationDIToken } from '@applications/di/domain';
import { AuthenticationDIToken } from '@applications/di/domain/authentication.di';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { MemoryCacheModule } from '@applications/module/infrastructure/configs/memoryCache.module';

import { MemoryCachedInstanceParams } from '@constants/types/infrastructures/memoryCache.type';

import { ITaskOperationDataMapper } from '@interfaces/dataMapper/taskOperation.dataMapper.interface';

const providers: Provider[] = [
  {
    provide: InfrastructuresPersistenceDIToken.MemoryCacheRepository,
    inject: [InfrastructuresConfigDIToken.MemoryCachedInstance],
    useFactory: (memoryCacheInstance: MemoryCachedInstanceParams) => new MemoryCacheRepository(memoryCacheInstance),
  },
  {
    provide: AuthenticationDIToken.AuthenticationMemoryCachedRepository,
    inject: [InfrastructuresConfigDIToken.MemoryCachedInstance],
    useFactory: (db: MemoryCachedInstanceParams) => new AuthenticationMemoryCachedRepository(db),
  },
  {
    provide: TaskOperationDIToken.TaskOperationMemoryCachedRepository,
    inject: [InfrastructuresConfigDIToken.MemoryCachedInstance, TaskOperationDIToken.TaskOperationDataMapper],
    useFactory: (db: MemoryCachedInstanceParams, dataMapper: ITaskOperationDataMapper) =>
      new TaskMemoryCachedRepository(db, dataMapper),
  },
];

@Module({
  imports: [MemoryCacheModule, DataMapperModule],
  providers,
  exports: [
    AuthenticationDIToken.AuthenticationMemoryCachedRepository,
    TaskOperationDIToken.TaskOperationMemoryCachedRepository,
  ],
})
export class MemoryCacheRepositoryModule {}
