import { DbModule } from '@modules/infrastructure/configs/db.module';
import { DataMapperModule } from '@modules/infrastructure/dataMapper.module';
import { Module, Provider } from '@nestjs/common';

// Repositories
import {
  AchievementRepository,
  BadgeRepository,
  AnnouncementRepository,
  CertificateRepository,
  ClassroomLocationEnrollmentRepository,
  ClassroomLocationRepository,
  ClassroomRepository,
  ClassroomRoundRepository,
  ColumnSettingRepository,
  CourseCategoryRepository,
  CourseItemCriteriaConfigRepository,
  CourseItemRepository,
  CourseMarketplaceRepository,
  CourseRepository,
  CourseVersionCertificateRepository,
  CourseVersionRepository,
  CustomerPartnerRepository,
  CustomerRepository,
  DepartmentRepository,
  EnrollmentAchievementRepository,
  EnrollmentAttachmentRepository,
  EnrollmentBadgeRepository,
  EnrollmentCertificateRepository,
  EnrollmentPlanPackageLicenseRepository,
  EnrollmentRegulatorReportRepository,
  EnrollmentRepository,
  IdentificationCardRepository,
  InstructorRepository,
  JobRepository,
  JobTransactionRepository,
  KnowledgeContentItemRepository,
  LearningPathEnrollmentCertificateRepository,
  LearningPathEnrollmentRepository,
  LearningPathRepository,
  LearningPathVersionRepository,
  LicenseRepository,
  LogActivityDetectRepository,
  LogFaceComparisonRepository,
  LoginProviderRepository,
  MediaRepository,
  OrganizationCertificateRepository,
  OrganizationColumnSettingRepository,
  OrganizationLoginProviderRepository,
  OrganizationRepository,
  OrganizationSchedulerRepository,
  OrganizationStorageRepository,
  PackageRepository,
  PartRepository,
  PlanPackageLicenseHistoryRepository,
  PlanPackageLicenseRepository,
  PlanPackageRepository,
  PlanRepository,
  PointHistoryRepository,
  PreAssignContentRepository,
  PreEnrollmentReservationRepository,
  PreEnrollmentTransactionRepository,
  ProductSKUBundleRepository,
  ProductSKUCourseRepository,
  ProductSKURepository,
  PromoteNotificationRepository,
  PurchaseOrderRepository,
  QuizAnswerRepository,
  QuizRepository,
  ReportHistoryRepository,
  RoundRepository,
  SummaryTSIQuizScoreRepository,
  SurveyRepository,
  SurveySubmissionRepository,
  TemplateColumnSettingRepository,
  UserDirectReportRepository,
  UserGroupRepository,
  UserLoginRepository,
  UserNotificationRepository,
  UserRepository,
  VerifyEnrollmentRepository,
} from '@infrastructures/persistence/repositories/database';

// DIToken

import {
  AchievementDIToken,
  AnnouncementDIToken,
  BadgeDIToken,
  CertificateDIToken,
  CourseCategoryDIToken,
  CourseDIToken,
  CreditDIToken,
  EnrollmentDIToken,
  InstructorDIToken,
  JobDIToken,
  KnowledgeContentDIToken,
  LearningPathDIToken,
  LicenseDIToken,
  MaterialMediaDIToken,
  NotificationDIToken,
  OrganizationDIToken,
  PlanDIToken,
  PreAssignContentDIToken,
  PreEnrollmentDIToken,
  ProductSKUDIToken,
  ReportDIToken,
  RoundDIToken,
  UserDIToken,
  UserGroupDIToken,
} from '@applications/di/domain';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';

// Data Mappers
import {
  IAchievementDataMapper,
  IBadgeDataMapper,
  ICertificateDataMapper,
  IAnnouncementDataMapper,
  IClassroomDataMapper,
  IClassroomLocationDataMapper,
  IClassroomLocationEnrollmentDataMapper,
  IClassroomRoundDataMapper,
  IColumnSettingDataMapper,
  ICourseCategoryDataMapper,
  ICourseDataMapper,
  ICourseItemCriteriaConfigDataMapper,
  ICourseItemDataMapper,
  ICourseMarketplaceDataMapper,
  ICourseVersionCertificateDataMapper,
  ICourseVersionDataMapper,
  ICustomerDataMapper,
  ICustomerPartnerDataMapper,
  IDepartmentDataMapper,
  IEnrollmentAchievementDataMapper,
  IEnrollmentAttachmentDataMapper,
  IEnrollmentBadgeDataMapper,
  IEnrollmentCertificateDataMapper,
  IEnrollmentDataMapper,
  IEnrollmentPlanPackageLicenseDataMapper,
  IEnrollmentRegulatorReportDataMapper,
  IIdentificationCardDataMapper,
  IInstructorDataMapper,
  IJobDataMapper,
  IJobTransactionDataMapper,
  IKnowledgeContentItemDataMapper,
  ILearningPathDataMapper,
  ILearningPathEnrollmentCertificateDataMapper,
  ILearningPathEnrollmentDataMapper,
  ILearningPathVersionDataMapper,
  ILicenseDataMapper,
  ILogActivityDetectDataMapper,
  ILogFaceComparisonDataMapper,
  ILoginProviderDataMapper,
  IMediaDataMapper,
  IOrganizationCertificateDataMapper,
  IOrganizationColumnSettingDataMapper,
  IOrganizationDataMapper,
  IOrganizationLoginProviderDataMapper,
  IOrganizationSchedulerDataMapper,
  IOrganizationStorageDataMapper,
  IPackageDataMapper,
  IPartDataMapper,
  IPlanDataMapper,
  IPlanPackageDataMapper,
  IPlanPackageLicenseDataMapper,
  IPlanPackageLicenseHistoryDataMapper,
  IPointHistoryDataMapper,
  IPreAssignContentDataMapper,
  IPreEnrollmentReservationDataMapper,
  IPreEnrollmentTransactionDataMapper,
  IProductSKUBundleDataMapper,
  IProductSKUCourseDataMapper,
  IProductSKUDataMapper,
  IPromoteNotificationDataMapper,
  IPurchaseOrderDataMapper,
  IQuizAnswerMapper,
  IQuizDataMapper,
  IReportHistoryDataMapper,
  IRoundDataMapper,
  ISummaryTSIQuizScoreDataMapper,
  ISurveyDataMapper,
  ISurveySubmissionDataMapper,
  ITemplateColumnSettingDataMapper,
  IUserDataMapper,
  IUserDirectReportDataMapper,
  IUserGroupDataMapper,
  IUserLoginDataMapper,
  IUserNotificationDataMapper,
  IVerifyEnrollmentDataMapper,
} from '@interfaces/dataMapper';

const databaseRepositoriesProvider: Provider[] = [
  {
    provide: AchievementDIToken.AchievementRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, AchievementDIToken.AchievementDataMapper],
    useFactory: (db: DbInstanceParams, achievementDataMapper: IAchievementDataMapper) =>
      new AchievementRepository(db, achievementDataMapper),
  },
  {
    provide: AchievementDIToken.EnrollmentAchievementRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, AchievementDIToken.EnrollmentAchievementDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentAchievementDataMapper: IEnrollmentAchievementDataMapper) =>
      new EnrollmentAchievementRepository(db, enrollmentAchievementDataMapper),
  },
  {
    provide: BadgeDIToken.BadgeRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, BadgeDIToken.BadgeDataMapper],
    useFactory: (db: DbInstanceParams, badgeDataMapper: IBadgeDataMapper) => new BadgeRepository(db, badgeDataMapper),
  },
  {
    provide: BadgeDIToken.EnrollmentBadgeRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, BadgeDIToken.EnrollmentBadgeDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentBadgeDataMapper: IEnrollmentBadgeDataMapper) =>
      new EnrollmentBadgeRepository(db, enrollmentBadgeDataMapper),
  },
  {
    provide: CertificateDIToken.CertificateRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CertificateDIToken.CertificateDataMapper],
    useFactory: (db: DbInstanceParams, certificateDataMapper: ICertificateDataMapper) =>
      new CertificateRepository(db, certificateDataMapper),
  },
  {
    provide: CourseDIToken.CourseVersionCertificateRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.CourseVersionCertificateDataMapper],
    useFactory: (db: DbInstanceParams, certificateDataMapper: ICourseVersionCertificateDataMapper) =>
      new CourseVersionCertificateRepository(db, certificateDataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationColumnSettingRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationColumnSettingDataMapper) =>
      new OrganizationColumnSettingRepository(db, dataMapper),
  },
  {
    provide: CreditDIToken.CustomerRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CreditDIToken.CustomerDataMapper],
    useFactory: (db: DbInstanceParams, customerDataMapper: ICustomerDataMapper) =>
      new CustomerRepository(db, customerDataMapper),
  },
  {
    provide: OrganizationDIToken.DepartmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.DepartmentDataMapper],
    useFactory: (db: DbInstanceParams, departmentDataMapper: IDepartmentDataMapper) =>
      new DepartmentRepository(db, departmentDataMapper),
  },
  {
    provide: LicenseDIToken.LicenseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LicenseDIToken.LicenseDataMapper],
    useFactory: (db: DbInstanceParams, licenseDataMapper: ILicenseDataMapper) =>
      new LicenseRepository(db, licenseDataMapper),
  },
  {
    provide: OrganizationDIToken.TemplateColumnSettingRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.TemplateColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, templateColumnSettingDataMapper: ITemplateColumnSettingDataMapper) =>
      new TemplateColumnSettingRepository(db, templateColumnSettingDataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationDataMapper],
    useFactory: (db: DbInstanceParams, organizationDataMapper: IOrganizationDataMapper) =>
      new OrganizationRepository(db, organizationDataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentDataMapper: IEnrollmentDataMapper) =>
      new EnrollmentRepository(db, enrollmentDataMapper),
  },
  {
    provide: ReportDIToken.ReportHistoryRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ReportDIToken.ReportHistoryDataMapper],
    useFactory: (db: DbInstanceParams, reportHistoryDataMapper: IReportHistoryDataMapper) =>
      new ReportHistoryRepository(db, reportHistoryDataMapper),
  },
  {
    provide: PreEnrollmentDIToken.PreEnrollmentTransactionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PreEnrollmentDIToken.PreEnrollmentTransactionDataMapper],
    useFactory: (db: DbInstanceParams, preEnrollmentTransactionDataMapper: IPreEnrollmentTransactionDataMapper) =>
      new PreEnrollmentTransactionRepository(db, preEnrollmentTransactionDataMapper),
  },
  {
    provide: OrganizationDIToken.ColumnSettingRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.ColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, columnSettingDataMapper: IColumnSettingDataMapper) =>
      new ColumnSettingRepository(db, columnSettingDataMapper),
  },
  {
    provide: OrganizationDIToken.TemplateColumnSettingRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.TemplateColumnSettingDataMapper],
    useFactory: (db: DbInstanceParams, templateColumnSettingDataMapper: ITemplateColumnSettingDataMapper) =>
      new TemplateColumnSettingRepository(db, templateColumnSettingDataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationCertificateRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationCertificateDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationCertificateDataMapper) =>
      new OrganizationCertificateRepository(db, dataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKURepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUDataMapper],
    useFactory: (db: DbInstanceParams, productSKUDataMapper: IProductSKUDataMapper) =>
      new ProductSKURepository(db, productSKUDataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKUBundleRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUBundleDataMapper],
    useFactory: (db: DbInstanceParams, productSKUBundleDataMapper: IProductSKUBundleDataMapper) =>
      new ProductSKUBundleRepository(db, productSKUBundleDataMapper),
  },
  {
    provide: ProductSKUDIToken.ProductSKUCourseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.ProductSKUCourseDataMapper],
    useFactory: (db: DbInstanceParams, productSKUCourseDataMapper: IProductSKUCourseDataMapper) =>
      new ProductSKUCourseRepository(db, productSKUCourseDataMapper),
  },
  {
    provide: RoundDIToken.RoundRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, RoundDIToken.RoundDataMapper],
    useFactory: (db: DbInstanceParams, roundDataMapper: IRoundDataMapper) => new RoundRepository(db, roundDataMapper),
  },
  {
    provide: UserDIToken.UserRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserDIToken.UserDataMapper],
    useFactory: (db: DbInstanceParams, userDataMapper: IUserDataMapper) => new UserRepository(db, userDataMapper),
  },
  {
    provide: UserDIToken.UserDirectReportRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserDIToken.UserDirectReportDataMapper],
    useFactory: (db: DbInstanceParams, userDirectReportDataMapper: IUserDirectReportDataMapper) =>
      new UserDirectReportRepository(db, userDirectReportDataMapper),
  },
  {
    provide: UserGroupDIToken.UserGroupRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserGroupDIToken.UserGroupDataMapper],
    useFactory: (db: DbInstanceParams, userGroupDataMapper: IUserGroupDataMapper) =>
      new UserGroupRepository(db, userGroupDataMapper),
  },
  {
    provide: CreditDIToken.CustomerPartnerRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CreditDIToken.CustomerPartnerDataMapper],
    useFactory: (db: DbInstanceParams, customerPartnerDataMapper: ICustomerPartnerDataMapper) =>
      new CustomerPartnerRepository(db, customerPartnerDataMapper),
  },
  {
    provide: CreditDIToken.PointHistoryRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CreditDIToken.PointHistoryDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPointHistoryDataMapper) =>
      new PointHistoryRepository(db, dataMapper),
  },
  {
    provide: UserDIToken.UserRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserDIToken.UserDataMapper],
    useFactory: (db: DbInstanceParams, userDataMapper: IUserDataMapper) => new UserRepository(db, userDataMapper),
  },
  {
    provide: PreEnrollmentDIToken.PreEnrollmentReservationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PreEnrollmentDIToken.PreEnrollmentReservationDataMapper],
    useFactory: (db: DbInstanceParams, preEnrollmentReservationDataMapper: IPreEnrollmentReservationDataMapper) =>
      new PreEnrollmentReservationRepository(db, preEnrollmentReservationDataMapper),
  },
  {
    provide: PreEnrollmentDIToken.EnrollmentRegulatorReportRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PreEnrollmentDIToken.EnrollmentRegulatorReportDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentRegulatorReportDataMapper: IEnrollmentRegulatorReportDataMapper) =>
      new EnrollmentRegulatorReportRepository(db, enrollmentRegulatorReportDataMapper),
  },
  {
    provide: CourseDIToken.CourseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.CourseDataMapper],
    useFactory: (db: DbInstanceParams, courseDataMapper: ICourseDataMapper) =>
      new CourseRepository(db, courseDataMapper),
  },
  {
    provide: CourseDIToken.CourseVersionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.CourseVersionDataMapper],
    useFactory: (db: DbInstanceParams, courseVersionDataMapper: ICourseVersionDataMapper) =>
      new CourseVersionRepository(db, courseVersionDataMapper),
  },
  {
    provide: JobDIToken.JobRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, JobDIToken.JobDataMapper],
    useFactory: (db: DbInstanceParams, jobDataMapper: IJobDataMapper) => new JobRepository(db, jobDataMapper),
  },
  {
    provide: JobDIToken.JobTransactionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, JobDIToken.JobTransactionDataMapper],
    useFactory: (db: DbInstanceParams, jobTransactionDataMapper: IJobTransactionDataMapper) =>
      new JobTransactionRepository(db, jobTransactionDataMapper),
  },
  {
    provide: UserDIToken.UserRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserDIToken.UserDataMapper],
    useFactory: (db: DbInstanceParams, userDataMapper: IUserDataMapper) => new UserRepository(db, userDataMapper),
  },
  {
    provide: MaterialMediaDIToken.ClassroomRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.ClassroomDataMapper],
    useFactory: (db: DbInstanceParams, classroomDataMapper: IClassroomDataMapper) =>
      new ClassroomRepository(db, classroomDataMapper),
  },
  {
    provide: MaterialMediaDIToken.ClassroomLocationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.ClassroomLocationDataMapper],
    useFactory: (db: DbInstanceParams, classroomLocationDataMapper: IClassroomLocationDataMapper) =>
      new ClassroomLocationRepository(db, classroomLocationDataMapper),
  },
  {
    provide: MaterialMediaDIToken.ClassroomLocationEnrollmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.ClassroomLocationEnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, classroomLocationEnrollmentDataMapper: IClassroomLocationEnrollmentDataMapper) =>
      new ClassroomLocationEnrollmentRepository(db, classroomLocationEnrollmentDataMapper),
  },
  {
    provide: MaterialMediaDIToken.ClassroomRoundRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.ClassroomRoundDataMapper],
    useFactory: (db: DbInstanceParams, classroomRoundDataMapper: IClassroomRoundDataMapper) =>
      new ClassroomRoundRepository(db, classroomRoundDataMapper),
  },
  {
    provide: MaterialMediaDIToken.QuizRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.QuizDataMapper],
    useFactory: (db: DbInstanceParams, quizDataMapper: IQuizDataMapper) => new QuizRepository(db, quizDataMapper),
  },
  {
    provide: CourseDIToken.PartRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.PartDataMapper],
    useFactory: (db: DbInstanceParams, partDataMapper: IPartDataMapper) => new PartRepository(db, partDataMapper),
  },
  {
    provide: CourseDIToken.CourseItemRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.CourseItemDataMapper],
    useFactory: (db: DbInstanceParams, courseItemDataMapper: ICourseItemDataMapper) =>
      new CourseItemRepository(db, courseItemDataMapper),
  },
  {
    provide: EnrollmentDIToken.QuizAnswerRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.QuizAnswerDataMapper],
    useFactory: (db: DbInstanceParams, quizAnswerDataMapper: IQuizAnswerMapper) =>
      new QuizAnswerRepository(db, quizAnswerDataMapper),
  },
  {
    provide: CourseDIToken.CourseItemCriteriaConfigRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseDIToken.CourseItemCriteriaConfigDataMapper],
    useFactory: (db: DbInstanceParams, courseItemCriteriaConfigDataMapper: ICourseItemCriteriaConfigDataMapper) =>
      new CourseItemCriteriaConfigRepository(db, courseItemCriteriaConfigDataMapper),
  },
  {
    provide: EnrollmentDIToken.VerifyEnrollmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.VerifyEnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, verifyEnrollmentDataMapper: IVerifyEnrollmentDataMapper) =>
      new VerifyEnrollmentRepository(db, verifyEnrollmentDataMapper),
  },
  {
    provide: MaterialMediaDIToken.SurveySubmissionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.SurveySubmissionDataMapper],
    useFactory: (db: DbInstanceParams, surveySubmissionDataMapper: ISurveySubmissionDataMapper) =>
      new SurveySubmissionRepository(db, surveySubmissionDataMapper),
  },
  {
    provide: MaterialMediaDIToken.SurveyRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.SurveyDataMapper],
    useFactory: (db: DbInstanceParams, surveyDataMapper: ISurveyDataMapper) =>
      new SurveyRepository(db, surveyDataMapper),
  },
  {
    provide: EnrollmentDIToken.IdentificationCardRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.IdentificationCardDataMapper],
    useFactory: (db: DbInstanceParams, identificationCardDataMapper: IIdentificationCardDataMapper) =>
      new IdentificationCardRepository(db, identificationCardDataMapper),
  },
  {
    provide: EnrollmentDIToken.LogActivityDetectRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.LogActivityDetectDataMapper],
    useFactory: (db: DbInstanceParams, logActivityDetectDataMapper: ILogActivityDetectDataMapper) =>
      new LogActivityDetectRepository(db, logActivityDetectDataMapper),
  },
  {
    provide: EnrollmentDIToken.LogFaceComparisonRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.LogFaceComparisonDataMapper],
    useFactory: (db: DbInstanceParams, logFaceComparisonDataMapper: ILogFaceComparisonDataMapper) =>
      new LogFaceComparisonRepository(db, logFaceComparisonDataMapper),
  },
  {
    provide: LearningPathDIToken.LearningPathRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LearningPathDIToken.LearningPathDataMapper],
    useFactory: (db: DbInstanceParams, learningPathDataMapper: ILearningPathDataMapper) =>
      new LearningPathRepository(db, learningPathDataMapper),
  },
  {
    provide: LearningPathDIToken.LearningPathEnrollmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LearningPathDIToken.LearningPathEnrollmentDataMapper],
    useFactory: (db: DbInstanceParams, learningPathEnrollmentDataMapper: ILearningPathEnrollmentDataMapper) =>
      new LearningPathEnrollmentRepository(db, learningPathEnrollmentDataMapper),
  },
  {
    provide: LearningPathDIToken.LearningPathEnrollmentCertificateRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LearningPathDIToken.LearningPathEnrollmentCertificateDataMapper],
    useFactory: (
      db: DbInstanceParams,
      learningPathEnrollmentCertificateDataMapper: ILearningPathEnrollmentCertificateDataMapper,
    ) => new LearningPathEnrollmentCertificateRepository(db, learningPathEnrollmentCertificateDataMapper),
  },
  {
    provide: LearningPathDIToken.LearningPathVersionRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, LearningPathDIToken.LearningPathVersionDataMapper],
    useFactory: (db: DbInstanceParams, learningPathVersionDataMapper: ILearningPathVersionDataMapper) =>
      new LearningPathVersionRepository(db, learningPathVersionDataMapper),
  },
  {
    provide: PreAssignContentDIToken.PreAssignContentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PreAssignContentDIToken.PreAssignContentDataMapper],
    useFactory: (db: DbInstanceParams, preAssignContentDataMapper: IPreAssignContentDataMapper) =>
      new PreAssignContentRepository(db, preAssignContentDataMapper),
  },
  {
    provide: UserDIToken.UserNotificationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserDIToken.UserNotificationDataMapper],
    useFactory: (db: DbInstanceParams, userNotificationDataMapper: IUserNotificationDataMapper) =>
      new UserNotificationRepository(db, userNotificationDataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentAttachmentRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentAttachmentDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentAttachmentDataMapper: IEnrollmentAttachmentDataMapper) =>
      new EnrollmentAttachmentRepository(db, enrollmentAttachmentDataMapper),
  },
  {
    provide: EnrollmentDIToken.IdentificationCardRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.IdentificationCardDataMapper],
    useFactory: (db: DbInstanceParams, identificationCardDataMapper: IIdentificationCardDataMapper) =>
      new IdentificationCardRepository(db, identificationCardDataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentCertificateRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentCertificateDataMapper],
    useFactory: (db: DbInstanceParams, enrollmentCertificateDataMapper: IEnrollmentCertificateDataMapper) =>
      new EnrollmentCertificateRepository(db, enrollmentCertificateDataMapper),
  },
  {
    provide: CreditDIToken.PurchaseOrderRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CreditDIToken.PurchaseOrderDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPurchaseOrderDataMapper) =>
      new PurchaseOrderRepository(db, dataMapper),
  },
  {
    provide: NotificationDIToken.PromoteNotificationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, NotificationDIToken.PromoteNotificationDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPromoteNotificationDataMapper) =>
      new PromoteNotificationRepository(db, dataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationStorageRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationStorageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationStorageDataMapper) =>
      new OrganizationStorageRepository(db, dataMapper),
  },
  {
    provide: KnowledgeContentDIToken.KnowledgeContentItemRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, KnowledgeContentDIToken.KnowledgeContentItemDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IKnowledgeContentItemDataMapper) =>
      new KnowledgeContentItemRepository(db, dataMapper),
  },
  {
    provide: AnnouncementDIToken.AnnouncementRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, AnnouncementDIToken.AnnouncementDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IAnnouncementDataMapper) =>
      new AnnouncementRepository(db, dataMapper),
  },
  {
    provide: InstructorDIToken.InstructorRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, InstructorDIToken.InstructorDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IInstructorDataMapper) => new InstructorRepository(db, dataMapper),
  },
  {
    provide: MaterialMediaDIToken.MediaRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, MaterialMediaDIToken.MediaDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IMediaDataMapper) => new MediaRepository(db, dataMapper),
  },
  {
    provide: EnrollmentDIToken.SummaryTSIQuizScoreRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.SummaryTSIQuizScoreDataMapper],
    useFactory: (db: DbInstanceParams, summaryTSIQuizScoreDataMapper: ISummaryTSIQuizScoreDataMapper) =>
      new SummaryTSIQuizScoreRepository(db, summaryTSIQuizScoreDataMapper),
  },
  {
    provide: PlanDIToken.PackageRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PackageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPackageDataMapper) => new PackageRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageLicenseHistoryRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanPackageLicenseHistoryDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageLicenseHistoryDataMapper) =>
      new PlanPackageLicenseHistoryRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageLicenseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanPackageLicenseDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageLicenseDataMapper) =>
      new PlanPackageLicenseRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanPackageRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanPackageDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanPackageDataMapper) => new PlanPackageRepository(db, dataMapper),
  },
  {
    provide: PlanDIToken.PlanRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, PlanDIToken.PlanDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IPlanDataMapper) => new PlanRepository(db, dataMapper),
  },
  {
    provide: ProductSKUDIToken.CourseMarketplaceRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, ProductSKUDIToken.CourseMarketplaceDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ICourseMarketplaceDataMapper) =>
      new CourseMarketplaceRepository(db, dataMapper),
  },
  {
    provide: OrganizationDIToken.LoginProviderRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.LoginProviderDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ILoginProviderDataMapper) =>
      new LoginProviderRepository(db, dataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationLoginProviderRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationLoginProviderDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationLoginProviderDataMapper) =>
      new OrganizationLoginProviderRepository(db, dataMapper),
  },
  {
    provide: OrganizationDIToken.OrganizationSchedulerRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, OrganizationDIToken.OrganizationSchedulerDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IOrganizationSchedulerDataMapper) =>
      new OrganizationSchedulerRepository(db, dataMapper),
  },
  {
    provide: UserDIToken.UserLoginRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, UserDIToken.UserLoginDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: IUserLoginDataMapper) => new UserLoginRepository(db, dataMapper),
  },
  {
    provide: CourseCategoryDIToken.CourseCategoryRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, CourseCategoryDIToken.CourseCategoryDataMapper],
    useFactory: (db: DbInstanceParams, dataMapper: ICourseCategoryDataMapper) =>
      new CourseCategoryRepository(db, dataMapper),
  },
  {
    provide: EnrollmentDIToken.EnrollmentPlanPackageLicenseRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, EnrollmentDIToken.EnrollmentPlanPackageLicenseDataMapper],
    useFactory: (
      db: DbInstanceParams,
      enrollmentPlanPackageLicenseDataMapper: IEnrollmentPlanPackageLicenseDataMapper,
    ) => new EnrollmentPlanPackageLicenseRepository(db, enrollmentPlanPackageLicenseDataMapper),
  },
];

@Module({
  imports: [DbModule, DataMapperModule],
  providers: databaseRepositoriesProvider,
  exports: [
    AchievementDIToken.AchievementRepository,
    AchievementDIToken.EnrollmentAchievementRepository,
    AnnouncementDIToken.AnnouncementRepository,
    BadgeDIToken.BadgeRepository,
    BadgeDIToken.EnrollmentBadgeRepository,
    CertificateDIToken.CertificateRepository,
    CourseDIToken.CourseItemCriteriaConfigRepository,
    CourseDIToken.CourseItemRepository,
    CourseDIToken.CourseRepository,
    CourseDIToken.CourseVersionCertificateRepository,
    CourseDIToken.CourseVersionRepository,
    CourseDIToken.PartRepository,
    CreditDIToken.CustomerPartnerRepository,
    CreditDIToken.CustomerRepository,
    CreditDIToken.PointHistoryRepository,
    CreditDIToken.PurchaseOrderRepository,
    EnrollmentDIToken.EnrollmentAttachmentRepository,
    EnrollmentDIToken.EnrollmentCertificateRepository,
    EnrollmentDIToken.EnrollmentRepository,
    EnrollmentDIToken.IdentificationCardRepository,
    EnrollmentDIToken.LogActivityDetectRepository,
    EnrollmentDIToken.LogFaceComparisonRepository,
    EnrollmentDIToken.QuizAnswerRepository,
    EnrollmentDIToken.SummaryTSIQuizScoreRepository,
    EnrollmentDIToken.VerifyEnrollmentRepository,
    EnrollmentDIToken.EnrollmentPlanPackageLicenseRepository,
    InstructorDIToken.InstructorRepository,
    JobDIToken.JobRepository,
    JobDIToken.JobTransactionRepository,
    KnowledgeContentDIToken.KnowledgeContentItemRepository,
    LearningPathDIToken.LearningPathEnrollmentCertificateRepository,
    LearningPathDIToken.LearningPathEnrollmentRepository,
    LearningPathDIToken.LearningPathRepository,
    LearningPathDIToken.LearningPathVersionRepository,
    LicenseDIToken.LicenseRepository,
    MaterialMediaDIToken.ClassroomLocationEnrollmentRepository,
    MaterialMediaDIToken.ClassroomLocationRepository,
    MaterialMediaDIToken.ClassroomRepository,
    MaterialMediaDIToken.ClassroomRoundRepository,
    MaterialMediaDIToken.MediaRepository,
    MaterialMediaDIToken.QuizRepository,
    MaterialMediaDIToken.SurveyRepository,
    MaterialMediaDIToken.SurveySubmissionRepository,
    NotificationDIToken.PromoteNotificationRepository,
    OrganizationDIToken.ColumnSettingRepository,
    OrganizationDIToken.DepartmentRepository,
    OrganizationDIToken.LoginProviderRepository,
    OrganizationDIToken.OrganizationCertificateRepository,
    OrganizationDIToken.OrganizationColumnSettingRepository,
    OrganizationDIToken.OrganizationLoginProviderRepository,
    OrganizationDIToken.OrganizationRepository,
    OrganizationDIToken.OrganizationSchedulerRepository,
    OrganizationDIToken.OrganizationStorageRepository,
    OrganizationDIToken.TemplateColumnSettingRepository,
    PlanDIToken.PackageRepository,
    PlanDIToken.PlanPackageLicenseHistoryRepository,
    PlanDIToken.PlanPackageLicenseRepository,
    PlanDIToken.PlanPackageRepository,
    PlanDIToken.PlanRepository,
    PreAssignContentDIToken.PreAssignContentRepository,
    PreEnrollmentDIToken.EnrollmentRegulatorReportRepository,
    PreEnrollmentDIToken.PreEnrollmentReservationRepository,
    PreEnrollmentDIToken.PreEnrollmentTransactionRepository,
    ProductSKUDIToken.CourseMarketplaceRepository,
    ProductSKUDIToken.ProductSKUBundleRepository,
    ProductSKUDIToken.ProductSKUCourseRepository,
    ProductSKUDIToken.ProductSKURepository,
    ReportDIToken.ReportHistoryRepository,
    RoundDIToken.RoundRepository,
    UserDIToken.UserDirectReportRepository,
    UserDIToken.UserLoginRepository,
    UserDIToken.UserNotificationRepository,
    UserDIToken.UserRepository,
    UserGroupDIToken.UserGroupRepository,
    CourseCategoryDIToken.CourseCategoryRepository,
  ],
})
export class DatabaseRepositoryModule {}
