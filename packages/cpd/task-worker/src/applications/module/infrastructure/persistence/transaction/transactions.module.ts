import { DbModule } from '@modules/infrastructure/configs/db.module';
import { EnvironmentsModule } from '@modules/infrastructure/configs/environments.module';
import { LoggerServiceModule } from '@modules/infrastructure/services/logger.service.module';
import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';

import { DatabaseTransaction } from '@infrastructures/persistence/transactions/database.transaction';
import { StorageTransaction } from '@infrastructures/persistence/transactions/storage.transaction';

import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';

import { DbClientParams } from '@constants/types/infrastructures/database.type';
@Module({
  imports: [DbModule, EnvironmentsModule, CacheModule.register(), LoggerServiceModule],
  providers: [
    {
      provide: InfrastructuresPersistenceDIToken.DatabaseTransaction,
      inject: [InfrastructuresConfigDIToken.DbClient],
      useFactory: (dbClient: DbClientParams) => new DatabaseTransaction(dbClient),
    },
    {
      provide: InfrastructuresPersistenceDIToken.StorageTransaction,
      useClass: StorageTransaction,
    },
  ],
  exports: [
    InfrastructuresPersistenceDIToken.DatabaseTransaction,
    InfrastructuresPersistenceDIToken.StorageTransaction,
  ],
})
export class PersistenceTransactionModule {}
