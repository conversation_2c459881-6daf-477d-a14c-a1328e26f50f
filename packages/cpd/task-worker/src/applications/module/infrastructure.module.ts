import { ConfigsModule } from '@modules/infrastructure/configs.module';
import { DataMapperModule } from '@modules/infrastructure/dataMapper.module';
import { PersistenceModule } from '@modules/infrastructure/persistence.module';
import { InfrastructureServiceModule } from '@modules/infrastructure/services.module';
import { Module } from '@nestjs/common';

@Module({
  imports: [ConfigsModule, PersistenceModule, DataMapperModule, InfrastructureServiceModule],
  exports: [ConfigsModule, PersistenceModule, DataMapperModule, InfrastructureServiceModule],
})
export class InfrastructureModule {}
