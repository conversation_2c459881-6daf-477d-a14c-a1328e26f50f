import { CourseCategoryModule } from '@modules/domains/courseCategory.module';
import { EnrollmentModule } from '@modules/domains/enrollment.module';
import { OrganizationModule } from '@modules/domains/organization.module';
import { PlanModule } from '@modules/domains/plan.module';
import { LoggerServiceModule } from '@modules/infrastructure/services/logger.service.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { EnrollmentDIToken, OrganizationDIToken, PlanDIToken } from '@applications/di/domain';

import { InfrastructureModule } from '@modules/infrastructure.module';

import { EnrollmentConsumer } from '@presenters/consumers/listeners/enrollment.consumer';
import { OrganizationConsumer } from '@presenters/consumers/listeners/organization.consumer';
import { PlanConsumer } from '@presenters/consumers/listeners/plan.consumer';

const presenterProviders: Provider[] = [
  {
    provide: EnrollmentDIToken.EnrollmentConsumer,
    useClass: EnrollmentConsumer,
  },
  {
    provide: OrganizationDIToken.OrganizationConsumer,
    useClass: OrganizationConsumer,
  },
  {
    provide: PlanDIToken.PlanConsumer,
    useClass: PlanConsumer,
  },
];

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    EnrollmentModule,
    OrganizationModule,
    PlanModule,
    LoggerServiceModule,
    CourseCategoryModule,
    InfrastructureModule,
  ],
  providers: [...presenterProviders],
})
export class PresenterModule {}
