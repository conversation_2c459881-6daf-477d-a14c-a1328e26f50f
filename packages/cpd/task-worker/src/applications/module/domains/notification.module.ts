import { EnvironmentsModule } from '@modules/infrastructure/configs/environments.module';
import { MessageBrokerServiceModule } from '@modules/infrastructure/services/messageBroker.service.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PublishPromoteNotificationUseCase } from '@usecases/notification/publishPromoteNotification.usecase';

import { MaterialMediaDIToken } from '@applications/di/domain/materialMedia.di';
import { NotificationDIToken } from '@applications/di/domain/notification.di';

import { InfrastructureModule } from '@modules/infrastructure.module';

import { MaterialMediaService } from '@domains/services/materialMedia.service';

import { NotificationConsumer } from '@presenters/consumers/listeners/notification.consumer';

const presenterProviders: Provider[] = [
  {
    provide: NotificationDIToken.NotificationConsumer,
    useClass: NotificationConsumer,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: NotificationDIToken.PublishPromoteNotificationUseCase,
    useClass: PublishPromoteNotificationUseCase,
  },
];

const serviceProviders: Provider[] = [
  {
    provide: MaterialMediaDIToken.MaterialMediaService,
    useClass: MaterialMediaService,
  },
];

@Module({
  imports: [EventEmitterModule.forRoot(), MessageBrokerServiceModule, EnvironmentsModule, InfrastructureModule],
  providers: [...usecaseProviders, ...presenterProviders, ...serviceProviders],
  exports: [],
})
export class NotificationModule {}
