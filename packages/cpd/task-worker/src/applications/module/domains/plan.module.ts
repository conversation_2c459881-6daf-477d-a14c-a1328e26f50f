import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PlanPackageLicenseExpiredUseCase } from '@usecases/plan/planPackageLicenseExpired.usecase';
import { BulkAssignPlanPackageLicenseUseCase } from '@usecases/planPackage/bulkAssignPlanPackageLicense.usecase';
import { BulkTransferPlanPackageLicenseUseCase } from '@usecases/planPackage/bulkTransferPlanPackageLicense.usecase';

import { PlanDIToken } from '@applications/di/domain';

import { InfrastructureModule } from '@modules/infrastructure.module';

const usecaseProviders: Provider[] = [
  {
    provide: PlanDIToken.BulkAssignPlanPackageLicenseUseCase,
    useClass: BulkAssignPlanPackageLicenseUseCase,
  },
  {
    provide: PlanDIToken.BulkTransferPlanPackageLicenseUseCase,
    useClass: BulkTransferPlanPackageLicenseUseCase,
  },
  {
    provide: PlanDIToken.PlanPackageLicenseExpiredUseCase,
    useClass: PlanPackageLicenseExpiredUseCase,
  },
];

const serviceProviders: Provider[] = [];

@Module({
  imports: [EventEmitterModule.forRoot(), InfrastructureModule],
  providers: [...usecaseProviders, ...serviceProviders],
  exports: [
    PlanDIToken.BulkAssignPlanPackageLicenseUseCase,
    PlanDIToken.BulkTransferPlanPackageLicenseUseCase,
    PlanDIToken.PlanPackageLicenseExpiredUseCase,
  ],
})
export class PlanModule {}
