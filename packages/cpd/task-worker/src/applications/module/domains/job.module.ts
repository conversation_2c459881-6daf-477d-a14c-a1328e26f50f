import { OrganizationModule } from '@modules/domains/organization.module';
import { EnvironmentsModule } from '@modules/infrastructure/configs/environments.module';
import { MessageBrokerServiceModule } from '@modules/infrastructure/services/messageBroker.service.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UploadCancelPreAssignLearningPathEnrollmentUseCase } from '@usecases/job/uploadCancelPreAssignLearningPathEnrollment.usecase';
import { UploadCancelPreEnrollmentUseCase } from '@usecases/job/uploadCancelPreEnrollment.usecase';
import { UploadPreAssignLearningPathEnrollmentUseCase } from '@usecases/job/uploadPreAssignLearningPathEnrollment.usecase';
import { UploadUserValidateUserCase } from '@usecases/job/uploadUserValidate.usecase';

import { JobDIToken } from '@applications/di/domain/job.di';

import { InfrastructureModule } from '@modules/infrastructure.module';

import { JobService } from '@domains/services/job.service';

import { JobConsumer } from '@presenters/consumers/listeners/job.consumer';

const presenterProviders: Provider[] = [
  {
    provide: JobDIToken.JobConsumer,
    useClass: JobConsumer,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: JobDIToken.UploadUserValidateUserCase,
    useClass: UploadUserValidateUserCase,
  },
  {
    provide: JobDIToken.UploadPreAssignLearningPathEnrollmentUseCase,
    useClass: UploadPreAssignLearningPathEnrollmentUseCase,
  },
  {
    provide: JobDIToken.UploadCancelPreAssignLearningPathEnrollmentUseCase,
    useClass: UploadCancelPreAssignLearningPathEnrollmentUseCase,
  },
  {
    provide: JobDIToken.UploadCancelPreEnrollmentUseCase,
    useClass: UploadCancelPreEnrollmentUseCase,
  },
];
const serviceProviders: Provider[] = [
  {
    provide: JobDIToken.JobService,
    useClass: JobService,
  },
];

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    MessageBrokerServiceModule,
    EnvironmentsModule,
    OrganizationModule,
    JobModule,
    InfrastructureModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders, ...serviceProviders],
  exports: [JobDIToken.JobService],
})
export class JobModule {}
