import { JobModule } from '@modules/domains/job.module';
import { MessageBrokerServiceModule } from '@modules/infrastructure/services/messageBroker.service.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CreateNewCustomerChannelUseCase } from '@usecases/customer/createNewCustomerChannel.usecase';
import { GetCustomerCodesUseCase } from '@usecases/customer/getCustomerCodes.usecase';
import { UploadCustomerUserValidationUseCase } from '@usecases/customer/uploadCustomerUserValidation.usecase';

import { CreditDIToken } from '@applications/di/domain/credit.di';

import { InfrastructureModule } from '@modules/infrastructure.module';

import { CustomerConsumer } from '@presenters/consumers/listeners/customer.consumer';

const usecaseProviders: Provider[] = [
  {
    provide: CreditDIToken.GetCustomerCodesUseCase,
    useClass: GetCustomerCodesUseCase,
  },
  {
    provide: CreditDIToken.UploadCustomerUserValidationUseCase,
    useClass: UploadCustomerUserValidationUseCase,
  },
  {
    provide: CreditDIToken.CreateNewCustomerChannelUseCase,
    useClass: CreateNewCustomerChannelUseCase,
  },
];

const presenterProviders: Provider[] = [
  {
    provide: CreditDIToken.CustomerConsumer,
    useClass: CustomerConsumer,
  },
];

@Module({
  imports: [EventEmitterModule.forRoot(), MessageBrokerServiceModule, JobModule, InfrastructureModule],
  providers: [...usecaseProviders, ...presenterProviders],
  exports: [],
})
export class CreditModule {}
