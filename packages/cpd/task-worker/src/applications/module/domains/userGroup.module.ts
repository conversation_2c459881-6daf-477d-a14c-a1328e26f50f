import { DatabaseRepositoryModule } from '@modules/infrastructure/persistence/repositories/database.repository.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UpdateUsersInUserGroupUseCase } from '@usecases/userGroup/updateUsersInUserGroupCondition.usecase';

import { UserGroupDbAdaptorService } from '@infrastructures/services/adaptors/userGroupDBAdaptors.service';

import { UserGroupDIToken } from '@applications/di/domain/userGroup.di';

import { InfrastructureModule } from '@modules/infrastructure.module';

import { UserGroupConsumer } from '@presenters/consumers/listeners/userGroup.consumer';

const persistanceProviders: Provider[] = [];

const adaptersProviders: Provider[] = [];

const presenterProviders: Provider[] = [
  {
    provide: UserGroupDIToken.UserGroupConsumer,
    useClass: UserGroupConsumer,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: UserGroupDIToken.UpdateUsersInUserGroupByConditionUseCase,
    useClass: UpdateUsersInUserGroupUseCase,
  },
];

const serviceProviders: Provider[] = [
  {
    provide: UserGroupDIToken.UserGroupDbAdaptorService,
    useClass: UserGroupDbAdaptorService,
  },
];

@Module({
  imports: [DatabaseRepositoryModule, EventEmitterModule.forRoot(), InfrastructureModule],
  providers: [
    ...adaptersProviders,
    ...persistanceProviders,
    ...serviceProviders,
    ...usecaseProviders,
    ...presenterProviders,
  ],
})
export class UserGroupModule {}
