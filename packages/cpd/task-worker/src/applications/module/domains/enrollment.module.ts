import { CertificateModule } from '@modules/domains/certificate.module';
import { JobModule } from '@modules/domains/job.module';
import { LearningPathModule } from '@modules/domains/learningPath.module';
import { MaterialMediaModule } from '@modules/domains/materialMedia.module';
import { OrganizationModule } from '@modules/domains/organization.module';
import { EnvironmentsModule } from '@modules/infrastructure/configs/environments.module';
import { StorageModule } from '@modules/infrastructure/configs/storage.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AutoApproveEnrollmentUseCase } from '@usecases/enrollment/autoApproveEnrollment.usecase';
import { BulkEnrollmentHistoryUseCase } from '@usecases/enrollment/bulkEnrollmentHistory.usecase';
import { CancelEnrollmentHistoryUseCase } from '@usecases/enrollment/cancelEnrollmentHistory.usecase';
import { CalculateCumulativeTSIQuizScoreUseCase } from '@usecases/summaryTSIQuizScore/calculateSummaryTSIQuizScore.usecase';

import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';

import { InfrastructureModule } from '@modules/infrastructure.module';

import { EnrollmentService } from '@domains/services/enrollment.service';
import { SummaryTSIQuizScoreService } from '@domains/services/summaryTSIQuizScore.service';

const persistanceProviders: Provider[] = [];

const adaptersProviders: Provider[] = [];

const usecaseProviders: Provider[] = [
  {
    provide: EnrollmentDIToken.BulkEnrollmentHistoryUseCase,
    useClass: BulkEnrollmentHistoryUseCase,
  },
  {
    provide: EnrollmentDIToken.CancelEnrollmentHistoryUseCase,
    useClass: CancelEnrollmentHistoryUseCase,
  },
  {
    provide: EnrollmentDIToken.AutoApproveEnrollmentUseCase,
    useClass: AutoApproveEnrollmentUseCase,
  },
  {
    provide: EnrollmentDIToken.CalculateCumulativeTSIQuizScoreUseCase,
    useClass: CalculateCumulativeTSIQuizScoreUseCase,
  },
];

const serviceProviders: Provider[] = [
  {
    provide: EnrollmentDIToken.EnrollmentService,
    useClass: EnrollmentService,
  },
  {
    provide: EnrollmentDIToken.SummaryTSIQuizScoreService,
    useClass: SummaryTSIQuizScoreService,
  },
];

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    EnvironmentsModule,
    StorageModule,
    OrganizationModule,
    JobModule,
    LearningPathModule,
    CertificateModule,
    MaterialMediaModule,
    InfrastructureModule,
  ],
  providers: [...adaptersProviders, ...persistanceProviders, ...usecaseProviders, ...serviceProviders],
  exports: [
    EnrollmentDIToken.AutoApproveEnrollmentUseCase,
    EnrollmentDIToken.BulkEnrollmentHistoryUseCase,
    EnrollmentDIToken.CalculateCumulativeTSIQuizScoreUseCase,
    EnrollmentDIToken.CancelEnrollmentHistoryUseCase,
    EnrollmentDIToken.EnrollmentService,
    EnrollmentDIToken.SummaryTSIQuizScoreService,
  ],
})
export class EnrollmentModule {}
