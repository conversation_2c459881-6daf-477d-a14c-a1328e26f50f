import { JobModule } from '@modules/domains/job.module';
import { OrganizationModule } from '@modules/domains/organization.module';
import { MessageBrokerServiceModule } from '@modules/infrastructure/services/messageBroker.service.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { InfrastructureModule } from '@modules/infrastructure.module';

const presenterProviders: Provider[] = [];

const usecaseProviders: Provider[] = [];

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    MessageBrokerServiceModule,
    JobModule,
    OrganizationModule,
    InfrastructureModule,
  ],
  providers: [...usecaseProviders, ...presenterProviders],
})
export class PreAssignContentModule {}
