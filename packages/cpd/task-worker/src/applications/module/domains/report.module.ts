import { ColumnSettingModule } from '@modules/domains/columnSetting.module';
import { EnrollmentModule } from '@modules/domains/enrollment.module';
import { MaterialMediaModule } from '@modules/domains/materialMedia.module';
import { OrganizationModule } from '@modules/domains/organization.module';
import { DatabaseRepositoryModule } from '@modules/infrastructure/persistence/repositories/database.repository.module';
import { StorageRepositoryModule } from '@modules/infrastructure/persistence/repositories/storage.repository.module';
import { MessageBrokerServiceModule } from '@modules/infrastructure/services/messageBroker.service.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ExportApprovalTSIReportUseCase } from '@usecases/report/exportApprovalTSIReport.usecase';
import { ExportCustomerCreditBalanceReportUseCase } from '@usecases/report/exportCustomerCreditBalanceReport.usecase';
import { ExportLearnerReportUseCase } from '@usecases/report/exportLearnerReport.usecase';
import { ExportOICDeductReportUseCase } from '@usecases/report/exportOICDeductReport.usecase';
import { ExportOICPostReportUseCase } from '@usecases/report/exportOICPostReport.usecase';
import { ExportOICPreReportUseCase } from '@usecases/report/exportOICPreReport.usecase';
import { ExportOICRegulatorPostReportUseCase } from '@usecases/report/exportOICRegulatorPostReport.usecase';
import { ExportOICRegulatorPreReportUseCase } from '@usecases/report/exportOICRegulatorPreReport.usecase';
import { ExportPreEnrollmentReportUseCase } from '@usecases/report/exportPreEnrollmentReport.usecase';
import { ExportRegularPreEnrollmentReportUseCase } from '@usecases/report/exportRegularPreEnrollmentReport.usecase';
import { ExportSurveySubmissionReportUseCase } from '@usecases/report/exportSurveySubmissionReport.usecase';

import { CourseDIToken } from '@applications/di/domain/course.di';
import { EnrollmentDIToken } from '@applications/di/domain/enrollment.di';
import { ReportDIToken } from '@applications/di/domain/report.di';

import { InfrastructureModule } from '@modules/infrastructure.module';

import { CourseService } from '@domains/services/course.service';
import { EnrollmentService } from '@domains/services/enrollment.service';
import { LearnerReportService } from '@domains/services/learnerReport.service';
import { OICPostReportService } from '@domains/services/oicPostReport.service';
import { OICPreReportService } from '@domains/services/oicPreReport.service';
import { SurveySubmissionReportService } from '@domains/services/surveySubmissionReport.service';

import { ReportConsumer } from '@presenters/consumers/listeners/report.consumer';

const persistanceProviders: Provider[] = [];

const adaptersProviders: Provider[] = [];

const presenterProviders: Provider[] = [
  {
    provide: ReportDIToken.ReportConsumer,
    useClass: ReportConsumer,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: ReportDIToken.ExportApprovalTSIReportUseCase,
    useClass: ExportApprovalTSIReportUseCase,
  },
  {
    provide: ReportDIToken.ExportPreEnrollmentReportUseCase,
    useClass: ExportPreEnrollmentReportUseCase,
  },
  {
    provide: ReportDIToken.ExportRegularPreEnrollmentReportUseCase,
    useClass: ExportRegularPreEnrollmentReportUseCase,
  },
  {
    provide: ReportDIToken.ExportOICDeductReportUseCase,
    useClass: ExportOICDeductReportUseCase,
  },
  {
    provide: ReportDIToken.ExportOICPreReportUseCase,
    useClass: ExportOICPreReportUseCase,
  },
  {
    provide: ReportDIToken.ExportOICRegulatorPreReportUseCase,
    useClass: ExportOICRegulatorPreReportUseCase,
  },
  {
    provide: ReportDIToken.ExportSurveySubmissionReportUseCase,
    useClass: ExportSurveySubmissionReportUseCase,
  },
  {
    provide: ReportDIToken.ExportOICPostReportUseCase,
    useClass: ExportOICPostReportUseCase,
  },
  {
    provide: ReportDIToken.ExportOICRegulatorPostReportUseCase,
    useClass: ExportOICRegulatorPostReportUseCase,
  },
  {
    provide: ReportDIToken.ExportCustomerCreditBalanceReportUseCase,
    useClass: ExportCustomerCreditBalanceReportUseCase,
  },
  {
    provide: ReportDIToken.ExportLearnerReportUseCase,
    useClass: ExportLearnerReportUseCase,
  },
];

const servicesProviders: Provider[] = [
  {
    provide: ReportDIToken.OICPreReportService,
    useClass: OICPreReportService,
  },
  {
    provide: ReportDIToken.OICPostReportService,
    useClass: OICPostReportService,
  },
  {
    provide: EnrollmentDIToken.EnrollmentService,
    useClass: EnrollmentService,
  },
  {
    provide: CourseDIToken.CourseService,
    useClass: CourseService,
  },
  {
    provide: ReportDIToken.SurveySubmissionReportService,
    useClass: SurveySubmissionReportService,
  },
  {
    provide: ReportDIToken.LearnerReportService,
    useClass: LearnerReportService,
  },
];

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    MessageBrokerServiceModule,
    DatabaseRepositoryModule,
    StorageRepositoryModule,
    OrganizationModule,
    ColumnSettingModule,
    MaterialMediaModule,
    EnrollmentModule,
    InfrastructureModule,
  ],
  providers: [
    ...adaptersProviders,
    ...persistanceProviders,
    ...servicesProviders,
    ...usecaseProviders,
    ...presenterProviders,
  ],
})
export class ReportModule {}
