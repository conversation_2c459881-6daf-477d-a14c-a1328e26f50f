import { AchievementModule } from '@modules/domains/achievement.module';
import { CertificateModule } from '@modules/domains/certificate.module';
import { ColumnSettingModule } from '@modules/domains/columnSetting.module';
import { CourseModule } from '@modules/domains/course.module';
import { CourseCategoryModule } from '@modules/domains/courseCategory.module';
import { CreditModule } from '@modules/domains/credit.module';
import { CustomerPartnerModule } from '@modules/domains/customerPartner.module';
import { EnrollmentModule } from '@modules/domains/enrollment.module';
import { HealthModule } from '@modules/domains/health.module';
import { JobModule } from '@modules/domains/job.module';
import { LearningPathModule } from '@modules/domains/learningPath.module';
import { LicenseModule } from '@modules/domains/license.module';
import { MaterialMediaModule } from '@modules/domains/materialMedia.module';
import { NotificationModule } from '@modules/domains/notification.module';
import { OrganizationModule } from '@modules/domains/organization.module';
import { PlanModule } from '@modules/domains/plan.module';
import { PreAssignContentModule } from '@modules/domains/preAssignContent.module';
import { PreEnrollmentModule } from '@modules/domains/preEnrollment.module';
import { PreEnrollmentTransactionModule } from '@modules/domains/preEnrollmentTransaction.module';
import { ReportModule } from '@modules/domains/report.module';
import { TaskOperationModule } from '@modules/domains/taskOperation.module';
import { TemplateColumnSettingModule } from '@modules/domains/templateColumnSetting.module';
import { UserModule } from '@modules/domains/user.module';
import { UserGroupModule } from '@modules/domains/userGroup.module';
import { Module } from '@nestjs/common';

@Module({
  imports: [
    CertificateModule,
    ColumnSettingModule,
    CourseModule,
    CreditModule,
    CustomerPartnerModule,
    EnrollmentModule,
    AchievementModule,
    HealthModule,
    JobModule,
    LearningPathModule,
    LicenseModule,
    MaterialMediaModule,
    NotificationModule,
    OrganizationModule,
    PlanModule,
    PreAssignContentModule,
    PreEnrollmentModule,
    PreEnrollmentTransactionModule,
    ReportModule,
    TemplateColumnSettingModule,
    UserGroupModule,
    UserModule,
    CourseCategoryModule,
    TaskOperationModule,
  ],
})
export class DomainModule {}
