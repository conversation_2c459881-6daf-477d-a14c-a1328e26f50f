import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import ms from 'ms';

import { IEnvironments } from '@interfaces/configs/environment.interface';

@Injectable()
export class Environments implements IEnvironments {
  constructor(private readonly configService: ConfigService) {}

  get appName(): string {
    return 'Worker-Scheduler-Service';
  }

  get port(): number {
    const _port = this.configService.get('PORT') || 5000;
    return Number(_port);
  }

  get dbURI(): string {
    return this.configService.getOrThrow('DB_URI');
  }

  get dbName(): string {
    return this.configService.getOrThrow('DB_NAME');
  }

  get amqpURI(): string {
    return this.configService.getOrThrow('AMQP_URI');
  }

  get awsAccessKeyId(): string {
    return this.configService.getOrThrow('AWS_ACCESS_KEY_ID');
  }

  get awsSecretAccessKey(): string {
    return this.configService.getOrThrow('AWS_SECRET_ACCESS_KEY');
  }

  get awsRegion(): string {
    return this.configService.getOrThrow('AWS_REGION');
  }

  get awsUploadBucketName(): string {
    return this.configService.getOrThrow('S3_UPLOAD_BUCKET');
  }

  get s3BucketUrl(): string {
    return this.configService.getOrThrow('S3_BUCKET_URL');
  }

  get clientProtocol(): string {
    return this.configService.getOrThrow('CLIENT_PROTOCOL');
  }

  get certificateEndpoint(): string {
    return this.configService.getOrThrow('CERTIFICATE_API_ENDPOINT');
  }

  get lmsApiEndpoint(): string {
    return this.configService.getOrThrow('LMS_API_ENDPOINT');
  }

  get redisURL(): string {
    return this.configService.getOrThrow('REDIS_URL');
  }

  get asURI(): string {
    return this.configService.getOrThrow('AS_URI');
  }

  get asClientId(): string {
    return this.configService.getOrThrow('AS_CLIENT_ID');
  }

  get asSecret(): string {
    return this.configService.getOrThrow('AS_SECRET');
  }

  get asScope(): string {
    return this.configService.getOrThrow('AS_SCOPE');
  }

  get advanceTokenRequestTime(): number {
    return +this.configService.getOrThrow<number>('ADVANCE_TOKEN_REQUEST_TIME');
  }

  get renewTokenSecondsBeforeExpires(): number {
    return +this.configService.getOrThrow<number>('RENEW_TOKEN_SECONDS_BEFORE_EXPIRES');
  }

  get firstPasswordTokenExpires(): string {
    return this.configService.getOrThrow('SET_FIRST_PASSWORD_TOKEN_EXPIRES');
  }

  get tokenAlgorithm(): string {
    return this.configService.getOrThrow('TOKEN_ALGORITHM');
  }

  get tokenAudience(): string {
    return this.configService.getOrThrow('TOKEN_AUDIENCE');
  }

  get tokenIssuer(): string {
    return this.configService.getOrThrow('TOKEN_ISSUER');
  }

  get managePasswordTokenPassphrase(): string {
    return this.configService.getOrThrow('MANAGE_PASSWORD_TOKEN_PASSPHRASE');
  }

  get managePasswordTokenPrivatekey(): string {
    return this.configService.getOrThrow('MANAGE_PASSWORD_TOKEN_PRIVATEKEY');
  }

  get managePasswordTokenPublickey(): string {
    return this.configService.getOrThrow('MANAGE_PASSWORD_TOKEN_PUBLICKEY');
  }

  get retryQueueMsDuration(): number {
    const duration = this.configService.get<string>('RETRY_QUEUE_MS_DURATION');
    return duration ? ms(duration) : 5000;
  }

  get encryptDataAlgorithm(): string {
    return this.configService.getOrThrow('ENCRYPT_DATA_ALGORITHM');
  }

  get encryptDataSecretKey(): string {
    return this.configService.getOrThrow('ENCRYPT_DATA_SECRET_KEY');
  }

  get encryptDataSecretIV(): string {
    return this.configService.getOrThrow('ENCRYPT_IV_SECRET_KEY');
  }

  get webhookBotNotificationUrl(): string {
    return this.configService.getOrThrow('SLACK_WEBHOOK_URL');
  }

  get webhookAutoNotificationUrl(): string {
    return this.configService.getOrThrow('SLACK_WEBHOOK_AUTO_URL');
  }
}
