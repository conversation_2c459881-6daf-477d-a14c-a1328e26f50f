import { GenericID } from '@iso/constants/commonTypes';
import { CourseObjectiveTypeEnum, LicenseTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { Course } from '@iso/lms/models/course.model';
import { CourseParams } from '@iso/lms/types/course.type';
import { CourseVersionParams } from '@iso/lms/types/courseVersion.type';
import { isEmpty } from 'lodash';

import { BaseRepository } from '@infrastructures/persistence/repositories/database/base.repository';

import {
  FilterSubjectRegulatorCourseParams,
  OutputCourseRegularAndNoneRegularIdsParams,
  OutputGroupSubjectRegulatorOICCourseByRegulatorCoursePropertyParams,
  OutputSubjectRegulatorCourseParams,
} from '@constants/types/course.type';
import { DbInstanceParams } from '@constants/types/infrastructures/database.type';

import { ICourseDataMapper } from '@interfaces/dataMapper/course.dataMapper.interface';
import { ICourseRepository } from '@interfaces/repositories/course.repository.interface';

const TABLE_NAME = DBCollectionEnum.COURSES;

export class CourseRepository extends BaseRepository<Course, CourseParams> implements ICourseRepository {
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: ICourseDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }

  async getCourseWithPublishedVersionByCode(courseCode: string, organizationId: GenericID): Promise<CourseParams> {
    const pipeline = [
      {
        $match: {
          code: courseCode,
          organizationId,
        },
      },
      {
        $lookup: {
          from: 'course-versions',
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$courseId', '$$id'] }, { $eq: ['$status', CourseVersionStatusEnum.PUBLISHED] }],
                },
              },
            },
          ],
          as: 'courseVersion',
        },
      },
      {
        $unwind: '$courseVersion',
      },
      {
        $limit: 1,
      },
    ];

    const [res] = await this.aggregate<CourseParams>(pipeline);
    return res;
  }
  async getCourseListWithVersionByCode(courseCode: string, organizationId: GenericID): Promise<CourseParams[]> {
    const aggregateQuery: Record<string, unknown>[] = [
      {
        $match: {
          code: courseCode,
          organizationId,
        },
      },
      {
        $lookup: {
          from: 'course-versions',
          let: {
            courseId: '$id',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$courseId', '$$courseId'] },
                    {
                      $ne: ['$status', CourseVersionStatusEnum.DRAFT],
                    },
                  ],
                },
              },
            },
          ],
          as: 'courseVersion',
        },
      },
      { $unwind: '$courseVersion' },
      {
        $sort: { 'courseVersion.publishedAt': -1 },
      },
    ];

    const result = await this.aggregate<CourseParams>(aggregateQuery);
    return result;
  }

  async findCourseRegularAndNoneRegularIdsByIds(
    ids: GenericID[],
    organizationId: GenericID,
  ): Promise<OutputCourseRegularAndNoneRegularIdsParams> {
    const pipeline = [
      {
        $match: {
          id: { $in: ids },
          organizationId,
        },
      },
      {
        $sort: { id: 1 },
      },
      {
        $facet: {
          regularCourses: [
            {
              $match: {
                objectiveType: CourseObjectiveTypeEnum.REGULAR,
              },
            },

            {
              $group: {
                _id: null,
                ids: { $push: '$id' },
              },
            },
          ],
          noneRegularCourses: [
            {
              $match: {
                objectiveType: CourseObjectiveTypeEnum.TRAINING,
              },
            },
            {
              $group: {
                _id: null,
                ids: { $push: '$id' },
              },
            },
          ],
        },
      },
      {
        $project: {
          regularCourse: {
            $arrayElemAt: ['$regularCourses', 0],
          },
          noneRegularCourse: {
            $arrayElemAt: ['$noneRegularCourses', 0],
          },
        },
      },
      {
        $project: {
          regularCourseIds: '$regularCourse.ids',
          noneRegularCourseIds: '$noneRegularCourse.ids',
        },
      },
    ];

    const [result] = await this.aggregate<OutputCourseRegularAndNoneRegularIdsParams>(pipeline);
    return {
      regularCourseIds: result?.regularCourseIds ?? [],
      noneRegularCourseIds: result?.noneRegularCourseIds ?? [],
    };
  }

  async findSubjectRegulatorOICCourseByRegulatorCourseProperty(
    filter: FilterSubjectRegulatorCourseParams,
    organizationId: GenericID,
  ): Promise<OutputSubjectRegulatorCourseParams[]> {
    const pipeline = [
      {
        $match: {
          $and: [
            { organizationId },
            { objectiveType: CourseObjectiveTypeEnum.TRAINING },
            { 'regulatorInfo.regulator': RegulatorEnum.OIC },
            filter?.trainingCenter && { 'regulatorInfo.trainingCenter': filter?.trainingCenter },
            filter?.licenseRenewal && { 'regulatorInfo.licenseRenewal': filter?.licenseRenewal },
            filter?.applicantType && { 'regulatorInfo.applicantType': filter?.applicantType },
            filter?.licenseType && { 'regulatorInfo.licenseType': { $all: filter?.licenseType } },
          ].filter(Boolean),
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          regulatorInfo: 1,
          subjects: 1,
          code: 1,
          contentProviderType: 1,
        },
      },
    ];
    const results = await this.aggregate<OutputSubjectRegulatorCourseParams>(pipeline);
    return results;
  }

  async findGroupSubjectRegulatorOICPreReportCourseByRegulatorCourseProperty(
    filter: FilterSubjectRegulatorCourseParams,
    organizationId: GenericID,
  ): Promise<OutputGroupSubjectRegulatorOICCourseByRegulatorCoursePropertyParams[]> {
    const pipeline = [
      {
        $match: {
          $and: [
            { organizationId },
            { objectiveType: CourseObjectiveTypeEnum.TRAINING },
            { 'regulatorInfo.regulator': RegulatorEnum.OIC },
            filter?.trainingCenter && { 'regulatorInfo.trainingCenter': filter?.trainingCenter },
            filter?.licenseRenewal && { 'regulatorInfo.licenseRenewal': filter?.licenseRenewal },
            filter?.applicantType && { 'regulatorInfo.applicantType': filter?.applicantType },
            filter?.licenseType && { 'regulatorInfo.licenseType': { $all: filter?.licenseType } },
          ].filter(Boolean),
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          regulatorInfo: 1,
          subjects: 1,
          code: 1,
          createdAt: 1,
          contentProviderType: 1,
        },
      },
      {
        $addFields: {
          adjustedLicenseType: {
            $cond: {
              if: {
                $and: [
                  {
                    $in: [LicenseTypeEnum.LIFE, '$regulatorInfo.licenseType'],
                  },
                  {
                    $in: [LicenseTypeEnum.NONLIFE, '$regulatorInfo.licenseType'],
                  },
                ],
              },
              then: { $concatArrays: [[LicenseTypeEnum.BOTH], '$regulatorInfo.licenseType'] },
              else: '$regulatorInfo.licenseType',
            },
          },
        },
      },
      {
        $unwind: {
          path: '$adjustedLicenseType',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: {
          createdAt: 1,
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          subjects: 1,
          code: 1,
          key: {
            trainingCenter: '$regulatorInfo.trainingCenter',
            licenseRenewal: '$regulatorInfo.licenseRenewal',
            applicantType: '$regulatorInfo.applicantType',
            licenseType: '$adjustedLicenseType',
          },
          regulatorInfo: 1,
          contentProviderType: 1,
        },
      },
      {
        $group: {
          _id: {
            trainingCenter: '$key.trainingCenter',
            licenseRenewal: '$key.licenseRenewal',
            applicantType: '$key.applicantType',
            licenseType: '$key.licenseType',
          },
          courses: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 0,
          regulatorInfo: '$_id',
          courses: 1,
        },
      },
    ];
    const results = await this.aggregate<OutputGroupSubjectRegulatorOICCourseByRegulatorCoursePropertyParams>(pipeline);
    return results;
  }

  async findGroupSubjectRegulatorOICPostReportCourseByRegulatorCourseProperty(
    filter: FilterSubjectRegulatorCourseParams,
    organizationId: GenericID,
  ): Promise<OutputGroupSubjectRegulatorOICCourseByRegulatorCoursePropertyParams[]> {
    const pipeline = [
      {
        $match: {
          $and: [
            { organizationId },
            { objectiveType: CourseObjectiveTypeEnum.TRAINING },
            { 'regulatorInfo.regulator': RegulatorEnum.OIC },
            filter?.trainingCenter && { 'regulatorInfo.trainingCenter': filter?.trainingCenter },
            filter?.licenseRenewal && { 'regulatorInfo.licenseRenewal': filter?.licenseRenewal },
            filter?.applicantType && { 'regulatorInfo.applicantType': filter?.applicantType },
            filter?.licenseType && { 'regulatorInfo.licenseType': { $all: filter?.licenseType } },
          ].filter(Boolean),
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          regulatorInfo: 1,
          subjects: 1,
          code: 1,
          contentProviderType: 1,
        },
      },
      {
        $addFields: {
          adjustedLicenseType: {
            $cond: {
              if: {
                $and: [
                  {
                    $in: [LicenseTypeEnum.LIFE, '$regulatorInfo.licenseType'],
                  },
                  {
                    $in: [LicenseTypeEnum.NONLIFE, '$regulatorInfo.licenseType'],
                  },
                ],
              },
              then: { $concatArrays: [[LicenseTypeEnum.BOTH], '$regulatorInfo.licenseType'] },
              else: '$regulatorInfo.licenseType',
            },
          },
        },
      },
      {
        $unwind: {
          path: '$adjustedLicenseType',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          subjects: 1,
          code: 1,
          key: {
            trainingCenter: '$regulatorInfo.trainingCenter',
            licenseRenewal: '$regulatorInfo.licenseRenewal',
            applicantType: '$regulatorInfo.applicantType',
            licenseType: '$adjustedLicenseType',
          },
          regulatorInfo: 1,
          contentProviderType: 1,
        },
      },
      {
        $group: {
          _id: {
            trainingCenter: '$key.trainingCenter',
            licenseRenewal: '$key.licenseRenewal',
            applicantType: '$key.applicantType',
            licenseType: '$key.licenseType',
          },
          courses: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 0,
          regulatorInfo: '$_id',
          courses: 1,
        },
      },
    ];

    const results = await this.aggregate<OutputGroupSubjectRegulatorOICCourseByRegulatorCoursePropertyParams>(pipeline);
    return results;
  }

  async getOnePublishCourseDetailById(id: GenericID): Promise<Course> {
    const pipeline = [
      {
        $match: { id },
      },
      {
        $lookup: {
          from: 'course-versions',
          let: { courseId: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$courseId', '$$courseId'],
                    },
                    {
                      $eq: ['$status', CourseVersionStatusEnum.PUBLISHED],
                    },
                  ],
                },
              },
            },
            {
              $sort: { publishedAt: -1 },
            },
            {
              $limit: 1,
            },
          ],
          as: 'courseVersion',
        },
      },
      {
        $unwind: '$courseVersion',
      },
      {
        $limit: 1,
      },
    ];

    const [res] = await this.aggregate<Course>(pipeline);

    return res;
  }

  async findTSICourseByOrganizationIdAndCourseIds(
    courseIds: GenericID[],
    organizationId: GenericID,
  ): Promise<CourseParams[]> {
    const pipeline = [
      {
        $match: {
          organizationId,
          'regulatorInfo.regulator': RegulatorEnum.TSI,
          ...(isEmpty(courseIds) ? {} : { id: { $in: courseIds } }),
        },
      },
    ];
    const result = await this.aggregate<CourseParams>(pipeline);
    return result;
  }

  async findEnabledPublishedCourseWithVersionByIds(
    courseIds: GenericID[],
  ): Promise<(CourseParams & { courseVersion: CourseVersionParams })[]> {
    const result = await this.aggregate<CourseParams & { courseVersion: CourseVersionParams }>([
      {
        $match: {
          id: { $in: courseIds },
          isEnabled: true,
        },
      },
      {
        $lookup: {
          from: 'course-versions',
          let: { courseId: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$courseId', '$$courseId'] }, { $eq: ['$status', CourseVersionStatusEnum.PUBLISHED] }],
                },
              },
            },
          ],
          as: 'courseVersion',
        },
      },
      {
        $unwind: '$courseVersion',
      },
    ]);

    return result;
  }

  async findIdsByOrganizationIdAndRegulatorType(
    regulator: RegulatorEnum,
    organizationId: GenericID,
  ): Promise<GenericID[]> {
    const pipeline = [
      {
        $match: {
          organizationId,
          'regulatorInfo.regulator': regulator,
          deletedAt: { $ne: null },
        },
      },
      {
        $group: {
          _id: null,
          ids: { $push: '$id' },
        },
      },
    ];
    const [result] = await this.aggregate<{ ids: GenericID[] }>(pipeline);
    return result?.ids ?? [];
  }
}
