import { GenericID } from '@iso/constants/commonTypes';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { LoginProviderTypeEnum } from '@iso/lms/enums/loginProvider.enum';
import { LoginProvider } from '@iso/lms/models/loginProvider.model';
import { LoginProviderParams } from '@iso/lms/types/loginProvider.type';

import { BaseRepository } from '@infrastructures/persistence/repositories/database/base.repository';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';

import { ILoginProviderDataMapper } from '@interfaces/dataMapper/loginProvider.dataMapper.interface';
import { ILoginProviderRepository } from '@interfaces/repositories/loginProvider.repository.interface';

const TABLE_NAME = DBCollectionEnum.LOGIN_PROVIDERS;

export class LoginProviderRepository
  extends BaseRepository<LoginProvider, LoginProviderParams>
  implements ILoginProviderRepository
{
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: ILoginProviderDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }

  async findOrganizationLoginProviderSkilllane(organizationId: GenericID) {
    const aggregateParams = [
      {
        $match: {
          type: LoginProviderTypeEnum.SKILLLANE,
        },
      },
      {
        $lookup: {
          from: 'organization-login-providers',
          let: { loginProviderId: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$loginProviderId', '$$loginProviderId'],
                    },
                    {
                      $eq: ['$organizationId', organizationId],
                    },
                    {
                      $eq: ['$isEnabled', true],
                    },
                  ],
                },
              },
            },
          ],
          as: 'organizationLoginProvider',
        },
      },
      {
        $unwind: '$organizationLoginProvider',
      },
      {
        $project: {
          id: 1,
          type: 1,
          organizationLoginProviderId: '$organizationLoginProvider.id',
        },
      },
    ];

    const result = this.aggregate(aggregateParams);

    return result;
  }
}
