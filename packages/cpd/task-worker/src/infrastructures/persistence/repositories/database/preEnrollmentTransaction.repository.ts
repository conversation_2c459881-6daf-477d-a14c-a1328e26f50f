import { GenericID } from '@iso/constants/commonTypes';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { BusinessTypeEnum } from '@iso/lms/enums/enrollment.enum';
import { PreEnrollmentReservationStatusEnum } from '@iso/lms/enums/preEnrollmentReservation.enum';
import { PreEnrollmentTransactionStatusEnum } from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { PreEnrollmentTransaction } from '@iso/lms/models/preEnrollmentTransaction.model';
import { PreEnrollmentTransactionParams } from '@iso/lms/types/preEnrollmentTransaction.type';
import { isEmpty } from 'lodash';

import { BaseRepository } from '@infrastructures/persistence/repositories/database/base.repository';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';
import { OutputFindPreEnrollmentTransactionPayloadOfOicReportParams } from '@constants/types/preEnrollmentTransaction.type';

import { IPreEnrollmentTransactionDataMapper } from '@interfaces/dataMapper/preEnrollmentTransaction.dataMapper.interface';
import { IPreEnrollmentTransactionRepository } from '@interfaces/repositories/preEnrollmentTransaction.repository.interface';

const TABLE_NAME = DBCollectionEnum.PRE_ENROLLMENT_TRANSACTIONS;

export class PreEnrollmentTransactionRepository
  extends BaseRepository<PreEnrollmentTransaction, PreEnrollmentTransactionParams>
  implements IPreEnrollmentTransactionRepository
{
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: IPreEnrollmentTransactionDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }

  async findPayloadsByIds(ids: GenericID[]): Promise<Pick<PreEnrollmentTransactionParams, 'id' | 'payload'>[]> {
    const pipeline = [
      {
        $match: {
          id: { $in: ids },
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          payload: 1,
        },
      },
    ];
    const results = await this.aggregate<Pick<PreEnrollmentTransactionParams, 'id' | 'payload'>>(pipeline);
    return results;
  }

  async findPayloadForOicReportByStatusAndRoundIds(
    roundIds: GenericID[],
    status: PreEnrollmentTransactionStatusEnum[],
    organizationId: GenericID,
  ): Promise<OutputFindPreEnrollmentTransactionPayloadOfOicReportParams[]> {
    const pipeline = [
      {
        $match: {
          $and: [
            { organizationId },
            { roundId: { $in: roundIds } },
            !isEmpty(status) && { status: { $in: status } },
          ].filter(Boolean),
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          payload: 1,
          preEnrollmentReservationId: 1,
          status: 1,
          enrollBy: 1,
          contentItems: 1,
          isCheckRegulator: 1,
          roundId: 1,
        },
      },
    ];
    const results = await this.aggregate<OutputFindPreEnrollmentTransactionPayloadOfOicReportParams>(pipeline);
    return results;
  }

  async findDuplicateInPayload(organizationId: GenericID, email: string, citizenId: string) {
    const aggregateParams = [
      {
        $project: {
          id: 1,
          jobId: 1,
          status: 1,
          businessType: 1,
          payload: 1,
          preEnrollmentReservationId: 1,
          organizationId: 1,
        },
      },
      {
        $match: {
          $and: [
            {
              $or: [
                {
                  'payload.email': email,
                },
                {
                  'payload.citizenId': citizenId,
                },
              ],
            },
            {
              organizationId,
            },
            {
              status: {
                $in: [
                  PreEnrollmentTransactionStatusEnum.PASSED,
                  PreEnrollmentTransactionStatusEnum.WAITING_VERIFY,
                  PreEnrollmentTransactionStatusEnum.EDITED_AFTER_VERIFY,
                ],
              },
            },
          ],
        },
      },
      {
        $facet: {
          b2b: [
            {
              $match: {
                businessType: BusinessTypeEnum.B2B,
              },
            },
            {
              $lookup: {
                from: 'pre-enrollment-reservations',
                let: {
                  preEnrollmentReservationId: '$preEnrollmentReservationId',
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          {
                            $eq: ['$id', '$$preEnrollmentReservationId'],
                          },
                          {
                            $eq: ['$status', PreEnrollmentReservationStatusEnum.PASSED],
                          },
                        ],
                      },
                    },
                  },
                  {
                    $project: {
                      operationType: 1,
                      customerCode: 1,
                      roundDate: 1,
                      status: 1,
                    },
                  },
                ],
                as: 'preEnrollmentReservation',
              },
            },
            {
              $unwind: '$preEnrollmentReservation',
            },
          ],
          b2c: [
            {
              $match: {
                businessType: BusinessTypeEnum.B2C,
              },
            },
          ],
        },
      },
      {
        $project: {
          transactions: {
            $concatArrays: ['$b2b', '$b2c'],
          },
        },
      },
      {
        $unwind: {
          path: '$transactions',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $ifNull: ['$transactions', {}],
          },
        },
      },
      {
        $project: {
          id: 1,
          jobId: 1,
          status: 1,
          businessType: 1,
          payload: 1,
        },
      },
      {
        $limit: 1,
      },
    ];

    const result = await this.aggregate(aggregateParams);

    return result;
  }
}
