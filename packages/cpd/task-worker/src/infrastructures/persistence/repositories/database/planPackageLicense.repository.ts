import { GenericID } from '@iso/constants/commonTypes';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { PlanPackageLicense } from '@iso/lms/models/planPackageLicense.model';
import { PlanPackageLicenseParams } from '@iso/lms/types/planPackageLicense.type';

import { BaseRepository } from '@infrastructures/persistence/repositories/database/base.repository';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';

import { IPlanPackageLicenseDataMapper } from '@interfaces/dataMapper/planPackageLicense.dataMapper.interface';
import { IPlanPackageLicenseRepository } from '@interfaces/repositories/planPackageLicense.repository.interface';

import { date } from '@domains/utils/date.util';

const TABLE_NAME = DBCollectionEnum.PLAN_PACKAGE_LICENSES;

export class PlanPackageLicenseRepository
  extends BaseRepository<PlanPackageLicense, PlanPackageLicenseParams>
  implements IPlanPackageLicenseRepository
{
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: IPlanPackageLicenseDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }

  async findPlanPackageLicenseSOPendingActive(
    userId: GenericID,
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const outout = await this.findPlanPackageLicenseMultipleUserSOPendingActive([userId], planPackageIds);
    return outout;
  }

  async findPlanPackageLicenseSOApprovedActive(
    userId: GenericID,
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const output = await this.findPlanPackageLicenseMultipleUserSOApprovedActive([userId], planPackageIds);
    return output;
  }

  async findPlanPackageLicenseMultipleUserSOApprovedActive(
    userIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const output = await this.find({
      userId: {
        $in: userIds,
      },
      planPackageId: {
        $in: planPackageIds,
      },
      startedAt: { $ne: null },
      expiredAt: { $gte: date().toDate() },
    });
    return output;
  }

  async findPlanPackageLicenseMultipleUserSOPendingActive(
    userIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const aggregateParams = [
      {
        $match: {
          userId: {
            $in: userIds,
          },
          planPackageId: {
            $in: planPackageIds,
          },
          startedAt: null,
          expiredAt: null,
        },
      },
      {
        $lookup: {
          from: 'plan-packages',
          let: {
            planPackageId: '$planPackageId',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$id', '$$planPackageId'],
                    },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      {
        $unwind: '$planPackage',
      },
      {
        $project: {
          id: 1,
          planPackageId: 1,
          userId: 1,
          startedAt: '$planPackage.startDate',
          expiredAt: '$planPackage.endDate',
          createdAt: 1,
          updatedAt: 1,
          deletedAt: 1,
        },
      },
      {
        $match: {
          expiredAt: {
            $gte: date().toDate(),
          },
        },
      },
    ];

    const output = await this.aggregate<PlanPackageLicenseParams>(aggregateParams);
    return output;
  }

  async findActiveLicenseUserIdsByPlanPackageIds(planPackageIds: GenericID[]): Promise<GenericID[]> {
    const dateNow = date().toDate();
    const pipeline = [
      {
        $match: {
          $and: [
            { planPackageId: { $in: planPackageIds } },
            {
              $or: [
                {
                  startedAt: null,
                  expiredAt: null,
                },
                {
                  startedAt: {
                    $lte: dateNow,
                  },
                  expiredAt: {
                    $gte: dateNow,
                  },
                },
              ],
            },
          ],
        },
      },
      {
        $group: {
          _id: null,
          userIds: { $addToSet: '$userId' },
        },
      },
    ];

    const [result] = await this.aggregate<{ userIds: GenericID[] }>(pipeline);
    return result?.userIds ?? [];
  }
}
