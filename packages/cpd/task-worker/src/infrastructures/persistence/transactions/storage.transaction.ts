import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { v4 } from 'uuid';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageTransactionTypeEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { StorageSessionParams, StorageTransactionParams } from '@constants/types/infrastructures/storage.type';

import { IStorageRepository } from '@interfaces/storage/storage';
import { IStorageTransaction } from '@interfaces/transaction/storage.transaction.interface';

@Injectable()
export class StorageTransaction implements IStorageTransaction {
  constructor(
    @Inject(CACHE_MANAGER) readonly cacheManager: Cache,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async runTransaction(
    storage: IStorageRepository,
    transaction: (
      session: StorageSessionParams,
      commit: () => Promise<void>,
      reverse: () => Promise<void>,
    ) => Promise<void>,
  ): Promise<void> {
    const session = await this.startTransaction();
    try {
      await transaction(
        session,
        () => this.commitTransaction(storage, session),
        () => this.rollbackTransaction(storage, session),
      );
      await this.commitTransaction(storage, session);
    } catch (error) {
      await this.rollbackTransaction(storage, session);
      throw error;
    } finally {
      await this.endTransaction(session);
    }
  }

  private async commitTransaction(storage: IStorageRepository, session: StorageSessionParams): Promise<void> {
    const transactions = await this.cacheManager.get<StorageTransactionParams[]>(session.key);
    const pendingTransaction = transactions.filter((transaction) => transaction.status === 'pending');
    for (const [index, transaction] of pendingTransaction.entries()) {
      try {
        switch (transaction.type) {
          case StorageTransactionTypeEnum.UPLOAD: {
            await storage.upload({
              raw: transaction.buffer,
              path: transaction.filePath,
              fileType: transaction.fileType,
            });
            break;
          }
          case StorageTransactionTypeEnum.DELETE: {
            const { base64, contentType } = await storage.downloadBase64File(transaction.filePath);
            transactions[index].buffer = Buffer.from(base64, 'base64');
            transactions[index].fileType = contentType as UploadFileTypeEnum;
            await storage.delete(transaction.filePath);
            break;
          }
          default: {
            break;
          }
        }
        transactions[index].status = 'complete';
      } catch (error) {
        transactions[index].status = 'failed';
        throw error;
      }
    }
  }

  private async startTransaction(): Promise<StorageSessionParams> {
    const key = v4();
    const newSession = { key: v4(), transactions: [] };
    await this.cacheManager.set(key, newSession.transactions);
    return newSession;
  }

  private async endTransaction(session: StorageSessionParams): Promise<void> {
    await this.cacheManager.del(session.key);
  }

  private async rollbackTransaction(storage: IStorageRepository, session: StorageSessionParams): Promise<void> {
    const { key } = session;
    const transactions = await this.cacheManager.get<StorageTransactionParams[]>(key);
    const rollbackTransactions = transactions?.filter((transaction) => transaction.status === 'complete') || [];
    try {
      for (const transaction of rollbackTransactions) {
        switch (transaction.type) {
          case StorageTransactionTypeEnum.UPLOAD: {
            await storage.delete(transaction.filePath);
            break;
          }
          case StorageTransactionTypeEnum.DELETE: {
            await storage.upload({
              path: transaction.filePath,
              raw: transaction.buffer,
              fileType: transaction.fileType,
            });
          }
          default: {
            break;
          }
        }
      }
    } catch (error) {
      this.logger.error(`Rollback transaction key: ${key} failed`);
    }
  }
}
