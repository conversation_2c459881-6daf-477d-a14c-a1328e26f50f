import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Inject, Injectable } from '@nestjs/common';
import { decode, Secret, sign, SignOptions, verify } from 'jsonwebtoken';

import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';

import { IEnvironments } from '@interfaces/configs/environment.interface';

import { IJwtService } from '@domains/interfaces/infrastructures/services/jwtService.interface';

@Injectable()
export class JwtService implements IJwtService {
  constructor(
    @Inject(InfrastructuresConfigDIToken.Environment)
    private readonly environment: IEnvironments,
  ) {}

  createFirstPasswordToken(params: { guid: GenericID; email: string; organizationId: GenericID }): Promise<string> {
    const { guid, email, organizationId } = params;
    const data = { guid, email, organizationId };

    const tokenSecret = {
      key: this.base64Decode(this.environment.managePasswordTokenPrivatekey || ''),
      passphrase: this.environment.managePasswordTokenPassphrase || '',
    };
    const jwtOptions = {
      algorithm: this.environment.tokenAlgorithm,
      expiresIn: this.environment.firstPasswordTokenExpires,
      issuer: this.environment.tokenIssuer,
      audience: this.environment.tokenAudience,
    };

    return this.createJwt(data, tokenSecret, jwtOptions);
  }

  createJwt(data: Record<string, any>, tokenSecret: Secret, jwtOptions: SignOptions): Promise<string> {
    return new Promise((resolve, reject) => {
      sign(data, tokenSecret, jwtOptions, (err: Error, token: string) => {
        if (!err) {
          resolve(token);
        } else {
          reject(err);
        }
      });
    });
  }

  decodeJwt(token: string | null | undefined): Nullable<Record<string, any>> {
    if (token) return decode(token) as Record<string, any>;
    return null;
  }

  jwtVerify(token: string, publicKey: string, tokenConfig?: Record<string, any>): Promise<Record<string, any>> {
    return new Promise((resolve, reject) => {
      verify(token, publicKey, tokenConfig, (err, payload: any) => {
        if (!err) {
          resolve(payload);
        } else {
          reject(err);
        }
      });
    });
  }

  base64Decode(dataEncode: string): string {
    return Buffer.from(dataEncode, 'base64').toString();
  }

  decodeBase64File(raw: string): Buffer {
    return Buffer.from(raw, 'base64');
  }
}
