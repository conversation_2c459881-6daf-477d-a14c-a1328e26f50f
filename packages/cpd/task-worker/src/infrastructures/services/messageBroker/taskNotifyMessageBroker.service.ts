import { TaskOperationKeyParams } from '@iso/lms/types/taskOperation.type';
import { Injectable } from '@nestjs/common';

import { MessageBrokerService } from '@infrastructures/services/messageBroker/messageBroker.service';

import { IMessageBrokerInstance } from '@interfaces/configs/messageBroker.interface';
import { ITaskNotifyMessageBrokerService } from '@interfaces/services/messageBrokers/taskNotifyMessageBroker.service.interface';

@Injectable()
export class TaskNotifyMessageBrokerService extends MessageBrokerService implements ITaskNotifyMessageBrokerService {
  private readonly taskNotifyQueueName = 'task-notify';
  // eslint-disable-next-line @typescript-eslint/no-useless-constructor
  constructor(brokerMessageInstance: IMessageBrokerInstance) {
    super(brokerMessageInstance);
  }

  async publishTaskNotify(memoryTaskOperationKey: TaskOperationKeyParams): Promise<void> {
    await this.publisher(this.taskNotifyQueueName, JSON.stringify({ memoryTaskOperationKey }), {});
  }
}
