import { GenericID } from '@iso/constants/commonTypes';
import { DomainMetaDataEnum } from '@iso/lms/enums/certificate.enum';
import {
  BuildCertificatePayloadParams,
  CertificatePropertyParams,
  GenerateCertificateParams,
  GenerateCertificateResult,
} from '@iso/lms/types/certificate.type';
import {
  OrganizationCertificatePropertyParams,
  OrganizationCertificatePropertyWithNameParams,
} from '@iso/lms/types/organizationCertificate.type';
import { Inject, Injectable } from '@nestjs/common';
import { INTERNAL_SERVER_ERROR } from 'http-status-codes';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageProviderEnum } from '@constants/enums/infrastructures/storage.enum';
import { MailAttachmentParams } from '@constants/types/infrastructures/notification.type';

import { IEnvironments } from '@interfaces/configs/environment.interface';
import { ICertificateService } from '@interfaces/services/certificate.service.interface';
import { IHttpService } from '@interfaces/services/httpService.interface';
import { IOAuthService } from '@interfaces/services/oAuth.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';

import { date, DateFormat } from '@domains/utils/date.util';

@Injectable()
export class CertificateService implements ICertificateService {
  constructor(
    @Inject(InfrastructuresServiceDIToken.OAuthService)
    private readonly oAuthService: IOAuthService,
    @Inject(InfrastructuresServiceDIToken.HttpService)
    private readonly httpService: IHttpService,
    @Inject(InfrastructuresConfigDIToken.Environment)
    private readonly environment: IEnvironments,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
  ) {}

  async create(payload: GenerateCertificateParams): Promise<GenerateCertificateResult> {
    const token = await this.oAuthService.getIDSAccessToken();

    const url = `${this.environment.certificateEndpoint}/api/v1/user-certificates`;

    const options = {
      headers: { Authorization: `Bearer ${token}` },
    };
    const response = await this.httpService.post(url, payload, options).catch(() => {
      throw Exception.new({
        code: Code.SERVICE_UNAVAILABLE,
        message: 'Cannot generate certificate',
      });
    });

    const { certificateCode, certificateUrl, certificatePDFUrl } = response.data;

    return {
      certificateCode,
      certificateUrl,
      certificatePDFUrl,
    };
  }

  async pretestGenerateCertificate(payload: GenerateCertificateParams): Promise<void> {
    const token = await this.oAuthService.getIDSAccessToken();

    const options = {
      headers: { Authorization: `Bearer ${token}` },
    };

    await this.httpService
      .post(`${this.environment.certificateEndpoint}/api/v1/user-certificates?pretest=true`, payload, options)
      .catch((error) => {
        throw Exception.new({
          code: Code.BAD_REQUEST_ERROR,
          message: 'cannot pretest generate certificate',
          data: error.response.data,
        });
      });
  }

  async fetchThumbnail(slugName: string): Promise<string> {
    const token = await this.oAuthService.getIDSAccessToken();

    const url = `${this.environment.certificateEndpoint}/api/v1/certificates/${slugName}/thumbnail`;
    const options = {
      headers: { Authorization: `Bearer ${token}` },
    };

    const result = await this.httpService
      .get(url, options)
      .then((res) => res.data)
      .catch((error) => {
        const { response } = error;

        if (response.status === INTERNAL_SERVER_ERROR) {
          throw Exception.new({
            code: Code.INTERNAL_SERVER_ERROR,
            data: response.data,
          });
        }

        throw Exception.new({
          code: Code.SERVICE_UNAVAILABLE,
          data: response.data,
        });
      });
    return result.data;
  }

  async regenerateCertificate(code: string, payload: Record<string, any>): Promise<GenerateCertificateResult> {
    const token = await this.oAuthService.getIDSAccessToken();
    const url = `${this.environment.certificateEndpoint}/api/v1/user-certificates/${code}`;
    const options = {
      headers: { Authorization: `Bearer ${token}` },
    };

    const res = await this.httpService.put(url, payload, options);
    if (res.httpStatus === INTERNAL_SERVER_ERROR) {
      throw Exception.new({
        code: Code.SERVICE_UNAVAILABLE,
        message: 'cannot generate certificate',
        data: res.data,
      });
    }

    const { certificateCode, certificateUrl, certificatePDFUrl } = res.data;

    return {
      certificateCode,
      certificateUrl,
      certificatePDFUrl,
    };
  }

  async buildCertificatePayload(payload: BuildCertificatePayloadParams): Promise<GenerateCertificateParams> {
    const {
      enrollmentId,
      slugName,
      salute,
      firstname,
      lastname,
      courseName,
      issuedDate,
      tsilicensetype,
      pillar,
      tsiCode,
      issuedBy,
      logoImageUrl,
      domainMetaData,
    } = payload;

    const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);

    const logoImageUrlS3 = logoImageUrl ? await storageRepository.getFileUrl(logoImageUrl) : '';

    return {
      dynamic_value: {
        salute,
        firstname,
        lastname,
        tsilicensetype: tsilicensetype || '',
        pillar: pillar || '',
        coursecode: tsiCode || '',
        coursename: courseName,
        issueddate: issuedDate
          ? date(issuedDate).locale('th').format(DateFormat.buddhistFullDateWithLocale)
          : date().locale('th').format(DateFormat.buddhistFullDateWithLocale),
        issuedby: issuedBy || '',
        logo: logoImageUrlS3,
      },
      slug_name: slugName,
      metadata_url: this.genMetaDataUrl(enrollmentId, domainMetaData),
    };
  }

  genMetaDataUrl(id: GenericID, domain: DomainMetaDataEnum): string {
    switch (domain) {
      case DomainMetaDataEnum.ENROLLMENT: {
        return `${this.environment.lmsApiEndpoint}/enrollments-v1/enrollments/${id}/certMetaData`;
      }
      case DomainMetaDataEnum.LEARNING_PATH_ENROLLMENT: {
        return `${this.environment.lmsApiEndpoint}/learning-path-enrollments-v1/learning-path-enrollments/${id}/certMetaData`;
      }
      default: {
        return '';
      }
    }
  }

  mergeProperties(
    certificateProperties: CertificatePropertyParams[],
    organizationCertificateProperties: OrganizationCertificatePropertyParams[],
  ): OrganizationCertificatePropertyWithNameParams[] {
    const organizationCertificatePropertiesMap = new Map(
      organizationCertificateProperties.map((prop) => [prop.key, prop]),
    );

    return certificateProperties.map((certificateProperty) => {
      if (!certificateProperty.isEnabledSetting) return certificateProperty;

      const organizationCertificatePropertiesMatch = organizationCertificatePropertiesMap.get(certificateProperty.key);
      if (organizationCertificatePropertiesMatch) {
        return {
          ...certificateProperty,
          value: organizationCertificatePropertiesMatch.value,
          mediaId: organizationCertificatePropertiesMatch.mediaId
            ? organizationCertificatePropertiesMatch.mediaId
            : null,
        };
      }

      return certificateProperty;
    });
  }

  async getCertificateAttachmentBuffers(certificates?: GenerateCertificateResult[]): Promise<MailAttachmentParams[]> {
    if (!certificates || certificates.length === 0) {
      return [];
    }

    const attachmentResults = await Promise.allSettled(
      certificates
        .filter((certificate) => !!certificate.certificatePDFUrl)
        .map(async (certificate) => {
          const { certificatePDFUrl, certificateCode } = certificate;
          const response = await this.httpService.get(certificatePDFUrl, {
            responseType: 'arraybuffer',
          });
          const file = Buffer.from(response, 'binary').toString('base64');
          const filename = `${certificateCode}.pdf`;

          return {
            type: 'application/pdf',
            name: filename,
            content: file,
          };
        }),
    );

    const attachments = attachmentResults.reduce<
      {
        type: string;
        name: string;
        content: string;
      }[]
    >((acc, result) => {
      if (result.status === 'fulfilled') {
        acc.push(result.value);
      }
      return acc;
    }, []);

    return attachments;
  }
}
