import { Nullable } from '@iso/constants/commonTypes';
import {
  AchievementAdditionalCompletedParams,
  AchievementCompletedParams,
  BadgeAdditionalCompletedParams,
} from '@iso/email/constants/mailer';
import { TemplateNameEnum } from '@iso/email/constants/template';
import { EmailTemplateFactory } from '@iso/email/factory/template';
import { BaseTemplateBuilder } from '@iso/email/services/baseTemplateBuilder';
import { CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { LoginEmailTypeEnum } from '@iso/lms/enums/loginProvider.enum';
import { PreAssignContentTypeEnum } from '@iso/lms/enums/preAssignContent.enum';
import { UserNotificationApplicationEnum } from '@iso/lms/enums/userNotification.enum';
import { OrganizationParams, OrganizationNotificationConfigParams } from '@iso/lms/types/organization.type';
import { Inject, Injectable } from '@nestjs/common';
import axios from 'axios';

import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageProviderEnum } from '@constants/enums/infrastructures/storage.enum';
import {
  EmailNotificationParams,
  InApplicationNotificationParams,
  MailAttachmentParams,
} from '@constants/types/infrastructures/notification.type';
import {
  MailPayloadAchievementAndBadgeCompleteParams,
  MailPayloadAssignEnrollmentCompleteParams,
  MailPayloadAssignPlanPackageLicenseExistingUserParams,
  MailPayloadEnrollmentApproveParams,
  MailPayloadEnrollmentCompleteAndCertificateParams,
  MailPayloadEnrollmentRejectedParams,
  MailPayloadFirstPasswordParams,
  MailPayloadLearningPathEnrollmentCertificateParams,
  MailPayloadLearningPathEnrollmentCompleteAndCertificateParams,
  MailPayloadLearningPathEnrollmentCompleteParams,
  MailPayloadPlanPackageLicenseExpiredParams,
  MailPayloadPromoteContentParams,
} from '@constants/types/mailer.type';

import { IEnvironments } from '@interfaces/configs/environment.interface';
import { IHttpService } from '@interfaces/services/httpService.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import { INotificationService } from '@interfaces/services/notification.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';

import { date, DateFormat, formatDateInThaiLocale, TimeZoneEnum } from '@domains/utils/date.util';

@Injectable()
export class NotificationService implements INotificationService {
  private readonly inApplicationNotificationQueueName: string = 'notification:in-application';
  private readonly emailNotificationQueueName: string = 'notification:email';

  constructor(
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerAdaptorService: IMessageBrokerService,
    @Inject(InfrastructuresServiceDIToken.HttpService)
    private readonly httpService: IHttpService,
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(InfrastructuresConfigDIToken.Environment)
    private readonly environment: IEnvironments,
  ) {}

  async sendInApplicationNotification(payload: InApplicationNotificationParams) {
    const message = JSON.stringify(payload);
    this.messageBrokerAdaptorService.publisher(this.inApplicationNotificationQueueName, Buffer.from(message), {
      persistent: true,
      deliveryMode: 2,
    });
  }

  async sendEmailNotification(payload: EmailNotificationParams) {
    const message = JSON.stringify(payload);
    this.messageBrokerAdaptorService.publisher(this.emailNotificationQueueName, Buffer.from(message), {
      persistent: true,
      deliveryMode: 2,
    });
  }

  async notifyRejected(
    email: string,
    payload: MailPayloadEnrollmentRejectedParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const { courseName, fullName } = payload;
    const { nameEng, notificationConfig } = organization;
    const subject = `แจ้งผลการไม่ผ่านหลักสูตร ${courseName} | ${nameEng}`;

    const contentVariables = {
      courseName,
      fullName,
    };

    const enrollmentRejectTemplate = EmailTemplateFactory.createTemplate(TemplateNameEnum.ENROLLMENT_REJECT);
    const contentHtml = enrollmentRejectTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig);
  }

  async notifyAssignEnrollmentComplete(
    email: string,
    payload: MailPayloadAssignEnrollmentCompleteParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const { contentName, contentType, fullName, learnerFullName, round, learnerUserId } = payload;
    const { nameEng, domain, fqdn, notificationConfig } = organization;

    const mainURL = this.getMainUrl(domain, fqdn);
    const buttonUrl = `${mainURL}/myTeam/users/${learnerUserId}?activeTab=enrollment_history`;
    let roundText = '';

    if (round) {
      const roundStartDate = round.roundDate
        ? formatDateInThaiLocale(round.roundDate, TimeZoneEnum.Bangkok, DateFormat.buddhistShortDate)
        : '';

      const roundExpiredDate = round.expiredDate
        ? formatDateInThaiLocale(
            date(round.expiredDate).startOf('day').toDate(),
            TimeZoneEnum.Bangkok,
            DateFormat.buddhistShortDate,
          )
        : '';

      if (roundStartDate) {
        roundText += `รอบวันที่ ${roundStartDate} เวลา 9:00 น. `;
      }

      if (roundStartDate && roundExpiredDate) {
        roundText += `ถึง ${roundExpiredDate} เวลา 23:59 น. `;
      }
    }

    let subjectEmail = '';
    let title1 = '';
    let title2 = '';
    let descriptionLine1 = '';

    if (contentType === PreAssignContentTypeEnum.COURSE) {
      subjectEmail = `แจ้งเตือน คุณ${learnerFullName} เรียนหลักสูตร “${contentName}” สำเร็จ | ${nameEng}`;
      title1 = `แจ้งเตือน คุณ${learnerFullName} เรียนหลักสูตร`;
      title2 = `“${contentName}” สำเร็จ`;
      descriptionLine1 = `แจ้งเตือน คุณ${learnerFullName} เรียนหลักสูตร “${contentName}” ${roundText} สำเร็จ`;
    }

    if (contentType === PreAssignContentTypeEnum.LEARNING_PATH) {
      subjectEmail = `แจ้งเตือน คุณ${learnerFullName} เรียนแผนการเรียนรู้ “${contentName}” สำเร็จ | ${nameEng}`;
      title1 = `แจ้งเตือน คุณ${learnerFullName} เรียนแผนการเรียนรู้`;
      title2 = `“${contentName}” สำเร็จ`;
      descriptionLine1 = `แจ้งเตือน คุณ${learnerFullName} เรียนแผนการเรียนรู้ “${contentName}” ${roundText} สำเร็จ`;
    }

    const contentVariables = {
      title1,
      title2,
      fullName,
      descriptionLine1,
      buttonText: 'รายละเอียดการเรียน',
      buttonWidth: '264px',
      buttonUrl,
    };

    const assignEnrollmentCompletedTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.ASSIGN_ENROLLMENT_COMPLETED,
    );
    const contentHtml = assignEnrollmentCompletedTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subjectEmail, notificationConfig);
  }

  async notifyApproved(
    email: string,
    payload: MailPayloadEnrollmentApproveParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const {
      courseName,
      fullName,
      isDeduct,
      isDeductApproved,
      objectiveType,
      regulator,
      startDate,
      endDate,
      sendDate,
      operationExpiredDate,
    } = payload;

    const { nameEng, domain, fqdn, notificationConfig } = organization;

    const isTSI = objectiveType === CourseObjectiveTypeEnum.TRAINING && regulator === RegulatorEnum.TSI;
    const isOIC = objectiveType === CourseObjectiveTypeEnum.TRAINING && regulator === RegulatorEnum.OIC;
    const isRegular = objectiveType === CourseObjectiveTypeEnum.REGULAR;

    const subject = `แจ้งผลการผ่านหลักสูตร “${courseName}” | ${nameEng}`;

    const mainURL = this.getMainUrl(domain, fqdn);
    const attachmentURL = `${mainURL}/enrollments/attachment`;

    const getDescription = (() => {
      if (isDeductApproved) {
        return `คุณผ่านหลักสูตร “${courseName}” และได้รับสิทธิการลดหย่อนชั่วโมงอบรม โดยจะได้รับประกาศนียบัตรจบการอบรมทางอีเมลนี้ ภายหลังจากจบรอบการอบรม`;
      } else if (isDeduct) {
        return `คุณผ่านหลักสูตร “${courseName}” แล้ว  โดยจะได้รับประกาศนียบัตรจบการอบรมทางอีเมลนี้ ภายหลังจากจบรอบการอบรม`;
      } else {
        return `คุณผ่านหลักสูตร “${courseName}” โดยจะได้รับประกาศนียบัตรจบการอบรมทางอีเมลนี้ ภายหลังจากจบรอบการอบรม`;
      }
    })();
    const description = getDescription;

    const contentVariables = {
      courseName,
      fullName,
      startDate,
      endDate,
      sendDate,
      operationExpiredDate,
      isDeduct,
      isDeductApproved,
      isTSI,
      isOIC,
      isRegular,
      attachmentURL,
      mainURL,
      description,
    };

    const enrollmentApprovedTemplate = EmailTemplateFactory.createTemplate(TemplateNameEnum.ENROLLMENT_APPROVED);
    const contentHtml = enrollmentApprovedTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig);
  }

  async notifyEnrollmentCompleteWithCertificate(
    email: string,
    payload: MailPayloadEnrollmentCompleteAndCertificateParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const {
      courseName,
      certificateUrl,
      certificatePDFUrl,
      certificateCode,
      fullName,
      isDeduct,
      isDeductApproved,
      operationExpiredDate,
      refName = '',
      objectiveType,
      regulator,
      sendAt,
    } = payload;
    const { nameEng, domain, fqdn, notificationConfig } = organization;

    const isTSI = objectiveType === CourseObjectiveTypeEnum.TRAINING && regulator === RegulatorEnum.TSI;
    const isOIC = objectiveType === CourseObjectiveTypeEnum.TRAINING && regulator === RegulatorEnum.OIC;
    const isRegular = objectiveType === CourseObjectiveTypeEnum.REGULAR;

    const response = await axios.get(certificatePDFUrl, {
      responseType: 'arraybuffer',
    });

    const attachment = Buffer.from(response.data, 'binary').toString('base64');
    const filename = `${certificateCode}.pdf`;

    let subject = `แจ้งผลการผ่านหลักสูตร “${courseName}” | ${nameEng}`;
    if (refName) {
      subject = `แจ้งผลการผ่านหลักสูตร “${courseName}” และได้รับประกาศนียบัตร ${refName} | ${nameEng}`;
    }

    const mainURL = this.getMainUrl(domain, fqdn);
    const attachmentURL = `${mainURL}/enrollments/attachment`;

    const contentVariables = {
      courseName,
      fullName,
      certificateURL: certificateUrl,
      isDeduct,
      isDeductApproved,
      isTSI,
      isOIC,
      isRegular,
      operationExpiredDate,
      attachmentURL,
      mainURL,
      refName: refName || '',
    };

    const attachments = [
      {
        type: 'application/pdf',
        name: filename,
        content: attachment,
      },
    ];

    const enrollmentCompleteAndCertificateTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.ENROLLMENT_COMPLETE_AND_CERTIFICATE,
    );
    const contentHtml = enrollmentCompleteAndCertificateTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, attachments, sendAt);
  }

  async notifyLearningPathEnrollmentComplete(
    email: string,
    payload: MailPayloadLearningPathEnrollmentCompleteParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const { learningPathName, fullName, learningPathCode } = payload;

    const { nameEng, domain, fqdn, notificationConfig } = organization;

    const subject = `แจ้งผลการเรียนจบแผนการเรียนรู้ “${learningPathName}” | ${nameEng}`;

    const mainURL = this.getMainUrl(domain, fqdn);
    const learningPathUrl = `${mainURL}/learningPaths/${learningPathCode}`;

    const contentVariables = {
      learningPathName,
      fullName,
      learningPathUrl,
      mainURL,
    };

    const learningPathEnrollmentCompleteTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.LEARNING_PATH_ENROLLMENT_COMPLETE,
    );
    const contentHtml = learningPathEnrollmentCompleteTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);
    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig);
  }

  async notifyLearningPathEnrollmentCertificate(
    email: string,
    payload: MailPayloadLearningPathEnrollmentCertificateParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const {
      learningPathName,
      certificateUrl,
      certificatePDFUrl,
      certificateCode,
      fullName,
      sendAt,
      refName = '',
    } = payload;
    const { nameEng, domain, fqdn, notificationConfig } = organization;

    const response = await this.httpService.get(certificatePDFUrl, {
      responseType: 'arraybuffer',
    });
    const attachment = Buffer.from(response.data, 'binary').toString('base64');
    const filename = `${certificateCode}.pdf`;

    const subject = `ประกาศนียบัตร ${refName} สำหรับแผนการเรียนรู้ “${learningPathName}” | ${nameEng}`;

    const mainURL = this.getMainUrl(domain, fqdn);

    const contentVariables = {
      learningPathName,
      fullName,
      certificateUrl,
      mainURL,
      refName: refName || '',
    };

    const attachments = [
      {
        type: 'application/pdf',
        name: filename,
        content: attachment,
      },
    ];

    const learningPathEnrollmentCertificateTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATE,
    );
    const contentHtml = learningPathEnrollmentCertificateTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, attachments, sendAt);
  }

  async notifyLearningPathEnrollmentCompleteAndCertificate(
    email: string,
    payload: MailPayloadLearningPathEnrollmentCompleteAndCertificateParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const {
      learningPathName,
      certificateUrl,
      certificatePDFUrl,
      certificateCode,
      fullName,
      sendAt,
      refName = '',
    } = payload;
    const { nameEng, domain, fqdn, notificationConfig } = organization;

    const response = await this.httpService.get(certificatePDFUrl, {
      responseType: 'arraybuffer',
    });
    const attachment = Buffer.from(response.data, 'binary').toString('base64');
    const filename = `${certificateCode}.pdf`;

    let subject = `แจ้งผลการเรียนจบแผนการเรียนรู้ “${learningPathName}” | ${nameEng}`;
    if (refName) {
      subject = `แจ้งผลการเรียนจบแผนการเรียนรู้ “${learningPathName}” และได้รับประกาศนียบัตร ${refName} | ${nameEng}`;
    }

    const mainURL = this.getMainUrl(domain, fqdn);

    const contentVariables = {
      learningPathName,
      fullName,
      certificateUrl,
      mainURL,
      refName: refName || '',
    };

    const attachments = [
      {
        type: 'application/pdf',
        name: filename,
        content: attachment,
      },
    ];

    const learningPathEnrollmentCompleteAndCertificateTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.LEARNING_PATH_ENROLLMENT_COMPLETE_AND_CERTIFICATE,
    );
    const contentHtml = learningPathEnrollmentCompleteAndCertificateTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, attachments, sendAt);
  }

  async notifyPromoteContent(
    email: string,
    payload: MailPayloadPromoteContentParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const { title, description, contents, fullName, url, textButton } = payload;
    const { domain, fqdn, notificationConfig } = organization;

    const mainHostUrl = this.getMainUrl(domain, fqdn);

    const contentVariables = {
      title,
      description,
      contents: contents
        .map((content) => ({
          ...content,
          url: `${mainHostUrl}/${content.url}`,
        }))
        .reduce((acc, cur) => {
          const size = acc.length;
          const last = acc[size - 1] ?? [];
          if (size === 0 || last.length === 2) {
            acc.push([cur]);
            return acc;
          }

          last.push(cur);
          acc[size - 1] = last;
          return acc;
        }, []),
      fullName,
      url: `${mainHostUrl}/${url}`,
      textButton,
    };

    const promoteNotificationTemplate = EmailTemplateFactory.createTemplate(TemplateNameEnum.PROMOTE_CONTENT);
    const contentHtml = promoteNotificationTemplate.build(contentVariables);
    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, title, notificationConfig);
  }

  async notifyFirstSetPassword(
    email: string,
    payload: MailPayloadFirstPasswordParams,
    organization: OrganizationParams,
  ) {
    const { nameEng, notificationConfig } = organization;
    const { fullName, username, url, loginType, planPackageLicenseTextList, sendAt } = payload;
    const instructionBanner =
      loginType === LoginEmailTypeEnum.LOCAL
        ? `${this.environment.s3BucketUrl}/assets/new_user_instruction_v2.png`
        : `${this.environment.s3BucketUrl}/assets/citizenId_instruction.png`;
    const subject = `ยินดีต้อนรับสู่ ${nameEng} - ตั้งรหัสผ่านเพื่อเริ่มต้นใช้งานบัญชีของคุณ`;

    const contentVariables = {
      fullName,
      url,
      organizationName: nameEng,
      instructionBanner,
      username,
      planPackageLicenseTextList,
    };

    const firstPasswordTemplate = EmailTemplateFactory.createTemplate(TemplateNameEnum.FIRST_PASSWORD);
    const contentHtml = firstPasswordTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, [], sendAt);
  }

  async notifyAssignPlanPackageLicenseExistingUser(
    email: string,
    payload: MailPayloadAssignPlanPackageLicenseExistingUserParams,
    organization: OrganizationParams,
  ): Promise<void> {
    const { fullName, planPackageLicenseTextList } = payload;
    const { nameEng, notificationConfig, domain, fqdn } = organization;
    const subject = `แพ็กเกจของคุณสามารถใช้งานได้ | ${nameEng}`;

    const mainURL = this.getMainUrl(domain, fqdn);
    const url = `${mainURL}/login`;

    const contentVariables = {
      url,
      fullName,
      planPackageLicenseTextList,
    };

    const enrollmentCancelTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.ASSIGN_PLAN_PACKAGE_LICENSE_EXISTING_USER,
    );
    const contentHtml = enrollmentCancelTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig);
  }

  async notifyPlanPackageLicenseExpired(
    email: string,
    payload: MailPayloadPlanPackageLicenseExpiredParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void> {
    const { fullName, planPackageLicenseTextList } = payload;
    const { nameEng, notificationConfig } = organization;
    const subject = `แพ็กเกจของคุณไม่สามารถใช้งานได้ | ${nameEng}`;

    const contentVariables = {
      fullName,
      planPackageLicenseTextList,
    };

    const enrollmentCancelTemplate = EmailTemplateFactory.createTemplate(TemplateNameEnum.PLAN_PACKAGE_LICENSE_EXPIRED);
    const contentHtml = enrollmentCancelTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, [], sendAt);
  }

  private async buildEmailHtmlWithBaseTemplate(contentHtml: string, organization: OrganizationParams) {
    const { appearance, email } = organization.themeConfig;
    const { colorPrimary } = appearance.theme;
    let { logo } = appearance;
    const { emailSignature, emailBanner } = email;
    const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);
    logo = await storageRepository.getFileUrl(logo);
    const mainBanner = await storageRepository.getFileUrl(emailBanner);
    const shadowImageUrl = await storageRepository.getFileUrl('assets/header-shadow.png');
    const theme = {
      logo,
      mainBanner,
      colorPrimary,
      emailSignature,
      shadowImageUrl,
    };

    const baseTemplateBuilder = BaseTemplateBuilder.getInstance();
    const template = baseTemplateBuilder.build(theme, contentHtml);

    return template;
  }

  private validateConfigurationAndSendEmail(
    renderedTemplate: string,
    targetEmail: string | string[],
    subject: string,
    notificationConfig: Nullable<OrganizationNotificationConfigParams>,
    attachments: MailAttachmentParams[] = [],
    sendAt?: Date,
  ): void {
    if (!notificationConfig?.isActive) return;
    this.sendEmail(notificationConfig, renderedTemplate, targetEmail, subject, attachments, sendAt);
  }

  private sendEmail(
    notificationConfig: OrganizationNotificationConfigParams,
    renderedTemplate: string,
    targetEmail: string | string[],
    subject: string,
    attachments: MailAttachmentParams[] = [],
    sendAt?: Date,
  ) {
    const { senderEmail, senderName, key: keyEmailProvider } = notificationConfig;
    const receiverEmails: string[] = Array.isArray(targetEmail) ? targetEmail : [targetEmail];
    const payload: EmailNotificationParams = {
      application: UserNotificationApplicationEnum.LMS,
      keyEmailProvider,
      senderEmail,
      senderName,
      receiverEmails,
      payload: {
        message: renderedTemplate,
        subject,
        attachments: attachments ?? [],
      },
      sendAt,
    };

    this.sendEmailNotification(payload);
  }

  private getMainUrl(domain: string, fqdn: string) {
    const mainURL = `${this.environment.clientProtocol}://${domain}.${fqdn}`;

    return mainURL;
  }
  async notifyAchievementComplete(
    email: string,
    payload: MailPayloadAchievementAndBadgeCompleteParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void> {
    const { courseName, fullName, achievementList, courseCode, enrollmentId, attachments } = payload;
    const { domain, fqdn, notificationConfig } = organization;

    const achievementName = achievementList.map((achievement) => achievement.name).join(', ');

    const subject = `แจ้งผลการได้รับความสำเร็จการเรียน “${achievementName}” สำหรับหลักสูตร “${courseName}”`;

    const mainURL = this.getMainUrl(domain, fqdn);
    const url = `${mainURL}/courses/${courseCode}?enrollmentId=${enrollmentId}&activeTab=achievement`;

    const contentVariables: AchievementCompletedParams = {
      fullName,
      achievementName,
      courseName,
      url,
      achievementList: achievementList.map((achievement) => {
        return {
          name: achievement.name,
          badges: achievement.badges,
          certificate: achievement?.certificate?.name,
        };
      }),
    };

    const AchievementCompleteTemplate = EmailTemplateFactory.createTemplate(TemplateNameEnum.ACHIEVEMENT_COMPLETED);
    const contentHtml = AchievementCompleteTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, attachments, sendAt);
  }

  async notifyAchievementAdditionalComplete(
    email: string,
    payload: MailPayloadAchievementAndBadgeCompleteParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void> {
    const { courseName, fullName, achievementList, courseCode, enrollmentId, attachments } = payload;
    const { domain, fqdn, notificationConfig } = organization;

    const achievementName = achievementList.map((achievement) => achievement.name).join(', ');

    const subject = `แจ้งผลการได้รับความสำเร็จการเรียนเพิ่ม “${achievementName}” สำหรับหลักสูตร “${courseName}”`;

    const mainURL = this.getMainUrl(domain, fqdn);
    const url = `${mainURL}/courses/${courseCode}?enrollmentId=${enrollmentId}&activeTab=achievement`;

    const contentVariables: AchievementAdditionalCompletedParams = {
      fullName,
      achievementName,
      courseName,
      url,
      achievementList: achievementList.map((achievement) => ({
        name: achievement.name,
        badges: achievement.badges,
        certificate: achievement?.certificate?.name,
      })),
    };

    const AchievementCompleteTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.ACHIEVEMENT_ADDITIONAL_COMPLETED,
    );
    const contentHtml = AchievementCompleteTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, attachments, sendAt);
  }

  async notifyBadgeAdditionalComplete(
    email: string,
    payload: MailPayloadAchievementAndBadgeCompleteParams,
    organization: OrganizationParams,
    sendAt?: Date,
  ): Promise<void> {
    const { courseName, fullName, achievementList, courseCode, enrollmentId } = payload;
    const { domain, fqdn, notificationConfig } = organization;

    const badgeName = Array.from(
      new Set(achievementList.flatMap((achievement) => achievement.badges.map((badge) => badge.name))),
    ).join(', ');

    const subject = `แจ้งผลการได้รับสัญลักษณ์รางวัลเพิ่ม “${badgeName}” สำหรับหลักสูตร “${courseName}”`;

    const mainURL = this.getMainUrl(domain, fqdn);
    const url = `${mainURL}/courses/${courseCode}?enrollmentId=${enrollmentId}&activeTab=achievement`;

    const contentVariables: BadgeAdditionalCompletedParams = {
      fullName,
      badgeName,
      courseName,
      url,
      achievementList: achievementList.map((achievement) => ({
        name: achievement.name,
        badges: achievement.badges,
      })),
    };

    const AchievementCompleteTemplate = EmailTemplateFactory.createTemplate(
      TemplateNameEnum.BADGE_ADDITIONAL_COMPLETED,
    );
    const contentHtml = AchievementCompleteTemplate.build(contentVariables);

    const renderedTemplate = await this.buildEmailHtmlWithBaseTemplate(contentHtml, organization);

    this.validateConfigurationAndSendEmail(renderedTemplate, email, subject, notificationConfig, [], sendAt);
  }
}
