import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import {
  IMessageBrokerServiceAdaptor,
  INotificationSchedulerTransactionMessageBrokerServiceAdaptor,
} from '@domains/interfaces/infrastructures/adapters/messageBroker.interface';
import {
  ICreateEmailNotificationUseCase,
  ICreateInApplicationNotificationUseCase,
  ICreateSchedulerTransactionNotificationUseCase,
} from '@domains/interfaces/usecases/notification.interface';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';

import { DataType } from '@core/constant';
import {
  CreateEmailNotificationDto,
  CreateInApplicationNotificationContentDto,
} from '@domains/constants/dto/notification.dto';
import { MessageBrokerEventEnums } from '@infrastructures/configs/messageBroker/constants';
import { OnEvent } from '@nestjs/event-emitter';
import { <PERSON><PERSON><PERSON><PERSON>, MessageBody } from '@presenters/consumers/decorator/index';
import { handleMessageConsumerError } from '@presenters/consumers/middleware/exception.middleware';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';
import { ILogger } from '@infrastructures/services/logger/interfaces';
@Injectable()
export class NotificationConsumersService implements OnModuleInit {
  private readonly inApplicationNotificationName: string = 'notification:in-application';
  private readonly emailNotificationQueueName: string = 'notification:email';
  private readonly notificationSchedularTransactionQueueName: string = 'notification:scheduler-transaction';

  constructor(
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerServiceAdaptor)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerServiceAdaptor,

    @Inject(NotificationDIToken.CreateInApplicationNotificationUseCase)
    private readonly createNotificationInApplicationUseCase: ICreateInApplicationNotificationUseCase,
    @Inject(NotificationDIToken.CreateEmailNotificationUseCase)
    private readonly createEmailNotificationUseCase: ICreateEmailNotificationUseCase,
    @Inject(NotificationDIToken.CreateSchedulerTransactionNotificationUseCase)
    private readonly createEmailSchedulerTransactionNotificationUseCase: ICreateSchedulerTransactionNotificationUseCase,
    @Inject(InfrastructuresAdaptersDIToken.NotificationSchedulerTransactionMessageBrokerServiceAdaptor)
    private readonly notificationSchedulerTransactionMessageBrokerServiceAdaptor: INotificationSchedulerTransactionMessageBrokerServiceAdaptor,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  onModuleInit() {
    this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnums.Connected, { async: true })
  onInitialConsumeMessageBroker() {
    this.logger.log('Initial consumers');
    this.messageBrokerServiceAdaptor.consumers(
      this.inApplicationNotificationName,
      this.handleInApplicationMessageConsumer.bind(this),
      { noAck: false },
    );
    this.messageBrokerServiceAdaptor.consumers(
      this.emailNotificationQueueName,
      this.handleEmailMessageConsumer.bind(this),
      { noAck: false },
    );
    this.notificationSchedulerTransactionMessageBrokerServiceAdaptor.consumer(
      this.notificationSchedularTransactionQueueName,
      this.handleNotificationSchedulerTransactionMessageConsumer.bind(this),
    );
  }

  @ConsumerHandler({ isAck: true, onError: handleMessageConsumerError })
  async handleInApplicationMessageConsumer(
    @MessageBody(CreateInApplicationNotificationContentDto) body: CreateInApplicationNotificationContentDto,
  ): Promise<void> {
    const createInApplicationNotificationParams = body.toJson();
    await this.createNotificationInApplicationUseCase.execute(createInApplicationNotificationParams);
  }

  @ConsumerHandler({ isAck: true, onError: handleMessageConsumerError })
  async handleEmailMessageConsumer(@MessageBody(CreateEmailNotificationDto) body: CreateEmailNotificationDto) {
    const createEmailNotificationParams = body.toJson();
    await this.createEmailNotificationUseCase.execute(createEmailNotificationParams);
  }

  @ConsumerHandler({ isAck: true, onError: handleMessageConsumerError })
  async handleNotificationSchedulerTransactionMessageConsumer(
    @MessageBody(DataType.StringType) id: string,
  ): Promise<void> {
    await this.createEmailSchedulerTransactionNotificationUseCase.execute(id);
  }
}
