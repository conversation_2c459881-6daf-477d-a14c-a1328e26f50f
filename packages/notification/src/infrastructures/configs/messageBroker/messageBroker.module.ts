import { Module } from '@nestjs/common';
import { EnvironmentsModule } from '@infrastructures/configs/environments/environments.module';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { MessageBrokerInstance } from './instance/messageBrokerInstance';
import { MessageBrokerEventService } from './instance/messageBrokerEvent.service';
import { LoggerServiceModule } from '@applications/module/infrastructure/services/logger.service.module';
@Module({
  imports: [EnvironmentsModule, LoggerServiceModule],
  providers: [
    {
      provide: InfrastructuresConfigDIToken.MessageBrokerInstance,
      useClass: MessageBrokerInstance,
    },
    {
      provide: InfrastructuresConfigDIToken.MessageBrokerEventService,
      useClass: MessageBrokerEventService,
    },
  ],
  exports: [InfrastructuresConfigDIToken.MessageBrokerInstance],
})
export class MessageBrokerModule {}
