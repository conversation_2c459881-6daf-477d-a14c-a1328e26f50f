import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { Module } from '@nestjs/common';
import { EmailServiceAdaptor } from './emailAdapter.service';
import { AwsSesService } from './emailProvider/awsSes.service';
import { ManDrillService } from './emailProvider/mandrill.service';
import { SendGridService } from './emailProvider/sendGrid.service';
import { LoggerServiceModule } from '@applications/module/infrastructure/services/logger.service.module';

@Module({
  imports: [LoggerServiceModule],
  providers: [
    {
      provide: InfrastructuresAdaptersDIToken.EmailServiceAdaptor,
      useClass: EmailServiceAdaptor,
    },
    {
      provide: InfrastructuresAdaptersDIToken.AwsSesService,
      useClass: AwsSesService,
    },
    {
      provide: InfrastructuresAdaptersDIToken.SendGridService,
      useClass: SendGridService,
    },
    {
      provide: InfrastructuresAdaptersDIToken.ManDrillService,
      useClass: ManDrillService,
    },
  ],
  exports: [InfrastructuresAdaptersDIToken.EmailServiceAdaptor],
})
export class EmailAdaptorModule {}
