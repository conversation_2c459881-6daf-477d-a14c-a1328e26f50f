import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';
import { Code } from '@core/exception/code';
import { Exception } from '@core/exception/exception';

import {
  MailAttachmentParams,
  MailAttachmentSendGridParams,
  MailParams,
  SendGridConfigParams,
} from '@domains/constants/types/emailProvider.type';
import { ISendGridService } from '@domains/interfaces/infrastructures/email/sendGrid.interface';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { Inject, Injectable } from '@nestjs/common';
import sendGridMail from '@sendgrid/mail';

@Injectable()
export class SendGridService implements ISendGridService {
  constructor(
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async sendEmail(params: MailParams, config: SendGridConfigParams): Promise<void> {
    const { receiverEmails, senderEmail, senderName, data, subject, attachments } = params;
    const { apiKey, templateId } = config;

    const mailDataList = receiverEmails.map((toEmail) => ({
      templateId,
      to: toEmail,
      from: {
        email: senderEmail,
        name: senderName,
      },
      dynamicTemplateData: {
        EMAIL_TEMPLATE: data,
        SUBJECT: subject,
      },
      attachments: this.buildAttachments(attachments),
    }));

    const emailProvider = sendGridMail;
    emailProvider.setApiKey(apiKey);

    try {
      await emailProvider.send(mailDataList);
      this.logger.log(`Send email ${receiverEmails.join(',')}, by send grid`);
      this.logger.log(`Send = ${receiverEmails.length} mails, by send grid`);
    } catch (error) {
      this.logger.error(Code.SERVICE_UNAVAILABLE.message, {
        code: Code.SERVICE_UNAVAILABLE,
        message: `${Code.SERVICE_UNAVAILABLE.message}, Mailer`,
        data: error,
      });
      throw Exception.new({ code: Code.SERVICE_UNAVAILABLE, message: 'Send Grid service is error', data: error });
    }
  }

  private buildAttachments(attachments: MailAttachmentParams[]): MailAttachmentSendGridParams[] {
    return attachments.map((attachment) => ({
      filename: attachment.name,
      content: attachment.content,
      type: attachment.type,
      disposition: 'attachment',
    }));
  }
}
