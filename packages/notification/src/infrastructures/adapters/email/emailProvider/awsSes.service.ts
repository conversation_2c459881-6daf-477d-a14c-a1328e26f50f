/* eslint-disable @typescript-eslint/naming-convention */
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';
import { SendRawEmailCommand, SendRawEmailCommandInput, SESClient } from '@aws-sdk/client-ses';
import { Code } from '@core/exception/code';
import { Exception } from '@core/exception/exception';
import {
  AwsSesConfigParams,
  MailAttachmentAwsSesParams,
  MailAttachmentParams,
  MailParams,
} from '@domains/constants/types/emailProvider.type';
import { IAwsSesService } from '@domains/interfaces/infrastructures/email/awsSes.interface';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { Inject, Injectable } from '@nestjs/common';

@Injectable()
export class AwsSesService implements IAwsSesService {
  constructor(
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}
  async sendEmail(params: MailPara<PERSON>, config: AwsSesConfigParams): Promise<void> {
    const { senderEmail, receiverEmails, subject, data, attachments } = params;
    const rawMessage = this.createRawMessage(
      senderEmail,
      receiverEmails,
      subject,
      data,
      this.buildAttachments(attachments),
    );

    const input: SendRawEmailCommandInput = {
      RawMessage: {
        Data: Buffer.from(rawMessage),
      },
    };

    const client = new SESClient({
      apiVersion: '2010-12-01',
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });

    try {
      const command = new SendRawEmailCommand(input);
      const result = await client.send(command);
      this.logger.log(`Send email ${receiverEmails.join(',')}, by aws ses`);
      this.logger.log('Send = 1 mails, by aws ses', result.MessageId);
      client.destroy();
    } catch (error) {
      this.logger.log(Code.SERVICE_UNAVAILABLE.message, {
        code: Code.SERVICE_UNAVAILABLE,
        message: `${Code.SERVICE_UNAVAILABLE.message}, Mailer`,
        data: error,
      });
      client.destroy();
      throw Exception.new({ code: Code.SERVICE_UNAVAILABLE, message: 'AWS service is error', data: error });
    }
  }

  private createRawMessage(
    source: string,
    toAddress: string[],
    subject: string,
    htmlContent: string,
    attachments: MailAttachmentAwsSesParams[],
  ): string {
    const htmlContentBase64 = Buffer.from(htmlContent).toString('base64');
    const message = [
      `From: ${source}\n`,
      `To: ${toAddress.join(',')}\n`,
      `Subject: ${subject}\n`,
      `MIME-Version: 1.0\n`,
      `Content-type: multipart/mixed; boundary="BOUNDARY"\n\n`,
      `--BOUNDARY\n`,
      `Content-type: text/html; charset=UTF-8\n`,
      `Content-transfer-encoding: base64\n\n`,
      `${htmlContentBase64}\n\n`,
    ];

    if (attachments.length) {
      for (const attachment of attachments) {
        message.push(`--BOUNDARY\n`);
        message.push(`Content-type: ${attachment.contentType}\n`);
        message.push(`Content-disposition: attachment; filename=${attachment.filename}\n`);
        message.push(`Content-transfer-encoding: base64\n\n`);
        message.push(`${attachment.content.toString()}\n\n`);
      }
    }

    message.push(`--BOUNDARY--\n`);

    return message.join('');
  }

  private buildAttachments(attachments: MailAttachmentParams[]): MailAttachmentAwsSesParams[] {
    return attachments.map((attachment) => ({
      filename: attachment.name,
      content: attachment.content,
      contentType: attachment.type,
    }));
  }
}
