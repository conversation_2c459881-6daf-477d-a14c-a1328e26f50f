import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';
import { Code } from '@core/exception/code';
import { Exception } from '@core/exception/exception';
import { MailParams, ManDrillConfigParams } from '@domains/constants/types/emailProvider.type';
import { IManDrillService } from '@domains/interfaces/infrastructures/email/manDrill.interface';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { Inject, Injectable } from '@nestjs/common';
import mandrill from 'mandrill-api';

@Injectable()
export class ManDrillService implements IManDrillService {
  constructor(
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async sendEmail(params: MailParams, config: ManDrillConfigParams): Promise<void> {
    const name = 'EMAIL_TEMPLATE';
    const { senderEmail, receiverEmails, data, subject, attachments } = params;
    const payload = {
      template_name: name,
      template_content: {},
      message: {
        from_email: senderEmail,
        to: receiverEmails.map((toEmail) => ({
          email: toEmail,
          type: 'to',
        })),
        global_merge_vars: {
          name,
          content: data,
        },
        attachments: attachments || [],
        subject,
      },
      async: true,
    };

    const { apiKey } = config;
    const client = new mandrill.Mandrill(apiKey);

    try {
      this.logger.log(`Send email ${receiverEmails.join(',')}, by Mandrill`);
      const emailProvider = client.messages;
      await new Promise((resolve, reject) => {
        emailProvider.sendTemplate(
          payload,
          (result: unknown[]) => {
            this.logger.log(`Send = ${result.length} mails, by Mandrill`);
            resolve(result);
          },
          (error: unknown) => reject(error),
        );
      });
    } catch (error) {
      this.logger.log(Code.SERVICE_UNAVAILABLE.message, {
        code: Code.SERVICE_UNAVAILABLE,
        message: `${Code.SERVICE_UNAVAILABLE.message}, Mailer`,
        data: error,
      });
      throw Exception.new({ code: Code.SERVICE_UNAVAILABLE, message: 'Mandrill service is error', data: error });
    }
  }
}
