import { Module } from '@nestjs/common';

import Logger from '@infrastructures/services/logger/logger';

import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

@Module({
  providers: [
    {
      provide: InfrastructuresServiceDIToken.LoggerApplication,
      useClass: Logger,
    },
  ],
  exports: [InfrastructuresServiceDIToken.LoggerApplication],
})
export class LoggerServiceModule {}
