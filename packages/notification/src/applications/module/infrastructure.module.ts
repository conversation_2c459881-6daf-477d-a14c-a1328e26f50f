import { Module } from '@nestjs/common';
import { AdaptersModule } from './infrastructure/adapters.module';
import { ConfigsModule } from './infrastructure/configs.module';
import { LoggerServiceModule } from '@applications/module/infrastructure/services/logger.service.module';

@Module({
  imports: [ConfigsModule, AdaptersModule, LoggerServiceModule],
  exports: [ConfigsModule, AdaptersModule, LoggerServiceModule],
})
export class InfrastructureModule {}
