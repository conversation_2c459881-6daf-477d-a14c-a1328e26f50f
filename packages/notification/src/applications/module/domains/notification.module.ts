import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { DbInstanceParams } from '@domains/constants/types/infrastructures/database.type';

import { INotificationDataMapper } from '@domains/interfaces/infrastructures/adapters/dataMapper/notificationDataMapper.interface';

import { NotificationDataMapper } from '@infrastructures/adapters/dataMapper/notification/notification.dataMapper';
import { EmailAdaptorModule } from '@infrastructures/adapters/email/emailAdapter.module';
import { NotificationEventService } from '@infrastructures/adapters/eventEmitter/notificatonEvent.service';

import { InfrastructureModule } from '@applications/module/infrastructure.module';
import { NotificationService } from '@domains/services/notification.service';
import { NotificationRepository } from '@infrastructures/persistence/repositories/notification/notification.repository';
import { PersistenceTransactionModule } from '@infrastructures/persistence/transactions/transactions.module';
import { Module, Provider } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { NotificationConsumersService } from '@presenters/consumers/notification/notificationConsumers.service';
import { NotificationInApplicationController } from '@presenters/controllers/notification/notification.controller';
import { CreateEmailNotificationUseCase } from '@usecases/createEmailNotification.usecase';
import { CreateInApplicationNotificationUseCase } from '@usecases/createInApplicationNotification.usecase';
import { CreateSchedulerTransactionNotificationUseCase } from '@usecases/createSchedulerTransactionNotification.usecase';
import { EmailProviderModule } from './emailProvider.module';

// Data Mapper
const adaptersProvider: Provider[] = [
  {
    provide: NotificationDIToken.NotificationDataMapper,
    useClass: NotificationDataMapper,
  },
  {
    provide: NotificationDIToken.NotificationEventService,
    useClass: NotificationEventService,
  },
];

// Repository
const persistenceProviders: Provider[] = [
  {
    provide: NotificationDIToken.NotificationRepository,
    inject: [InfrastructuresConfigDIToken.DbInstance, NotificationDIToken.NotificationDataMapper],
    useFactory: (db: DbInstanceParams, notificationDataMapper: INotificationDataMapper) =>
      new NotificationRepository(db, notificationDataMapper),
  },
];

const serviceProvider: Provider[] = [
  {
    provide: NotificationDIToken.NotificationService,
    useClass: NotificationService,
  },
];

const usecaseProviders: Provider[] = [
  {
    provide: NotificationDIToken.CreateInApplicationNotificationUseCase,
    useClass: CreateInApplicationNotificationUseCase,
  },
  {
    provide: NotificationDIToken.CreateEmailNotificationUseCase,
    useClass: CreateEmailNotificationUseCase,
  },
  {
    provide: NotificationDIToken.CreateSchedulerTransactionNotificationUseCase,
    useClass: CreateSchedulerTransactionNotificationUseCase,
  },
];

const presentersProvider: Provider[] = [NotificationConsumersService];

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    EmailProviderModule,
    EmailAdaptorModule,
    PersistenceTransactionModule,
    InfrastructureModule,
  ],
  controllers: [NotificationInApplicationController],
  providers: [
    ...adaptersProvider,
    ...persistenceProviders,
    ...serviceProvider,
    ...usecaseProviders,
    ...presentersProvider,
  ],
  exports: [],
})
export class NotificationModule {}
