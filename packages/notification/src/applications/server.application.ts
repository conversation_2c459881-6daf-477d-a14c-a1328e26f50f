import helmet from '@fastify/helmet';
import fastifyCsrf from '@fastify/csrf-protection';
import { AppModule } from '../app.module';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { InfrastructuresConfigDIToken } from '@applications/di/infrastructures/configs/config';
import { IEnvironmentService } from '@domains/interfaces/infrastructures/configs/environment.interface';
import Logger from '@infrastructures/services/logger/logger';
import { ILogger } from '@infrastructures/services/logger/interfaces';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

export class ServerApplication {
  private static instance: ServerApplication;

  public static new() {
    if (!ServerApplication.instance) {
      ServerApplication.instance = new ServerApplication();
    }

    return ServerApplication.instance;
  }

  async run(): Promise<void> {
    const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter(), {
      logger: new Logger('Notification'),
    });
    const environmentService = app.get<symbol, IEnvironmentService>(InfrastructuresConfigDIToken.EnvironmentService);

    this.initialApplication(app);

    await this.initialSecurity(app);

    const { port } = environmentService;
    await app.listen(port, '0.0.0.0');
  }

  private initialApplication(app: NestFastifyApplication): void {
    const LoggerApplication = app.get<symbol, ILogger>(InfrastructuresServiceDIToken.LoggerApplication);
    LoggerApplication.setContext('Notification');
    app.useLogger(LoggerApplication);
    app.useGlobalPipes(new ValidationPipe());
  }

  private async initialSecurity(app: NestFastifyApplication) {
    app.enableCors({ origin: '*' });

    await app.register(helmet);
    await app.register(fastifyCsrf);
  }
}
