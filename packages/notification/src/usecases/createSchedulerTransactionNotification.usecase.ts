import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { ICreateSchedulerTransactionNotificationUseCase } from '@domains/interfaces/usecases/notification.interface';

import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { Inject, Injectable } from '@nestjs/common';

import { EmailProviderDIToken } from '@applications/di/domain/emailProvider.di';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';
import { Code } from '@core/exception/code';
import { Exception } from '@core/exception/exception';
import { NotificationMethodEnum, NotificationStatusEnum } from '@domains/constants/enums/notification.enum';
import { IEmailServiceAdaptor } from '@domains/interfaces/infrastructures/email/emailServiceAdaptor.interface';
import { INotificationEventService } from '@domains/interfaces/infrastructures/eventEmitter/notification.interface';
import { IEmailProviderRepository } from '@domains/interfaces/infrastructures/persistance/repositories/emailProvider.interface';
import { INotificationRepository } from '@domains/interfaces/infrastructures/persistance/repositories/notification.interface';
import { IDatabaseTransactionService } from '@domains/interfaces/infrastructures/persistance/transaction/databaseTransaction.interface';
import { EmailNotificationPayload, InApplicationNotificationPayload } from '@domains/models/notification.model';
import { ILogger } from '@infrastructures/services/logger/interfaces';
@Injectable()
export class CreateSchedulerTransactionNotificationUseCase implements ICreateSchedulerTransactionNotificationUseCase {
  constructor(
    @Inject(NotificationDIToken.NotificationRepository)
    private readonly notificationRepository: INotificationRepository,
    @Inject(EmailProviderDIToken.EmailProviderRepository)
    private readonly emailProviderRepository: IEmailProviderRepository,
    @Inject(NotificationDIToken.NotificationEventService)
    private readonly notificationEventService: INotificationEventService,
    @Inject(InfrastructuresAdaptersDIToken.EmailServiceAdaptor)
    private readonly emailServiceAdaptor: IEmailServiceAdaptor,
    @Inject(InfrastructuresPersistenceDIToken.DatabaseTransaction)
    private readonly databaseTransaction: IDatabaseTransactionService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(id: string): Promise<void> {
    this.logger.log(`start send scheduler notification id ${id}`);
    const notification = await this.notificationRepository.findOne({ id });
    if (!notification) {
      throw Exception.new({ code: Code.ENTITY_NOT_FOUND_ERROR, message: ' Notification is not found', data: id });
    }

    if (notification.status === NotificationStatusEnum.SENT) {
      this.logger.log(`notification id (${notification.id} method: ${notification.method}) has bee send already`);
      return;
    }

    await this.databaseTransaction.runTransaction(async (session) => {
      notification.status = NotificationStatusEnum.SENT;
      await this.notificationRepository.save(notification, { session });

      if (notification.method === NotificationMethodEnum.IN_APPLICATION) {
        const payload = notification.payload as InApplicationNotificationPayload;
        this.notificationEventService.sendInApplicationNotification({
          groupId: payload.groupId,
          application: notification.application,
        });
        return;
      }

      if (notification.method === NotificationMethodEnum.EMAIL) {
        const payload = notification.payload as EmailNotificationPayload;
        const emailProvider = await this.emailProviderRepository.findOne({ id: payload.emailProviderId });

        if (!emailProvider) {
          throw Exception.new({
            code: Code.ENTITY_NOT_FOUND_ERROR,
            message: 'Email Provider not found',
            data: payload.emailProviderId,
          });
        }

        const { provider, config } = emailProvider;
        const mailParams = {
          receiverEmails: [payload.receiverEmail],
          senderEmail: payload.senderEmail,
          senderName: payload.senderName,
          data: payload.body.message,
          subject: payload.body.subject,
          attachments: payload.body.attachments ?? [],
        };
        await this.emailServiceAdaptor.sendEmail(provider, mailParams, config);
      }
    });
  }
}
