import { Injectable, Inject } from '@nestjs/common';
import { ICreateEmailNotificationUseCase } from '@domains/interfaces/usecases/notification.interface';
import {
  CreateEmailNotificationParams,
  EmailNotificationPayloadParams,
} from '@domains/constants/types/notification.type';
import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { INotificationRepository } from '@domains/interfaces/infrastructures/persistance/repositories/notification.interface';
import { NotificationMethodEnum, NotificationStatusEnum } from '@domains/constants/enums/notification.enum';
import { Notification } from '@domains/models/notification.model';
import { EmailProviderDIToken } from '@applications/di/domain/emailProvider.di';
import { IEmailProviderRepository } from '@domains/interfaces/infrastructures/persistance/repositories/emailProvider.interface';
import { isNull } from '@domains/utils/validate.util';
import { Exception } from '@core/exception/exception';
import { Code } from '@core/exception/code';

import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { IEmailServiceAdaptor } from '@domains/interfaces/infrastructures/email/emailServiceAdaptor.interface';
import { INotificationSchedulerTransactionMessageBrokerServiceAdaptor } from '@domains/interfaces/infrastructures/adapters/messageBroker.interface';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { IDatabaseTransactionService } from '@domains/interfaces/infrastructures/persistance/transaction/databaseTransaction.interface';
import { INotificationService } from '@domains/interfaces/services/notification.service.interface';
import { GenericID } from '@iso/constants/commonTypes';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';
import { ILogger } from '@infrastructures/services/logger/interfaces';

@Injectable()
export class CreateEmailNotificationUseCase implements ICreateEmailNotificationUseCase {
  constructor(
    @Inject(NotificationDIToken.NotificationRepository)
    private readonly notificationRepository: INotificationRepository,
    @Inject(EmailProviderDIToken.EmailProviderRepository)
    private readonly emailProviderRepository: IEmailProviderRepository,
    @Inject(InfrastructuresAdaptersDIToken.EmailServiceAdaptor)
    private readonly emailServiceAdaptor: IEmailServiceAdaptor,
    @Inject(InfrastructuresAdaptersDIToken.NotificationSchedulerTransactionMessageBrokerServiceAdaptor)
    private readonly notificationSchedulerTransactionMessageBrokerServiceAdaptor: INotificationSchedulerTransactionMessageBrokerServiceAdaptor,
    @Inject(InfrastructuresPersistenceDIToken.DatabaseTransaction)
    private readonly databaseTransaction: IDatabaseTransactionService,
    @Inject(NotificationDIToken.NotificationService) private readonly notificationService: INotificationService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: CreateEmailNotificationParams): Promise<void> {
    const { application, keyEmailProvider, senderEmail, senderName, receiverEmails, payload, sendAt } = params;

    const emailProvider = await this.emailProviderRepository.findOne({ key: keyEmailProvider });

    if (isNull(emailProvider)) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'Email Provider not found',
        data: keyEmailProvider,
      });
    }

    const isScheduler = this.notificationService.validateSendAtIsSchedulerNotification(sendAt);
    const notifications = [];
    const notificationIds: GenericID[] = [];

    await this.databaseTransaction.runTransaction(async (session, commit, reverse) => {
      for (const receiverEmail of receiverEmails) {
        const emailPayload: EmailNotificationPayloadParams = {
          emailProviderId: emailProvider.id,
          senderEmail,
          senderName,
          receiverEmail,
          body: payload,
        };

        const notification = await Notification.new({
          application,
          method: NotificationMethodEnum.EMAIL,
          status: isScheduler ? NotificationStatusEnum.SCHEDULER : NotificationStatusEnum.SENT,
          payload: emailPayload,
          sendAt,
        });

        notificationIds.push(notification.id);
        notifications.push({
          insertOne: notification,
        });

        if (notifications.length > 1000) {
          await this.notificationRepository.bulkWrite(notifications, { session });
          notifications.splice(0, notifications.length);
        }
      }

      if (notifications.length) {
        await this.notificationRepository.bulkWrite(notifications, { session });
      }

      await commit();

      if (isScheduler) {
        for (const id of notificationIds) {
          const isSuccess = await this.notificationSchedulerTransactionMessageBrokerServiceAdaptor.publish(
            id as string,
            sendAt,
          );
          if (!isSuccess) {
            await reverse();
            throw Exception.new({
              code: Code.MESSAGE_BROKER_SERVICE_UNAVAILABLE,
              message: 'Send scheduler transaction is error',
            });
          }
          this.logger.log('send email scheduler transaction id: ', id);
        }
      } else {
        const { provider, config } = emailProvider;
        const mailParams = {
          receiverEmails,
          senderEmail,
          senderName,
          data: payload.message,
          subject: payload.subject,
          attachments: payload.attachments ?? [],
        };
        await this.emailServiceAdaptor.sendEmail(provider, mailParams, config);
      }
    });
  }
}
