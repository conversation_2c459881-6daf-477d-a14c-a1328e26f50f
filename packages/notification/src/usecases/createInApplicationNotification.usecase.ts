import { Inject, Injectable } from '@nestjs/common';
import { NotificationDIToken } from '@applications/di/domain/notification.di';

import {
  CreateInApplicationNotificationParams,
  InApplicationNotificationBodyParams,
} from '@domains/constants/types/notification.type';
import { INotificationEventService } from '@domains/interfaces/infrastructures/eventEmitter/notification.interface';
import { ICreateInApplicationNotificationUseCase } from '@domains/interfaces/usecases/notification.interface';
import { INotificationRepository } from '@domains/interfaces/infrastructures/persistance/repositories/notification.interface';
import { Notification } from '@domains/models/notification.model';
import { NotificationMethodEnum, NotificationStatusEnum } from '@domains/constants/enums/notification.enum';
import { INotificationSchedulerTransactionMessageBrokerServiceAdaptor } from '@domains/interfaces/infrastructures/adapters/messageBroker.interface';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { Exception } from '@core/exception/exception';
import { Code } from '@core/exception/code';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { IDatabaseTransactionService } from '@domains/interfaces/infrastructures/persistance/transaction/databaseTransaction.interface';
import { INotificationService } from '@domains/interfaces/services/notification.service.interface';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';
import { ILogger } from '@infrastructures/services/logger/interfaces';
@Injectable()
export class CreateInApplicationNotificationUseCase implements ICreateInApplicationNotificationUseCase {
  constructor(
    @Inject(NotificationDIToken.NotificationEventService)
    private readonly notificationEventService: INotificationEventService,
    @Inject(NotificationDIToken.NotificationRepository)
    private readonly notificationRepository: INotificationRepository,
    @Inject(InfrastructuresAdaptersDIToken.NotificationSchedulerTransactionMessageBrokerServiceAdaptor)
    private readonly notificationSchedulerTransactionMessageBrokerServiceAdaptor: INotificationSchedulerTransactionMessageBrokerServiceAdaptor,
    @Inject(InfrastructuresPersistenceDIToken.DatabaseTransaction)
    private readonly databaseTransaction: IDatabaseTransactionService,
    @Inject(NotificationDIToken.NotificationService) private readonly notificationService: INotificationService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async execute(params: CreateInApplicationNotificationParams): Promise<void> {
    const { groupId, application, payload, sendAt } = params;

    const applicationPayload: InApplicationNotificationBodyParams = {
      groupId,
      body: payload,
    };

    const isScheduler = this.notificationService.validateSendAtIsSchedulerNotification(sendAt);
    const notificationStatus = isScheduler ? NotificationStatusEnum.SCHEDULER : NotificationStatusEnum.SENT;

    const notification = await Notification.new({
      application,
      method: NotificationMethodEnum.IN_APPLICATION,
      payload: applicationPayload,
      status: notificationStatus,
      sendAt,
    });

    await this.databaseTransaction.runTransaction(async (session, commit, reverse) => {
      await this.notificationRepository.save(notification, { session });
      await commit();

      if (isScheduler) {
        const isSuccess = await this.notificationSchedulerTransactionMessageBrokerServiceAdaptor.publish(
          notification.id as string,
          sendAt,
        );

        if (!isSuccess) {
          await reverse();
          throw Exception.new({
            code: Code.MESSAGE_BROKER_SERVICE_UNAVAILABLE,
            message: 'Send scheduler transaction is error',
          });
        }
        this.logger.log(`send in application scheduler transaction id: ${notification.id}`);
        return;
      }

      this.notificationEventService.sendInApplicationNotification({
        groupId,
        application,
      });
    });
  }
}
