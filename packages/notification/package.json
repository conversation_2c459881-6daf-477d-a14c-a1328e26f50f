{"name": "notification", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "packageManager": "pnpm@9.12.0", "scripts": {"clean": "rimraf node_modules", "clean:build": "<PERSON><PERSON><PERSON> dist", "build-check": "tsc --diagnostics --project tsconfig.build.json --noEmit", "build": "nest build swc", "format": "prettier --write --config ./node_modules/@lms/eslint-config/.prettierrc.json \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start -b swc", "dev": "NODE_ENV=dev nest start -b swc --watch", "release": "NODE_ENV=release nest start -b swc --watch", "master": "NODE_ENV=master nest start -b swc --watch", "watch-dev": "nest start -b swc --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "dev:migrate-status": "dotenv -e .env.dev -- cross-var \"echo DB_HOST = %DB_URL%\n echo DB_NAME = %DB_NAME%\" && NODE_ENV=dev migrate-mongo status", "release:migrate-status": "dotenv -e .env.release -- cross-var \"echo DB_HOST = %DB_URL%\n echo DB_NAME = %DB_NAME%\" && NODE_ENV=release migrate-mongo status", "master:migrate-status": "dotenv -e .env.master -- cross-var \"echo DB_HOST = %DB_URL%\n echo DB_NAME = %DB_NAME%\" && NODE_ENV=master migrate-mongo status", "dev:migrate-up": "dotenv -e .env.dev -- cross-var \"echo DB_HOST = %DB_URL%\n echo DB_NAME = %DB_NAME%\" && cli-confirm \"The DB_HOST in env is correct?(type y/n)\" && NODE_ENV=dev migrate-mongo up", "release:migrate-up": "dotenv -e .env.release -- cross-var \"echo DB_HOST = %DB_URL%\n echo DB_NAME = %DB_NAME%\" && cli-confirm \"The DB_HOST in env is correct?(type y/n)\" && NODE_ENV=release migrate-mongo up", "master:migrate-up": "dotenv -e .env.master -- cross-var \"echo DB_HOST = %DB_URL%\n echo DB_NAME = %DB_NAME%\" && cli-confirm \"The DB_HOST in env is correct?(type y/n)\" && NODE_ENV=master migrate-mongo up", "dev:migrate-down": "dotenv -e .env.dev -- cross-var \"echo DB_HOST = %DB_HOST%\n echo DB_NAME = %DB_NAME%\" && NODE_ENV=dev migrate-mongo down", "release:migrate-down": "dotenv -e .env.release -- cross-var \"echo DB_HOST = %DB_HOST%\n echo DB_NAME = %DB_NAME%\" && NODE_ENV=release migrate-mongo down", "master:migrate-down": "dotenv -e .env.master -- cross-var \"echo DB_HOST = %DB_HOST%\n echo DB_NAME = %DB_NAME%\" && NODE_ENV=master migrate-mongo down"}, "dependencies": {"@aws-sdk/client-ses": "3.758.0", "@fastify/csrf-protection": "6.4.1", "@fastify/helmet": "11.1.1", "@iso/constants": "workspace:*", "@nestjs/common": "9.2.1", "@nestjs/config": "^3.2.2", "@nestjs/core": "9.2.1", "@nestjs/event-emitter": "^2.0.4", "@nestjs/platform-fastify": "9.2.1", "@sendgrid/mail": "^7.2.2", "@types/mandrill-api": "^1.0.30", "amqplib": "^0.10.4", "aws-sdk": "^2.571.0", "class-transformer": "^0.5.1", "class-validator": "0.14.1", "cli-confirm": "^1.0.1", "cross-var": "^1.1.0", "dayjs": "1.11.10", "dd-trace": "5.24.0", "dotenv": "^16.4.5", "dotenv-cli": "^7.4.2", "mandrill-api": "^1.0.45", "migrate-mongo": "^11.0.0", "mongodb": "3.7.3", "ms": "2.1.3", "nest-winston": "1.10.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "11.0.2", "winston": "3.3.3"}, "devDependencies": {"@lms/eslint-config": "workspace:*", "@lms/typescript-config": "workspace:*", "@nestjs/cli": "10.4.7", "@nestjs/schematics": "10.2.3", "@nestjs/testing": "9.2.1", "@types/amqplib": "0.10.5", "@types/mongodb": "3.5.18", "@types/supertest": "2.0.12", "jest": "29.5.0", "supertest": "6.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "./", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s?$": ["@swc/jest"]}, "collectCoverageFrom": ["**/*.(t|j)s"], "modulePathIgnorePatterns": ["__mocks__"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"@applications/(.*)$": ["<rootDir>/src/applications/$1"], "@core/(.*)$": ["<rootDir>/src/core/$1"], "@domains/(.*)$": ["<rootDir>/src/domains/$1"], "@infrastructures/(.*)$": ["<rootDir>/src/infrastructures/$1"], "@usecases/(.*)$": ["<rootDir>/src/usecases/$1"], "@presenters/(.*)$": ["<rootDir>/src/presenters/$1"]}}}