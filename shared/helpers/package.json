{"name": "@iso/helpers", "version": "1.0.0", "description": "", "main": "dist/index.js", "private": true, "exports": {"./*": {"types": "./src/*.ts", "default": "./dist/*.js"}, "./columnSetting": {"types": "./src/columnSetting.ts", "default": "./dist/columnSetting.js"}, "./userNotification": {"types": "./src/userNotification.ts", "default": "./dist/userNotification.js"}}, "scripts": {"clean": "rimraf node_modules", "clean:build": "<PERSON><PERSON><PERSON> dist", "build-check": "rm -rf dist && tsc --diagnostics", "build:swc": "swc src --out-dir dist --strip-leading-paths --config-file ./build.swcrc", "build": "pnpm clean:build && pnpm build:swc", "test": "jest --bail --silent", "lint": "eslint \"src/**/*.ts\" --fix", "lint:debug": "eslint \"src/**/*.ts\" --fix --debug", "format": "prettier --write 'src/**/*.ts' --config ./node_modules/@lms/eslint-config/.prettierrc.json "}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@iso/constants": "workspace:*", "dayjs": "1.11.10", "uuid": "11.0.2", "cron-parser": "4.9.0"}, "devDependencies": {"@lms/eslint-config": "workspace:*", "@lms/typescript-config": "workspace:*", "jest": "29.5.0", "@types/uuid": "10.0.0"}}