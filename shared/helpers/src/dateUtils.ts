import dayjs from 'dayjs';
import 'dayjs/locale/th';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import isSameOrAfterPlugin from 'dayjs/plugin/isSameOrAfter';
import isSameOrBeforePlugin from 'dayjs/plugin/isSameOrBefore';
import timezone from 'dayjs/plugin/timezone'; // dependent on utc plugin
import utc from 'dayjs/plugin/utc';
import { parseExpression } from 'cron-parser';

dayjs.extend(buddhistEra);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfterPlugin);
dayjs.extend(isSameOrBeforePlugin);
dayjs.extend(duration);
dayjs.tz.setDefault('UTC');

export const TimeZoneEnum = {
  Bangkok: 'Asia/Bangkok',
  UTC: 'UTC',
};

export const DateFormat = {
  iso: 'YYYY-MM-DDTHH:mm:ss.SSS[Z]',
  yearMonthDayDash: 'YYYY-MM-DD',
  yearMonthDay: 'YYYYMMDD',
  yearMonth: 'YYYYMM',
  monthDayYearWithLeadingZero: 'MM/DD/YYYY',
  yearMonthDayHourMinuteSecond: 'YYYYMMDD-HHmmss',
  yearMonthDayHourMinuteWithLeadingZero: 'YYYY/MM/DD HH:mm',
  yearMonthDayWithLeadingZero: 'YYYY/MM/DD',
  dayMonthYearHourMinuteWithLeadingZero: 'DD/MM/YYYY HH:mm',
  dayMonthYearWithLeadingZero: 'DD/MM/YYYY',
  buddhistShortDate: 'DD MMM BBBB',
  buddhistShortDateWithDateOneDigit: 'D MMM BBBB',
  buddhistDayMonthYearWithLeadingZero: 'DD/MM/BBBB',
  buddhistDayMonthYearHourMinuteWithLeadingZero: 'DD/MM/BBBB HH:mm',
  buddhistDayMonthYearWithSecondTimeCurly: 'DD/MM/BBBB (HH:mm:ss)',
  hourMinute: 'HH:mm',
};

export const date = (
  val?: string | number | dayjs.Dayjs | Date | null | undefined,
  format?: dayjs.OptionType | undefined,
): dayjs.Dayjs => dayjs(val ?? undefined, format).tz('Asia/Bangkok');

export const formatDate = (val: Date, format = 'DD/MM/YYYY', locale = 'th'): string => {
  return dayjs.tz(val, TimeZoneEnum.Bangkok).locale(locale).format(format);
};

export const formatDateInThaiLocale = (
  _date: Date,
  targetTimeZone = TimeZoneEnum.UTC,
  format = DateFormat.buddhistShortDate,
): string => dayjs.tz(_date).tz(targetTimeZone).locale('th').format(format);

export const getPublishDateTimeOfNotification = (cron: string, endDateOfDay?: Date): Date => {
  if (!cron) return null;
  const interval = parseExpression(cron, {
    currentDate: date().startOf('day').toDate(),
    endDate: endDateOfDay ?? date().endOf('day').toDate(),
    tz: TimeZoneEnum.Bangkok,
    iterator: false,
  });

  const dateTime = interval.next().toISOString();
  return date(dateTime).toDate();
};
