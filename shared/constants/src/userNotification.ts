export enum UserNotificationTypeEnum {
  SELF_ENROLLMENT = 'SELF_ENROLLMENT',
  SELF_PRE_ENROLLMENT = 'SELF_PRE_ENROLLMENT',
  PRE_ENROLLMENT_SUCCESS = 'PRE_ENROLLMENT_SUCCESS',
  PRE_ENROLLMENT_REJECTED_BY_SUPERVISOR = 'PRE_ENROLLMENT_REJECTED_BY_SUPERVISOR',
  PRE_ENROLLMENT_REJECTED = 'PRE_ENROLLMENT_REJECTED',
  PRE_ASSIGN_COURSE_REQUEST = 'PRE_ASSIGN_COURSE_REQUEST',
  PRE_ASSIGN_COURSE_COMPLETED = 'PRE_ASSIGN_COURSE_COMPLETED',
  PRE_ASSIGN_COURSE_CANCELED = 'PRE_ASSIGN_COURSE_CANCELED',
  PRE_ASSIGN_LEARNING_PATH_REQUEST = 'PRE_ASSIGN_LEARNING_PATH_REQUEST',
  PRE_ASSIGN_LEARNING_PATH_COMPLETED = 'PRE_ASSIGN_LEARNING_PATH_COMPLETED',
  PRE_ASSIGN_LEARNING_PATH_CANCELED = 'PRE_ASSIGN_LEARNING_PATH_CANCELED',
  ENROLLMENT_SUCCESS = 'ENROLLMENT_SUCCESS',
  ENROLLMENT_WITH_ROUND_SUCCESS = 'ENROLLMENT_WITH_ROUND_SUCCESS',
  ENROLLMENT_REJECT = 'ENROLLMENT_REJECT',
  ENROLLMENT_EXPIRED = 'ENROLLMENT_EXPIRED',
  ENROLLMENT_APPROVED = 'ENROLLMENT_APPROVED',
  ENROLLMENT_APPROVED_WITH_CERTIFICATE = 'ENROLLMENT_APPROVED_WITH_CERTIFICATE',
  ENROLLMENT_PASSED = 'ENROLLMENT_PASSED',
  ENROLLMENT_PASSED_WITH_CERTIFICATE = 'ENROLLMENT_PASSED_WITH_CERTIFICATE',
  LEARNING_PATH_PRE_ENROLLMENT_SUCCESS = 'LEARNING_PATH_PRE_ENROLLMENT_SUCCESS',
  LEARNING_PATH_ENROLLMENT_SUCCESS = 'LEARNING_PATH_ENROLLMENT_SUCCESS',
  LEARNING_PATH_ENROLLMENT_WITH_ROUND_SUCCESS = 'LEARNING_PATH_ENROLLMENT_WITH_ROUND_SUCCESS',
  LEARNING_PATH_SELF_ENROLLMENT = 'LEARNING_PATH_SELF_ENROLLMENT',
  LEARNING_PATH_SELF_PRE_ENROLLMENT = 'LEARNING_PATH_SELF_PRE_ENROLLMENT',
  LEARNING_PATH_SELF_PRE_ENROLLMENT_CANCELED = 'LEARNING_PATH_SELF_PRE_ENROLLMENT_CANCELED',
  LEARNING_PATH_CANCELED_WITH_ROUND = 'LEARNING_PATH_CANCELED_WITH_ROUND',
  LEARNING_PATH_CANCELED_WITHOUT_ROUND = 'LEARNING_PATH_CANCELED_WITHOUT_ROUND',
  LEARNING_PATH_EXPAND_EXPIRY_DAY = 'LEARNING_PATH_EXPAND_EXPIRY_DAY',
  LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE = 'LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE',
  LEARNING_PATH_ENROLLMENT_COMPLETED = 'LEARNING_PATH_ENROLLMENT_COMPLETED',
  LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE = 'LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE',
  LEARNING_PATH_ENROLLMENT_EXPIRED = 'LEARNING_PATH_ENROLLMENT_EXPIRED',
  LEARNING_PATH_ENROLLMENT_CANCELED = 'LEARNING_PATH_ENROLLMENT_CANCELED',
  LEARNING_PATH_PRE_ENROLLMENT_CANCELED = 'LEARNING_PATH_PRE_ENROLLMENT_CANCELED',
  ENROLLMENT_REQUEST_ADDITIONAL_ATTACHMENT = 'ENROLLMENT_REQUEST_ADDITIONAL_ATTACHMENT',
  ENROLLMENT_REQUEST_DEDUCT_ATTACHMENT = 'ENROLLMENT_REQUEST_DEDUCT_ATTACHMENT',
  ENROLLMENT_APPROVE_ADDITIONAL_ATTACHMENT = 'ENROLLMENT_APPROVE_ADDITIONAL_ATTACHMENT',
  ENROLLMENT_REJECTED_ADDITIONAL_ATTACHMENT = 'ENROLLMENT_REJECTED_ADDITIONAL_ATTACHMENT',
  ENROLLMENT_APPROVE_DEDUCT_ATTACHMENT = 'ENROLLMENT_APPROVE_DEDUCT_ATTACHMENT',
  ENROLLMENT_REJECTED_DEDUCT_ATTACHMENT = 'ENROLLMENT_REJECTED_DEDUCT_ATTACHMENT',
  ENROLLMENT_CANCELED_ADDITIONAL_ATTACHMENT = 'ENROLLMENT_CANCELED_ADDITIONAL_ATTACHMENT',
  ENROLLMENT_CANCELED_DEDUCT_ATTACHMENT = 'ENROLLMENT_CANCELED_DEDUCT_ATTACHMENT',
  PROMOTE_COURSE = 'PROMOTE_COURSE',
  PROMOTE_LEARNING_PATH = 'PROMOTE_LEARNING_PATH',
  PROMOTE_KNOWLEDGE_CONTENT = 'PROMOTE_KNOWLEDGE_CONTENT',
  PROMOTE_ANNOUNCEMENT = 'PROMOTE_ANNOUNCEMENT',
  REMIND_CLASSROOM_ROUND_START = 'REMIND_CLASSROOM_ROUND_START',
  RESET_LEARNING_PROGRESS = 'RESET_LEARNING_PROGRESS',
  CANCELED_CLASSROOM_WAITING = 'CANCELED_CLASSROOM_WAITING',
  EDIT_CLASSROOM_ROUND = 'EDIT_CLASSROOM_ROUND',
  CLASSROOM_REGISTER_CANCELED = 'CLASSROOM_REGISTER_CANCELED',
  CLASSROOM_REGISTER_SUCCESS = 'CLASSROOM_REGISTER_SUCCESS',
  CLASSROOM_REGISTER_WAITING = 'CLASSROOM_REGISTER_WAITING',
  CHANGE_CLASSROOM_ROUND = 'CHANGE_CLASSROOM_ROUND',
  REMIND_ENROLLMENT_EXPIRED = 'REMIND_ENROLLMENT_EXPIRED',
  RESOLVED_COURSE_DISCUSSION_BOARD = 'RESOLVED_COURSE_DISCUSSION_BOARD',
  CANCEL_COURSE_APPROVAL = 'CANCEL_COURSE_APPROVAL',
  CANCEL_COURSE_PASS_RESULT = 'CANCEL_COURSE_PASS_RESULT',
  CANCEL_LEARNING_PATH_CERTIFICATE = 'CANCEL_LEARNING_PATH_CERTIFICATE',
  ASSIGN_ENROLLMENT_COMPLETED = 'ASSIGN_ENROLLMENT_COMPLETED',
  ASSIGN_LEARNING_PATH_ENROLLMENT_COMPLETED = 'ASSIGN_LEARNING_PATH_ENROLLMENT_COMPLETED',
  ASSIGN_PLAN_PACKAGE_LICENSE = 'ASSIGN_PLAN_PACKAGE_LICENSE',
  PLAN_PACKAGE_LICENSE_EXPIRED = 'PLAN_PACKAGE_LICENSE_EXPIRED',
  ACHIEVEMENT_COMPLETED = 'ACHIEVEMENT_COMPLETED',
  ACHIEVEMENT_ADDITIONAL_COMPLETED = 'ACHIEVEMENT_ADDITIONAL_COMPLETED',
  BADGE_ADDITIONAL_COMPLETED = 'BADGE_ADDITIONAL_COMPLETED',
}

export type UserNotificationPayloadParams = {
  contentName: string;
  roundDate?: Date;
  expiryDay?: number;
  refName?: string;
};

export type UserNotificationAssigningPayloadParams = {
  assignorName: string;
  receiverName: string;
};
