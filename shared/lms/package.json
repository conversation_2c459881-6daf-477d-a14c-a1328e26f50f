{"name": "@iso/lms", "version": "1.0.0", "description": "", "author": "", "license": "ISC", "keywords": [], "private": true, "scripts": {"clean": "rimraf node_modules", "clean:build": "<PERSON><PERSON><PERSON> dist", "build-check": "rm -rf dist && tsc --diagnostics", "build:tsc": "tsc --project tsconfig.build.json --diagnostics && tsc-alias -p tsconfig.build.json", "build:swc": "swc src --out-dir dist --strip-leading-paths --config-file build.swcrc", "build": "pnpm clean:build && pnpm build:swc", "lint": "eslint \"src/**/*.ts\" --fix", "lint:debug": "eslint \"src/**/*.ts\" --fix --debug", "test": "jest --bail --silent", "test-all": "jest", "test-clear": "jest --clear<PERSON>ache", "test-watch": "jest --watch --verbose --maxWorkers=20%", "test-cov": "jest --coverage", "test-pre-merge": "jest --bail --silent --maxWorkers=20%", "format": "prettier --write 'src/**/*.ts' --config ./node_modules/@lms/eslint-config/.prettierrc.json"}, "exports": {"./models/*": {"types": "./src/models/*.ts", "default": "./dist/models/*.js"}, "./types/*": {"types": "./src/constants/types/*.ts", "default": "./dist/constants/types/*.js"}, "./enums/*": {"types": "./src/constants/enums/*.ts", "default": "./dist/constants/enums/*.js"}, "./core-instances/*": {"types": "./src/core/instances/*.ts", "default": "./dist/core/instances/*.js"}, "./core-type": {"types": "./src/core/constant/types/index.ts", "default": "./dist/core/constant/types/index.js"}, "./services/*": {"types": "./src/services/*.ts", "default": "./dist/services/*.js"}}, "dependencies": {"@iso/constants": "workspace:*", "@iso/helpers": "workspace:*", "aws-sdk": "^2.1691.0", "class-transformer": "0.5.1", "class-validator": "0.14.1", "dayjs": "1.11.10", "mongodb": "3.7.3", "uuid": "11.0.2"}, "devDependencies": {"@lms/eslint-config": "workspace:*", "@lms/typescript-config": "workspace:*", "@types/mongodb": "3.5.18", "@types/uuid": "10.0.0", "tsc-alias": "1.8.10", "jest": "29.5.0"}}