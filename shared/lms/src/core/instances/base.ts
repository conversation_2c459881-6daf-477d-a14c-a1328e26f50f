import { GenericID, Nullable, Optional } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsDate, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ObjectId } from 'mongodb';
import { v4 } from 'uuid';

import { CoreValidationDetailParams } from '@shared/lms/core/constant/types';
import { Code } from '@shared/lms/core/instances/code';
import { Exception } from '@shared/lms/core/instances/exception';
import { CoreValidator, IsMongoObjectId } from '@shared/lms/core/instances/validator';

export class BaseModel {
  @IsNotEmpty()
  protected _id: GenericID;

  @IsDate()
  protected _createdAt: Date;

  @IsDate()
  protected _updatedAt: Date;

  @IsDate()
  @IsOptional()
  protected _deletedAt: Nullable<Date>;

  constructor(id?: GenericID, createdAt?: Date, updatedAt?: Date, deletedAt?: Nullable<Date>) {
    const currentDate = date().toDate();

    this._id = id ?? v4();
    this._createdAt = createdAt ?? currentDate;
    this._updatedAt = updatedAt ?? currentDate;
    this._deletedAt = deletedAt ?? null;
  }

  async validate(): Promise<void> {
    const details: Optional<CoreValidationDetailParams> = await CoreValidator.validate(this);

    if (details) {
      throw Exception.new({ code: Code.ENTITY_VALIDATION_ERROR, data: details });
    }
  }

  @Expose()
  get id(): GenericID {
    return this._id;
  }

  set id(val: GenericID) {
    this._id = val;
  }

  @Expose()
  get createdAt(): Date {
    return this._createdAt;
  }

  set createdAt(val: Date) {
    this._createdAt = val;
  }

  @Expose()
  get updatedAt(): Date {
    return this._updatedAt;
  }

  set updatedAt(val: Date) {
    this._updatedAt = val;
  }

  @Expose()
  get deletedAt(): Nullable<Date> {
    return this._deletedAt;
  }

  set deletedAt(val: Nullable<Date>) {
    this._deletedAt = val;
  }
}

export class BaseGuidModel {
  @IsString()
  @IsNotEmpty()
  protected _guid: GenericID;

  @IsDate()
  protected _createdAt: Date;

  @IsDate()
  protected _updatedAt: Date;

  @IsDate()
  @IsOptional()
  protected _deletedAt: Nullable<Date>;

  constructor(guid?: GenericID, createdAt?: Date, updatedAt?: Date, deletedAt?: Nullable<Date>) {
    const currentDate = date().toDate();

    this._guid = guid ?? v4();
    this._createdAt = createdAt ?? currentDate;
    this._updatedAt = updatedAt ?? currentDate;
    this._deletedAt = deletedAt ?? null;
  }

  protected async validate(): Promise<void> {
    const details: Optional<CoreValidationDetailParams> = await CoreValidator.validate(this);
    if (details) {
      throw Exception.new({ code: Code.ENTITY_VALIDATION_ERROR, data: details });
    }
  }

  @Expose()
  get guid(): GenericID {
    return this._guid;
  }

  set guid(val: GenericID) {
    this._guid = val;
  }

  @Expose()
  get createdAt(): Date {
    return this._createdAt;
  }

  set createdAt(val: Date) {
    this._createdAt = val;
  }

  @Expose()
  get updatedAt(): Date {
    return this._updatedAt;
  }

  set updatedAt(val: Date) {
    this._updatedAt = val;
  }

  @Expose()
  get deletedAt(): Nullable<Date> {
    return this._deletedAt;
  }

  set deletedAt(val: Nullable<Date>) {
    this._deletedAt = val;
  }
}

export class BaseDbModel {
  @IsMongoObjectId()
  @IsNotEmpty()
  __id: ObjectId;

  @IsString()
  @IsNotEmpty()
  protected _id: GenericID;

  @IsDate()
  protected _createdAt: Date;

  @IsDate()
  protected _updatedAt: Date;

  @IsDate()
  @IsOptional()
  protected _deletedAt: Nullable<Date>;

  constructor(_id?: ObjectId, id?: GenericID, createdAt?: Date, updatedAt?: Date, deletedAt?: Nullable<Date>) {
    const currentDate = date().toDate();

    this.__id = _id ?? new ObjectId();
    this._id = id ?? v4();
    this._createdAt = createdAt ?? currentDate;
    this._updatedAt = updatedAt ?? currentDate;
    this._deletedAt = deletedAt ?? null;
  }

  protected async validate(): Promise<void> {
    const details: Optional<CoreValidationDetailParams> = await CoreValidator.validate(this);
    if (details) {
      throw Exception.new({ code: Code.ENTITY_VALIDATION_ERROR, data: details });
    }
  }

  @Expose({
    name: '_id',
  })
  get objectId(): ObjectId {
    return this.__id;
  }

  set objectId(val: ObjectId) {
    this.__id = val;
  }

  @Expose()
  get id(): GenericID {
    return this._id;
  }

  set id(val: GenericID) {
    this._id = val;
  }

  @Expose()
  get createdAt(): Date {
    return this._createdAt;
  }

  set createdAt(val: Date) {
    this._createdAt = val;
  }

  @Expose()
  get updatedAt(): Date {
    return this._updatedAt;
  }

  set updatedAt(val: Date) {
    this._updatedAt = val;
  }

  @Expose()
  get deletedAt(): Nullable<Date> {
    return this._deletedAt;
  }

  set deletedAt(val: Nullable<Date>) {
    this._deletedAt = val;
  }
}

export class BaseSchema {
  async validate(): Promise<void> {
    const details: Optional<CoreValidationDetailParams> = await CoreValidator.validate(this);
    if (details) {
      throw Exception.new({ code: Code.ENTITY_VALIDATION_ERROR, data: details });
    }
  }
}
