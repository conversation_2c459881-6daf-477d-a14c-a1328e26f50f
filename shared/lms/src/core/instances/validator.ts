import { Optional } from '@iso/constants/commonTypes';
import {
  registerDecorator,
  validate,
  ValidationError,
  ValidationOptions,
  ValidationArguments,
  isEnum,
  isString,
  isFQDN,
} from 'class-validator';
import { ObjectId } from 'mongodb';

import { CoreValidationDetailParams } from '../constant/types';
import { BulkOpTypeEnum } from '@shared/lms/constants/enums/job.enum';
import { validateDomain } from '@shared/lms/services/organization.service';

export class CoreValidator {
  static async validate<TTarget extends object>(
    target: TTarget,
    context?: string,
  ): Promise<Optional<CoreValidationDetailParams>> {
    const errors: ValidationError[] = await validate(target);

    if (!errors.length) return;
    const detail: Optional<CoreValidationDetailParams> = {
      context: context || target.constructor.name,
      errors: [],
    };

    detail.errors = errors.map((error) => {
      let message: string[] | ValidationError[] = [];
      if (error.constraints) {
        message = Object.values(error.constraints);
      } else if (error.children) {
        message = error.children;
      }
      return {
        property: error.property,
        message,
      };
    });

    return detail;
  }
}

export function IsMongoObjectId(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isObjectId',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _arg: ValidationArguments) {
          return ObjectId.isValid(value);
        },
      },
    });
  };
}

export function IsGenericId(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'IsGenericId',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          return typeof value === 'string' || typeof value === 'number';
        },
        defaultMessage(_args: ValidationArguments) {
          return 'property must be a string or a number';
        },
      },
    });
  };
}

export function IsTaskOperationKey(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'IsTaskOperationKey',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: string, _args: ValidationArguments) {
          const isStringType = isString(value);
          if (!isStringType) return false;
          const payload = String(value).split(':');

          const [key1, key2, operationType, domain, id] = payload;

          const isKey1Matched = key1 === 'lms';
          const isKey2Matched = key2 === 'task';
          const isDomainMatch = validateDomain(domain);
          const isOperationTypeMatch = isEnum(operationType, BulkOpTypeEnum);
          const isIdMatch = isString(id);
          return isKey1Matched && isKey2Matched && isDomainMatch && isOperationTypeMatch && isIdMatch;
        },
        defaultMessage(_args: ValidationArguments) {
          return 'property must be a task operation id format: lms:task:{operationType}:{domain}:{uuid}';
        },
      },
    });
  };
}
