import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date, TimeZoneEnum, DateFormat, formatDateInThaiLocale } from '@iso/helpers/dateUtils';
import { sortBy } from 'lodash';

import { PackageTypeEnum } from '@shared/lms/constants/enums/packages.enum';
import { SaleOrderStatusEnum } from '@shared/lms/constants/enums/plan.enum';
import {
  PackageContentTypeEnumLocalizeMapper,
  PlanPackageTypeContentParams,
} from '@shared/lms/constants/types/planPackage.type';
import {
  AvailablePeriodLicenseByUserParams,
  NotifyPlanPackageLicenseTextParams,
  PlanPackageLicenseParams,
} from '@shared/lms/constants/types/planPackageLicense.type';
import { Plan } from '@shared/lms/models/plan.model';
import { PlanPackage } from '@shared/lms/models/planPackage.model';
import { PlanPackageLicense } from '@shared/lms/models/planPackageLicense.model';

export const getAvailablePeriodLicenseByUser = (
  planPackageLicenses: PlanPackageLicenseParams[],
  userId: GenericID,
): AvailablePeriodLicenseByUserParams[] => {
  const stack: AvailablePeriodLicenseByUserParams[] = [];
  const planPackageLicenseASC = sortBy(planPackageLicenses, ['startedAt'], ['asc']);

  for (const current of planPackageLicenseASC) {
    if (current.userId !== userId) continue;
    if (stack.length === 0) {
      if (!current.startedAt || !current.expiredAt) continue;

      stack.push({
        planPackageId: current.planPackageId,
        planPackageLicenseIds: [current.id],
        userId: current.userId,
        startedAt: current.startedAt,
        expiredAt: current.expiredAt,
      });

      continue;
    }

    const lastStackIndex = stack.length - 1;
    const lastItemStack = stack[lastStackIndex];

    if (!current.startedAt || !current.expiredAt) continue;

    const isNoGap = date(current.startedAt).toDate().getTime() === date(lastItemStack.expiredAt).toDate().getTime() + 1;
    if (isNoGap) {
      stack[lastStackIndex].planPackageLicenseIds = [...stack[lastStackIndex].planPackageLicenseIds, current.id];
      stack[lastStackIndex].expiredAt = current.expiredAt;
      continue;
    }

    const isOverlap = date(lastItemStack.expiredAt).isSameOrAfter(date(current.startedAt));
    if (isOverlap) {
      stack[lastStackIndex].planPackageLicenseIds = [...stack[lastStackIndex].planPackageLicenseIds, current.id];
      stack[lastStackIndex].expiredAt = current.expiredAt;
      continue;
    }

    stack.push({
      planPackageId: current.planPackageId,
      planPackageLicenseIds: [current.id],
      userId: current.userId,
      startedAt: current.startedAt,
      expiredAt: current.expiredAt,
    });
  }

  return stack;
};

export const checkHavePlanPackageLicenseAvailableToday = (
  planPackageLicenses: AvailablePeriodLicenseByUserParams[],
): boolean => {
  if (!planPackageLicenses.length) return false;

  const now = date();
  const planPackageLicenseASC = sortBy(planPackageLicenses, ['startedAt'], ['asc']);

  return planPackageLicenseASC.some(
    (item) => date(item.startedAt).isSameOrBefore(now) && now.isSameOrBefore(date(item.expiredAt)),
  );
};

export const getPlanPackageLicenseAvailableToday = (
  planPackageLicenses: PlanPackageLicenseParams[],
): PlanPackageLicenseParams[] => {
  if (!planPackageLicenses.length) return [];

  const now = date();
  const planPackageLicenseASC = sortBy(planPackageLicenses, ['startedAt'], ['asc']);

  return planPackageLicenseASC.filter(
    (item) => date(item.startedAt).isSameOrBefore(now) && now.isSameOrBefore(date(item.expiredAt)),
  );
};

export const checkVoluntaryEnrollableInAvailablePeriodLicense = (
  startedAt: Date,
  planPackageLicenses: AvailablePeriodLicenseByUserParams[],
): boolean => {
  if (!planPackageLicenses.length) return false;
  if (!startedAt) return false;

  const planPackageLicenseASC = sortBy(planPackageLicenses, ['startedAt'], ['asc']);

  return planPackageLicenseASC.some(
    (item) =>
      date(item.startedAt).isSameOrBefore(date(startedAt)) && date(startedAt).isSameOrBefore(date(item.expiredAt)),
  );
};

export const checkCompulsoryEnrollableInAvailablePeriodLicense = (
  startedAt: Date,
  expiredAt: Date,
  planPackageLicenses: AvailablePeriodLicenseByUserParams[],
): boolean => {
  if (!planPackageLicenses.length) return false;
  if (!startedAt || !expiredAt) return false;

  const planPackageLicenseASC = sortBy(planPackageLicenses, ['startedAt'], ['asc']);

  return planPackageLicenseASC.some(
    (item) =>
      date(item.startedAt).isSameOrBefore(date(startedAt)) && date(expiredAt).isSameOrBefore(date(item.expiredAt)),
  );
};

export const checkHavePlanPackageLicenseAvailablePreEnroll = (
  roundDate: Date,
  planPackageLicenses: AvailablePeriodLicenseByUserParams[],
): boolean => {
  if (!planPackageLicenses.length) return false;
  if (!roundDate) return false;

  const planPackageLicenseASC = sortBy(planPackageLicenses, ['startedAt'], ['asc']);

  return planPackageLicenseASC.some(
    (item) =>
      date(item.startedAt).isSameOrBefore(date(roundDate)) && date(roundDate).isSameOrBefore(date(item.expiredAt)),
  );
};

const getDateRageInThaiLocaleText = (
  planPackage: PlanPackage,
  startedAt: Nullable<Date>,
  expiredAt: Nullable<Date>,
) => {
  const startDateText = formatDateInThaiLocale(
    startedAt ?? planPackage.startDate,
    TimeZoneEnum.Bangkok,
    DateFormat.buddhistDayMonthYearWithLeadingZero,
  );
  const endDateText = formatDateInThaiLocale(
    expiredAt ?? planPackage.endDate,
    TimeZoneEnum.Bangkok,
    DateFormat.buddhistDayMonthYearWithLeadingZero,
  );

  return `${startDateText} - ${endDateText}`;
};

export const getPlanPackageLicenseTextList = (
  plans: Plan[],
  planPackages: PlanPackage[],
  planPackageLicenses: PlanPackageLicense[],
): NotifyPlanPackageLicenseTextParams[] => {
  const dateList: NotifyPlanPackageLicenseTextParams[] = [];

  const planPackageIds = planPackageLicenses.map((val) => val.planPackageId);
  const planSaleOrderStatusApprovedList = plans.filter((val) => val.saleOrderStatus === SaleOrderStatusEnum.APPROVED);

  for (const plan of planSaleOrderStatusApprovedList) {
    const planPackageDataList = planPackages.filter((val) => val.planId === plan.id && planPackageIds.includes(val.id));
    if (planPackageDataList.length === 0) {
      continue;
    }
    const mainPlanPackage = planPackageDataList.find((val) => val.type === PackageTypeEnum.PLATFORM);
    const subPlanPackages = planPackageDataList.filter((val) => val.type === PackageTypeEnum.CONTENT);

    const mainPlanPackageLicense = planPackageLicenses.find((val) => val.planPackageId === mainPlanPackage?.id);

    const subPackages = subPlanPackages.map((subPlanPackage) => {
      const subPlanPackageLicense = planPackageLicenses.find((subVal) => subVal.planPackageId === subPlanPackage.id);
      const content = subPlanPackage.content as PlanPackageTypeContentParams;
      return {
        name: subPlanPackage.name,
        type: PackageContentTypeEnumLocalizeMapper[content.type],
        dateText: getDateRageInThaiLocaleText(
          subPlanPackage,
          subPlanPackageLicense?.startedAt,
          subPlanPackageLicense?.expiredAt,
        ),
      };
    });

    const planPackageData = {
      planName: plan.name,
      ...(mainPlanPackage && {
        mainPackage: {
          name: mainPlanPackage.name,
          dateText: getDateRageInThaiLocaleText(
            mainPlanPackage,
            mainPlanPackageLicense?.startedAt,
            mainPlanPackageLicense?.expiredAt,
          ),
        },
      }),
      subPackages,
    };
    dateList.push(planPackageData);
  }

  return dateList;
};
