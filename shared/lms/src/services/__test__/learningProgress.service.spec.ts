import { ClassroomLocationEnrollmentStatusEnum } from '@shared/lms/constants/enums/classroomLocationEnrollment.enum';
import { CourseItemCriteriaConfigTypeEnum } from '@shared/lms/constants/enums/courseItemCriteriaConfig.enum';
import { CourseItemStatusCodeEnum } from '@shared/lms/constants/enums/courseItemProgress.enum';
import { CourseItemProgressHistoryEventTypeEnum } from '@shared/lms/constants/enums/courseItemProgressHistory.enum';
import { CourseVersionCriteriaTimeSpentTypeEnum } from '@shared/lms/constants/enums/courseVersion.enum';
import { MaterialMediaTypeEnum } from '@shared/lms/constants/enums/materialMedia.enum';
import {
  getCourseItemProgressHistoryEventType,
  getTimeSpentVideo,
  isPassedAllLearningCriteria,
} from '@shared/lms/services/learningProgress.service';

describe('isPassedAllLearningCriteria', () => {
  it('when pass all criteria, expect return true', () => {
    const params = {
      learningProgress: [
        {
          courseItemId: '1',
          statusCode: CourseItemStatusCodeEnum.COMPLETE,
          type: MaterialMediaTypeEnum.ARTICLE,
          timeSpent: 60,
        },
        {
          courseItemId: '2',
          statusCode: CourseItemStatusCodeEnum.COMPLETE,
          type: MaterialMediaTypeEnum.VIDEO,
          timeSpent: 100,
        },
        {
          courseItemId: '3',
          statusCode: CourseItemStatusCodeEnum.COMPLETE,
          type: MaterialMediaTypeEnum.QUIZ,
          quizAnswer: {
            criteriaCertificate: {
              isPass: true,
            },
          },
        },
        { courseItemId: '4', statusCode: CourseItemStatusCodeEnum.COMPLETE, type: MaterialMediaTypeEnum.CLASSROOM },
      ],
      courseItemDurations: [
        { id: '1', duration: 60, type: MaterialMediaTypeEnum.ARTICLE },
        { id: '2', duration: 100, type: MaterialMediaTypeEnum.VIDEO },
      ],
      completionCriteria: {
        learningProgress: { isEnabled: true, percentage: 50 },
        timeSpent: {
          isEnabled: true,
          type: CourseVersionCriteriaTimeSpentTypeEnum.COURSE_TIME_SPENT,
          courseTimeSpentPercentage: 50,
          courseItemTimeSpentPercentage: 0,
        },
      },
      courseItemCriteriaConfigs: [
        {
          courseVersionId: '1',
          courseItemType: CourseItemCriteriaConfigTypeEnum.QUIZ,
          isEnabled: true,
          courseItemId: '3',
          config: {
            passScore: 10,
          },
        },
        {
          courseVersionId: '1',
          courseItemType: CourseItemCriteriaConfigTypeEnum.CLASSROOM,
          isEnabled: true,
          courseItemId: '4',
          config: {
            passScoreHomework: 8,
            maxScoreHomework: 10,
            passAttendance: 2,
            maxAttendance: 3,
          },
        },
      ],
      isCountdownArticle: true,
      totalDurationSec: 100,
      totalDurationArticleSec: 60,
      totalCourseItems: 4,
    };

    expect(isPassedAllLearningCriteria(params)).toEqual(true);
  });

  it('when course time spent is not pass, expect return false', () => {
    const params = {
      learningProgress: [
        {
          courseItemId: '1',
          statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
          type: MaterialMediaTypeEnum.ARTICLE,
          timeSpent: 0,
        },
        {
          courseItemId: '2',
          statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
          type: MaterialMediaTypeEnum.VIDEO,
          timeSpent: 0,
        },
        { courseItemId: '3', statusCode: CourseItemStatusCodeEnum.IN_PROGRESS, type: MaterialMediaTypeEnum.QUIZ },
        { courseItemId: '4', statusCode: CourseItemStatusCodeEnum.IN_PROGRESS, type: MaterialMediaTypeEnum.CLASSROOM },
      ],
      courseItemDurations: [
        { id: '1', duration: 100, type: MaterialMediaTypeEnum.ARTICLE },
        { id: '2', duration: 200, type: MaterialMediaTypeEnum.VIDEO },
      ],
      completionCriteria: {
        learningProgress: { isEnabled: true, percentage: 50 },
        timeSpent: {
          isEnabled: true,
          type: CourseVersionCriteriaTimeSpentTypeEnum.COURSE_TIME_SPENT,
          courseTimeSpentPercentage: 50,
          courseItemTimeSpentPercentage: 0,
        },
      },
      courseItemCriteriaConfigs: [
        {
          courseVersionId: '1',
          courseItemType: CourseItemCriteriaConfigTypeEnum.QUIZ,
          isEnabled: true,
          courseItemId: '3',
          config: {
            passScore: 10,
          },
        },
        {
          courseVersionId: '1',
          courseItemType: CourseItemCriteriaConfigTypeEnum.CLASSROOM,
          isEnabled: true,
          courseItemId: '4',
          config: {
            passScoreHomework: 8,
            maxScoreHomework: 10,
            passAttendance: 2,
            maxAttendance: 3,
          },
        },
      ],
      isCountdownArticle: true,
      totalDurationSec: 300,
      totalDurationArticleSec: 60,
      totalCourseItems: 4,
    };

    expect(isPassedAllLearningCriteria(params)).toEqual(false);
  });
});

describe('Test Function: getCourseItemProgressHistoryEventType', () => {
  const MOCK_VALUE = 'MOCK_VALUE';
  const mockVideoPayload: Record<string, any> = {
    duration: 10,
  };

  const mockQuizPayload: Record<string, any> = {
    id: MOCK_VALUE,
    criteriaCertificate: {
      passScore: 1,
      isPass: true,
    },
    finishedAt: null,
  };

  const mockArticlePayload: Record<string, any> = {
    duration: 10,
  };

  const mockSurveyPayload: Record<string, any> = {
    type: 'SELF | JOTFORM',
  };

  const mockResultProgressPayload: Record<string, any> = {
    logSnapshotEnrollmentId: MOCK_VALUE,
  };

  it.each([
    {
      testName: 'start watch video',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.START_VIDEO,
    },
    {
      testName: 'start watch video',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 0,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.START_VIDEO,
    },
    {
      testName: 'watching video',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 5,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_VIDEO,
    },
    {
      testName: 'watching video when backward video',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 999,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_VIDEO,
    },
    {
      testName: 'completed video (legacy case)',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_VIDEO,
    },
    {
      testName: 'completed video (legacy case)',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 0,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_VIDEO,
    },
    {
      testName: 'completed video',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 10,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_VIDEO,
    },
    {
      testName: 'revisit video',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 999,
        payload: mockVideoPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.REVISIT_VIDEO,
    },
  ])('video: #$# when $testName  \t expect eventType: $expectedResult', ({ input, expectedResult }) => {
    const { type, statusCode, timeSpent, payload } = input;

    const result = getCourseItemProgressHistoryEventType(type, statusCode, timeSpent, payload);
    expect(result).toEqual(expectedResult);
  });

  it.each([
    {
      testName: 'start quiz',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          ...mockQuizPayload,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.START_QUIZ,
    },
    {
      testName: 'start quiz',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 0,
        payload: {
          ...mockQuizPayload,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.START_QUIZ,
    },
    {
      testName: 'doing quiz',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 999,
        payload: {
          ...mockQuizPayload,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_QUIZ,
    },
    {
      testName: 'completed quiz but not not pass',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 999,
        payload: {
          ...mockQuizPayload,
          criteriaCertificate: {
            isPass: false,
            passScore: 5,
          },
          finishedAt: new Date(),
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_QUIZ,
    },
    {
      testName: 'completed quiz',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 999,
        payload: {
          ...mockQuizPayload,
          criteriaCertificate: {
            isPass: true,
            passScore: 7,
          },
          finishedAt: new Date(),
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_QUIZ,
    },
    {
      testName: 'retest quiz after completed',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 999,
        payload: {
          ...mockQuizPayload,
          criteriaCertificate: {
            isPass: true,
            passScore: 7,
          },
          finishedAt: null,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_QUIZ,
    },
  ])('quiz: #$# when $testName  \t expect eventType: $expectedResult', ({ input, expectedResult }) => {
    const { type, statusCode, timeSpent, payload } = input;

    const result = getCourseItemProgressHistoryEventType(type, statusCode, timeSpent, payload);
    expect(result).toEqual(expectedResult);
  });

  it.each([
    {
      testName: 'start watch article',
      input: {
        type: MaterialMediaTypeEnum.ARTICLE,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: mockArticlePayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.START_ARTICLE,
    },
    {
      testName: 'start watch article',
      input: {
        type: MaterialMediaTypeEnum.ARTICLE,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 0,
        payload: mockArticlePayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.START_ARTICLE,
    },
    {
      testName: 'watching article',
      input: {
        type: MaterialMediaTypeEnum.ARTICLE,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 5,
        payload: mockArticlePayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_ARTICLE,
    },
    {
      testName: 'watching article',
      input: {
        type: MaterialMediaTypeEnum.ARTICLE,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 10,
        payload: mockArticlePayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_ARTICLE,
    },
    {
      testName: 'completed article',
      input: {
        type: MaterialMediaTypeEnum.ARTICLE,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 10,
        payload: mockArticlePayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_ARTICLE,
    },
  ])('article: #$# when $testName \t expect eventType: $expectedResult', ({ input, expectedResult }) => {
    const { type, statusCode, timeSpent, payload } = input;

    const result = getCourseItemProgressHistoryEventType(type, statusCode, timeSpent, payload);
    expect(result).toEqual(expectedResult);
  });

  it.each([
    {
      testName: 'completed survey',
      input: {
        type: MaterialMediaTypeEnum.SURVEY,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: mockSurveyPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_SURVEY,
    },
    {
      testName: 'completed survey',
      input: {
        type: MaterialMediaTypeEnum.SURVEY,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 0,
        payload: mockSurveyPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.COMPLETED_SURVEY,
    },
  ])('survey: #$# when $testName \t expect eventType: $expectedResult', ({ input, expectedResult }) => {
    const { type, statusCode, timeSpent, payload } = input;

    const result = getCourseItemProgressHistoryEventType(type, statusCode, timeSpent, payload);
    expect(result).toEqual(expectedResult);
  });

  it.each([
    {
      testName: 'register classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.REGISTER_CLASSROOM,
    },
    {
      testName: 'register classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.REGISTER_CLASSROOM,
    },
    {
      testName: 'marked result classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.NOT_PASS,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM,
    },
    {
      testName: 'marked result classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 0,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.NOT_PASS,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM,
    },
    {
      testName: 'canceled classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.CANCELED,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.CANCEL_CLASSROOM,
    },
    {
      testName: 'canceled classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: 0,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.CANCELED,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.CANCEL_CLASSROOM,
    },
    {
      testName: 'marked result classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.PASSED,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM,
    },
    {
      testName: 'marked result classroom',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: 0,
        payload: {
          status: ClassroomLocationEnrollmentStatusEnum.PASSED,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM,
    },
  ])('classroom: #$# when $testName \t expect eventType: $expectedResult', ({ input, expectedResult }) => {
    const { type, statusCode, timeSpent, payload } = input;

    const result = getCourseItemProgressHistoryEventType(type, statusCode, timeSpent, payload);
    expect(result).toEqual(expectedResult);
  });

  it.each([
    {
      testName: 'reset progress video and status IN_PROGRESS',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress video and status COMPLETE',
      input: {
        type: MaterialMediaTypeEnum.VIDEO,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress quiz and status IN_PROGRESS',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress quiz and status COMPLETE',
      input: {
        type: MaterialMediaTypeEnum.QUIZ,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress article and status IN_PROGRESS',
      input: {
        type: MaterialMediaTypeEnum.ARTICLE,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress article and status COMPLETE',
      input: {
        type: MaterialMediaTypeEnum.ARTICLE,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress survey and status IN_PROGRESS',
      input: {
        type: MaterialMediaTypeEnum.SURVEY,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress survey and status COMPLETE',
      input: {
        type: MaterialMediaTypeEnum.SURVEY,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: mockResultProgressPayload,
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress classroom and status IN_PROGRESS',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          ...mockResultProgressPayload,
          status: ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.CANCEL_CLASSROOM,
    },
    {
      testName: 'reset progress classroom and status PRE_ENROLL',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          ...mockResultProgressPayload,
          status: ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.CANCEL_CLASSROOM,
    },
    {
      testName: 'reset progress classroom and status CANCELED',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          ...mockResultProgressPayload,
          status: ClassroomLocationEnrollmentStatusEnum.CANCELED,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.CANCEL_CLASSROOM,
    },
    {
      testName: 'reset progress classroom and status WAITING_PRE_ENROLL',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          ...mockResultProgressPayload,
          status: ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL,
        },
      },
      expectedResult: null,
    },
    {
      testName: 'reset progress classroom and status NOT_PASS (marked result)',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
        timeSpent: null,
        payload: {
          ...mockResultProgressPayload,
          status: ClassroomLocationEnrollmentStatusEnum.NOT_PASS,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
    {
      testName: 'reset progress classroom and status PASSED (marked result)',
      input: {
        type: MaterialMediaTypeEnum.CLASSROOM,
        statusCode: CourseItemStatusCodeEnum.COMPLETE,
        timeSpent: null,
        payload: {
          ...mockResultProgressPayload,
          status: ClassroomLocationEnrollmentStatusEnum.PASSED,
        },
      },
      expectedResult: CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS,
    },
  ])('reset progress: #$# when $testName \t expect eventType: $expectedResult', ({ input, expectedResult }) => {
    const { type, statusCode, timeSpent, payload } = input;

    const result = getCourseItemProgressHistoryEventType(type, statusCode, timeSpent, payload);
    expect(result).toEqual(expectedResult);
  });
});

// use for migrate script only
describe('Test Function: getTimeSpentVideo', () => {
  it.each([
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: null,
      currentTime: 0,
      maxSeekTime: 0,
      expectedResult: 0,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: null,
      currentTime: 0,
      maxSeekTime: 7,
      expectedResult: 7,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: null,
      currentTime: 5,
      maxSeekTime: 0,
      expectedResult: 5,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: null,
      currentTime: 5,
      maxSeekTime: 7,
      expectedResult: 7,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 0,
      currentTime: 0,
      maxSeekTime: 0,
      expectedResult: 0,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 0,
      currentTime: 0,
      maxSeekTime: 7,
      expectedResult: 7,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 0,
      currentTime: 5,
      maxSeekTime: 0,
      expectedResult: 5,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 0,
      currentTime: 5,
      maxSeekTime: 7,
      expectedResult: 7,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 2,
      currentTime: 0,
      maxSeekTime: 0,
      expectedResult: 2,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 2,
      currentTime: 0,
      maxSeekTime: 7,
      expectedResult: 2,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 2,
      currentTime: 5,
      maxSeekTime: 0,
      expectedResult: 2,
    },
    {
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: 2,
      currentTime: 5,
      maxSeekTime: 7,
      expectedResult: 2,
    },
  ])(
    '#$# when status: IN_PROGRESS timeSpent: $timeSpent, currentTime: $currentTime, maxSeekTime: $maxSeekTime \t expected: $expectedResult',
    ({ statusCode, timeSpent, currentTime, maxSeekTime, expectedResult }) => {
      const result = getTimeSpentVideo(statusCode, timeSpent, currentTime, maxSeekTime);
      expect(result).toEqual(expectedResult);
    },
  );

  it.each([
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: null,
      currentTime: 0,
      maxSeekTime: 0,
      expectedResult: 0,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: null,
      currentTime: 0,
      maxSeekTime: 7,
      expectedResult: 7,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: null,
      currentTime: 5,
      maxSeekTime: 7,
      expectedResult: 12,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: 0,
      currentTime: 0,
      maxSeekTime: 0,
      expectedResult: 0,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: 0,
      currentTime: 0,
      maxSeekTime: 7,
      expectedResult: 7,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: 0,
      currentTime: 5,
      maxSeekTime: 0,
      expectedResult: 5,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: 0,
      currentTime: 5,
      maxSeekTime: 7,
      expectedResult: 12,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: 7,
      currentTime: 0,
      maxSeekTime: 7,
      expectedResult: 7,
    },
    {
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: 12,
      currentTime: 5,
      maxSeekTime: 7,
      expectedResult: 12,
    },
  ])(
    '#$# when status: COMPLETED timeSpent: $timeSpent, currentTime: $currentTime, maxSeekTime: $maxSeekTime \t expected: $expectedResult',
    ({ statusCode, timeSpent, currentTime, maxSeekTime, expectedResult }) => {
      const result = getTimeSpentVideo(statusCode, timeSpent, currentTime, maxSeekTime);
      expect(result).toEqual(expectedResult);
    },
  );
});
