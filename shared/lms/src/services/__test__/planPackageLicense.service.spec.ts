import { Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { v4 } from 'uuid';

import {
  AvailablePeriodLicenseByUserParams,
  PlanPackageLicenseParams,
} from '@shared/lms/constants/types/planPackageLicense.type';
import {
  checkCompulsoryEnrollableInAvailablePeriodLicense,
  checkHavePlanPackageLicenseAvailableToday,
  checkVoluntaryEnrollableInAvailablePeriodLicense,
  getAvailablePeriodLicenseByUser,
} from '@shared/lms/services/planPackageLicense.service';

const createMockPlanPackageLicense = (props?: Partial<PlanPackageLicenseParams>): PlanPackageLicenseParams => {
  return {
    id: props?.id ?? v4(),
    planPackageId: props?.planPackageId ?? 'mock-planPackageId',
    userId: props?.userId ?? 'mock-userId',
    startedAt: props?.startedAt ?? null,
    expiredAt: props?.expiredAt ?? null,
    createdAt: props?.createdAt ?? date().toDate(),
    updatedAt: props?.updatedAt ?? date().toDate(),
    deletedAt: props?.deletedAt ?? null,
  };
};

const createMockCombinePlanPackageLicenseAvailable = (
  props?: Partial<AvailablePeriodLicenseByUserParams>,
): AvailablePeriodLicenseByUserParams => {
  return {
    planPackageId: props?.planPackageId ?? v4(),
    planPackageLicenseIds: props?.planPackageLicenseIds ?? [v4()],
    userId: props?.userId ?? 'mock-userId',
    startedAt: props?.startedAt ?? date().toDate(),
    expiredAt: props?.expiredAt ?? date().toDate(),
  };
};

describe('Test Function: getAvailablePeriodLicenseByUser', () => {
  const mockUserId = 'mock-userId';
  it('when empty array, expect: empty array', () => {
    const mockPlanPackageLicenses: PlanPackageLicenseParams[] = [];

    const result = getAvailablePeriodLicenseByUser(mockPlanPackageLicenses, mockUserId);
    expect(result.length).toEqual(0);
  });

  it('when startedAt NULL expiredAt NULL, expect: empty array', () => {
    const mockPlanPackageLicenses: PlanPackageLicenseParams[] = [
      createMockPlanPackageLicense({
        startedAt: null,
        expiredAt: null,
      }),
      createMockPlanPackageLicense({
        startedAt: null,
        expiredAt: null,
      }),
    ];

    const result = getAvailablePeriodLicenseByUser(mockPlanPackageLicenses, mockUserId);
    expect(result.length).toEqual(0);
  });

  it('when not gap date 2 items, expect: 1 items with union', () => {
    const mockPlanPackageLicenses: PlanPackageLicenseParams[] = [
      createMockPlanPackageLicense({
        startedAt: date().add(1, 'day').startOf('day').toDate(),
        expiredAt: date().add(4, 'day').endOf('day').toDate(),
      }),
      createMockPlanPackageLicense({
        startedAt: date().add(5, 'day').startOf('day').toDate(),
        expiredAt: date().add(7, 'day').endOf('day').toDate(),
      }),
    ];

    const result = getAvailablePeriodLicenseByUser(mockPlanPackageLicenses, mockUserId);
    expect(result.length).toEqual(1);
    expect(result[0].startedAt).toEqual(date().add(1, 'day').startOf('day').toDate());
    expect(result[0].expiredAt).toEqual(date().add(7, 'day').endOf('day').toDate());
  });

  it('when have gap date 2 items, expect: 2 items', () => {
    const mockPlanPackageLicenses: PlanPackageLicenseParams[] = [
      createMockPlanPackageLicense({
        startedAt: date().add(1, 'day').startOf('day').toDate(),
        expiredAt: date().add(4, 'day').endOf('day').toDate(),
      }),
      createMockPlanPackageLicense({
        startedAt: date().add(6, 'day').startOf('day').toDate(),
        expiredAt: date().add(7, 'day').endOf('day').toDate(),
      }),
    ];

    const result = getAvailablePeriodLicenseByUser(mockPlanPackageLicenses, mockUserId);
    expect(result.length).toEqual(2);
  });

  it('when overlap date 2 items, expect: 1 items with union', () => {
    const mockPlanPackageLicenses: PlanPackageLicenseParams[] = [
      createMockPlanPackageLicense({
        startedAt: date().add(1, 'day').startOf('day').toDate(),
        expiredAt: date().add(4, 'day').endOf('day').toDate(),
      }),
      createMockPlanPackageLicense({
        startedAt: date().add(3, 'day').startOf('day').toDate(),
        expiredAt: date().add(7, 'day').endOf('day').toDate(),
      }),
    ];

    const result = getAvailablePeriodLicenseByUser(mockPlanPackageLicenses, mockUserId);
    expect(result.length).toEqual(1);
    expect(result[0].startedAt).toEqual(date().add(1, 'day').startOf('day').toDate());
    expect(result[0].expiredAt).toEqual(date().add(7, 'day').endOf('day').toDate());
  });

  it('when overlap date 2 items and gap 1 item, expect: 2 items with union', () => {
    const mockPlanPackageLicenses: PlanPackageLicenseParams[] = [
      createMockPlanPackageLicense({
        startedAt: date().add(1, 'day').startOf('day').toDate(),
        expiredAt: date().add(4, 'day').endOf('day').toDate(),
      }),
      createMockPlanPackageLicense({
        startedAt: date().add(5, 'day').startOf('day').toDate(),
        expiredAt: date().add(7, 'day').endOf('day').toDate(),
      }),
      createMockPlanPackageLicense({
        startedAt: date().add(9, 'day').startOf('day').toDate(),
        expiredAt: date().add(10, 'day').endOf('day').toDate(),
      }),
    ];

    const result = getAvailablePeriodLicenseByUser(mockPlanPackageLicenses, mockUserId);
    expect(result.length).toEqual(2);
    expect(result[0].startedAt).toEqual(date().add(1, 'day').startOf('day').toDate());
    expect(result[0].expiredAt).toEqual(date().add(7, 'day').endOf('day').toDate());
    expect(result[1].startedAt).toEqual(date().add(9, 'day').startOf('day').toDate());
    expect(result[1].expiredAt).toEqual(date().add(10, 'day').endOf('day').toDate());
  });

  it('when overlap date 2 items and gap 1 item but not order, expect: 2 items with union', () => {
    const mockPlanPackageLicenses: PlanPackageLicenseParams[] = [
      createMockPlanPackageLicense({
        startedAt: date().add(5, 'day').startOf('day').toDate(),
        expiredAt: date().add(7, 'day').endOf('day').toDate(),
      }),
      createMockPlanPackageLicense({
        startedAt: date().add(1, 'day').startOf('day').toDate(),
        expiredAt: date().add(4, 'day').endOf('day').toDate(),
      }),
      createMockPlanPackageLicense({
        startedAt: date().add(9, 'day').startOf('day').toDate(),
        expiredAt: date().add(10, 'day').endOf('day').toDate(),
      }),
    ];

    const result = getAvailablePeriodLicenseByUser(mockPlanPackageLicenses, mockUserId);
    expect(result.length).toEqual(2);
    expect(result[0].startedAt).toEqual(date().add(1, 'day').startOf('day').toDate());
    expect(result[0].expiredAt).toEqual(date().add(7, 'day').endOf('day').toDate());
    expect(result[1].startedAt).toEqual(date().add(9, 'day').startOf('day').toDate());
    expect(result[1].expiredAt).toEqual(date().add(10, 'day').endOf('day').toDate());
  });
});

describe('Test Function: checkHavePlanPackageLicenseAvailableToday', () => {
  it('when empty array, expect: false', () => {
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [];

    const isEnrollableInPlanPackageContent = checkHavePlanPackageLicenseAvailableToday(mockPlanPackageLicenses);

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });

  it('when today not match planPackageLicense, expect: false', () => {
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(5, 'day').toDate(),
        expiredAt: date().add(7, 'day').toDate(),
      }),
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-10, 'day').toDate(),
        expiredAt: date().add(-1, 'day').toDate(),
      }),
    ];

    const isEnrollableInPlanPackageContent = checkHavePlanPackageLicenseAvailableToday(mockPlanPackageLicenses);

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });

  it('when today match planPackageLicense, expect: true', () => {
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-10, 'day').toDate(),
        expiredAt: date().add(-3, 'day').toDate(),
      }),
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-1, 'day').toDate(),
        expiredAt: date().add(1, 'day').toDate(),
      }),
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(4, 'day').toDate(),
        expiredAt: date().add(7, 'day').toDate(),
      }),
    ];

    const isEnrollableInPlanPackageContent = checkHavePlanPackageLicenseAvailableToday(mockPlanPackageLicenses);

    expect(isEnrollableInPlanPackageContent).toEqual(true);
  });
});

describe('Test Function: checkVoluntaryEnrollableInAvailablePeriodLicense', () => {
  it('when empty array, expect: false', () => {
    const enrollmentStartedAt: Nullable<Date> = date().toDate();
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [];

    const isEnrollableInPlanPackageContent = checkVoluntaryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });

  it('when enrollment startedAt NULL, expect: false', () => {
    const enrollmentStartedAt: Nullable<Date> = null;
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable(),
    ];

    const isEnrollableInPlanPackageContent = checkVoluntaryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });

  it('when enrollment period match planPackageLicense available, expect: true', () => {
    const enrollmentStartedAt: Nullable<Date> = date().toDate();
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(5, 'day').toDate(),
      }),
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-10, 'day').toDate(),
      }),
    ];

    const isEnrollableInPlanPackageContent = checkVoluntaryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(true);
  });

  it('when enrollment period not match planPackageLicense available, expect: false', () => {
    const enrollmentStartedAt: Nullable<Date> = date().toDate();
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-10, 'day').toDate(),
        expiredAt: date().add(-3, 'day').toDate(),
      }),
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(4, 'day').toDate(),
        expiredAt: date().add(7, 'day').toDate(),
      }),
    ];

    const isEnrollableInPlanPackageContent = checkVoluntaryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });
});

describe('Test Function: checkCompulsoryEnrollableInAvailablePeriodLicense', () => {
  it('when empty array, expect: false', () => {
    const enrollmentStartedAt: Nullable<Date> = date().toDate();
    const enrollmentExpired: Nullable<Date> = date().toDate();
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [];

    const isEnrollableInPlanPackageContent = checkCompulsoryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      enrollmentExpired,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });

  it('when enrollment expiredAt NULL, expect: false', () => {
    const enrollmentStartedAt: Nullable<Date> = date().toDate();
    const enrollmentExpired: Nullable<Date> = null;
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable(),
    ];

    const isEnrollableInPlanPackageContent = checkCompulsoryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      enrollmentExpired,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });

  it('when enrollment period match planPackageLicense available, expect: true', () => {
    const enrollmentStartedAt: Nullable<Date> = date().toDate();
    const enrollmentExpired: Nullable<Date> = date().add(2, 'day').toDate();
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-1, 'day').toDate(),
        expiredAt: date().add(5, 'day').toDate(),
      }),
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-10, 'day').toDate(),
        expiredAt: date().add(-3, 'day').toDate(),
      }),
    ];

    const isEnrollableInPlanPackageContent = checkCompulsoryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      enrollmentExpired,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(true);
  });

  it('when enrollment period not match planPackageLicense available, expect: false', () => {
    const enrollmentStartedAt: Nullable<Date> = date().toDate();
    const enrollmentExpired: Nullable<Date> = date().add(2, 'day').toDate();
    const mockPlanPackageLicenses: AvailablePeriodLicenseByUserParams[] = [
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-10, 'day').toDate(),
        expiredAt: date().add(-3, 'day').toDate(),
      }),
      createMockCombinePlanPackageLicenseAvailable({
        startedAt: date().add(-4, 'day').toDate(),
        expiredAt: date().add(-1, 'day').toDate(),
      }),
    ];

    const isEnrollableInPlanPackageContent = checkCompulsoryEnrollableInAvailablePeriodLicense(
      enrollmentStartedAt,
      enrollmentExpired,
      mockPlanPackageLicenses,
    );

    expect(isEnrollableInPlanPackageContent).toEqual(false);
  });
});
