import { GenericID, Nullable } from '@iso/constants/commonTypes';

import { AchievementPostTestCriteriaTypeEnum } from '@shared/lms/constants/enums/achievement.enum';
import { ClassroomLocationEnrollmentStatusEnum } from '@shared/lms/constants/enums/classroomLocationEnrollment.enum';
import { CourseItemCriteriaConfigTypeEnum } from '@shared/lms/constants/enums/courseItemCriteriaConfig.enum';
import { CourseItemStatusCodeEnum, CourseItemTypeEnum } from '@shared/lms/constants/enums/courseItemProgress.enum';
import { CourseItemProgressHistoryEventTypeEnum } from '@shared/lms/constants/enums/courseItemProgressHistory.enum';
import { CourseVersionCriteriaTimeSpentTypeEnum } from '@shared/lms/constants/enums/courseVersion.enum';
import { MaterialMediaTypeEnum } from '@shared/lms/constants/enums/materialMedia.enum';
import { QuizTestTypeEnum } from '@shared/lms/constants/enums/quiz.enum';
import {
  AchievementLearningProgressCriteriaParams,
  AchievementQuizCriteriaParams,
  AchievementTimeSpentCriteriaParams,
} from '@shared/lms/constants/types/achievement.type';
import { CourseItemDetailParams } from '@shared/lms/constants/types/courseItem.type';
import { CourseItemCriteriaConfigParams } from '@shared/lms/constants/types/courseItemCriteriaConfig.type';
import {
  CourseVersionCompletionCriteriaParams,
  CourseVersionLearningProgressCriteriaParams,
  CourseVersionTimeSpentCriteriaParams,
} from '@shared/lms/constants/types/courseVersion.type';
import { QuizAnswerParams } from '@shared/lms/constants/types/quizAnswer.type';

export function formatDurationContextTH(secs: number): string {
  if (secs === undefined || secs === 0) {
    return '0 นาที';
  }
  const secNum = parseInt(secs.toString(), 10);
  const hours = Math.floor(secNum / 3600);
  const minutes = Math.floor(secNum / 60) % 60;

  let str = '';
  if (hours > 0) {
    str += `${hours} ชม. `;
  }
  if (minutes > 0 || hours > 0) {
    str += `${minutes} นาที`;
  } else {
    str = 'น้อยกว่า 1 นาที';
  }
  return str;
}

export function isPassedCriteriaLearningProgress(
  learningProgress: Record<string, any>[],
  learningProgressCriteria: CourseVersionLearningProgressCriteriaParams | AchievementLearningProgressCriteriaParams,
  totalCourseItems: number,
) {
  if (!learningProgressCriteria.isEnabled) return true;

  const totalCourseItemPassed = learningProgress.filter((item) => {
    return item.statusCode === CourseItemStatusCodeEnum.COMPLETE;
  });

  return (totalCourseItemPassed.length / totalCourseItems) * 100 >= learningProgressCriteria.percentage;
}

export function isPassedCriteriaTimeSpent(
  learningProgress: Record<string, any>[],
  courseItemDurations: { id: GenericID; duration: number; type: MaterialMediaTypeEnum }[],
  timeSpentCriteria: CourseVersionTimeSpentCriteriaParams | AchievementTimeSpentCriteriaParams,
  isCountdownArticle: boolean,
  totalDuration: number,
): boolean {
  const { isEnabled, type, courseTimeSpentPercentage, courseItemTimeSpentPercentage } = timeSpentCriteria;

  if (!isEnabled) return true;

  if (type === CourseVersionCriteriaTimeSpentTypeEnum.COURSE_TIME_SPENT) {
    const totalTimeSpent = learningProgress
      .filter(
        (item) =>
          (item.type === MaterialMediaTypeEnum.ARTICLE && isCountdownArticle) ||
          item.type === MaterialMediaTypeEnum.VIDEO,
      )
      .map((item) => item.timeSpent)
      .reduce((a, b) => a + b, 0);

    return (totalTimeSpent / totalDuration) * 100 >= courseTimeSpentPercentage;
  }

  if (type === CourseVersionCriteriaTimeSpentTypeEnum.COURSE_ITEM_TIME_SPENT) {
    const totalCourseItemPassed = learningProgress.filter((item) => {
      if (
        (item.type === MaterialMediaTypeEnum.ARTICLE && isCountdownArticle) ||
        item.type === MaterialMediaTypeEnum.VIDEO
      ) {
        const duration = courseItemDurations.find((courseItem) => courseItem.id === item.courseItemId)?.duration;

        if (!duration) return false;

        const timeSpentPercentage = (item.timeSpent / duration) * 100;
        return timeSpentPercentage >= courseItemTimeSpentPercentage;
      }
    });
    const totalCourseItemCriteriaTimeSpent = courseItemDurations.filter(
      (item) =>
        (item.type === MaterialMediaTypeEnum.ARTICLE && isCountdownArticle) ||
        item.type === MaterialMediaTypeEnum.VIDEO,
    );

    return totalCourseItemPassed.length >= totalCourseItemCriteriaTimeSpent.length;
  }

  return false;
}

function isPassedCriteriaQuiz(
  learningProgress: Record<string, any>[],
  quizCriteriaConfigs: CourseItemCriteriaConfigParams[],
): boolean {
  const passedQuizCriteriaConfigs = quizCriteriaConfigs.filter((config) => {
    const courseItem = learningProgress.find((item) => item.courseItemId === config.courseItemId);
    const isPassQuizCriteria = courseItem?.quizAnswer?.criteriaCertificate?.isPass;

    return isPassQuizCriteria;
  });

  return passedQuizCriteriaConfigs.length === quizCriteriaConfigs.length;
}

function isPassedCriteriaClassroom(
  learningProgress: Record<string, any>[],
  classroomCriteriaConfigs: CourseItemCriteriaConfigParams[],
): boolean {
  const classroomCriteriaConfigPassList = classroomCriteriaConfigs.filter((config) => {
    const isPassClassroomCriteria =
      learningProgress.find((item) => item.courseItemId === config.courseItemId)?.statusCode ===
      CourseItemStatusCodeEnum.COMPLETE;

    return isPassClassroomCriteria;
  });

  return classroomCriteriaConfigs.length === classroomCriteriaConfigPassList.length;
}

export function isPassedAllLearningCriteria(params: {
  learningProgress: Record<string, any>[];
  courseItemDurations: { id: GenericID; duration: number; type: MaterialMediaTypeEnum }[];
  completionCriteria: CourseVersionCompletionCriteriaParams;
  courseItemCriteriaConfigs: CourseItemCriteriaConfigParams[];
  isCountdownArticle: boolean;
  totalDurationSec: number;
  totalDurationArticleSec: number;
  totalCourseItems: number;
}): boolean {
  const {
    learningProgress,
    courseItemDurations,
    completionCriteria,
    courseItemCriteriaConfigs,
    isCountdownArticle,
    totalDurationSec,
    totalDurationArticleSec,
    totalCourseItems,
  } = params;

  const isPassedLearningProgress = isPassedCriteriaLearningProgress(
    learningProgress,
    completionCriteria.learningProgress,
    totalCourseItems,
  );

  const isPassedTimeSpent = isPassedCriteriaTimeSpent(
    learningProgress,
    courseItemDurations,
    completionCriteria.timeSpent,
    isCountdownArticle,
    totalDurationSec + (isCountdownArticle ? totalDurationArticleSec : 0),
  );

  const quizCriteriaConfigs = courseItemCriteriaConfigs.filter(
    (item) => item.courseItemType === CourseItemCriteriaConfigTypeEnum.QUIZ && item.isEnabled,
  );

  const classroomCriteriaConfigs = courseItemCriteriaConfigs.filter(
    (item) => item.courseItemType === CourseItemCriteriaConfigTypeEnum.CLASSROOM && item.isEnabled,
  );

  const isPassedQuiz = isPassedCriteriaQuiz(learningProgress, quizCriteriaConfigs);

  const isPassedClassroom = isPassedCriteriaClassroom(learningProgress, classroomCriteriaConfigs);

  return isPassedLearningProgress && isPassedTimeSpent && isPassedQuiz && isPassedClassroom;
}

export function isPassedCriteriaPostTestQuiz(
  quizAnswersData: QuizAnswerParams[],
  courseItems: CourseItemDetailParams[],
  quizCriteria: AchievementQuizCriteriaParams,
): boolean {
  const { isEnabled, type } = quizCriteria;
  if (!isEnabled) return true;

  const quizPostTestList = courseItems
    .filter((courseItem) => courseItem.testType === QuizTestTypeEnum.POST_TEST && !!courseItem.quizId)
    .map((courseItem) => ({ quizId: courseItem.quizId, questions: courseItem.questions }));

  if (!quizPostTestList?.length) return false;

  if (!quizAnswersData?.length) return false;

  const quizAnswerMapperById = quizAnswersData?.reduce(
    (acc, quizAnswer) => {
      if (!quizAnswer?.id) return acc;

      acc[quizAnswer.quizId] = quizAnswer;
      return acc;
    },
    {} as Record<GenericID, QuizAnswerParams>,
  );

  if (type === AchievementPostTestCriteriaTypeEnum.ALL_POST_TEST_QUIZ) {
    const { allPostTestScorePercentage } = quizCriteria;

    return quizPostTestList.every((quiz) => {
      const quizAnswer = quizAnswerMapperById[quiz.quizId];
      const userQuizAnswerPercentage = quizAnswer.scorePercents;
      return userQuizAnswerPercentage >= allPostTestScorePercentage;
    });
  }

  if (type === AchievementPostTestCriteriaTypeEnum.LAST_POST_TEST_QUIZ) {
    const { lastPostTestScorePercentage } = quizCriteria;
    const lastPostTestQuiz = quizPostTestList.at(-1);

    const quizAnswer = quizAnswerMapperById[lastPostTestQuiz.quizId];
    const userQuizAnswerPercentage = quizAnswer.scorePercents;
    return userQuizAnswerPercentage >= lastPostTestScorePercentage;
  }

  return false;
}

export const getCourseItemProgressHistoryEventType = (
  type: string,
  statusCode: CourseItemStatusCodeEnum,
  timeSpent: Nullable<number>,
  payload: Record<string, any>,
): Nullable<CourseItemProgressHistoryEventTypeEnum> => {
  if (payload?.logSnapshotEnrollmentId && type !== CourseItemTypeEnum.Classroom) {
    return CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS;
  }

  switch (type) {
    case CourseItemTypeEnum.Video:
      if (statusCode === CourseItemStatusCodeEnum.IN_PROGRESS && !timeSpent) {
        return CourseItemProgressHistoryEventTypeEnum.START_VIDEO;
      }

      if (statusCode === CourseItemStatusCodeEnum.IN_PROGRESS && timeSpent > 0) {
        return CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_VIDEO;
      }

      if (statusCode === CourseItemStatusCodeEnum.COMPLETE && timeSpent > payload?.duration) {
        return CourseItemProgressHistoryEventTypeEnum.REVISIT_VIDEO;
      }

      if (statusCode === CourseItemStatusCodeEnum.COMPLETE) {
        return CourseItemProgressHistoryEventTypeEnum.COMPLETED_VIDEO;
      }

      return null;

    case CourseItemTypeEnum.Quiz:
      if (payload?.finishedAt) {
        return CourseItemProgressHistoryEventTypeEnum.COMPLETED_QUIZ;
      }

      if (statusCode === CourseItemStatusCodeEnum.IN_PROGRESS && !timeSpent) {
        return CourseItemProgressHistoryEventTypeEnum.START_QUIZ;
      }

      if (statusCode === CourseItemStatusCodeEnum.IN_PROGRESS && timeSpent > 0) {
        return CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_QUIZ;
      }

      if (!payload?.finishedAt) {
        return CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_QUIZ;
      }

      return null;

    case CourseItemTypeEnum.Article:
      if (statusCode === CourseItemStatusCodeEnum.IN_PROGRESS && !timeSpent) {
        return CourseItemProgressHistoryEventTypeEnum.START_ARTICLE;
      }

      if (statusCode === CourseItemStatusCodeEnum.IN_PROGRESS && timeSpent > 0) {
        return CourseItemProgressHistoryEventTypeEnum.IN_PROGRESS_ARTICLE;
      }

      if (statusCode === CourseItemStatusCodeEnum.COMPLETE) {
        return CourseItemProgressHistoryEventTypeEnum.COMPLETED_ARTICLE;
      }

      return null;

    case CourseItemTypeEnum.Survey:
      if (statusCode === CourseItemStatusCodeEnum.COMPLETE) {
        return CourseItemProgressHistoryEventTypeEnum.COMPLETED_SURVEY;
      }

      return null;

    case CourseItemTypeEnum.Classroom:
      if (
        payload?.logSnapshotEnrollmentId &&
        [
          ClassroomLocationEnrollmentStatusEnum.PASSED,
          ClassroomLocationEnrollmentStatusEnum.NOT_PASS,
          ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL,
          ClassroomLocationEnrollmentStatusEnum.CANCELED,
        ].includes(payload?.status)
      ) {
        return CourseItemProgressHistoryEventTypeEnum.RESET_LEARNING_PROGRESS;
      }

      if (
        payload?.logSnapshotEnrollmentId &&
        [ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL, ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS].includes(
          payload?.status,
        )
      ) {
        return CourseItemProgressHistoryEventTypeEnum.CANCEL_CLASSROOM;
      }

      if (
        statusCode === CourseItemStatusCodeEnum.IN_PROGRESS &&
        [ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL, ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS].includes(
          payload?.status,
        )
      ) {
        return CourseItemProgressHistoryEventTypeEnum.REGISTER_CLASSROOM;
      }

      if (
        statusCode === CourseItemStatusCodeEnum.IN_PROGRESS &&
        payload?.status === ClassroomLocationEnrollmentStatusEnum.CANCELED
      ) {
        return CourseItemProgressHistoryEventTypeEnum.CANCEL_CLASSROOM;
      }

      if (
        statusCode === CourseItemStatusCodeEnum.IN_PROGRESS &&
        payload?.status === ClassroomLocationEnrollmentStatusEnum.NOT_PASS
      ) {
        return CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM;
      }

      if (
        statusCode === CourseItemStatusCodeEnum.COMPLETE &&
        payload?.status === ClassroomLocationEnrollmentStatusEnum.PASSED
      ) {
        return CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM;
      }

      return null;

    default:
      return null;
  }
};

export const getTimeSpentVideo = (
  statusCode: CourseItemStatusCodeEnum,
  timeSpent: Nullable<number>,
  currentTime: number,
  maxSeekTime: number,
) => {
  let result = timeSpent;
  if (statusCode === CourseItemStatusCodeEnum.COMPLETE && !timeSpent) {
    result = !timeSpent ? maxSeekTime + currentTime : maxSeekTime;
  }
  if (statusCode === CourseItemStatusCodeEnum.IN_PROGRESS) {
    result = timeSpent || maxSeekTime || currentTime;
  }

  return result;
};
