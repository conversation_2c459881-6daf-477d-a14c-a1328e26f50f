import { Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';

import { AnnouncementStatusEnum } from '@shared/lms/constants/enums/announcement.enum';

export const convertAnnouncementStatus = (
  isEnabled: boolean,
  publishedStartAt: Nullable<Date>,
  publishedEndAt: Nullable<Date>,
): AnnouncementStatusEnum => {
  const now = date().toDate();
  const startDate = publishedStartAt ? date(publishedStartAt).toDate() : null;
  const endDate = publishedEndAt ? date(publishedEndAt).toDate() : null;

  if (!isEnabled && !startDate && !endDate) {
    return AnnouncementStatusEnum.DRAFT;
  }

  if (!isEnabled && (startDate || endDate)) {
    return AnnouncementStatusEnum.UNPUBLISHED;
  }

  if (startDate && date(now).isBefore(startDate)) {
    return AnnouncementStatusEnum.SCHEDULED;
  }

  if (endDate && date(now).isSameOrAfter(endDate)) {
    return AnnouncementStatusEnum.EXPIRED;
  }

  return AnnouncementStatusEnum.PUBLISHED;
};
