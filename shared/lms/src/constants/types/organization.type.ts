import { GenericID, Nullable } from '@iso/constants/commonTypes';

import {
  ApplicantTypeEnum,
  CourseObjectiveTypeEnum,
  LicenseRenewalEnum,
  LicenseTypeEnum,
} from '@shared/lms/constants/enums/course.enum';
import { UserLicenseTypeCodeEnum } from '@shared/lms/constants/enums/license.enum';
import { HomePagePathEnum, LivenessModeEnum } from '@shared/lms/constants/enums/organization.enum';
import { KeyNameItem, BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type OrganizationParams = BaseEntityParams & {
  domain: string;
  fqdn: string;
  name: string;
  nameEng: string;
  availableCourseObjectiveTypes: CourseObjectiveTypeEnum[];
  courseObjectiveConfigs: OrganizationCourseObjectiveConfigParams[];
  videoAuthenInstruction: OrganizationVideoAuthenInstructionParams;
  organizationContact: OrganizationContactParams;
  userValidateTemplate: OrganizationUserValidationTemplateParams;
  certificateConfig: OrganizationCertificateConfigParams;
  themeConfig: OrganizationThemeConfigParams;
  isEnableLocalLogin: boolean;
  validationPasswordConfig: OrganizationValidationPasswordConfigParams;
  tokenConfig: OrganizationTokenConfigParams;
  autoApprovalEnrollmentConfig?: Nullable<OrganizationAutoApprovalEnrollmentConfigParams>;
  featureConfig?: OrganizationFeatureConfigParams;
  notificationConfig?: Nullable<OrganizationNotificationConfigParams>;
  transactionCounter?: OrganizationTransactionCounterParams;
  licensesConfiguration?: Nullable<OrganizationLicensesConfigurationParams>;
  passwordPolicyConfig?: Nullable<OrganizationPasswordPolicyConfigParams>;
  isLivenessEnabled: boolean;
  livenessMode?: LivenessModeEnum;
  isEnabled: boolean;
  createByAdminUserId: GenericID;
};

export type CreateOrganizationParams = {
  domain: string;
  fqdn: string;
  name: string;
  nameEng: string;
  availableCourseObjectiveTypes: CourseObjectiveTypeEnum[];
  courseObjectiveConfigs: OrganizationCourseObjectiveConfigParams[];
  videoAuthenInstruction: OrganizationVideoAuthenInstructionParams;
  organizationContact: OrganizationContactParams;
  userValidateTemplate: OrganizationUserValidationTemplateParams;
  certificateConfig: OrganizationCertificateConfigParams;
  themeConfig: OrganizationThemeConfigParams;
  isEnableLocalLogin: boolean;
  validationPasswordConfig: OrganizationValidationPasswordConfigParams;
  tokenConfig: OrganizationTokenConfigParams;
  autoApprovalEnrollmentConfig?: Nullable<OrganizationAutoApprovalEnrollmentConfigParams>;
  featureConfig?: OrganizationFeatureConfigParams;
  notificationConfig?: Nullable<OrganizationNotificationConfigParams>;
  transactionCounter?: OrganizationTransactionCounterParams;
  licensesConfiguration?: Nullable<OrganizationLicensesConfigurationParams>;
  passwordPolicyConfig?: Nullable<OrganizationPasswordPolicyConfigParams>;
  isLivenessEnabled: boolean;
  livenessMode?: LivenessModeEnum;
  isEnabled: boolean;
  createByAdminUserId: GenericID;
};

export type OrganizationTransactionCounterParams = {
  materialMediaVideoCode: number;
  materialMediaArticleCode: number;
  materialMediaQuizCode: number;
  materialMediaSurveyCode: number;
  materialMediaClassroomCode: number;
  materialMediaAudioCode: number;
  materialMediaAttachmentCode: number;
  classroomLocationCode: number;
};

export type OrganizationThemeConfigParams = {
  homePagePath: HomePagePathEnum;
  appearance: OrganizationThemeAppearanceParams;
  login: OrganizationThemeLoginParams;
  email: OrganizationThemeEmailParams;
  footer: OrganizationThemeFooterParams;
  contact: OrganizationThemeContactParams;
};

export type OrganizationVideoAuthenInstructionParams = {
  path: string;
  duration: string;
};

export type OrganizationUserValidationTemplateParams = {
  defaultPath: string;
  customTemplate: OrganizationCustomerTemplateParams[];
};

export type OrganizationCustomerTemplateParams = {
  customerCode: string;
  path: string;
};

export type OrganizationCertificateConfigParams = {
  logoImageUrl: string;
  textDynamicCertificate: string;
  certificateTypes: Record<string, any>[];
};

export type OutputGetUserValidationTemplateParams = { userValidateTemplate: string };

export type OutputOrganizationPermissionGroupParams = {
  id: GenericID;
  name: string;
};

export type OrganizationValidationPasswordConfigParams = {
  min: number;
  max: number;
  isRequiredUppercase: boolean;
  isRequiredLowercase: boolean;
  isRequiredNumber: boolean;
  isNotAllowWhitespace: boolean;
  isRequiredSymbol: boolean;
  customRequiredSymbols: string[];
};

export type OrganizationTokenConfigParams = {
  accessTokenExpiration: number;
  refreshTokenExpiration: number;
};

export type OrganizationAutoApprovalEnrollmentConfigParams = {
  autoApproveCriteria: OrganizationAutoApprovalEnrollmentCriteriaParams;
  autoVerifyCriteria: OrganizationAutoApprovalEnrollmentCriteriaParams;
};

export type OrganizationAutoApprovalRangePercentageParams = {
  minimum: number;
  maximum: number;
};

export type OrganizationAutoApprovalEnrollmentCriteriaParams = {
  isActive: boolean;
  isAllDocsIsApprove: boolean;
  isUploadIdCard: boolean;
  totalLivenessPercent: number;
  citizenIdMatchPercent: OrganizationAutoApprovalRangePercentageParams;
  fullNameMatchPercent: OrganizationAutoApprovalRangePercentageParams;
};

export type OrganizationLearningConfigParams = {
  isIdentityVerificationEnabled: boolean;
  isLivenessEnabled: boolean;
  rpc: OrganizationRpcConfigParams;
  idle: OrganizationIdleConfigParams;
};

export type OrganizationRpcConfigParams = {
  isEnabled: boolean;
  durationMinute: number;
  delayMinute: number;
  stayTimeoutMinute: number;
  gapIdleMinute: number;
  forwardDelayMinute: number;
};

export type OrganizationIdleConfigParams = {
  isEnabled: boolean;
  timeoutMinute: number;
  stayTimeoutMinute: number;
};

export type OrganizationRegulatorConfigOptionParams = {
  isDenormalizeBothLicense?: boolean;
  isContainBothLicense?: boolean;
};

export type OrganizationTrainingCentersParams = KeyNameItem<string>;

export type OrganizationLicenseRenewalParams = KeyNameItem<LicenseRenewalEnum>;

export type OrganizationApplicantTypeParams = KeyNameItem<ApplicantTypeEnum> & {
  licenseRenewalKeys?: LicenseRenewalEnum[];
};

export type OrganizationLicenseTypeParams = KeyNameItem<LicenseTypeEnum> & {
  licenseRenewalKeys: LicenseRenewalEnum[];
  applicantTypeKeys: ApplicantTypeEnum[];
};

export type OrganizationCourseObjectiveConfigParams = {
  objectiveType: CourseObjectiveTypeEnum;
  regulators: OrganizationRegulatorParams[];
  trainingCenters: OrganizationTrainingCenterParams[];
  defaultLearningConfig?: OrganizationLearningConfigParams;
};

export type OrganizationThemeColorParams = {
  colorPrimary: string;
};

export type OrganizationThemeAppearanceParams = {
  theme: OrganizationThemeColorParams;
  favicon: string;
  logo: string;
  updatedByUserId: GenericID;
  updatedAt: Nullable<Date>;
};

export type OrganizationThemeLoginParams = {
  loginTitle: string;
  loginContent: string;
  loginBackground: string;
  updatedByUserId: GenericID;
  updatedAt: Nullable<Date>;
};

export type OrganizationThemeEmailParams = {
  emailBanner: string;
  emailSignature: string;
  updatedByUserId: GenericID;
  updatedAt: Nullable<Date>;
};

export type OrganizationThemeFooterParams = {
  info1: Nullable<string>;
  info2: Nullable<string>;
  updatedByUserId: GenericID;
  updatedAt: Nullable<Date>;
};

export type OrganizationThemeContactParams = {
  info: Nullable<string>;
  updatedByUserId: GenericID;
  updatedAt: Nullable<Date>;
};

export type OrganizationFeatureConfigParams = {
  isShowSearchBar: boolean;
  isCreateUser: boolean;
  isDownloadUser: boolean;
  isUpdateUser: boolean;
  isUpdatePlan: boolean;
  isUpdatePreEnrollment: boolean;
  defaultVideoQuality: string;
};

export type OrganizationContactParams = {
  userManual?: string;
  faq?: string;
};

export type OrganizationNotificationConfigParams = {
  key: string;
  senderEmail: string;
  senderName: string;
  isActive: boolean;
};

export type OrganizationAvailableLicenseConfigParams = {
  type: UserLicenseTypeCodeEnum;
  isDisplay: boolean;
};

export type OrganizationLicensesConfigurationParams = {
  availableLicensesTypes: OrganizationAvailableLicenseConfigParams[];
};

export type OrganizationPasswordPolicyConfigParams = {
  isEnabled: boolean;
  passwordExpiryDay: Nullable<number>;
};

export type OrganizationRegulatorParams = {
  name: string;
  trainingCenterKeys: string[];
  config?: Nullable<Record<string, unknown>>;
  isEnabled?: boolean;
};

export type OrganizationTrainingCenterParams = {
  key: string;
  name: string;
  regulator: string;
  licenseRenewals: OrganizationLicenseRenewalParams[];
  applicantTypes: OrganizationApplicantTypeParams[];
  licenseTypes: OrganizationLicenseTypeParams[];
  learningConfig?: OrganizationLearningConfigParams;
};

export const defaultIdleLearningConfig = Object.freeze({
  isEnabled: false,
  timeoutMinute: 25,
  stayTimeoutMinute: 5,
});

export const defaultRpcLearningConfig = Object.freeze({
  isEnabled: false,
  durationMinute: 60,
  delayMinute: 20,
  stayTimeoutMinute: 5,
  gapIdleMinute: 10,
  forwardDelayMinute: 10,
});

export const defaultRegularLearningConfig = Object.freeze({
  idle: defaultIdleLearningConfig,
  rpc: defaultRpcLearningConfig,
  isIdentityVerificationEnabled: false,
  isLivenessEnabled: false,
});

export const defaultTrainingLearningConfig = Object.freeze({
  idle: {
    isEnabled: true,
    timeoutMinute: 25,
    stayTimeoutMinute: 5,
  },
  rpc: {
    isEnabled: true,
    durationMinute: 60,
    delayMinute: 20,
    stayTimeoutMinute: 5,
    gapIdleMinute: 10,
    forwardDelayMinute: 10,
  },
  isIdentityVerificationEnabled: true,
  isLivenessEnabled: true,
});
//#endregion

//#region Business domain
export type SignInLmsParams = {
  email: string;
  organizationId: GenericID;
  userId?: GenericID;
};

export type SignInLmsResultPayload = {
  authUrl: string;
  userId: GenericID;
};
//#endregion
