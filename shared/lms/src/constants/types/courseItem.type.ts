import { GenericID, Nullable } from '@iso/constants/commonTypes';

import { MaterialMediaTypeEnum } from '@shared/lms/constants/enums/materialMedia.enum';
import { QuizTestTypeEnum } from '@shared/lms/constants/enums/quiz.enum';
import { MaterialMediaParams } from '@shared/lms/constants/types/materialMedia.type';
import {
  QuestionParams,
  QuizLimitTimeDurationParams,
  QuizRetestParams,
  QuizShowAnswerParams,
} from '@shared/lms/constants/types/quiz.type';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type AttachmentMaterialMediaParams = {
  id: GenericID;
  path?: string;
  name?: string;
  url?: string;
};

export type CourseItemParams = BaseEntityParams & {
  partId: GenericID;
  materialMediaId?: GenericID;
  type: MaterialMediaTypeEnum;
  name: string;
  description?: string;
  position?: number;
  attachmentMaterialMedia?: AttachmentMaterialMediaParams[];
  isEnabled?: boolean;
  isPreviewVideo?: boolean;
  publishedAt?: Nullable<Date>;
  materialMedia?: Nullable<MaterialMediaParams>;
};

export type CreateCourseItemParams = {
  partId: GenericID;
  materialMediaId?: GenericID;
  type: MaterialMediaTypeEnum;
  name: string;
  description?: string;
  position?: number;
  attachmentMaterialMedia?: AttachmentMaterialMediaParams[];
  isEnabled?: boolean;
  isPreviewVideo?: boolean;
  publishedAt?: Nullable<Date>;
  materialMedia?: Nullable<MaterialMediaParams>;
};
//#endregion

//#region Business domain

export type CourseItemParamsWithType = CourseItemParams & {
  materialMedia: MaterialMediaParams;
};

export type CourseItemDetailParams = {
  id: GenericID;
  partId?: GenericID;
  materialMediaId?: GenericID;

  name: string;
  type: MaterialMediaTypeEnum;
  position: number;
  isEnabled: boolean;
  duration?: number;
  description?: string;
  attachments?: AttachmentMaterialMediaParams[];

  // video
  chapterId?: number; // use for verify video token on B2C Content
  videoUrl?: string;
  isPreviewVideo?: boolean;
  transcodeStatus?: string;
  mediaId?: GenericID;

  // article
  articleId?: number;

  // survey
  surveyId?: number;
  contentHtml?: string;

  // quiz
  quizId?: number;
  testType?: QuizTestTypeEnum;
  retest?: QuizRetestParams;
  limitTimeDuration?: QuizLimitTimeDurationParams;
  showAnswer?: QuizShowAnswerParams;
  questions?: QuestionParams[];
  publishedAt?: Date;
};
//#endregion
