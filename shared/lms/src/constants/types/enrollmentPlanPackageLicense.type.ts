import { GenericID } from '@iso/constants/commonTypes';

import { PackageTypeEnum } from '@shared/lms/constants/enums/packages.enum';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type CreateEnrollmentPlanPackageLicenseParams = {
  userId: GenericID;
  enrollmentId: GenericID;
  planPackageLicenseId: GenericID;
  packageType: PackageTypeEnum;
};

export type EnrollmentPlanPackageLicenseParams = BaseEntityParams & {
  userId: GenericID;
  enrollmentId: GenericID;
  planPackageLicenseId: GenericID;
  packageType: PackageTypeEnum;
};
//#endregion

//#region Business domain

//#endregion
