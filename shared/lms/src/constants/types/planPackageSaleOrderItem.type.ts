import { GenericID } from "@iso/constants/commonTypes";

import { BaseEntityParams } from "@shared/lms/core/constant/types";

//#region Entity domain
export type PlanPackageSaleOrderItemParams = BaseEntityParams & {
  orderId: string;
  orderItemId: number;
  saleOrderNo: string;
  planPackageId: GenericID;
  note: string;
  startDate: Date;
  endDate: Date;
};

export type CreatePlanPackageSaleOrderItemParams = {
  orderId: string;
  orderItemId: number;
  saleOrderNo: string;
  planPackageId: GenericID;
  note?: string;
  startDate: Date;
  endDate: Date;
};
//#endregion

//#region Business domain

//#endregion
