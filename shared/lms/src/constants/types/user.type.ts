import { GenericID, Nullable, Optional } from '@iso/constants/commonTypes';

import { GenderEnum, UserRoleEnum } from '@shared/lms/constants/enums/user.enum';
import { BaseGuidEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type UserParams = BaseGuidEntityParams & {
  username: string;
  email: string;
  passwordHash: string;
  verified: boolean;
  citizenId: string;
  last4DigitCitizenId: string;
  organizationId: GenericID;
  customerCodes: string[];
  profile: UserProfileParams;
  active: boolean;
  isTest: boolean;
  permissions: UserPermissionParams[];
  isTwoFactorEnable: boolean;
  isTwoFactorRegister: boolean;
  isTwoFactorLogin: boolean;
  twoFactorSecret: Nullable<UserTwoFactorSecretParams>;
  regulatorProfile: Nullable<UserRegulatorProfileParams>;
  lostToken: Optional<string>;
  firstPasswordToken: string;
  additionalField: Nullable<Record<string, unknown>>;
  permissionGroupIds: GenericID[];
  permissionRegulator: Nullable<UserPermissionRegulatorParams>;
  setPasswordDate: Nullable<Date>;
  firstActiveDate: Nullable<Date>;
  isEnableLocalLogin: boolean;
  suspendedUntil: Date;
  returnUrl: Nullable<string>;
  isPassedUlSaleQualify: boolean;
  isTerminated: boolean;
  role: UserRoleEnum;
  employeeId: Nullable<string>;
  position: Nullable<string>;
};

export type CreateUserParams = {
  username: string;
  email: string;
  passwordHash: string;
  verified: boolean;
  citizenId: string;
  last4DigitCitizenId: string;
  organizationId: GenericID;
  customerCodes: string[];
  profile: UserProfileParams;
  isTest?: boolean;
  permissions?: UserPermissionParams[];
  isTwoFactorEnable: boolean;
  isTwoFactorRegister: boolean;
  isTwoFactorLogin: boolean;
  twoFactorSecret: UserTwoFactorSecretParams;
  regulatorProfile?: UserRegulatorProfileParams;
  lostToken?: string;
  firstPasswordToken: string;
  additionalField?: Nullable<Record<string, any>>;
  permissionGroupIds?: GenericID[];
  permissionRegulator?: Nullable<UserPermissionRegulatorParams>;
  setPasswordDate?: Nullable<Date>;
  firstActiveDate?: Nullable<Date>;
  isEnableLocalLogin: boolean;
  suspendedUntil?: Date;
  returnUrl?: Nullable<string>;
  isPassedUlSaleQualify?: boolean;
  isTerminated?: boolean;
  role: UserRoleEnum;
  employeeId: Nullable<string>;
  position: Nullable<string>;
};

export type UserProfileParams = {
  salute: string;
  firstname: string;
  lastname: string;
  middlename?: string;
  avatar: string;
  mobilePhoneNumber: string;
  gender: Nullable<GenderEnum>;
  dateOfBirth: Nullable<Date>;
};

export type UserPermissionParams = {
  id: GenericID;
  isGrant: boolean;
};

export type UserRegulatorProfileParams = {
  prefix: string;
  firstname: string;
  lastname: string;
};

export type UserTwoFactorSecretParams = {
  ascii: string;
  hex: string;
  base32: string;
  otpauth_url?: string;
};

export type UserPermissionRegulatorParams = {
  regulator: string;
  trainingCenterCodes: string[];
};
//#endregion

//#region Business domain

//#endregion
