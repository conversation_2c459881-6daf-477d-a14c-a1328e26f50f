import { GenericID, Nullable } from '@iso/constants/commonTypes';

import { CourseObjectiveTypeEnum } from '@shared/lms/constants/enums/course.enum';
import {
  CourseVersionCriteriaTimeSpentTypeEnum,
  CourseVersionStatusEnum,
} from '@shared/lms/constants/enums/courseVersion.enum';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type CourseVersionParams = BaseEntityParams & {
  courseId: GenericID;
  version: number;
  status: CourseVersionStatusEnum;

  name: string;
  expiryDay?: Nullable<number>;
  instructorIds?: Array<string>;
  totalCourseItems?: number;
  totalVideos?: number;
  totalQuizzes?: number;
  totalArticles?: number;
  totalSurveys?: number;
  totalClassrooms?: number;
  totalInfographics?: number;
  totalDurationSec?: number;
  totalDurationArticleSec?: number;

  isRPCEnabled?: boolean;
  isOCREnabled?: boolean;
  isLivenessEnabled?: boolean;
  isCountdownArticle?: boolean;
  isCertificateEnabled?: boolean;
  isMultipleCertificate?: boolean;
  isSentCertificateEmailOnExpiredDate?: boolean;
  isSeekEnabled?: boolean;
  isVideoSpeedEnabled?: boolean;
  isPlayVideoBackground?: boolean;
  isLearnableFullscreen?: boolean;
  isIdentityVerificationEnabled?: boolean;
  isAttentionCheckEnabled?: boolean;
  isAutoApproveEnabled?: boolean;
  publishedByUserId?: Nullable<GenericID>;
  publishedAt?: Nullable<Date>;
  report?: Nullable<ReportParams>;
  completionCriteria: CourseVersionCompletionCriteriaParams;
};

export type CreateCourseVersionParams = {
  courseId: GenericID;
  version: number;
  status: CourseVersionStatusEnum;
  name: string;
  expiryDay?: Nullable<number>;
  instructorIds?: Array<string>;
  totalCourseItems?: number;
  totalVideos?: number;
  totalQuizzes?: number;
  totalArticles?: number;
  totalSurveys?: number;
  totalClassrooms?: number;
  totalInfographics?: number;
  totalDurationSec?: number;
  totalDurationArticleSec?: number;
  isRPCEnabled?: boolean;
  isOCREnabled?: boolean;
  isLivenessEnabled?: boolean;
  isCountdownArticle?: boolean;
  isCertificateEnabled?: boolean;
  isMultipleCertificate?: boolean;
  isSentCertificateEmailOnExpiredDate?: boolean;
  isSeekEnabled?: boolean;
  isVideoSpeedEnabled?: boolean;
  isPlayVideoBackground?: boolean;
  isLearnableFullscreen?: boolean;
  isIdentityVerificationEnabled?: boolean;
  isAttentionCheckEnabled?: boolean;
  isAutoApproveEnabled?: boolean;
  publishedByUserId?: Nullable<GenericID>;
  publishedAt?: Nullable<Date>;
  report?: ReportParams;
  completionCriteria?: CourseVersionCompletionCriteriaParams;
};

export type ReportParams = {
  tsiCode: string;
  pillarName: string;
};

export type CourseVersionTimeSpentCriteriaParams = {
  isEnabled: boolean;
  type: CourseVersionCriteriaTimeSpentTypeEnum;
  courseTimeSpentPercentage: number;
  courseItemTimeSpentPercentage: number;
};

export type CourseVersionLearningProgressCriteriaParams = {
  isEnabled: boolean;
  percentage: number;
};

export type CourseVersionCompletionCriteriaParams = {
  learningProgress: CourseVersionLearningProgressCriteriaParams;
  timeSpent: CourseVersionTimeSpentCriteriaParams;
};
//#endregion

//#region Business domain

export const PreCourseVersionConfig = {
  [CourseObjectiveTypeEnum.REGULAR]: {
    isAutoApproveEnabled: false,
    isSeekEnabled: true,
    isVideoSpeedEnabled: true,
    isCountdownArticle: false,
    isCertificateEnabled: false,
    isAttentionCheckEnabled: false,
    isIdentityVerificationEnabled: false,
    isRPCEnabled: false,
    isOCREnabled: false,
    isLivenessEnabled: false,
    isPlayVideoBackground: true,
    isLearnableFullscreen: false,
    isSentCertificateEmailOnExpiredDate: false,
    expiryDay: 0,
  },
  [CourseObjectiveTypeEnum.TRAINING]: {
    isAutoApproveEnabled: true,
    isSeekEnabled: false,
    isVideoSpeedEnabled: false,
    isCountdownArticle: false,
    isCertificateEnabled: false,
    isAttentionCheckEnabled: true,
    isIdentityVerificationEnabled: true,
    isRPCEnabled: true,
    isOCREnabled: true,
    isLivenessEnabled: true,
    isPlayVideoBackground: false,
    isLearnableFullscreen: true,
    isSentCertificateEmailOnExpiredDate: true,
    expiryDay: 6,
  },
};

//#endregion
