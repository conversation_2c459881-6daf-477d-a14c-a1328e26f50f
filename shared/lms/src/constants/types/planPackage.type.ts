import { GenericID, Nullable } from '@iso/constants/commonTypes';

import {
  PackageContentModelTypeEnum,
  PackageContentTypeEnum,
  PackageTypeEnum,
} from '@shared/lms/constants/enums/packages.enum';
import { PlanPackageLicenseParams } from '@shared/lms/constants/types/planPackageLicense.type';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type PlanPackageParams = BaseEntityParams & {
  planId: GenericID;
  packageId: GenericID;
  name: string;
  description: string;
  type: PackageTypeEnum;
  content: PlanPackageContentParams;
  totalUsageDay: number;
  startDate: Date;
  gracingDate: Nullable<Date>;
  endDate: Date;
};

export type CreatePlanPackageParams = {
  planId: GenericID;
  packageId: GenericID;
  name: string;
  description: string;
  type: PackageTypeEnum;
  content: PlanPackageContentParams;
  totalUsageDay: number;
  startDate: Date;
  gracingDate?: Nullable<Date>;
  endDate: Date;
};

export type PlanPackageContentParams =
  | PlanPackageTypeContentParams
  | PlanPackageTypePlatformParams
  | PlanPackageTypeCreditParams;

export type PlanPackageTypeContentParams = {
  model: PackageContentModelTypeEnum;
  type: PackageContentTypeEnum;
  productSKUCourseIds: GenericID[];
  totalLicense: number;
  totalTransferLicense: number;
  remainLicense: number;
  remainTransferLicense: number;
};

export type PlanPackageTypePlatformParams = {
  totalLicense: number;
  totalTransferLicense: number;
  remainLicense: number;
  remainTransferLicense: number;
};

export type PlanPackageTypeCreditParams = {
  totalCredit: number;
  remainPoint: number;
};
//#endregion

//#region Business domain
export type PlanPackageWithLicenseParams = PlanPackageParams & {
  planPackageLicenses: PlanPackageLicenseParams[];
};

export const PackageContentTypeEnumLocalizeMapper: Record<PackageContentTypeEnum, string> = {
  [PackageContentTypeEnum.SKILLLANE_PLUS]: 'หลักสูตรทั่วไป',
  [PackageContentTypeEnum.ACADEMIC]: 'หลักสูตรวิชาการ',
  [PackageContentTypeEnum.ONWARD]: 'หลักสูตรไมโคร',
  [PackageContentTypeEnum.CPD]: 'หลักสูตร CPD',
};

//#endregion
