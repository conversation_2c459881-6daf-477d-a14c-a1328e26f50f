import { GenericID, Nullable } from '@iso/constants/commonTypes';

import {
  ApplicantTypeEnum,
  ContentProviderTypeEnum,
  ContentTypeEnum,
  CourseAccessTypeEnum,
  CourseEnrollTypeEnum,
  CourseObjectiveTypeEnum,
  ExternalContentTypeEnum,
  LicenseRenewalEnum,
  LicenseTypeEnum,
  RegulatorEnum,
} from '@shared/lms/constants/enums/course.enum';
import { CourseVersionStatusEnum } from '@shared/lms/constants/enums/courseVersion.enum';
import { ImagePathParams, ThumbnailMediaIdParams } from '@shared/lms/constants/types/media.type';
import { BaseEntityParams } from '@shared/lms/core/constant/types';
import { CourseVersion } from '@shared/lms/models/courseVersion.model';
import { Instructor } from '@shared/lms/models/instructor.model';

//#region Entity domain
export type CourseParams = BaseEntityParams & {
  organizationId: GenericID;
  productSKUCourseId: Nullable<GenericID>;
  code: string;
  url: string;
  contentType: ContentTypeEnum;
  contentProviderType: ContentProviderTypeEnum;
  externalContentTypes: ExternalContentTypeEnum[];
  objectiveType: Nullable<CourseObjectiveTypeEnum>;
  regulatorInfo: Nullable<CourseRegulatorParams>;
  description: string;
  thumbnailUrl: Nullable<string>;
  thumbnailId: Nullable<string>;
  thumbnailMediaId?: Nullable<ThumbnailMediaIdParams>;
  videoPreviewImage?: Nullable<string>;
  disableIntro?: boolean;
  isEnabled?: boolean;
  isSelfEnrollEnabled?: boolean;
  isReEnrollEnabled?: boolean;
  reEnrollDay?: Nullable<number>;
  isReEnrollExpireEnabled?: boolean;
  reEnrollExpireDay?: Nullable<number>;
  accessType?: CourseAccessTypeEnum;
  enrollType?: CourseEnrollTypeEnum;
  subjects: CourseSubjectParams[];
  courseVersion?: CourseVersion;
};

export type CreateCourseParams = {
  organizationId: GenericID;
  productSKUCourseId?: Nullable<GenericID>;
  code?: string;
  url?: string;
  contentType: ContentTypeEnum;
  contentProviderType: ContentProviderTypeEnum;
  externalContentTypes: ExternalContentTypeEnum[];
  objectiveType?: Nullable<CourseObjectiveTypeEnum>;
  regulatorInfo?: Nullable<CourseRegulatorParams>;
  description?: string;
  thumbnailUrl?: Nullable<string>;
  thumbnailId?: Nullable<string>;
  thumbnailMediaId?: Nullable<ThumbnailMediaIdParams>;
  videoPreviewImage?: Nullable<string>;
  disableIntro?: boolean;
  isEnabled?: boolean;
  isSelfEnrollEnabled?: boolean;
  isReEnrollEnabled?: boolean;
  reEnrollDay?: Nullable<number>;
  isReEnrollExpireEnabled?: boolean;
  reEnrollExpireDay?: Nullable<number>;
  accessType?: CourseAccessTypeEnum;
  enrollType?: CourseEnrollTypeEnum;
  subjects?: CourseSubjectParams[];
};

export type CourseSubjectParams = {
  courseName: string;
  courseShortName: string;
  courseCode: string;
  diplomaName: string;
  subjectName: string;
  subjectCode: string;
  pillarName: string;
  trainingType: string;
  trainingDuration?: string;
};

export type CourseRegulatorParams = {
  regulator: RegulatorEnum;
  trainingCenter: string;
  licenseRenewal: LicenseRenewalEnum;
  applicantType: ApplicantTypeEnum | '';
  licenseType: LicenseTypeEnum[];
  isDeduct: boolean;
  isRequireDeductDocument: boolean;
};
//#endregion

//#region Business domain
export type CourseWithVersionParams = {
  id?: GenericID;
  organizationId: GenericID;
  code?: string;
  url?: string;
  contentType: ContentTypeEnum;
  contentProviderType: ContentProviderTypeEnum;
  objectiveType?: Nullable<CourseObjectiveTypeEnum>;
  regulatorInfo?: Nullable<CourseRegulatorParams>;
  description?: string;
  thumbnailUrl?: Nullable<string | ImagePathParams>;
  thumbnailId?: Nullable<string>;
  thumbnailMediaId?: Nullable<ThumbnailMediaIdParams>;
  videoPreviewImage?: Nullable<string>;
  disableIntro?: boolean;
  isEnabled?: boolean;
  isSelfEnrollEnabled?: boolean;
  isReEnrollEnabled?: boolean;
  reEnrollDay?: Nullable<number>;
  isReEnrollExpireEnabled?: boolean;
  reEnrollExpireDay?: Nullable<number>;
  accessType?: CourseAccessTypeEnum;
  enrollType?: CourseEnrollTypeEnum;
  subjects?: CourseSubjectParams[];
  courseVersionId: GenericID;
  version: number;
  status: CourseVersionStatusEnum;
  name: string;
  expiryDay?: Nullable<number>;
  instructorIds?: Array<string>;
  instructors?: Nullable<Instructor[]>;
  totalCourseItems?: number;
  totalVideos?: number;
  totalQuizzes?: number;
  totalArticles?: number;
  totalSurveys?: number;
  totalDurationSec?: number;
  totalDurationArticleSec?: number;
  totalClassrooms?: number;
  isRPCEnabled?: boolean;
  isOCREnabled?: boolean;
  isLivenessEnabled?: boolean;
  isCountdownArticle?: boolean;
  isCertificateEnabled?: boolean;
  isMultipleCertificate?: boolean;
  isSentCertificateEmailOnExpiredDate?: boolean;
  isSeekEnabled?: boolean;
  isVideoSpeedEnabled?: boolean;
  isPlayVideoBackground?: boolean;
  isLearnableFullscreen?: boolean;
  isIdentityVerificationEnabled?: boolean;
  isAttentionCheckEnabled?: boolean;
  isAutoApproveEnabled?: boolean;
  publishedByUserId?: Nullable<GenericID>;
  publishedAt?: Nullable<Date>;
  createdAt?: Date;
  updatedAt?: Date;
};

export const CourseObjectiveTypeTextMapper = {
  [CourseObjectiveTypeEnum.REGULAR]: 'การเรียนทั่วไป',
  [CourseObjectiveTypeEnum.TRAINING]: 'การอบรม',
};

export const CourseRegulatorTypeTextMapper = {
  [RegulatorEnum.NONE]: 'การอบรมทั่วไป',
  [RegulatorEnum.OIC]: 'การอบรมวิชาชีพประกัน (OIC)',
  [RegulatorEnum.TSI]: 'การอบรมวิชาชีพการลงทุน (TSI)',
};

//#region Business domain
export const PreCourseConfig = {
  [CourseObjectiveTypeEnum.REGULAR]: {
    objectiveType: CourseObjectiveTypeEnum.REGULAR,
    enrollType: CourseEnrollTypeEnum.IMMEDIATE,
    accessType: CourseAccessTypeEnum.PUBLIC,
    isSelfEnrollEnabled: true,
    isReEnrollEnabled: false,
    isReEnrollExpireEnabled: false,
  },
  [CourseObjectiveTypeEnum.TRAINING]: {
    objectiveType: CourseObjectiveTypeEnum.TRAINING,
    enrollType: CourseEnrollTypeEnum.PRE_ENROLL,
    accessType: CourseAccessTypeEnum.PRIVATE,
    isSelfEnrollEnabled: false,
    isReEnrollEnabled: true,
    isReEnrollExpireEnabled: true,
  },
};
//#endregion
