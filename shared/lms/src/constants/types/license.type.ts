import { GenericID, Nullable } from '@iso/constants/commonTypes';

import { ApplicantTypeTHEnum } from '@shared/lms/constants/enums/course.enum';
import { UserLicenseTypeCodeEnum } from '@shared/lms/constants/enums/license.enum';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type LicenseParams = BaseEntityParams & {
  userId: GenericID;
  organizationId: GenericID;
  licenseTypeCode: UserLicenseTypeCodeEnum;
  licenseNo: string;
  type?: Nullable<ApplicantTypeTHEnum>;
  startedAt?: Nullable<Date>;
  expiredAt?: Nullable<Date>;
};

export type CreateLicenseParams = {
  userId: GenericID;
  licenseTypeCode: UserLicenseTypeCodeEnum;
  licenseNo: string;
  type?: Nullable<ApplicantTypeTHEnum>;
  organizationId: GenericID;
  startedAt?: Nullable<Date>;
  expiredAt?: Nullable<Date>;
};
//#endregion

//#region Business domain
export type BuildLicenseParams = {
  userId: string;
  licenseTypeCode: UserLicenseTypeCodeEnum;
  licenseNo: string;
  type?: Nullable<ApplicantTypeTHEnum>;
  createdAt: Date;
  updatedAt: Date;
};

export const LicenseTypeCodeENMapper = {
  [UserLicenseTypeCodeEnum.OIC_LIFE]: 'LIFE',
  [UserLicenseTypeCodeEnum.OIC_NON_LIFE]: 'NON-LIFE',
  [UserLicenseTypeCodeEnum.TSI]: 'TSI',
};
//#endregion
