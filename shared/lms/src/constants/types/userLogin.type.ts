import { GenericID, Nullable } from '@iso/constants/commonTypes';

import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type UserLoginParams = BaseEntityParams & {
  organizationLoginProviderId: GenericID;
  userId: GenericID;
  loginProviderKey: Nullable<string>;
  isEnabled: boolean;
};

export type CreateUserLoginParams = {
  organizationLoginProviderId: GenericID;
  userId: GenericID;
  loginProviderKey: Nullable<string>;
  isEnabled?: boolean;
};
//#endregion
