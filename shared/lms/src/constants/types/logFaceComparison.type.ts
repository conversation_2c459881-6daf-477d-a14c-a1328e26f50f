import { GenericID } from '@iso/constants/commonTypes';
import { Rekognition } from 'aws-sdk';

import { LivenessModeEnum } from '@shared/lms/constants/enums/organization.enum';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type LogFaceComparisonParams = BaseEntityParams & {
  _id?: any;
  enrollmentId: GenericID;
  percentSimilarity: number;
  imageFacePath: string;
  imageIdCardPath: string;
  imageComparePath: string;
  isFaceLivenessMode: boolean;
  isPass: boolean;
  isFraud?: boolean;
  fraudScore?: number;
  targetFaceLabels?: TargetFaceLabels;
  response: any;
  mode?: LivenessModeEnum;
};

export type TargetFaceLabels = Rekognition.Labels;

export type EditLogFaceComparisonParams = Omit<Partial<LogFaceComparisonParams>, 'id' | 'enrollmentId' | 'createdAt'>;

export type EditableLogFaceComparisonProperties = keyof EditLogFaceComparisonParams;
//#endregion

//#region Business domain

//#endregion
