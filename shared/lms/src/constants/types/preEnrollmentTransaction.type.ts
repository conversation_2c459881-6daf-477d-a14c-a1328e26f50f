import { GenericID, Nullable } from '@iso/constants/commonTypes';

import { ExternalContentTypeEnum } from '@shared/lms/constants/enums/course.enum';
import { BusinessTypeEnum, EnrollTypeEnum } from '@shared/lms/constants/enums/enrollment.enum';
import {
  PreEnrollmentTransactionAutoBulkOparationEnum,
  PreEnrollmentTransactionEnrollByEnum,
  PreEnrollmentTransactionPaymentTypeEnum,
  PreEnrollmentTransactionStatusEnum,
} from '@shared/lms/constants/enums/preEnrollmentTransaction.enum';
import { UserRegulatorProfileParams } from '@shared/lms/constants/types/user.type';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type PreEnrollmentTransactionParams = BaseEntityParams & {
  jobId: string;
  roundId: Nullable<GenericID>;
  customerPartnerId?: Nullable<GenericID>;
  preAssignContentId?: GenericID;
  preEnrollmentReservationId: GenericID;
  organizationId: GenericID;
  userId: GenericID;
  status: PreEnrollmentTransactionStatusEnum;
  enrollType?: Nullable<EnrollTypeEnum>;
  externalContentType?: ExternalContentTypeEnum;
  isCheckRegulator: boolean;
  errorMsg: string;
  warnMsg: string;
  payload: PreEnrollmentTransactionPayloadParams;
  warnList: string[];
  businessType: BusinessTypeEnum;
  paymentType: PreEnrollmentTransactionPaymentTypeEnum;
  enrollBy: PreEnrollmentTransactionEnrollByEnum;
  contentItems: ContentItemParams[];
  isUpdatedRetailOrder: boolean;
  autoBulkOparation?: PreEnrollmentTransactionAutoBulkOparationEnum[];
  operationExecute?: OperationExecuteParams;
};

export type PreEnrollmentTransactionUserPayloadParams = {
  username?: string;
  email: string;
  mobile: string;
  prefix: string;
  firstname: string;
  lastname: string;
  citizenId: string;
  last4DigitCitizenId: string;
};

export type PreEnrollmentTransactionTSILicensePayloadParams = {
  tsiLicenseNo: string;
  tsiLicenseType: string;
  tsiStartDate: string;
  tsiEnddate: string;
};

export type PreEnrollmentTransactionOICNonLifeLicensePayloadParams = {
  oicLicenseNonLifeNo: string;
  oicNonLifeStartDate: string;
  oicNonLifeEndDate: string;
};

export type PreEnrollmentTransactionOICLifeLicensePayloadParams = {
  oicLicenseLifeNo: string;
  oicLifeStartDate: string;
  oicLifeEndDate: string;
};

export type PreEnrollmentTransactionOICRenew4PayloadParams = {
  oicExtendYearType: string;
  oicReductionType: string;
};

export type PreEnrollmentTransactionPayloadParams = PreEnrollmentTransactionUserPayloadParams &
  PreEnrollmentTransactionTSILicensePayloadParams &
  PreEnrollmentTransactionOICNonLifeLicensePayloadParams &
  PreEnrollmentTransactionOICLifeLicensePayloadParams &
  PreEnrollmentTransactionOICRenew4PayloadParams & {
    graduatedDate: string;
    university: string;
    productSKUCode: string | null;
    courseCode: string | null;
    orderNo: string;
    remark: string;
    regulatorProfile: UserRegulatorProfileParams;
  };

export type OperationExecuteParams = {
  isActivateUser: boolean;
  isEditUser: boolean;
  isEnrollment: boolean;
  isEnrollmentBundle: boolean;
  isAssginPlanPackageLicense: boolean;
};

export type ContentItemParams = {
  productSKUId: GenericID;
  isHavingCost: boolean;
};
//#endregion

//#region Business domain

//#endregion
