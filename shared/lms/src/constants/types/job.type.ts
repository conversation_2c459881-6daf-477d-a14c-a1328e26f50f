import { GenericID, Nullable, ObjectValue } from '@iso/constants/commonTypes';

import { BulkOpTypeEnum, JobStatusEnum } from '@shared/lms/constants/enums/job.enum';
import { BaseGuidEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type JobParams = BaseGuidEntityParams & {
  organizationId?: GenericID;
  operationType: BulkOpTypeEnum;
  queueName: string;
  status: JobStatusEnum;
  total: number;
  userId: string;
  tempFilename: string;
  warnList: JobWarningParams[];
  errorList: ObjectValue[];
  totalError: number;
  totalWarn: number;
  rawHeaders: string[];
  rawPayloads: Array<(string | string[])[]>;
  operationPayload: Nullable<Record<string, any>>;
};

export type CreateJobParams = {
  organizationId?: GenericID;
  operationType: BulkOpTypeEnum;
  queueName: string;
  status: JobStatusEnum;
  total: number;
  userId?: string;
  tempFilename?: string;
  warnList?: JobWarningParams[];
  errorList?: any[];
  totalError?: number;
  totalWarn?: number;
  rawHeaders?: string[];
  rawPayloads?: Array<(string | string[])[]>;
  operationPayload?: Nullable<Record<string, any>>;
};

export type JobWarningParams = {
  originalPayload: {
    email: string;
  };
  warnings: {
    message: string;
    code: string;
  }[];
};
//#endregion

//#region Business domain
export const BulkOpTypeThaiText = {
  [BulkOpTypeEnum.ACTIVATE]: 'เพิ่มผู้ใช้งานแบบกลุ่ม',
  [BulkOpTypeEnum.EDIT_USER]: 'แก้ไขข้อมูลผู้ใช้งานแบบกลุ่ม',
  [BulkOpTypeEnum.ENROLLMENT]: 'ลงทะเบียนหลักสูตร',
  [BulkOpTypeEnum.ENROLLMENT_BUNDLE]: 'ลงทะเบียนกลุ่มหลักสูตร',
  [BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE]: 'เพิ่มไลเซนส์ผู้ใช้งานแบบกลุ่ม',
  [BulkOpTypeEnum.ENROLLMENT_ACHIEVEMENT_COMPLETED]: 'แจกความสำเร็จการเรียน',
  [BulkOpTypeEnum.TRANSFER_PLAN_PACKAGE_LICENSE]: 'โอนย้ายไลเซนส์ผู้ใช้งานแบบกลุ่ม',
  [BulkOpTypeEnum.PLAN_PACKAGE_LICENSE_EXPIRED]: 'แจ้งเตือนแพลนแพ็กเกจไลเซนส์หมดอายุให้กับผู้ใช้งาน',
};
//#endregion
