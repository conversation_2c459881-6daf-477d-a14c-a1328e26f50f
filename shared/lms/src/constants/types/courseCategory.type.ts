import { GenericID } from '@iso/constants/commonTypes';

import { BaseEntityParams } from '@shared/lms/core/constant/types';

//#region Entity domain
export type CourseCategoryParams = BaseEntityParams & {
  organizationId: GenericID;
  name: string;
  code: string;
  parentId?: string;
  description: string;
  isActive: boolean;
  courseIds?: string[];
  position?: number;
};

export type CreateCourseCategoryParams = {
  organizationId: GenericID;
  name: string;
  code: string;
  parentId?: string;
  description: string;
  isActive: boolean;
  courseIds?: string[];
  position?: number;
};

//#endregion

//#region Business domain

//#endregion
