import { BulkOpTypeEnum } from '@shared/lms/constants/enums/job.enum';

// Key composite of lms:task:{operationType}:{domain}:{uuidV4}
export type TaskOperationKeyParams = `lms:task:${BulkOpTypeEnum}:${string}:${string}`;

export type TaskOperationParams = {
  key: TaskOperationKeyParams;
  total: number;
  success: number;
  error: number;
};

export type CreateTaskOperationParams = {
  key: TaskOperationKeyParams;
  total?: number;
  success?: number;
  error?: number;
};
