//#region Entity domain
export enum ColumnSettingTemplateEnum {
  userManagement = 'userManagement',
  bulkActivateUser = 'bulkActivateUser',
  bulkEditUser = 'bulkEditUser',
  memberManagement = 'memberManagement',
  subordinateManagement = 'subordinateManagement',
  courseManagement = 'courseManagement',
  regularEnrollment = 'regularEnrollment',
  enrollmentLearning = 'enrollmentLearning',
  enrollmentApproval = 'enrollmentApproval',
  preEnrollment = 'preEnrollment',
  regularPreEnrollment = 'regularPreEnrollment',
  additionalDocument = 'additionalDocument',
  deductDocument = 'deductDocument',
  userMapUserGroupManagement = 'userMapUserGroupManagement',
  userGroupConditionManagement = 'userGroupConditionManagement',
  learningPathManagement = 'learningPathManagement',
  preAssignLearningPathEnrollmentManagement = 'preAssignLearningPathEnrollmentManagement',
  inProgressLearningPathEnrollmentManagement = 'inProgressLearningPathEnrollmentManagement',
  knowledgeContentItem = 'knowledgeContentItemManagement',
  dashboardKnowledgeContentManagement = 'dashboardKnowledgeContentManagement',
  assignContentManagement = 'assignContentManagement',
  transferClassroomUserManagement = 'transferClassroomUserManagement',
  attendanceClassroomManagement = 'attendanceClassroomManagement',
}

export enum ColumnSettingFieldTypeEnum {
  primary = 'primary',
  additionalField = 'additionalField',
}

export enum ColumnSettingDataTypeEnum {
  text = 'text',
  phone = 'phone',
  email = 'email',
  citizenId = 'citizenId',
  imageUrl = 'imageUrl',
  imageAvatarUrl = 'imageAvatarUrl',
  url = 'url',
  flag = 'flag',
  date = 'date',
  arraySize = 'arraySize',
  enum = 'enum',
  number = 'number',
}

export enum ColumnSettingFilterTypeEnum {
  text = 'text',
  number = 'number',
  dateRange = 'dateRange',
  numberRange = 'numberRange',
  multipleEnrollmentSelector = 'multipleEnrollmentSelector',
  multipleSelector = 'multipleSelector',
  singleSelector = 'singleSelector',
}

export enum ColumnSettingDropdownValueTypeEnum {
  static = 'static',
  sync = 'sync',
}

export enum ColumnSettingComponentTypeEnum {
  text = 'text',
  tag = 'tag',
  list = 'list',
  imageRectangle = 'imageRectangle',
  imageAvatar = 'imageAvatar',
}

export enum ColumnSettingAlignmentEnum {
  left = 'left',
  center = 'center',
  right = 'right',
}

export enum ColumnSettingModuleEnum {
  ACHIEVEMENT = 'achievement',
  USER = 'user',
  COURSE = 'course',
  LEARNING_PATH = 'learningPath',
  ENROLLMENT = 'enrollment',
  ENROLLMENT_ATTACHMENT = 'enrollmentAttachment',
  PRE_ENROLLMENT = 'preEnrollment',
  LEARNING_PATH_ENROLLMENT = 'learningPathEnrollment',
  KNOWLEDGE_CONTENT_ITEM = 'knowledgeContentItem',
  DEPARTMENT = 'department',
  LICENSE = 'license',
  USER_DIRECT_REPORT = 'userDirectReport',
  CLASSROOM_ENROLLMENT = 'classroomEnrollment',
  JOB_TRANSACTION = 'jobTransaction',
  USER_GROUP = 'userGroup',
}

export enum ColumnSettingKeyEnum {
  ACHIEVEMENT_NAME = 'achievement.name',
  USER_USERNAME = 'user.username',
  USER_EMAIL = 'user.email',
  USER_FULLNAME = 'user.fullname',
  USER_FIRSTNAME_LASTNAME = 'user.firstname_lastname',
  USER_SALUTE = 'user.salute',
  USER_FIRSTNAME = 'user.firstname',
  USER_MIDDLENAME = 'user.middlename',
  USER_LASTNAME = 'user.lastname',
  USER_CITIZEN_ID = 'user.citizenId',
  USER_MOBILE_PHONE_NUMBER = 'user.mobilePhoneNumber',
  USER_GENDER = 'user.gender',
  USER_DATE_OF_BIRTH = 'user.dateOfBirth',
  USER_EMPLOYEE_ID = 'user.employeeId',
  USER_POSITION = 'user.position',
  USER_AVATAR = 'user.avatar',
  USER_ACTIVE = 'user.active',
  USER_OIC_LICENSE_LIFE_NO = 'user.oicLicenseLifeNo',
  USER_OIC_LICENSE_LIFE_START_DATE = 'user.oicStartDateLife',
  USER_OIC_LICENSE_LIFE_END_DATE = 'user.oicEndDateLife',
  USER_OIC_LICENSE_NON_LIFE_NO = 'user.oicLicenseNonLifeNo',
  USER_OIC_LICENSE_NON_LIFE_START_DATE = 'user.oicStartDateNonLife',
  USER_OIC_LICENSE_NON_LIFE_END_DATE = 'user.oicEndDateNonLife',
  USER_TSI_LICENSE_NO = 'user.tsiLicenseNo',
  USER_TSI_LICENSE_TYPE = 'user.tsiLicenseType',
  USER_TSI_LICENSE_START_DATE = 'user.tsiStartDate',
  USER_TSI_LICENSE_END_DATE = 'user.tsiEndDate',
  USER_IS_TERMINATED = 'user.isTerminated',
  USER_IS_PASSED_UL_SALE_QUALIFY = 'user.isPassedUlSaleQualify',
  USER_DEPARTMENT = 'user.department',
  USER_SUPERVISOR = 'user.supervisor',
  USER_CUSTOMER = 'user.customer',
  USER_PERMISSION_GROUP = 'user.permissionGroup',
  COURSE_CODE = 'course.code',
  COURSE_IMAGE = 'course.image',
  COURSE_NAME = 'course.name',
  COURSE_OBJECTIVE = 'course.objective',
  COURSE_REGULATOR = 'course.regulator',
  COURSE_IS_CERTIFICATE_ENABLED = 'course.certificate',
  COURSE_SELF_ENROLL = 'course.selfEnroll',
  COURSE_IS_ENABLED = 'course.isEnabled',
  COURSE_CREATED_AT = 'course.createdAt',
  COURSE_UPDATED_AT = 'course.updatedAt',
  COURSE_PRODUCT_SKU_ID = 'course.productSKUId',
  COURSE_PRODUCT_SKU_CODE = 'course.productSKUCode',
  COURSE_PRODUCT_SKU_CREDIT = 'course.credit',
  COURSE_PILLAR_NAME = 'course.pillarName',
  COURSE_CONTENT_PROVIDER = 'course.contentProvider',
  COURSE_IS_ACTIVATED = 'course.isActivated',
  LEARNING_PATH_CODE = 'learning_path.code',
  LEARNING_PATH_THUMBNAIL = 'learning_path.thumbnail',
  LEARNING_PATH_NAME = 'learning_path.name',
  LEARNING_PATH_IS_CERTIFICATE_ENABLED = 'learning_path.is_certificate_enabled',
  LEARNING_PATH_IS_ENABLED = 'learning_path.status',
  LEARNING_PATH_NUMBER_OF_CONTENT = 'learning_path.number_of_content',
  LEARNING_PATH_ENROLL_TYPE = 'learning_path.enroll_type',
  LEARNING_PATH_UPDATED_AT = 'learning_path.updated_at',
  LEARNING_PATH_CREATED_AT = 'learning_path.created_at',
  LEARNING_PATH_EXPIRY_DAY = 'learning_path.expiry_day',
  ENROLLMENT_COURSE_NAME = 'enrollment.course_name',
  ENROLLMENT_USER_FULLNAME = 'enrollment.user_fullname',
  ENROLLMENT_USER_EMAIL = 'enrollment.user_email',
  ENROLLMENT_USER_CITIZEN_ID = 'enrollment.user_citizenId',
  ENROLLMENT_CUSTOMER_CODE = 'enrollment.customerCode',
  ENROLLMENT_PERCENT_LEARNING_PROGRESS = 'enrollment.percentLearningProgress',
  ENROLLMENT_STARTED_AT = 'enrollment.startedAt',
  ENROLLMENT_EXPIRED_AT = 'enrollment.expiredAt',
  ENROLLMENT_REQUESTED_APPROVAL_AT = 'enrollment.requestedApprovalAt',
  ENROLLMENT_APPROVAL_AT = 'enrollment.approvalAt',
  ENROLLMENT_LEARNING_STATUS = 'enrollment.learningStatus',
  ENROLLMENT_APPROVAL_STATUS = 'enrollment.approvalStatus',
  ENROLLMENT_ENROLL_TYPE = 'enrollment.enrollType',
  LEARNING_PATH_ENROLLMENT_FULL_NAME = 'learning_path_enrollment.full_name',
  LEARNING_PATH_ENROLLMENT_EMAIL = 'learning_path_enrollment.email',
  LEARNING_PATH_ENROLLMENT_LEARNING_PATH_NAME = 'learning_path_enrollment.learning_path_name',
  LEARNING_PATH_ENROLLMENT_STARTED_DATE = 'learning_path_enrollment.started_date',
  LEARNING_PATH_ENROLLMENT_EXPIRED_DATE = 'learning_path_enrollment.expired_date',
  LEARNING_PATH_ENROLLMENT_CONTENT_PROGRESS = 'learning_path_enrollment.content_progress',
  LEARNING_PATH_ENROLLMENT_STATUS = 'learning_path_enrollment.status',
  LEARNING_PATH_ENROLLMENT_ENROLL_BY = 'learning_path_enrollment.enroll_by',
  PRE_ENROLLMENT_BUSINESS_TYPE = 'pre_enrollment.business_type',
  PRE_ENROLLMENT_CUSTOMER_CODE = 'pre_enrollment.customer_code',
  PRE_ENROLLMENT_CUSTOMER_PARTNER_CODE = 'pre_enrollment.customer_partner_code',
  PRE_ENROLLMENT_ID = 'pre_enrollment.id',
  PRE_ENROLLMENT_EID = 'pre_enrollment.eid',
  PRE_ENROLLMENT_FULL_NAME = 'pre_enrollment.full_name',
  PRE_ENROLLMENT_EMAIL = 'pre_enrollment.email',
  PRE_ENROLLMENT_CITIZEN_ID = 'pre_enrollment.citizen_id',
  PRE_ENROLLMENT_MOBILE_PHONE_NUMBER = 'pre_enrollment.mobilePhoneNumber',
  PRE_ENROLLMENT_TSI_LICENSE_NO = 'pre_enrollment.tsi_license_no',
  PRE_ENROLLMENT_TSI_LICENSE_TYPE = 'pre_enrollment.tsiLicenseType',
  PRE_ENROLLMENT_TSI_LICENSE_START_DATE = 'pre_enrollment.tsiStartDate',
  PRE_ENROLLMENT_TSI_LICENSE_END_DATE = 'pre_enrollment.tsiEnddate',
  PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_NO = 'pre_enrollment.oic_license_non_life_no',
  PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_START_DATE = 'pre_enrollment.oicNonLifeStartDate',
  PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_END_DATE = 'pre_enrollment.oicNonLifeEndDate',
  PRE_ENROLLMENT_OIC_LICENSE_LIFE_NO = 'pre_enrollment.oic_license_life_no',
  PRE_ENROLLMENT_OIC_LICENSE_LIFE_START_DATE = 'pre_enrollment.oicLifeStartDate',
  PRE_ENROLLMENT_OIC_LICENSE_LIFE_END_DATE = 'pre_enrollment.oicLifeEndDate',
  PRE_ENROLLMENT_COURSES = 'pre_enrollment.courses',
  PRE_ENROLLMENT_COURSE_GROUP = 'pre_enrollment.course_group',
  PRE_ENROLLMENT_PRODUCT_SKU_CODE = 'pre_enrollment.productSKUCode',
  PRE_ENROLLMENT_PRODUCT_SKU_ID = 'pre_enrollment.productSKUId',
  PRE_ENROLLMENT_COURSE_CODE = 'pre_enrollment.courseCode',
  PRE_ENROLLMENT_REGULATOR = 'pre_enrollment.regulator',
  PRE_ENROLLMENT_TRAINING_CENTER = 'pre_enrollment.training_center',
  PRE_ENROLLMENT_ROUND_DATE = 'pre_enrollment.round_date',
  PRE_ENROLLMENT_STATUS = 'pre_enrollment.status',
  PRE_ENROLLMENT_IS_CHECK_REGULATOR = 'pre_enrollment.is_check_regulator',
  PRE_ENROLLMENT_USER_STATUS = 'pre_enrollment.user_status',
  PRE_ENROLLMENT_ENROLL_TYPE = 'pre_enrollment.enrollType',
  ENROLLMENT_ATTACHMENT_REQUEST_NO = 'enrollment_attachment.requestNo',
  ENROLLMENT_ATTACHMENT_CREATED_AT = 'enrollment_attachment.createdAt',
  ENROLLMENT_ATTACHMENT_STARTED_AT = 'enrollment_attachment.startedAt',
  ENROLLMENT_ATTACHMENT_EXPIRED_AT = 'enrollment_attachment.expiredAt',
  ENROLLMENT_ATTACHMENT_APPROVAL_DATE = 'enrollment_attachment.approvalDate',
  ENROLLMENT_ATTACHMENT_SENT_DATE = 'enrollment_attachment.sentDate',
  ENROLLMENT_ATTACHMENT_ADDITIONAL_DOC_STATUS = 'enrollment_attachment.additionalDocStatus',
  ENROLLMENT_ATTACHMENT_DEDUCT_DOC_STATUS = 'enrollment_attachment.deductDocStatus',
  ENROLLMENT_ATTACHMENT_ENROLLMENT_CUSTOMER_CODE = 'enrollment_attachment.enrollment_customerCode',
  ENROLLMENT_ATTACHMENT_COURSE_NAME = 'enrollment_attachment.course_name',
  ENROLLMENT_ATTACHMENT_USER_FULLNAME = 'enrollment_attachment.user_fullname',
  ENROLLMENT_ATTACHMENT_USER_EMAIL = 'enrollment_attachment.user_email',
  ENROLLMENT_ATTACHMENT_USER_CITIZEN_ID = 'enrollment_attachment.user_citizenId',
  ENROLLMENT_ATTACHMENT_LICENSE_OIC_LICENSE_LIFE_NO = 'enrollment_attachment.license_oicLicenseLifeNo',
  ENROLLMENT_ATTACHMENT_LICENSE_OIC_LICENSE_NON_LIFE_NO = 'enrollment_attachment.license_oicLicenseNonLifeNo',
  KNOWLEDGE_CONTENT_ITEM_CODE = 'knowledge_content_item.code',
  KNOWLEDGE_CONTENT_ITEM_TITLE = 'knowledge_content_item.title',
  KNOWLEDGE_CONTENT_ITEM_THUMBNAIL = 'knowledge_content_item.thumbnail',
  KNOWLEDGE_CONTENT_ITEM_TYPE = 'knowledge_content_item.type',
  KNOWLEDGE_CONTENT_ITEM_STATUS = 'knowledge_content_item.status',
  KNOWLEDGE_CONTENT_ITEM_UPDATED_AT = 'knowledge_content_item.updatedAt',
  KNOWLEDGE_CONTENT_ITEM_PUBLISHED_START_AT = 'knowledge_content_item.publishedStartAt',
  KNOWLEDGE_CONTENT_ITEM_PUBLISHED_END_AT = 'knowledge_content_item.publishedEndAt',
  KNOWLEDGE_CONTENT_ITEM_IS_DOWNLOAD_ENABLED = 'knowledge_content_item.isDownloadEnabled',
  KNOWLEDGE_CONTENT_ITEM_DASHBOARD_TITLE = 'knowledge_content_item.dashboardTitle',
  KNOWLEDGE_CONTENT_ITEM_CATEGORY_NAME = 'knowledge_content_item.category_name',
  KNOWLEDGE_CONTENT_ITEM_INSTRUCTOR_NAME = 'knowledge_content_item.instructor_name',
  JOB_TRANSACTION_STATUS = 'job_transaction.status',
  JOB_TRANSACTION_ERROR_MESSAGES = 'job_transaction.errorMessages',
  JOB_TRANSACTION_USER_AVATAR = 'job_transaction.user.avatar',
  JOB_TRANSACTION_USER_FULLNAME = 'job_transaction.user.fullname',
  JOB_TRANSACTION_USER_EMAIL = 'job_transaction.user.email',
  JOB_TRANSACTION_USER_MOBILE_PHONE_NUMBER = 'job_transaction.user.mobilePhoneNumber',
  JOB_TRANSACTION_USER_GENDER = 'job_transaction.user.gender',
  JOB_TRANSACTION_USER_DATE_OF_BIRTH = 'job_transaction.user.dateOfBirth',
  JOB_TRANSACTION_USER_EMPLOYEE_ID = 'job_transaction.user.employeeId',
  JOB_TRANSACTION_USER_POSITION = 'job_transaction.user.position',
  JOB_TRANSACTION_USER_DEPARTMENT = 'job_transaction.user.department',
  JOB_TRANSACTION_USER_SUPERVISOR = 'job_transaction.user.supervisor',
  JOB_TRANSACTION_USER_ACTIVE = 'job_transaction.user.active',
  JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_NO = 'job_transaction.user.oicLicenseLifeNo',
  JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_START_DATE = 'job_transaction.user.oicStartDateLife',
  JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_END_DATE = 'job_transaction.user.oicEndDateLife',
  JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_NO = 'job_transaction.user.oicLicenseNonLifeNo',
  JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_START_DATE = 'job_transaction.user.oicStartDateNonLife',
  JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_END_DATE = 'job_transaction.user.oicEndDateNonLife',
  JOB_TRANSACTION_USER_TSI_LICENSE_NO = 'job_transaction.user.tsiLicenseNo',
  JOB_TRANSACTION_USER_TSI_LICENSE_TYPE = 'job_transaction.user.tsiLicenseType',
  JOB_TRANSACTION_USER_TSI_LICENSE_START_DATE = 'job_transaction.user.tsiStartDate',
  JOB_TRANSACTION_USER_TSI_LICENSE_END_DATE = 'job_transaction.user.tsiEndDate',
  JOB_TRANSACTION_USER_IS_TERMINATED = 'job_transaction.user.isTerminated',
  JOB_TRANSACTION_USER_IS_PASSED_UL_SALE_QUALIFY = 'job_transaction.user.isPassedUlSaleQualify',
  CLASSROOM_TRANSFER_ENROLLMENT_STATUS = 'classroom.transfer.enrollmentStatus',
  CLASSROOM_ROUND_DATE = 'classroom.roundDate',
  CLASSROOM_LOCATION = 'classroom.location',
  CLASSROOM_ENROLLMENT_STATUS = 'classroom.enrollmentStatus',
  CLASSROOM_TOTAL_ATTENDED = 'classroom.totalAttended',
  CLASSROOM_SCORE_HOMEWORK = 'classroom.scoreHomework',
  CLASSROOM_REMARK = 'classroom.remark',
  USER_GROUP_USER_GROUP_PARTICIPATION = 'user_group.user_group_participation',
  USER_GROUP_DEPARTMENT = 'user_group.department',
  USER_GROUP_SUPERVISOR = 'user_group.supervisor',
  USER_GROUP_OIC_LICENSE_LIFE_NO = 'user_group.oicLicenseLifeNo',
  USER_GROUP_OIC_LICENSE_LIFE_START_DATE = 'user_group.oicStartDateLife',
  USER_GROUP_OIC_LICENSE_LIFE_END_DATE = 'user_group.oicEndDateLife',
  USER_GROUP_OIC_LICENSE_NON_LIFE_NO = 'user_group.oicLicenseNonLifeNo',
  USER_GROUP_OIC_LICENSE_NON_LIFE_START_DATE = 'user_group.oicStartDateNonLife',
  USER_GROUP_OIC_LICENSE_NON_LIFE_END_DATE = 'user_group.oicEndDateNonLife',
  USER_GROUP_TSI_LICENSE_NO = 'user_group.tsiLicenseNo',
  USER_GROUP_TSI_LICENSE_TYPE = 'user_group.tsiLicenseType',
  USER_GROUP_TSI_LICENSE_START_DATE = 'user_group.tsiStartDate',
  USER_GROUP_TSI_LICENSE_END_DATE = 'user_group.tsiEndDate',
  USER_GROUP_COURSE = 'user_group.course',
}

export enum ColumnSettingNameDefaultEnum {
  ACHIEVEMENT_NAME = 'ชื่อความสำเร็จการเรียน',
  USER_USERNAME = 'ชื่อผู้ใช้',
  USER_EMAIL = 'อีเมล',
  USER_FULLNAME = 'คำนำหน้า/ชื่อจริง/ชื่อกลาง/นามสกุล',
  USER_SALUTE_FIRSTNAME_LASTNAME = 'คำนำหน้า/ชื่อจริง/นามสกุล',
  USER_FIRSTNAME_LASTNAME = 'ชื่อจริง/นามสกุล',
  USER_SALUTE = 'คำนำหน้า',
  USER_FIRSTNAME = 'ชื่อจริง',
  USER_MIDDLENAME = 'ชื่อกลาง',
  USER_LASTNAME = 'นามสกุล',
  USER_CITIZEN_ID = 'เลขประจำตัวประชาชน',
  USER_MOBILE_PHONE_NUMBER = 'เบอร์โทรศัพท์มือถือ',
  USER_GENDER = 'เพศ',
  USER_DATE_OF_BIRTH = 'วันเดือนปีเกิด',
  USER_EMPLOYEE_ID = 'เลขประจำตัวพนักงาน',
  USER_POSITION = 'ตำแหน่ง',
  USER_AVATAR = 'รูปโพรไฟล์',
  USER_ACTIVE = 'สถานะผู้ใช้',
  USER_OIC_LICENSE_LIFE_NO = 'เลขที่ใบอนุญาตประกันชีวิต',
  USER_OIC_LICENSE_LIFE_START_DATE = 'วันที่ออกใบอนุญาตประกันชีวิต',
  USER_OIC_LICENSE_LIFE_END_DATE = 'วันที่ใบอนุญาตประกันชีวิตหมดอายุ',
  USER_OIC_LICENSE_NON_LIFE_NO = 'เลขที่ใบอนุญาตประกันวินาศภัย',
  USER_OIC_LICENSE_NON_LIFE_START_DATE = 'วันที่ออกใบอนุญาตประกันวินาศภัย',
  USER_OIC_LICENSE_NON_LIFE_END_DATE = 'วันที่ใบอนุญาตประกันวินาศภัยหมดอายุ',
  USER_TSI_LICENSE_NO = 'เลขที่ใบอนุญาตด้านการลงทุน',
  USER_TSI_LICENSE_TYPE = 'ประเภทใบอนุญาตด้านการลงทุน',
  USER_TSI_LICENSE_START_DATE = 'วันที่ออกใบอนุญาตด้านการลงทุน',
  USER_TSI_LICENSE_END_DATE = 'วันที่ใบอนุญาตด้านการลงทุนหมดอายุ',
  USER_IS_TERMINATED = 'สถานะพนักงาน',
  USER_IS_PASSED_UL_SALE_QUALIFY = 'ผลงานการขายสำหรับหลักสูตร UL',
  USER_DEPARTMENT = 'แผนก',
  USER_SUPERVISOR = 'หัวหน้างาน',
  USER_CUSTOMER = 'ผู้ดูแลบริษัท',
  USER_PERMISSION_GROUP = 'กลุ่มสิทธิ์ผู้ใช้งาน',

  COURSES = 'หลักสูตร',
  COURSE_CODE = 'รหัสหลักสูตร',
  COURSE_IMAGE = 'รูปภาพหลักสูตร',
  COURSE_NAME = 'ชื่อหลักสูตร',
  COURSE_OBJECTIVE = 'วัตถุประสงค์หลักสูตร',
  COURSE_CERTIFICATE = 'ประกาศนียบัตรหลักสูตร',
  COURSE_SELF_ENROLL = 'ลงทะเบียนได้ด้วยตนเอง',
  COURSE_IS_ENABLED = 'สถานะหลักสูตร',
  COURSE_REGULATOR = 'วัตถุประสงค์การอบรม',
  COURSE_TRAINING_CENTER = 'ศูนย์อบรม',
  COURSE_CREATED_AT = 'วันที่สร้างหลักสูตร',
  COURSE_UPDATED_AT = 'วันที่แก้ไขหลักสูตร',
  COURSE_PRODUCT_SKU = 'หลักสูตร/กลุ่มหลักสูตร',
  COURSE_PRODUCT_SKU_CODE = 'รหัสการขาย',
  COURSE_PRODUCT_SKU_ID = 'Product SKU Id',
  COURSE_PRODUCT_SKU_CREDIT = 'ราคาเครดิต',
  COURSE_PILLAR_NAME = 'องค์ความรู้',
  COURSE_CONTENT_PROVIDER = 'แหล่งที่มา',
  COURSE_IS_ACTIVATED = 'สิทธิ์การใช้หลักสูตร',
  BUSINESS_TYPE = 'ประเภทลูกค้า',
  CUSTOMER_CODE = 'รหัสบริษัท',
  CUSTOMER_PARTNER_CODE = 'รหัสบริษัทในเครือ',

  LEARNING_PATH_CODE = 'รหัสแผนการเรียนรู้',
  LEARNING_PATH_THUMBNAIL = 'รูปภาพแผนการเรียนรู้',
  LEARNING_PATH_NAME = 'ชื่อแผนการเรียนรู้',
  LEARNING_PATH_CERTIFICATE = 'ประกาศนียบัตรแผนการเรียนรู้',
  LEARNING_PATH_IS_ENABLED = 'สถานะแผนการเรียนรู้',
  LEARNING_PATH_NUMBER_OF_CONTENT = 'จำนวนเนื้อหา',
  LEARNING_PATH_ENROLL_TYPE = 'แผนการเรียน',
  LEARNING_PATH_CREATED_AT = 'วันที่สร้างแผนการเรียนรู้',
  LEARNING_PATH_UPDATED_AT = 'วันที่แก้ไขแผนการเรียนรู้',
  LEARNING_PATH_EXPIRY_DAY = 'กำหนดระยะเวลาเรียน',

  INSTRUCTOR = 'ผู้เขียน/ผู้สอน',

  LEARNING_STARTED_AT = 'วันที่เริ่มอบรม',
  LEARNING_EXPIRED_AT = 'วันที่หมดเวลาอบรม',
  LEARNING_PROGRESS = 'เรียนไปแล้ว (%)',
  APPROVAL_DATE = 'วันที่อนุมัติ/ไม่อนุมัติการอบรม',
  REQUESTED_APPROVAL_DATE = 'วันที่ส่งผลการอบรม',
  ENROLLMENT_LEARNING_STATUS = 'สถานะกำลังเรียน',
  ENROLLMENT_APPROVAL_STATUS = 'สถานะตรวจสอบการอนุมัติ',
  ENROLLMENT_ENROLL_TYPE = 'ประเภทการลงทะเบียน',

  LEARNING_PATH_ENROLLMENT_STATUS = 'สถานะการเรียนแผนการเรียนรู้',
  LEARNING_PATH_ENROLLMENT_ENROLL_BY = 'ลงเรียนแผนการเรียนรู้โดย',

  PRE_ENROLLMENT_ID = 'Pre-Enroll ID',
  PRE_ENROLLMENT_EID = 'EID',
  PRE_ENROLLMENT_STATUS = 'สถานะรอลงเรียน',
  PRE_ENROLLMENT_REGULATOR = 'หน่วยงาน',
  PRE_ENROLLMENT_IS_CHECK_REGULATOR = 'ตรวจสอบกับหน่วยงานกำกับดูแล',
  PRE_ENROLLMENT_USER_STATUS = 'สถานะลูกค้า',

  ENROLLMENT_ATTACHMENT_DOC_ID = 'หมายเลขเอกสาร',
  ENROLLMENT_ATTACHMENT_DOC_REQUEST_DATE = 'วันที่ขอเอกสาร',
  ENROLLMENT_ATTACHMENT_DOC_DUE_DATE = 'วันที่กำหนดส่งเอกสาร',
  ENROLLMENT_ATTACHMENT_DOC_SUBMISSION_DATE = 'วันที่ส่งเอกสาร',
  ENROLLMENT_ATTACHMENT_DOC_APPROVAL_DATE = 'วันที่อนุมัติ/ไม่อนุมัติเอกสาร',
  ENROLLMENT_ATTACHMENT_ADDITIONAL_DOC_STATUS = 'สถานะเอกสารเพิ่มเติม',
  ENROLLMENT_ATTACHMENT_DEDUCT_DOC_STATUS = 'สถานะเอกสารลดหย่อน',

  KNOWLEDGE_CONTENT_ITEM_CODE = 'รหัสสื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_THUMBNAIL = 'รูปภาพสื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_TITLE = 'ชื่อสื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_TYPE = 'ประเภทสื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_CATEGORY_NAME = 'หมวดหมู่สื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_STATUS = 'สถานะสื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_UPDATED_AT = 'วันที่แก้ไขสื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_PUBLISHED_START_AT = 'วันที่เผยแพร่สื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_PUBLISHED_END_AT = 'วันที่หมดอายุสื่อความรู้',
  KNOWLEDGE_CONTENT_ITEM_IS_DOWNLOAD_ENABLED = 'เปิดให้ดาวน์โหลดสื่อความรู้',

  JOB_TRANSACTION_STATUS = 'สถานะจัดการแบบกลุ่ม',
  JOB_TRANSACTION_ERROR_MESSAGES = 'เหตุผลข้อผิดพลาด',

  CLASSROOM_ROUND_DATE = 'วันที่และเวลาเข้าเรียนห้องเรียน',
  CLASSROOM_LOCATION = 'รายละเอียดห้องเรียน',
  CLASSROOM_ENROLLMENT_STATUS = 'สถานะสมัครห้องเรียน',
  CLASSROOM_TOTAL_ATTENDED = 'จำนวนการเข้าเรียน',
  CLASSROOM_SCOR_EHOMEWORK = 'คะแนนการบ้าน',
  CLASSROOM_REMARK = 'หมายเหตุห้องเรียน',

  USER_GROUP_PARTICIPATION = 'วิธีการจัดกลุ่ม',
}
//#endregion

//#region Business domain

//#endregion
