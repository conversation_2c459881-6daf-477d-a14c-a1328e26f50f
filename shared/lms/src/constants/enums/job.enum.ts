//#region Entity domain
export enum BulkOpTypeEnum {
  ACTIVATE = 'activate',
  ASSIGN_PLAN_PACKAGE_LICENSE = 'assign_plan_package_license',
  CANCEL_ENROLLMENT_HISTORY = 'cancel_enrollment_history',
  CANCEL_PRE_ASSIGN_LEARNING_PATH = 'cancel_pre_assign_learning_path',
  CANCEL_PRE_ENROLLMENT = 'cancel_pre_enrollment',
  CHANGE_CITIZEN_ID = 'change_citizen_id',
  CHANGE_EMAIL_USER = 'change_email_user',
  CHANGE_USERNAME = 'change_username',
  CLASSROOM_MARK_RESULT = 'classroom_mark_result',
  EDIT_LEARNING_PATH_EXPIRE_DATE = 'edit_learning_path_expire_date',
  EDIT_USER = 'edit_user_bulk',
  ENROLLMENT = 'enrollment',
  ENROLLMENT_ACHIEVEMENT_COMPLETED = 'enrollment_achievement_completed',
  ENROLLMENT_BUNDLE = 'enrollment_bundle',
  ENROLLMENT_HISTORY = 'enrollment_history',
  LEARNER_REPORT = 'learner_report',
  LEARNING_PATH_ENROLLMENT = 'learning_path_enrollment',
  OIC_DEDUCT_REPORT = 'oic_deduct_report',
  OIC_POST_REPORT = 'oic_post_report',
  OIC_PRE_REPORT = 'oic_pre_report',
  OIC_REGULATOR_POST_REPORT = 'oic_regulator_post_report',
  OIC_REGULATOR_PRE_REPORT = 'oic_regulator_pre_report',
  PRE_ASSIGN_LEARNING_PATH_ENROLLMENT = 'pre_assign_learning_path_enrollment',
  PRE_ENROLLMENT_REPORT = 'pre_enrollment_report',
  REGULAR_PRE_ENROLLMENT_REPORT = 'regular_pre_enrollment_report',
  RETRY_PRE_ENROLLMENT = 'retry_pre_enrollment',
  SURVEY_SUBMISSION_REPORT = 'survey_submission_report',
  CUSTOMER_CREDIT_BALANCE_REPORT = 'customer_credit_balance_report',
  UPDATE_COURSE_CATEGORY = 'update_course_category',
  TRANSFER_PLAN_PACKAGE_LICENSE = 'transfer_plan_package_license',
  TSI_REPORT = 'tsi_report',
  UPDATE_CUSTOMER = 'update_new_customer',
  UPLOAD_PRE_ASSIGN_LEARNING_PATH_ENROLLMENT = 'upload_pre_assign_learning_path_enrollment',
  UPLOAD_USER_VALIDATION = 'upload_user_validation',
  USER_VALIDATE = 'user_validate',
  PLAN_PACKAGE_LICENSE_EXPIRED = 'plan_package_license_expired',
}

export enum JobStatusEnum {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  WARN = 'WARN',
  ERROR = 'ERROR',
  ABORTED = 'ABORTED',
  CANCELED = 'CANCELED',
}
//#endregion

//#region Business domain

//#endregion
