//#region Entity domain
export enum UserLicenseTypeCodeEnum {
  TSI = 'TSI-001',
  OIC_NON_LIFE = 'OIC-001',
  OIC_LIFE = 'OIC-002',
  TFAC_CPA = 'TFAC_CPA',
  TFAC_RA = 'TFAC_RA',
}

export enum ApplicantTypeTHEnum {
  AGENT = 'ตัวแทน',
  BROKER = 'นายหน้า',
  ADVISOR_ANALYST_PLANNER = 'ผู้แนะนำการลงทุน / ผู้วางแผนการลงทุน / นักวิเคราะห์การลงทุน',
  ADVISOR = 'ผู้แนะนำการลงทุน',
  ANALYST = 'นักวิเคราะห์การลงทุน',
  PLANNER = 'ผู้วางแผนการลงทุน',
}
//#endregion

//#region Business domain

//#endregion

export const LicenseTypeCodeENMapper = {
  [UserLicenseTypeCodeEnum.OIC_LIFE]: 'LIFE',
  [UserLicenseTypeCodeEnum.OIC_NON_LIFE]: 'NON-LIFE',
  [UserLicenseTypeCodeEnum.TSI]: 'TSI',
  [UserLicenseTypeCodeEnum.TFAC_CPA]: 'CPA',
  [UserLicenseTypeCodeEnum.TFAC_RA]: 'RA',
};
