import { BulkOpTypeEnum } from '@shared/lms/constants/enums/job.enum';

//#region Entity domain
export enum PreEnrollmentTransactionWarningStatusEnum {
  EMAIL_DEBOUNCE = 'EMAIL_DEBOUNCE',
  UNCERTAIN_EMAIL = 'UNCERTAIN_EMAIL',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',
  OIC_FULL_NAME_INVALID = 'OIC_FULL_NAME_INVALID',
  LICENSE_EXPIRED = 'LICENSE_EXPIRED',
  UNCHECK_REGULATOR = 'UNCHECK_REGULATOR',
}

export enum PreEnrollmentTransactionStatusEnum {
  WAITING_VERIFY = 'WAITING_VERIFY', // รอการแก้ไข
  EDITED_AFTER_VERIFY = 'EDITED_AFTER_VERIFY', // แก้ไขหลังตรวจสอบ
  WARNING = 'WARNING', // รายการน่าสงสัย
  ERROR = 'ERROR', // รายการมีปัญหา
  CANCELED = 'CANCELED', // retail cancel booking or system cancel
  REJECTED = 'REJECTED', // retail rejected หรือ (ปฏิเสธจาก admin หรือ ผู้เรียน)
  PASSED = 'PASSED', // พร้อมลงทะเบียน
  APPLIED = 'APPLIED', // ลงทะเบียนสำเร็จ
  FAILED_TO_APPLY = 'FAILED_TO_APPLY', // ลงทะเบียนไม่สำเร็จ พบข้อผิดพลาด
  BOOKING = 'BOOKING', // the retail order pre-enrollment
}

export enum PreEnrollmentTransactionPaymentTypeEnum {
  RETAIL = 'retail',
  CREDIT = 'credit',
  NON_CREDIT = 'non_credit',
}

export enum PreEnrollmentTransactionEnrollByEnum {
  SELF = 'self',
  ADMIN = 'admin',
}

export enum PreEnrollmentTransactionAutoBulkOparationEnum {
  ACTIVATE = BulkOpTypeEnum.ACTIVATE,
  EDIT_USER = BulkOpTypeEnum.EDIT_USER,
  ENROLLMENT = BulkOpTypeEnum.ENROLLMENT,
  ENROLLMENT_BUNDLE = BulkOpTypeEnum.ENROLLMENT_BUNDLE,
  ASSIGN_PLAN_PACKAGE_LICENSE = BulkOpTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE,
}

export enum OICExtendYearTypeEnum {
  NONLIFE = 'NON-LIFE',
  LIFE = 'LIFE',
  BOTH = 'BOTH',
}

export enum OICReductionTypeEnum {
  YES = 'YES',
  NO = 'NO',
}
//#endregion

//#region Business domain

//#endregion
