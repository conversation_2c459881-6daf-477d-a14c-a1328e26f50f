//#region Entity domain
export enum OrganizationSchedulerTypeEnum {
  SEND_EMAIL_CREATE_ENROLLMENT = 'SEND_EMAIL_CREATE_ENROLLMENT',
  SEND_EMAIL_CREATE_USER = 'SEND_EMAIL_CREATE_USER',
  SEND_EMAIL_ASSIGN_LEARNING_PATH_ENROLLMENT = 'SEND_EMAIL_ASSIGN_LEARNING_PATH_ENROLLMENT',
  SEND_EMAIL_REMIND_CLASSROOM_ROUND_START = 'SEND_EMAIL_REMIND_CLASSROOM_ROUND_START',
  SEND_NOTIFICATION_REMIND_EXPIRED_ENROLLMENT = 'SEND_NOTIFICATION_REMIND_EXPIRED_ENROLLMENT',
  SEND_NOTIFICATION_ENROLLMENT_ACHIEVEMENT_COMPLETED = 'SEND_NOTIFICATION_ENROLLMENT_ACHIEVEMENT_COMPLETED',
  SEND_NOTIFICATION_PLAN_PACKAGE_LICENSE_EXPIRED = 'SEND_NOTIFICATION_PLAN_PACKAGE_LICENSE_EXPIRED',
}
//#endregion

//#region Business domain

//#endregion
