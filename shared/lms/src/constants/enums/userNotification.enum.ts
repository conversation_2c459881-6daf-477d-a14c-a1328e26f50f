import { UserNotificationTypeEnum } from '@iso/constants/userNotification';

//#region Entity domain
export const UserNotificationFilterTypes: Record<string, Array<string>> = {
  ALL: [],
  MY_LEARNING: [
    UserNotificationTypeEnum.PRE_ENROLLMENT_SUCCESS,
    UserNotificationTypeEnum.PRE_ENROLLMENT_REJECTED_BY_SUPERVISOR,
    UserNotificationTypeEnum.PRE_ENROLLMENT_REJECTED,
    UserNotificationTypeEnum.ENROLLMENT_SUCCESS,
    UserNotificationTypeEnum.ENROLLMENT_WITH_ROUND_SUCCESS,
    UserNotificationTypeEnum.ENROLLMENT_REJECT,
    UserNotificationTypeEnum.ENROLLMENT_EXPIRED,
    UserNotificationTypeEnum.ENROLLMENT_APPROVED,
    UserNotificationTypeEnum.ENROLLMENT_APPROVED_WITH_CERTIFICATE,
    UserNotificationTypeEnum.ENROLLMENT_PASSED,
    UserNotificationTypeEnum.ENROLLMENT_PASSED_WITH_CERTIFICATE,
    UserNotificationTypeEnum.LEARNING_PATH_PRE_ENROLLMENT_SUCCESS,
    UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_SUCCESS,
    UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_ROUND_SUCCESS,
    UserNotificationTypeEnum.LEARNING_PATH_CANCELED_WITH_ROUND,
    UserNotificationTypeEnum.LEARNING_PATH_CANCELED_WITHOUT_ROUND,
    UserNotificationTypeEnum.LEARNING_PATH_EXPAND_EXPIRY_DAY,
    UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE,
    UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE,
    UserNotificationTypeEnum.LEARNING_PATH_PRE_ENROLLMENT_CANCELED,
    UserNotificationTypeEnum.ENROLLMENT_REQUEST_ADDITIONAL_ATTACHMENT,
    UserNotificationTypeEnum.ENROLLMENT_REQUEST_DEDUCT_ATTACHMENT,
    UserNotificationTypeEnum.ENROLLMENT_APPROVE_ADDITIONAL_ATTACHMENT,
    UserNotificationTypeEnum.ENROLLMENT_REJECTED_ADDITIONAL_ATTACHMENT,
    UserNotificationTypeEnum.ENROLLMENT_APPROVE_DEDUCT_ATTACHMENT,
    UserNotificationTypeEnum.ENROLLMENT_REJECTED_DEDUCT_ATTACHMENT,
    UserNotificationTypeEnum.SELF_ENROLLMENT,
    UserNotificationTypeEnum.SELF_PRE_ENROLLMENT,
    UserNotificationTypeEnum.LEARNING_PATH_SELF_ENROLLMENT,
    UserNotificationTypeEnum.LEARNING_PATH_SELF_PRE_ENROLLMENT,
    UserNotificationTypeEnum.LEARNING_PATH_SELF_PRE_ENROLLMENT_CANCELED,
    UserNotificationTypeEnum.ENROLLMENT_CANCELED_ADDITIONAL_ATTACHMENT,
    UserNotificationTypeEnum.ENROLLMENT_CANCELED_DEDUCT_ATTACHMENT,
    UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_EXPIRED,
    UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_CANCELED,
    UserNotificationTypeEnum.REMIND_CLASSROOM_ROUND_START,
    UserNotificationTypeEnum.REMIND_ENROLLMENT_EXPIRED,
    UserNotificationTypeEnum.RESET_LEARNING_PROGRESS,
    UserNotificationTypeEnum.CANCELED_CLASSROOM_WAITING,
    UserNotificationTypeEnum.CLASSROOM_REGISTER_SUCCESS,
    UserNotificationTypeEnum.CLASSROOM_REGISTER_WAITING,
    UserNotificationTypeEnum.CLASSROOM_REGISTER_CANCELED,
    UserNotificationTypeEnum.CHANGE_CLASSROOM_ROUND,
    UserNotificationTypeEnum.EDIT_CLASSROOM_ROUND,
    UserNotificationTypeEnum.RESOLVED_COURSE_DISCUSSION_BOARD,
    UserNotificationTypeEnum.CANCEL_COURSE_APPROVAL,
    UserNotificationTypeEnum.CANCEL_COURSE_PASS_RESULT,
    UserNotificationTypeEnum.CANCEL_LEARNING_PATH_CERTIFICATE,
    UserNotificationTypeEnum.ASSIGN_PLAN_PACKAGE_LICENSE,
    UserNotificationTypeEnum.PLAN_PACKAGE_LICENSE_EXPIRED,
    UserNotificationTypeEnum.ACHIEVEMENT_COMPLETED,
    UserNotificationTypeEnum.ACHIEVEMENT_ADDITIONAL_COMPLETED,
    UserNotificationTypeEnum.BADGE_ADDITIONAL_COMPLETED,
  ],
  ANNOUNCEMENT: [
    UserNotificationTypeEnum.PROMOTE_COURSE,
    UserNotificationTypeEnum.PROMOTE_KNOWLEDGE_CONTENT,
    UserNotificationTypeEnum.PROMOTE_ANNOUNCEMENT,
    UserNotificationTypeEnum.PROMOTE_LEARNING_PATH,
  ],
  MY_TEAM: [
    UserNotificationTypeEnum.PRE_ASSIGN_COURSE_REQUEST,
    UserNotificationTypeEnum.PRE_ASSIGN_COURSE_COMPLETED,
    UserNotificationTypeEnum.PRE_ASSIGN_LEARNING_PATH_REQUEST,
    UserNotificationTypeEnum.PRE_ASSIGN_LEARNING_PATH_COMPLETED,
    UserNotificationTypeEnum.PRE_ASSIGN_COURSE_CANCELED,
    UserNotificationTypeEnum.PRE_ASSIGN_LEARNING_PATH_CANCELED,
    UserNotificationTypeEnum.ASSIGN_ENROLLMENT_COMPLETED,
    UserNotificationTypeEnum.ASSIGN_LEARNING_PATH_ENROLLMENT_COMPLETED,
  ],
};

//#endregion

//#region Business domain
export const PreEnrollmentEnrollmentRoleByThaiTextArray = ['ผู้ดูแลระบบ', 'หน่วยงานต้นสังกัด'];

export enum UserNotificationApplicationEnum {
  LMS = 'LMS',
}
//#endregion
