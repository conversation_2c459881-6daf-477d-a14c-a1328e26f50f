//#region Entity domain
export enum DBCollectionEnum {
  ACHIEVEMENTS = 'achievements',
  ADMIN_USER_ACCESS_ORGANIZATION_LOGS = 'admin-user-access-organization-logs',
  ADMIN_USER_ORGANIZATION_ACCOUNTS = 'admin-user-organization-accounts',
  ADMIN_USERS = 'admin-users',
  ANNOUNCEMENTS = 'announcements',
  ARTICLES = 'articles',
  ATTACHMENTS = 'attachments',
  AUDIOS = 'audios',
  BADGES = 'badges',
  BUNDLES = 'bundles',
  CERTIFICATES = 'certificates',
  CLASSROOM_LOCATION_ENROLLMENTS = 'classroom-location-enrollments',
  CLASSROOM_LOCATIONS = 'classroom-locations',
  CLASSROOM_ROUNDS = 'classroom-rounds',
  CLASSROOMS = 'classrooms',
  COLUMN_SETTINGS = 'column-settings',
  COMMENTS = 'comments',
  COURSE_CATEGORIES = 'course-categories',
  COURSE_ITEM_CRITERIA_CONFIGS = 'course-item-criteria-configs',
  COURSE_ITEM_PROGRESS_HISTORIES = 'course-item-progress-histories',
  COURSE_ITEMS = 'course-items',
  COURSE_MARKETPLACES = 'course-marketplaces',
  COURSE_VERSION_CERTIFICATES = 'course-version-certificates',
  COURSE_VERSIONS = 'course-versions',
  COURSES = 'courses',
  CUSTOMER_PARTNERS = 'customer-partners',
  CUSTOMER_PRODUCT_SKUS = 'customer-product-skus',
  CUSTOMERS = 'customers',
  DEBOUNCE_EMAILS = 'debounce-emails',
  DEPARTMENTS = 'departments',
  EMAIL_TRANSACTIONS = 'email-transactions',
  ENROLLMENT_ACHIEVEMENTS = 'enrollment-achievements',
  ENROLLMENT_ATTACHMENT_ADDITIONAL_TYPES = 'enrollment-attachment-additional-types',
  ENROLLMENT_ATTACHMENTS = 'enrollment-attachments',
  ENROLLMENT_BADGES = 'enrollment-badges',
  ENROLLMENT_CERTIFICATES = 'enrollment-certificates',
  ENROLLMENT_PLAN_PACKAGE_LICENSES = 'enrollment-plan-package-licenses',
  ENROLLMENT_REGULATOR_REPORTS = 'enrollment-regulator-reports',
  ENROLLMENTS = 'enrollments',
  HIGHLIGHT_KNOWLEDGE_CONTENTS = 'highlight-knowledge-contents',
  IDENTIFICATION_CARDS = 'identification-cards',
  INSTRUCTORS = 'instructors',
  INVOICE_ITEMS = 'invoice-items',
  INVOICES = 'invoices',
  JOB_EMAIL_NOTIFICATIONS = 'job-email-notifications',
  JOB_TRANSACTIONS = 'job-transactions',
  JOBS = 'jobs',
  KNOWLEDGE_CONTENT_CATEGORIES = 'knowledge-content-categories',
  KNOWLEDGE_CONTENT_DURATION_HISTORIES = 'knowledge-content-duration-histories',
  KNOWLEDGE_CONTENT_INTERACTION_HISTORIES = 'knowledge-content-interaction-histories',
  KNOWLEDGE_CONTENT_INTERACTIONS = 'knowledge-content-interactions',
  KNOWLEDGE_CONTENT_ITEMS = 'knowledge-content-items',
  LEARNING_PATH_ENROLLMENT_CERTIFICATES = 'learning-path-enrollment-certificates',
  LEARNING_PATH_ENROLLMENTS = 'learning-path-enrollments',
  LEARNING_PATH_VERSIONS = 'learning-path-versions',
  LEARNING_PATHS = 'learning-paths',
  LICENSE_TYPES = 'license-types',
  LICENSES = 'licenses',
  LOG_RESET_ENROLLMENTS = 'log-reset-enrollments',
  LOGIN_PROVIDERS = 'login-providers',
  LOGS_ACTIONS = 'logs-actions',
  LOGS_ACTIVITY_DETECT = 'logs-activity-detect',
  LOGS_FACE_COMPARISON = 'logs-face-comparison',
  LOGS_SNAPSHOT_ENROLLMENTS = 'logs-snapshot-enrollments',
  MATERIAL_MEDIAS = 'material-medias',
  MEDIA = 'media',
  MEDIA_TRANSCODES = 'media-transcodes',
  ORGANIZATION_CERTIFICATES = 'organization-certificates',
  ORGANIZATION_COLUMN_SETTINGS = 'organization-column-settings',
  ORGANIZATION_LOGIN_PROVIDERS = 'organization-login-providers',
  ORGANIZATION_REPORTS = 'organization-reports',
  ORGANIZATION_SCHEDULERS = 'organization-schedulers',
  ORGANIZATION_STORAGES = 'organization-storages',
  ORGANIZATIONS = 'organizations',
  PACKAGES = 'packages',
  PARTS = 'parts',
  PERMISSION_GROUPS = 'permission-groups',
  PERMISSIONS = 'permissions',
  PLAN_PACKAGE_LICENSE_HISTORIES = 'plan-package-license-histories',
  PLAN_PACKAGE_LICENSES = 'plan-package-licenses',
  PLAN_PACKAGE_SALE_ORDER_ITEMS = 'plan-package-sale-order-items',
  PLAN_PACKAGES = 'plan-packages',
  PLANS = 'plans',
  POINT_HISTORIES = 'point-histories',
  PRE_ASSIGN_CONTENT_JOBS = 'pre-assign-content-jobs',
  PRE_ASSIGN_CONTENTS = 'pre-assign-contents',
  PRE_ENROLLMENT_RESERVATIONS = 'pre-enrollment-reservations',
  PRE_ENROLLMENT_TRANSACTION_HISTORIES = 'pre-enrollment-transaction-histories',
  PRE_ENROLLMENT_TRANSACTIONS = 'pre-enrollment-transactions',
  PRODUCT_SKU_BUNDLES = 'product-sku-bundles',
  PRODUCT_SKU_COURSES = 'product-sku-courses',
  PRODUCT_SKUS = 'product-skus',
  PROMOTE_NOTIFICATIONS = 'promote-notifications',
  PURCHASE_ORDERS = 'purchase-orders',
  QUIZ_ANSWERS = 'quiz-answers',
  QUIZZES = 'quizzes',
  REGISTRATIONS = 'registrations',
  REPORT_HISTORIES = 'report-histories',
  ROUNDS = 'rounds',
  SESSIONS = 'sessions',
  SUMMARY_TSI_QUIZ_SCORES = 'summary-tsi-quiz-scores',
  SURVEY_SUBMISSIONS = 'survey-submissions',
  SURVEYS = 'surveys',
  TEMPLATE_COLUMN_SETTINGS = 'template-column-settings',
  TERMS_AND_CONDITIONS = 'terms-and-conditions',
  TRANSACTION_COUNTERS = 'transaction-counters',
  UNIVERSITIES = 'universities',
  USER_ACCESS_LOGS = 'user-access-logs',
  USER_DIRECT_REPORTS = 'user-direct-reports',
  USER_GROUPS = 'user-groups',
  USER_LIVENESS_ACCOUNTS = 'user-liveness-accounts',
  USER_LOGINS = 'user-logins',
  USER_NOTIFICATIONS = 'user-notifications',
  USER_SALUTES = 'user-salutes',
  USERS = 'users',
  USERS_AUTHENTICATIONS = 'users-authentications',
  USERS_LOGIN_FAILS = 'users-login-fails',
  VERIFY_ENROLLMENT = 'verify-enrollment',
  VERIFY_ENROLLMENT_ATTACHMENTS = 'verify-enrollment-attachments',
  VIDEOS = 'videos',
}
//#endregion
