//#region Entity domain
export enum JobTransactionStatusEnum {
  PASSED = 'PASSED',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
}

export enum JobTransactionStatusCodeEnum {
  ERROR_ASSIGN_PLAN_PACKAGE_LICENSE = 'ERROR_ASSIGN_PLAN_PACKAGE_LICENSE',
  ERROR_CREATE_USER = 'ERROR_CREATE_USER',
  ERROR_PLAN_PACKAGE_NOT_FOUND = 'ERROR_PLAN_PACKAGE_NOT_FOUND',
  ERROR_RECEIVER_NOT_FOUND = 'ERROR_RECEIVER_NOT_FOUND',
  ERROR_SENDER_NOT_FOUND = 'ERROR_SENDER_NOT_FOUND',
  ERROR_TRANSFER_PLAN_PACKAGE_LICENSE = 'ERROR_TRANSFER_PLAN_PACKAGE_LICENSE',
  ERROR_USER_NOT_FOUND = 'ERROR_USER_NOT_FOUND',
  SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_NEW = 'SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_NEW',
  SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_NOTHING = 'SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_NOTHING',
  SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_OLD = 'SUCCESS_ASSIGN_PLAN_PACKAGE_LICENSE_OLD',
  SUCCESS_CREATE_USER = 'SUCCESS_CREATE_USER',
  SUCCESS_TRANSFER_PLAN_PACKAGE_LICENSE = 'SUCCESS_TRANSFER_PLAN_PACKAGE_LICENSE',
  SUCCESS_TRANSFER_PLAN_PACKAGE_LICENSE_NOTHING = 'SUCCESS_TRANSFER_PLAN_PACKAGE_LICENSE_NOTHING',
}
//#endregion

//#region Business domain

//#endregion
