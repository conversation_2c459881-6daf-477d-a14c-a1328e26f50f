import { CourseObjectiveTypeEnum } from '@shared/lms/constants/enums/course.enum';

//#region Entity domain
export enum CourseVersionStatusEnum {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  PREVIEW = 'PREVIEW',
}

export enum CourseVersionCriteriaTimeSpentTypeEnum {
  COURSE_TIME_SPENT = 'COURSE_TIME_SPENT',
  COURSE_ITEM_TIME_SPENT = 'COURSE_ITEM_TIME_SPENT',
}

export const defaultCourseExpiryDay = 6;
//#endregion

//#region Business domain

//#endregion
