import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsDate, IsNumber, IsOptional, IsString } from 'class-validator';

import {
  CreateSummaryTSIQuizScoreParams,
  SummaryTSIQuizScoreParams,
} from '@shared/lms/constants/types/summaryTSIQuizScore.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class SummaryTSIQuizScore extends BaseModel {
  @IsString()
  private _tsiCode: string;

  @IsString()
  private _courseId: GenericID;

  @IsOptional()
  @IsString()
  private _materialMediaId: Nullable<GenericID>;

  @IsOptional()
  private _itemId: Nullable<GenericID>;

  @IsNumber()
  private _count: number;

  @IsNumber()
  private _max: number;

  @IsNumber()
  private _min: number;

  @IsNumber()
  private _avg: number;

  @IsNumber()
  private _std: number;

  @IsNumber()
  private _sumSquare: number;

  @IsDate()
  private _periodCutOffAt: Date;

  constructor(props: SummaryTSIQuizScoreParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._tsiCode = props.tsiCode;
    this._courseId = props.courseId;
    this._materialMediaId = props.materialMediaId;
    this._itemId = props.itemId;
    this._count = props.count;
    this._max = props.max;
    this._min = props.min;
    this._avg = props.avg;
    this._std = props.std;
    this._sumSquare = props.sumSquare;
    this._periodCutOffAt = props.periodCutOffAt;
  }

  static async new(props: CreateSummaryTSIQuizScoreParams) {
    const entity = new SummaryTSIQuizScore({
      tsiCode: props.tsiCode,
      courseId: props.courseId,
      materialMediaId: props.materialMediaId ?? null,
      itemId: props.itemId ?? null,
      periodCutOffAt: props.periodCutOffAt,
      count: props.count ?? 0,
      max: props.max ?? 0,
      min: props.min ?? 0,
      avg: props.avg ?? 0,
      std: props.std ?? 0,
      sumSquare: props.sumSquare ?? 0,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get tsiCode(): string {
    return this._tsiCode;
  }
  set tsiCode(val: string) {
    this._tsiCode = val;
  }

  @Expose()
  get courseId(): GenericID {
    return this._courseId;
  }
  set courseId(val: GenericID) {
    this._courseId = val;
  }

  @Expose()
  get materialMediaId(): Nullable<GenericID> {
    return this._materialMediaId;
  }
  set materialMediaId(val: Nullable<GenericID>) {
    this._materialMediaId = val;
  }

  @Expose()
  get itemId(): GenericID {
    return this._itemId;
  }

  set itemId(value: GenericID) {
    this._itemId = value;
  }

  @Expose()
  get count(): number {
    return this._count;
  }
  set count(val: number) {
    this._count = val;
  }

  @Expose()
  get max(): number {
    return this._max;
  }
  set max(val: number) {
    this._max = val;
  }

  @Expose()
  get min(): number {
    return this._min;
  }
  set min(val: number) {
    this._min = val;
  }

  @Expose()
  get avg(): number {
    return this._avg;
  }
  set avg(val: number) {
    this._avg = val;
  }

  @Expose()
  get std(): number {
    return this._std;
  }
  set std(val: number) {
    this._std = val;
  }

  @Expose()
  get sumSquare(): number {
    return this._sumSquare;
  }
  set sumSquare(val: number) {
    this._sumSquare = val;
  }

  @Expose()
  get periodCutOffAt(): Date {
    return this._periodCutOffAt;
  }
  set periodCutOffAt(val: Date) {
    this._periodCutOffAt = val;
  }
}
