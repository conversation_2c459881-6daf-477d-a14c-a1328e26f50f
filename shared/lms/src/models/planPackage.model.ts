import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsEnum, IsString, IsOptional, IsDate, IsObject, IsNumber } from 'class-validator';

import { PackageTypeEnum } from '@shared/lms/constants/enums/packages.enum';
import {
  CreatePlanPackageParams,
  PlanPackageContentParams,
  PlanPackageParams,
} from '@shared/lms/constants/types/planPackage.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class PlanPackage extends BaseModel {
  @IsString()
  private _planId: GenericID;

  @IsString()
  private _packageId: GenericID;

  @IsString()
  private _name: string;

  @IsString()
  private _description: string;

  @IsEnum(PackageTypeEnum)
  private _type: PackageTypeEnum;

  @IsObject()
  private _content: PlanPackageContentParams;

  @IsNumber()
  private _totalUsageDay: number;

  @IsDate()
  @IsOptional()
  private _gracingDate: Nullable<Date>;

  @IsDate()
  private _startDate: Date;

  @IsDate()
  private _endDate: Date;

  constructor(props: PlanPackageParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._planId = props.planId;
    this._packageId = props.packageId;
    this._name = props.name;
    this._description = props.description;
    this._type = props.type;
    this._content = props.content;
    this._totalUsageDay = props.totalUsageDay;
    this._startDate = props.startDate;
    this._gracingDate = props.gracingDate;
    this._endDate = props.endDate;
  }

  static async new(props: CreatePlanPackageParams) {
    const entity = new PlanPackage({
      ...props,
      gracingDate: props?.gracingDate ?? null,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get planId(): GenericID {
    return this._planId;
  }

  set planId(val: GenericID) {
    this._planId = val;
  }

  @Expose()
  get packageId(): GenericID {
    return this._packageId;
  }

  set packageId(val: GenericID) {
    this._packageId = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get description(): string {
    return this._description;
  }

  set description(val: string) {
    this._description = val;
  }

  @Expose()
  get type(): PackageTypeEnum {
    return this._type;
  }

  set type(val: PackageTypeEnum) {
    this._type = val;
  }

  @Expose()
  get content(): PlanPackageContentParams {
    return this._content;
  }

  set content(val: PlanPackageContentParams) {
    this._content = val;
  }

  @Expose()
  get totalUsageDay(): number {
    return this._totalUsageDay;
  }

  set totalUsageDay(val: number) {
    this._totalUsageDay = val;
  }

  @Expose()
  get startDate(): Date {
    return this._startDate;
  }

  set startDate(val: Date) {
    this._startDate = val;
  }

  @Expose()
  get gracingDate(): Nullable<Date> {
    return this._gracingDate;
  }

  set gracingDate(val: Nullable<Date>) {
    this._gracingDate = val;
  }

  @Expose()
  get endDate(): Date {
    return this._endDate;
  }

  set endDate(val: Date) {
    this._endDate = val;
  }
}
