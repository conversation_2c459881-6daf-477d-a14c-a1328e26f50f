import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsEnum, IsObject, IsString } from 'class-validator';
import { v4 } from 'uuid';

import { LoginProviderMethodEnum, LoginProviderTypeEnum } from '@shared/lms/constants/enums/loginProvider.enum';
import {
  CreateLoginProviderParams,
  LoginProviderOAuthParams,
  LoginProviderParams,
} from '@shared/lms/constants/types/loginProvider.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class LoginProvider extends BaseModel {
  @IsString()
  private _name: string;

  @IsEnum(LoginProviderTypeEnum)
  private _type: LoginProviderTypeEnum;

  @IsEnum(LoginProviderMethodEnum)
  private _method: LoginProviderMethodEnum;

  @IsObject()
  private _payload: LoginProviderOAuthParams;

  constructor(payload: LoginProviderParams) {
    super(payload.id, payload.createdAt, payload.updatedAt);

    this._name = payload.name;
    this._type = payload.type;
    this._method = payload.method;
    this._payload = payload.payload;
  }

  static async new(props: CreateLoginProviderParams): Promise<LoginProvider> {
    const currentDate = date().toDate();
    const entity = new LoginProvider({ id: v4(), createdAt: currentDate, updatedAt: currentDate, ...props });
    await entity.validate();
    return entity;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get type(): LoginProviderTypeEnum {
    return this._type;
  }

  set type(val: LoginProviderTypeEnum) {
    this._type = val;
  }

  @Expose()
  get method(): LoginProviderMethodEnum {
    return this._method;
  }

  set method(val: LoginProviderMethodEnum) {
    this._method = val;
  }

  @Expose()
  get payload(): LoginProviderOAuthParams {
    return this._payload;
  }

  set payload(val: LoginProviderOAuthParams) {
    this._payload = val;
  }
}
