import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  IsEnum,
  IsInstance,
  IsDate,
  IsObject,
  ValidateIf,
} from 'class-validator';

import {
  LearningPathContentTypeEnum,
  LearningPathVersionStatusEnum,
} from '@shared/lms/constants/enums/learningPathVersion.enum';
import {
  EditLearningPathVersionParams,
  LearningPathCertificateParams,
  LearningPathContentCourseConfigParams,
  LearningPathContentParams,
  LearningPathContentQuizConfigParams,
  LearningPathVersionParams,
} from '@shared/lms/constants/types/learningPathVersion.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class LearningPathContent {
  @IsOptional()
  @IsString()
  private _courseId: GenericID;

  @IsOptional()
  @IsString()
  private _materialMediaId: GenericID;

  @IsEnum(LearningPathContentTypeEnum)
  private _type: LearningPathContentTypeEnum;

  @IsNumber()
  private _position: number;

  @IsBoolean()
  private _isCriteriaEnabled: boolean;

  @IsOptional()
  @IsObject()
  private _config: Nullable<LearningPathContentCourseConfigParams | LearningPathContentQuizConfigParams>;

  constructor(params: LearningPathContentParams) {
    this.courseId = params.courseId ?? '';
    this.materialMediaId = params.materialMediaId ?? '';
    this.type = params.type;
    this.position = params.position;
    this.isCriteriaEnabled = params.isCriteriaEnabled ?? true;
    this.config = params.config || null;
  }

  @Expose()
  get materialMediaId(): GenericID {
    return this._materialMediaId;
  }
  set materialMediaId(val: GenericID) {
    this._materialMediaId = val;
  }

  @Expose()
  get courseId(): GenericID {
    return this._courseId;
  }
  set courseId(val: GenericID) {
    this._courseId = val;
  }

  @Expose()
  get type(): LearningPathContentTypeEnum {
    return this._type;
  }
  set type(val: LearningPathContentTypeEnum) {
    this._type = val;
  }

  @Expose()
  get position(): number {
    return this._position;
  }
  set position(val: number) {
    this._position = val;
  }

  @Expose()
  get isCriteriaEnabled(): boolean {
    return this._isCriteriaEnabled;
  }
  set isCriteriaEnabled(val: boolean) {
    this._isCriteriaEnabled = val;
  }

  @Expose()
  get config(): Nullable<LearningPathContentCourseConfigParams | LearningPathContentQuizConfigParams> {
    return this._config;
  }
  set config(val: Nullable<LearningPathContentCourseConfigParams | LearningPathContentQuizConfigParams>) {
    this._config = val;
  }
}

export class LearningPathCertificate {
  @IsString()
  private _organizationCertificateId: GenericID;

  @IsString()
  private _refCode: string;

  @IsString()
  private _refName: string;

  @IsDate()
  private _createdAt: Date;

  @IsDate()
  private _updatedAt: Date;

  constructor(params: LearningPathCertificateParams) {
    this.organizationCertificateId = params.organizationCertificateId;
    this.refCode = params.refCode;
    this.refName = params.refName;
    this.createdAt = params.createdAt || date().toDate();
    this.updatedAt = params.updatedAt || date().toDate();
  }

  @Expose()
  get organizationCertificateId(): GenericID {
    return this._organizationCertificateId;
  }

  set organizationCertificateId(val: GenericID) {
    this._organizationCertificateId = val;
  }

  @Expose()
  get refCode(): string {
    return this._refCode;
  }
  set refCode(val: string) {
    this._refCode = val;
  }

  @Expose()
  get refName(): string {
    return this._refName;
  }
  set refName(val: string) {
    this._refName = val;
  }

  @Expose()
  get createdAt(): Date {
    return this._createdAt;
  }
  set createdAt(val: Date) {
    this._createdAt = val;
  }

  @Expose()
  get updatedAt(): Date {
    return this._updatedAt;
  }
  set updatedAt(val: Date) {
    this._updatedAt = val;
  }
}

export class LearningPathVersion extends BaseModel {
  @IsString()
  private _learningPathId: GenericID;

  @IsNumber()
  private _version: number;

  @IsEnum(LearningPathVersionStatusEnum)
  private _status: LearningPathVersionStatusEnum;

  @IsString()
  private _name: string;

  @IsNumber()
  @ValidateIf((object, value) => value !== null)
  private _expiryDay: Nullable<number>;

  @IsBoolean()
  private _isCertificateEnabled: boolean;

  @IsBoolean()
  private _isSequenceEnabled: boolean;

  @IsOptional()
  @IsDate()
  private _publishedAt: Nullable<Date>;

  @IsOptional()
  @IsString()
  private _publishedByUserId: GenericID;

  @IsArray()
  @IsInstance(LearningPathContent, { each: true })
  private _contents: LearningPathContent[];

  @IsObject()
  @IsOptional()
  private _certificate: Nullable<LearningPathCertificate>;

  constructor(params: LearningPathVersionParams) {
    const { id, createdAt, updatedAt, deletedAt } = params;
    super(id, createdAt, updatedAt, deletedAt);

    this.learningPathId = params.learningPathId;
    this.version = params.version;
    this.status = params.status;
    this.name = params.name;
    this.expiryDay = params.expiryDay || 0;
    this.isCertificateEnabled = params.isCertificateEnabled ?? false;
    this.isSequenceEnabled = params.isSequenceEnabled ?? false;
    this.publishedAt = params.publishedAt || null;
    this.publishedByUserId = params.publishedByUserId ?? '';
    this.contents = (params.contents || []).map((content) => new LearningPathContent(content));
    this.certificate = params.certificate ? new LearningPathCertificate(params.certificate) : null;
  }

  @Expose()
  get learningPathId(): GenericID {
    return this._learningPathId;
  }
  set learningPathId(val: GenericID) {
    this._learningPathId = val;
  }

  @Expose()
  get version(): number {
    return this._version;
  }
  set version(val: number) {
    this._version = val;
  }

  @Expose()
  get status(): LearningPathVersionStatusEnum {
    return this._status;
  }
  set status(val: LearningPathVersionStatusEnum) {
    this._status = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }
  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get expiryDay(): Nullable<number> {
    return this._expiryDay;
  }
  set expiryDay(val: Nullable<number>) {
    this._expiryDay = val;
  }

  @Expose()
  get isCertificateEnabled(): boolean {
    return this._isCertificateEnabled;
  }
  set isCertificateEnabled(val: boolean) {
    this._isCertificateEnabled = val;
  }

  @Expose()
  get isSequenceEnabled(): boolean {
    return this._isSequenceEnabled;
  }
  set isSequenceEnabled(val: boolean) {
    this._isSequenceEnabled = val;
  }

  @Expose()
  get publishedAt(): Nullable<Date> {
    return this._publishedAt;
  }
  set publishedAt(val: Nullable<Date>) {
    this._publishedAt = val;
  }

  @Expose()
  get publishedByUserId(): GenericID {
    return this._publishedByUserId;
  }
  set publishedByUserId(val: GenericID) {
    this._publishedByUserId = val;
  }

  @Expose()
  get contents(): LearningPathContent[] {
    return this._contents;
  }
  set contents(val: LearningPathContent[]) {
    this._contents = val;
  }

  @Expose()
  get certificate(): Nullable<LearningPathCertificate> {
    return this._certificate;
  }
  set certificate(val: Nullable<LearningPathCertificate>) {
    this._certificate = val;
  }

  static async new(props: LearningPathVersionParams): Promise<LearningPathVersion> {
    const entity: LearningPathVersion = new LearningPathVersion(props);
    await entity.validate();
    return entity;
  }

  async edit(payload: EditLearningPathVersionParams): Promise<void> {
    for (const [key, value] of Object.entries(payload)) {
      if (typeof value === 'undefined') continue;

      const keyProp = key as keyof EditLearningPathVersionParams;
      if (payload[keyProp] !== undefined) {
        this[keyProp] = payload[keyProp] as never;
      }
    }
  }
}
