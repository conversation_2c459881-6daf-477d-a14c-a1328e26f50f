import { GenericID } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsEnum, IsString, IsNotEmpty } from 'class-validator';

import { PackageTypeEnum } from '@shared/lms/constants/enums/packages.enum';
import {
  CreateEnrollmentPlanPackageLicenseParams,
  EnrollmentPlanPackageLicenseParams,
} from '@shared/lms/constants/types/enrollmentPlanPackageLicense.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class EnrollmentPlanPackageLicense extends BaseModel {
  @IsString()
  @IsNotEmpty()
  private _userId: GenericID;

  @IsString()
  @IsNotEmpty()
  private _enrollmentId: GenericID;

  @IsString()
  @IsNotEmpty()
  private _planPackageLicenseId: GenericID;

  @IsEnum(PackageTypeEnum)
  private _packageType: PackageTypeEnum;

  constructor(props: EnrollmentPlanPackageLicenseParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._userId = props.userId;
    this._enrollmentId = props.enrollmentId;
    this._planPackageLicenseId = props.planPackageLicenseId;
    this._packageType = props.packageType;
  }

  static async new(props: CreateEnrollmentPlanPackageLicenseParams) {
    const entity = new EnrollmentPlanPackageLicense({ ...props });
    await entity.validate();
    return entity;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }

  set userId(val: GenericID) {
    this._userId = val;
  }

  @Expose()
  get enrollmentId(): GenericID {
    return this._enrollmentId;
  }

  set enrollmentId(val: GenericID) {
    this._enrollmentId = val;
  }

  @Expose()
  get planPackageLicenseId(): GenericID {
    return this._planPackageLicenseId;
  }

  set planPackageLicenseId(val: GenericID) {
    this._planPackageLicenseId = val;
  }

  @Expose()
  get packageType(): PackageTypeEnum {
    return this._packageType;
  }

  set packageType(val: PackageTypeEnum) {
    this._packageType = val;
  }
}
