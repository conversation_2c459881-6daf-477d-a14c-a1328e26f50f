import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

import {
  CreateTermsAndConditionsParams,
  TermsAndConditionsContentParams,
  TermsAndConditionsParams,
} from '@shared/lms/constants/types/termsAndConditions.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class TermsAndConditions extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsOptional()
  private _terms: Nullable<TermsAndConditionsContentParams>;

  @IsOptional()
  private _privacy: Nullable<TermsAndConditionsContentParams>;

  constructor(props: TermsAndConditionsParams) {
    const { id, createdAt, updatedAt } = props;
    super(id, createdAt, updatedAt);

    const defaultContent: TermsAndConditionsContentParams = {
      title: '',
      subTitle: '',
      content: '',
      isEnabled: false,
      updatedByUserId: null,
      updatedAt: null,
    };

    this._organizationId = props.organizationId;
    this._terms = props.terms ?? defaultContent;
    this._privacy = props.privacy ?? defaultContent;
  }

  static async new(props: CreateTermsAndConditionsParams): Promise<TermsAndConditions> {
    const entity = new TermsAndConditions(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get terms(): Nullable<TermsAndConditionsContentParams> {
    return this._terms;
  }
  set terms(val: Nullable<TermsAndConditionsContentParams>) {
    this._terms = val;
  }

  @Expose()
  get privacy(): Nullable<TermsAndConditionsContentParams> {
    return this._privacy;
  }
  set privacy(val: Nullable<TermsAndConditionsContentParams>) {
    this._privacy = val;
  }
}
