import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsArray, IsDate, IsEnum, IsObject, IsOptional, IsString } from 'class-validator';

import {
  PromoteNotificationContentTypeEnum,
  PromoteNotificationStatusEnum,
} from '@shared/lms/constants/enums/promoteNotification.enum';
import {
  CreatePromoteNotificationParams,
  PromoteNotificationContentParams,
  PromoteNotificationParams,
  PromoteNotificationTargetUserGroupsParams,
} from '@shared/lms/constants/types/promoteNotification.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class PromoteNotification extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _name: string;

  @IsEnum(PromoteNotificationContentTypeEnum)
  private _contentType: PromoteNotificationContentTypeEnum;

  @IsObject()
  @IsOptional()
  private _content: Nullable<PromoteNotificationContentParams>;

  @IsArray()
  @IsOptional()
  private _targetUserGroups: PromoteNotificationTargetUserGroupsParams[];

  @IsDate()
  @IsOptional()
  private _publishedAt: Nullable<Date>;

  constructor(props: PromoteNotificationParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._name = props.name;
    this._contentType = props.contentType;
    this._content = props.content;
    this._targetUserGroups = props.targetUserGroups;
    this._publishedAt = props.publishedAt;
  }

  static async new(props: CreatePromoteNotificationParams): Promise<PromoteNotification> {
    const entity: PromoteNotification = new PromoteNotification({
      ...props,
      content: props.content ?? { title: '', description: '' },
      targetUserGroups: props.targetUserGroups ?? [],
      publishedAt: props.publishedAt ?? null,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(organizationId: GenericID) {
    this._organizationId = organizationId;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(name: string) {
    this._name = name;
  }

  @Expose()
  get contentType(): PromoteNotificationContentTypeEnum {
    return this._contentType;
  }

  set contentType(contentType: PromoteNotificationContentTypeEnum) {
    this._contentType = contentType;
  }

  @Expose()
  get content(): Nullable<PromoteNotificationContentParams> {
    return this._content;
  }

  set content(content: Nullable<PromoteNotificationContentParams>) {
    this._content = content;
  }

  @Expose()
  get targetUserGroups(): PromoteNotificationTargetUserGroupsParams[] {
    return this._targetUserGroups;
  }

  set targetUserGroups(targetUserGroups: PromoteNotificationTargetUserGroupsParams[]) {
    this._targetUserGroups = targetUserGroups;
  }

  @Expose()
  get publishedAt(): Nullable<Date> {
    return this._publishedAt;
  }

  set publishedAt(publishedAt: Nullable<Date>) {
    this._publishedAt = publishedAt;
  }

  @Expose()
  get status(): PromoteNotificationStatusEnum {
    const now = date().toDate();

    if (!this._publishedAt) {
      return PromoteNotificationStatusEnum.DRAFT;
    }

    if (this._publishedAt > now) {
      return PromoteNotificationStatusEnum.SCHEDULED;
    }

    return PromoteNotificationStatusEnum.SUCCESS;
  }
}
