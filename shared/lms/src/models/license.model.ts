import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';

import { ApplicantTypeTHEnum } from '@shared/lms/constants/enums/course.enum';
import { UserLicenseTypeCodeEnum } from '@shared/lms/constants/enums/license.enum';
import { CreateLicenseParams, LicenseParams } from '@shared/lms/constants/types/license.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class License extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _userId: GenericID;

  @IsEnum(UserLicenseTypeCodeEnum)
  private _licenseTypeCode: UserLicenseTypeCodeEnum;

  @IsString()
  private _licenseNo: string;

  @IsOptional()
  @IsEnum(ApplicantTypeTHEnum)
  private _type: Nullable<ApplicantTypeTHEnum>;

  @IsDate()
  @IsOptional()
  private _startedAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _expiredAt: Nullable<Date>;

  constructor(props: LicenseParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._userId = props.userId;
    this._licenseTypeCode = props.licenseTypeCode;
    this._licenseNo = props.licenseNo;
    this._type = props.type;
    this._startedAt = props.startedAt;
    this._expiredAt = props.expiredAt;
  }

  static async new(props: CreateLicenseParams): Promise<License> {
    const entity = new License({
      organizationId: props.organizationId,
      userId: props.userId,
      licenseTypeCode: props.licenseTypeCode,
      licenseNo: props.licenseNo,
      type: props.type ?? null,
      startedAt: props.startedAt ?? null,
      expiredAt: props.expiredAt ?? null,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(organizationId: GenericID) {
    this._organizationId = organizationId;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }

  set userId(userId: GenericID) {
    this._userId = userId;
  }

  @Expose()
  get licenseTypeCode(): UserLicenseTypeCodeEnum {
    return this._licenseTypeCode;
  }

  set licenseTypeCode(licenseTypeCode: UserLicenseTypeCodeEnum) {
    this._licenseTypeCode = licenseTypeCode;
  }

  @Expose()
  get licenseNo(): string {
    return this._licenseNo;
  }

  set licenseNo(licenseNo: string) {
    this._licenseNo = licenseNo;
  }

  @Expose()
  get type(): Nullable<ApplicantTypeTHEnum> {
    return this._type;
  }

  set type(type: Nullable<ApplicantTypeTHEnum>) {
    this._type = type;
  }

  @Expose()
  get startedAt(): Nullable<Date> {
    return this._startedAt;
  }

  set startedAt(startedAt: Nullable<Date>) {
    this._startedAt = startedAt;
  }

  @Expose()
  get expiredAt(): Nullable<Date> {
    return this._expiredAt;
  }

  set expiredAt(expiredAt: Nullable<Date>) {
    this._expiredAt = expiredAt;
  }
}
