import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsArray, IsBoolean, IsDate, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

import {
  KnowledgeContentItemStatusEnum,
  KnowledgeContentItemTypeEnum,
} from '@shared/lms/constants/enums/knowledgeContentItem.enum';
import { AttachmentMaterialMediaParams } from '@shared/lms/constants/types/courseItem.type';
import { KnowledgeContentItemParams } from '@shared/lms/constants/types/knowledgeContentItem.type';
import { ThumbnailMediaIdParams } from '@shared/lms/constants/types/media.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class KnowledgeContentItem extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  @IsOptional()
  private _materialMediaId: GenericID;

  @IsString()
  private _title: string;

  @IsString()
  @IsOptional()
  private _description: string;

  @IsString()
  private _code: string;

  @IsEnum(KnowledgeContentItemTypeEnum)
  private _type: KnowledgeContentItemTypeEnum;

  @IsString()
  @IsOptional()
  private _thumbnailId: string;

  @IsString()
  @IsOptional()
  private _thumbnailUrl: string;

  @IsOptional()
  private _thumbnailMediaId: Nullable<ThumbnailMediaIdParams>;

  @IsArray()
  @IsString({ each: true })
  private _instructorIds: Array<string>;

  @IsArray()
  private _media: AttachmentMaterialMediaParams[];

  @IsBoolean()
  private _isDownloadEnabled: boolean;

  @IsBoolean()
  private _isEnabled: boolean;

  @IsBoolean()
  @IsOptional()
  private _isCommentEnabled: boolean;

  @IsDate()
  @IsOptional()
  private _publishedStartAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _publishedEndAt: Nullable<Date>;

  @IsNumber()
  @IsOptional()
  private _countTotalView: number;

  @IsNumber()
  @IsOptional()
  private _countTotalLike: number;

  @IsNumber()
  @IsOptional()
  private _countTotalShare: number;

  @IsNumber()
  @IsOptional()
  private _countTotalDownload: number;

  @IsNumber()
  @IsOptional()
  private _countTotalPlaying: number;

  @IsNumber()
  @IsOptional()
  private _countTotalRating: number;

  @IsNumber()
  @IsOptional()
  private _averageRating: number;

  constructor(payload: KnowledgeContentItemParams) {
    const { id, createdAt, updatedAt, deletedAt } = payload;
    super(id, createdAt, updatedAt, deletedAt);

    this.organizationId = payload.organizationId;
    this.materialMediaId = payload.materialMediaId ?? null;
    this.title = payload.title;
    this.description = payload.description ?? '';
    this.code = payload.code;
    this.thumbnailUrl = payload.thumbnailUrl ?? '';
    this.thumbnailId = payload.thumbnailId ?? '';
    this.thumbnailMediaId = payload.thumbnailMediaId ?? null;
    this.type = payload.type;
    this.instructorIds = payload.instructorIds ?? [];
    this.media = payload.media ?? [];
    this.isDownloadEnabled = payload.isDownloadEnabled || false;
    this.isEnabled = payload.isEnabled || false;
    this.isCommentEnabled = payload.isCommentEnabled ?? true;
    this.publishedStartAt = payload.publishedStartAt || null;
    this.publishedEndAt = payload.publishedEndAt || null;
    this.countTotalLike = payload.countTotalLike || 0;
    this.countTotalView = payload.countTotalView || 0;
    this.countTotalShare = payload.countTotalShare || 0;
    this.countTotalDownload = payload.countTotalDownload || 0;
    this.countTotalPlaying = payload.countTotalPlaying || 0;
    this.countTotalRating = payload.countTotalRating || 0;
    this.averageRating = payload.averageRating || 0;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get materialMediaId(): GenericID {
    return this._materialMediaId;
  }
  set materialMediaId(val: GenericID) {
    this._materialMediaId = val;
  }

  @Expose()
  get title(): string {
    return this._title;
  }

  set title(val: string) {
    this._title = val;
  }

  @Expose()
  get description(): string {
    return this._description;
  }
  set description(val: string) {
    this._description = val;
  }

  @Expose()
  get code(): string {
    return this._code;
  }
  set code(val: string) {
    this._code = val;
  }

  @Expose()
  get type(): KnowledgeContentItemTypeEnum {
    return this._type;
  }
  set type(val: KnowledgeContentItemTypeEnum) {
    this._type = val;
  }

  @Expose()
  get thumbnailUrl(): string {
    return this._thumbnailUrl;
  }

  set thumbnailUrl(val: string) {
    this._thumbnailUrl = val;
  }

  @Expose()
  get thumbnailMediaId(): Nullable<ThumbnailMediaIdParams> {
    return this._thumbnailMediaId;
  }
  set thumbnailMediaId(val: Nullable<ThumbnailMediaIdParams>) {
    this._thumbnailMediaId = val;
  }

  @Expose()
  get instructorIds(): Array<string> {
    return this._instructorIds;
  }

  set instructorIds(val: Array<string>) {
    this._instructorIds = val;
  }

  @Expose()
  get media(): AttachmentMaterialMediaParams[] {
    return this._media;
  }

  set media(val: AttachmentMaterialMediaParams[]) {
    this._media = val;
  }

  @Expose()
  get isDownloadEnabled(): boolean {
    return this._isDownloadEnabled;
  }

  set isDownloadEnabled(val: boolean) {
    this._isDownloadEnabled = val;
  }

  @Expose()
  get isEnabled(): boolean {
    return this._isEnabled;
  }

  set isEnabled(val: boolean) {
    this._isEnabled = val;
  }

  @Expose()
  get publishedStartAt(): Nullable<Date> {
    return this._publishedStartAt;
  }

  set publishedStartAt(val: Nullable<Date>) {
    this._publishedStartAt = val;
  }

  @Expose()
  get publishedEndAt(): Nullable<Date> {
    return this._publishedEndAt;
  }

  set publishedEndAt(val: Nullable<Date>) {
    this._publishedEndAt = val;
  }

  @Expose()
  get countTotalView(): number {
    return this._countTotalView;
  }

  set countTotalView(val: number) {
    this._countTotalView = val;
  }

  @Expose()
  get countTotalLike(): number {
    return this._countTotalLike;
  }

  set countTotalLike(val: number) {
    this._countTotalLike = val;
  }

  @Expose()
  get countTotalShare(): number {
    return this._countTotalShare;
  }

  set countTotalShare(val: number) {
    this._countTotalShare = val;
  }

  @Expose()
  get countTotalDownload(): number {
    return this._countTotalDownload;
  }

  set countTotalDownload(val: number) {
    this._countTotalDownload = val;
  }

  @Expose()
  get countTotalPlaying(): number {
    return this._countTotalPlaying;
  }

  set countTotalPlaying(val: number) {
    this._countTotalPlaying = val;
  }

  @Expose()
  get countTotalRating(): number {
    return this._countTotalRating;
  }

  set countTotalRating(val: number) {
    this._countTotalRating = val;
  }

  @Expose()
  get averageRating(): number {
    return this._averageRating;
  }

  set averageRating(val: number) {
    this._averageRating = val;
  }

  @Expose()
  get isCommentEnabled(): boolean {
    return this._isCommentEnabled;
  }

  set isCommentEnabled(val: boolean) {
    this._isCommentEnabled = val;
  }

  @Expose()
  get status(): KnowledgeContentItemStatusEnum {
    return convertKnowledgeContentItemStatus(this._isEnabled, this._publishedStartAt, this._publishedEndAt);
  }

  @Expose()
  get thumbnailId(): string {
    return this._thumbnailId;
  }

  set thumbnailId(thumbnailId: string) {
    this._thumbnailId = thumbnailId;
  }

  static async new(payload: KnowledgeContentItemParams): Promise<KnowledgeContentItem> {
    const entity = new KnowledgeContentItem(payload);
    await entity.validate();
    return entity;
  }
}

const convertKnowledgeContentItemStatus = (
  isEnabled: boolean,
  publishedStartAt: Nullable<Date>,
  publishedEndAt: Nullable<Date>,
): KnowledgeContentItemStatusEnum => {
  const now = date().toDate();
  const startDate = publishedStartAt ? date(publishedStartAt).toDate() : null;
  const endDate = publishedEndAt ? date(publishedEndAt).toDate() : null;

  if (!isEnabled && !startDate && !endDate) {
    return KnowledgeContentItemStatusEnum.DRAFT;
  }

  if (!isEnabled && (startDate || endDate)) {
    return KnowledgeContentItemStatusEnum.UNPUBLISHED;
  }

  if (startDate && date(now).isBefore(startDate)) {
    return KnowledgeContentItemStatusEnum.SCHEDULED;
  }

  if (endDate && (date(now).isSame(endDate) || date(now).isAfter(endDate))) {
    return KnowledgeContentItemStatusEnum.EXPIRED;
  }

  return KnowledgeContentItemStatusEnum.PUBLISHED;
};
