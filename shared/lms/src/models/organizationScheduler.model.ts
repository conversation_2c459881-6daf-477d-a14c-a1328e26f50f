import { GenericID } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsEnum, IsString } from 'class-validator';
import { v4 } from 'uuid';

import { OrganizationSchedulerTypeEnum } from '@shared/lms/constants/enums/organizationScheduler.enum';
import {
  CreateOrganizationSchedulerParams,
  OrganizationSchedulerParams,
} from '@shared/lms/constants/types/organizationScheduler.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class OrganizationScheduler extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsEnum(OrganizationSchedulerTypeEnum)
  private _type: OrganizationSchedulerTypeEnum;

  @IsString()
  private _cron: string;

  constructor(payload: OrganizationSchedulerParams) {
    const { id, createdAt, updatedAt } = payload;
    super(id, createdAt, updatedAt);

    this._organizationId = payload.organizationId;
    this._type = payload.type;
    this._cron = payload.cron;
  }

  static async new(props: CreateOrganizationSchedulerParams): Promise<OrganizationScheduler> {
    const currentDate = date().toDate();
    const entity = new OrganizationScheduler({ id: v4(), createdAt: currentDate, updatedAt: currentDate, ...props });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(organizationId: GenericID) {
    this._organizationId = organizationId;
  }

  @Expose()
  get type(): OrganizationSchedulerTypeEnum {
    return this._type;
  }

  set type(val: OrganizationSchedulerTypeEnum) {
    this._type = val;
  }

  @Expose()
  get cron(): string {
    return this._cron;
  }

  set cron(val: string) {
    this._cron = val;
  }
}
