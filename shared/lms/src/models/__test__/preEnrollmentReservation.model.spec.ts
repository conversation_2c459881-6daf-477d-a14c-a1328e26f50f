import { date } from '@iso/helpers/dateUtils';

import { PreEnrollmentReservationStatusEnum } from '@shared/lms/constants/enums/preEnrollmentReservation.enum';
import { PreEnrollmentReservation } from '@shared/lms/models/preEnrollmentReservation.model';

describe('Test Case: PreEnrollmentReservation Model', () => {
  const model = {
    id: 'mock-id',
    jobId: 'mock-job-001',
    userId: 'mock-user-001',
    operationType: 'user_validate',
    filePath: 'xlsx/user-validates/20210610-080677-UserValidation-test1.xlsx',
    status: PreEnrollmentReservationStatusEnum.PASSED,
    customerCode: 'SKL001',
    organizationId: '1',
  };

  it(`when new model expect type & data is correct`, async () => {
    const {
      id,
      jobId,
      userId,
      operationType,
      organizationId,
      status,
      customerCode,
      roundDate,
      filePath,
      totalPoint,
      createdAt,
      updatedAt,
    } = await PreEnrollmentReservation.new(model);

    expect(id).toEqual(model.id);
    expect(jobId).toEqual(model.jobId);
    expect(organizationId).toEqual(model.organizationId);
    expect(userId).toEqual(model.userId);
    expect(operationType).toEqual(model.operationType);
    expect(filePath).toEqual(model.filePath);
    expect(status).toEqual(model.status);
    expect(customerCode).toEqual(model.customerCode);
    expect(roundDate).toEqual(null);
    expect(totalPoint).toEqual(0);
    expect(createdAt).toBeInstanceOf(Date);
    expect(updatedAt).toBeInstanceOf(Date);
  });

  it(`when new model with optional expect type & data is correct`, async () => {
    const data = {
      ...model,
      roundDate: date('2020-01-01').toDate(),
      totalPoint: 20,
    };
    const { totalPoint } = await PreEnrollmentReservation.new(data);

    expect(data.totalPoint).toEqual(totalPoint);
  });
});
