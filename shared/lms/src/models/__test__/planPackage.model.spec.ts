import { date } from '@iso/helpers/dateUtils';

import {
  PackageContentModelTypeEnum,
  PackageContentTypeEnum,
  PackageTypeEnum,
} from '@shared/lms/constants/enums/packages.enum';
import { CreatePlanPackageParams } from '@shared/lms/constants/types/planPackage.type';
import { Exception } from '@shared/lms/core/instances/exception';
import { PlanPackage } from '@shared/lms/models/planPackage.model';

describe('Test Case: Plan Package Model', () => {
  test('When input is CreatePlanPackageParams, expect output should be valid', async () => {
    const input: CreatePlanPackageParams = {
      planId: 'planId',
      packageId: 'packageId',
      name: 'Package Platform A',
      description: 'description',
      type: PackageTypeEnum.PLATFORM,
      content: {
        totalLicense: 10,
        totalTransferLicense: 10,
        remainLicense: 10,
        remainTransferLicense: 10,
      },
      totalUsageDay: 2,
      startDate: date().add(1, 'day').toDate(),
      gracingDate: date().add(3, 'day').toDate(),
      endDate: date().add(5, 'day').toDate(),
    };

    const result = await PlanPackage.new(input);
    expect(result.planId).toBe(input.planId);
    expect(result.packageId).toBe(input.packageId);
    expect(result.name).toBe(input.name);
    expect(result.description).toBe(input.description);
    expect(result.type).toBe(input.type);
    expect(result.content).toBe(input.content);
    expect(result.totalUsageDay).toBe(input.totalUsageDay);
    expect(result.startDate).toBe(input.startDate);
    expect(result.gracingDate).toBe(input.gracingDate);
    expect(result.endDate).toBe(input.endDate);

    expect(typeof result.id).toBe('string');
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  test('When input is CreatePlanPackageParams and not assign optional value , expect output should be valid in default value', async () => {
    const input: CreatePlanPackageParams = {
      planId: 'planId',
      packageId: 'packageId',
      name: 'Package Custom A',
      description: 'description',
      type: PackageTypeEnum.CONTENT,
      content: {
        model: PackageContentModelTypeEnum.CUSTOM,
        type: PackageContentTypeEnum.SKILLLANE_PLUS,
        courseIds: ['courseId-1', 'courseId-2', 'courseId-3', 'courseId-4'],
        totalLicense: 10,
        totalTransferLicense: 10,
        remainLicense: 10,
        remainTransferLicense: 10,
      },
      totalUsageDay: 4,
      startDate: date().add(1, 'day').toDate(),
      endDate: date().add(5, 'day').toDate(),
    };

    const result = await PlanPackage.new(input);
    expect(result.planId).toBe(input.planId);
    expect(result.packageId).toBe(input.packageId);
    expect(result.name).toBe(input.name);
    expect(result.description).toBe(input.description);
    expect(result.type).toBe(input.type);
    expect(result.content).toBe(input.content);
    expect(result.totalUsageDay).toBe(input.totalUsageDay);
    expect(result.startDate).toBe(input.startDate);
    expect(result.gracingDate).toBe(null);
    expect(result.endDate).toBe(input.endDate);

    expect(typeof result.id).toBe('string');
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  test('When input is CreatePlanPackageParams and some value is not match,  expect output should be invalid and throw error', async () => {
    const input = {
      planId: 'planId',
      packageId: 'packageId',
      type: 'INVALID_TYPE',
      startDate: date().add(1, 'day').toDate(),
      endDate: date().add(5, 'day').toDate(),
    };
    await expect(PlanPackage.new(input as CreatePlanPackageParams)).rejects.toBeInstanceOf(Exception);
  });
});
