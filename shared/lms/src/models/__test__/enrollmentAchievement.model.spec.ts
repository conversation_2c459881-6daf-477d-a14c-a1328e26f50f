import { date } from '@iso/helpers/dateUtils';

import {
  AchievementPostTestCriteriaTypeEnum,
  AchievementTimeSpentCriteriaTypeEnum,
  AchievementTypeEnum,
} from '@shared/lms/constants/enums/achievement.enum';
import { CreateEnrollmentAchievementParams } from '@shared/lms/constants/types/enrollmentAchievement.type';
import { EnrollmentAchievement } from '@shared/lms/models/enrollmentAchievement.model';

describe('Test Case: EnrollmentAchievement Model', () => {
  it('When input is full CreateEnrollmentAchievementParams, expect output should be valid', async () => {
    const input: CreateEnrollmentAchievementParams = {
      achievementId: 'achieve_001',
      enrollmentId: 'enroll_001',
      name: 'Completed Course Mastery',
      type: AchievementTypeEnum.COMPULSORY,
      criteria: {
        learningProgressPercentage: 100,
        timeSpent: {
          type: AchievementTimeSpentCriteriaTypeEnum.COURSE_TIME_SPENT,
          percentage: 90,
        },
        quiz: {
          type: AchievementPostTestCriteriaTypeEnum.ALL_POST_TEST_QUIZ,
          percentage: 85,
        },
      },
      certificate: {
        organizationCertificateId: 'org_cert_001',
        refCode: 'CERT_REF_MASTER',
        refName: 'Mastery Certificate Reference',
        certificateUrl: 'https://example.com/cert/mastery.pdf',
        certificateCode: 'MASTER_CERT_XYZ',
        sentAt: date().toDate(),
      },
    };

    const enrollmentAchievement = await EnrollmentAchievement.new(input);

    expect(enrollmentAchievement.id).toBeDefined();
    expect(enrollmentAchievement.achievementId).toEqual(input.achievementId);
    expect(enrollmentAchievement.enrollmentId).toEqual(input.enrollmentId);
    expect(enrollmentAchievement.name).toEqual(input.name);
    expect(enrollmentAchievement.type).toEqual(input.type);

    // Criteria
    expect(enrollmentAchievement.criteria.learningProgressPercentage).toEqual(
      input.criteria.learningProgressPercentage,
    );

    expect(enrollmentAchievement.criteria.timeSpent.type).toEqual(input.criteria.timeSpent.type);
    expect(enrollmentAchievement.criteria.timeSpent.percentage).toEqual(input.criteria.timeSpent.percentage);

    expect(enrollmentAchievement.criteria.quiz.type).toEqual(input.criteria.quiz.type);
    expect(enrollmentAchievement.criteria.quiz.percentage).toEqual(input.criteria.quiz.percentage);

    // Certificate
    expect(enrollmentAchievement.certificate.organizationCertificateId).toEqual(
      input.certificate.organizationCertificateId,
    );
    expect(enrollmentAchievement.certificate.refCode).toEqual(input.certificate.refCode);
    expect(enrollmentAchievement.certificate.refName).toEqual(input.certificate.refName);
    expect(enrollmentAchievement.certificate.certificateUrl).toEqual(input.certificate.certificateUrl);
    expect(enrollmentAchievement.certificate.certificateCode).toEqual(input.certificate.certificateCode);
    expect(enrollmentAchievement.certificate.sentAt).toEqual(input.certificate.sentAt);

    // BaseModel properties
    expect(enrollmentAchievement.createdAt).toBeInstanceOf(Date);
    expect(enrollmentAchievement.updatedAt).toBeInstanceOf(Date);
    expect(enrollmentAchievement.deletedAt).toBeNull();
  });

  it('When criteria sub-fields are null, expect correct instantiation and null values', async () => {
    const inputWithNullCriteria: CreateEnrollmentAchievementParams = {
      achievementId: 'achieve_002',
      enrollmentId: 'enroll_002',
      name: 'Achievement with Optional Criteria',
      type: AchievementTypeEnum.VOLUNTARY,
      criteria: {
        learningProgressPercentage: null,
        timeSpent: null,
        quiz: null,
      },
      certificate: {
        organizationCertificateId: 'org_cert_002',
        refCode: 'CERT_REF_OPTIONAL',
        refName: 'Optional Criteria Certificate Reference',
        certificateUrl: 'https://example.com/cert/optional.pdf',
        certificateCode: 'OPT_CERT_ABC',
        sentAt: null,
      },
    };

    const enrollmentAchievement = await EnrollmentAchievement.new(inputWithNullCriteria);

    expect(enrollmentAchievement.id).toBeDefined();
    expect(enrollmentAchievement.achievementId).toEqual(inputWithNullCriteria.achievementId);
    expect(enrollmentAchievement.enrollmentId).toEqual(inputWithNullCriteria.enrollmentId);
    expect(enrollmentAchievement.name).toEqual(inputWithNullCriteria.name);
    expect(enrollmentAchievement.type).toEqual(inputWithNullCriteria.type);

    // Criteria
    expect(enrollmentAchievement.criteria.learningProgressPercentage).toBeNull();
    expect(enrollmentAchievement.criteria.timeSpent).toBeNull();
    expect(enrollmentAchievement.criteria.quiz).toBeNull();

    // Certificate
    expect(enrollmentAchievement.certificate.organizationCertificateId).toEqual(
      inputWithNullCriteria.certificate.organizationCertificateId,
    );
    expect(enrollmentAchievement.certificate.refCode).toEqual(inputWithNullCriteria.certificate.refCode);
    expect(enrollmentAchievement.certificate.refName).toEqual(inputWithNullCriteria.certificate.refName);
    expect(enrollmentAchievement.certificate.certificateUrl).toEqual(inputWithNullCriteria.certificate.certificateUrl);
    expect(enrollmentAchievement.certificate.certificateCode).toEqual(
      inputWithNullCriteria.certificate.certificateCode,
    );
    expect(enrollmentAchievement.certificate.sentAt).toEqual(inputWithNullCriteria.certificate.sentAt);

    // BaseModel properties
    expect(enrollmentAchievement.createdAt).toBeInstanceOf(Date);
    expect(enrollmentAchievement.updatedAt).toBeInstanceOf(Date);
    expect(enrollmentAchievement.deletedAt).toBeNull();
  });

  it('When criteria and certificate is null, expect correct instantiation', async () => {
    const input: CreateEnrollmentAchievementParams = {
      achievementId: 'achieve_003',
      enrollmentId: 'enroll_003',
      name: 'Mixed Criteria Achievement',
      type: AchievementTypeEnum.VOLUNTARY,
      criteria: {
        learningProgressPercentage: null,
        timeSpent: null,
        quiz: null,
      },
      certificate: null,
    };

    const enrollmentAchievement = await EnrollmentAchievement.new(input);

    expect(enrollmentAchievement.id).toBeDefined();
    expect(enrollmentAchievement.achievementId).toEqual(input.achievementId);
    expect(enrollmentAchievement.enrollmentId).toEqual(input.enrollmentId);
    expect(enrollmentAchievement.name).toEqual(input.name);
    expect(enrollmentAchievement.type).toEqual(input.type);

    // Criteria
    expect(enrollmentAchievement.criteria.learningProgressPercentage).toBeNull();

    expect(enrollmentAchievement.criteria.timeSpent).toBeNull();

    expect(enrollmentAchievement.criteria.quiz).toBeNull();

    // Certificate
    expect(enrollmentAchievement.certificate).toBeNull();

    // BaseModel properties
    expect(enrollmentAchievement.createdAt).toBeInstanceOf(Date);
    expect(enrollmentAchievement.updatedAt).toBeInstanceOf(Date);
    expect(enrollmentAchievement.deletedAt).toBeNull();
  });
});
