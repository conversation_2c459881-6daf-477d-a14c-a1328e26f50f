import { BulkOpTypeEnum } from '@shared/lms/constants/enums/job.enum';
import { CreateTaskOperationParams } from '@shared/lms/constants/types/taskOperation.type';
import { TaskOperation } from '@shared/lms/models/taskOperation.model';
import { generateTaskOperationKey } from '@shared/lms/services/taskOperation.service';

describe('Test Case: TaskOperation Model', () => {
  it('When input is CrateTaskOperation, expect output should be valid', async () => {
    const input: CreateTaskOperationParams = {
      key: generateTaskOperationKey('domain', BulkOpTypeEnum.ENROLLMENT_ACHIEVEMENT_COMPLETED),
      total: 10,
      success: 7,
      error: 3,
    };

    const taskOperation = await TaskOperation.new(input);
    expect(taskOperation.key).toEqual(input.key);
    expect(taskOperation.total).toEqual(input.total);
    expect(taskOperation.success).toEqual(input.success);
    expect(taskOperation.error).toEqual(input.error);
  });

  it('When input is CrateTaskOperationParams with optional, expect output should be valid in default value', async () => {
    const input: CreateTaskOperationParams = {
      key: generateTaskOperationKey('domain', BulkOpTypeEnum.ENROLLMENT_ACHIEVEMENT_COMPLETED),
    };

    const taskOperation = await TaskOperation.new(input);
    expect(taskOperation.key).toEqual(input.key);
    expect(taskOperation.total).toEqual(0);
    expect(taskOperation.success).toEqual(0);
    expect(taskOperation.error).toEqual(0);
  });
});
