import { date } from '@iso/helpers/dateUtils';

import { CreatePlanPackageSaleOrderItemParams } from '@shared/lms/constants/types/planPackageSaleOrderItem.type';
import { PlanPackageSaleOrderItem } from '@shared/lms/models/planPackageSaleOrderItem.model';

describe('Test Case: PlanPackageSaleOrderItem Model', () => {
  it('When input is PlanPackageSaleOrderItem, expect output should be valid', async () => {
    const input: CreatePlanPackageSaleOrderItemParams = {
      orderId: 'SO1',
      orderItemId: 1,
      saleOrderNo: 'SO1-1',
      planPackageId: 'mock-planPackageId',
      startDate: date().toDate(),
      endDate: date().add(5, 'day').toDate(),
    };

    const model = await PlanPackageSaleOrderItem.new(input);

    expect(model.id).toBeDefined();
    expect(model.orderId).toEqual(input.orderId);
    expect(model.orderItemId).toEqual(input.orderItemId);
    expect(model.planPackageId).toEqual(input.planPackageId);
    expect(model.note).toEqual('');
    expect(model.startDate).toEqual(input.startDate);
    expect(model.endDate).toEqual(input.endDate);
    expect(model.createdAt).toBeInstanceOf(Date);
    expect(model.updatedAt).toBeInstanceOf(Date);
    expect(model.deletedAt).toBeNull();
  });

  it('When input PlanPackageSaleOrderItem params some field optional, expect output should be default', async () => {
    const input: CreatePlanPackageSaleOrderItemParams = {
      orderId: 'SO1',
      orderItemId: 1,
      saleOrderNo: 'SO1-1',
      planPackageId: 'mock-planPackageId',
      note: 'text',
      startDate: date().toDate(),
      endDate: date().add(5, 'day').toDate(),
    };

    const model = await PlanPackageSaleOrderItem.new(input);

    expect(model.id).toBeDefined();
    expect(model.orderId).toEqual(input.orderId);
    expect(model.orderItemId).toEqual(input.orderItemId);
    expect(model.planPackageId).toEqual(input.planPackageId);
    expect(model.startDate).toEqual(input.startDate);
    expect(model.endDate).toEqual(input.endDate);
    expect(model.note).toEqual('text');
    expect(model.createdAt).toBeInstanceOf(Date);
    expect(model.updatedAt).toBeInstanceOf(Date);
    expect(model.deletedAt).toBeNull();
  });
});
