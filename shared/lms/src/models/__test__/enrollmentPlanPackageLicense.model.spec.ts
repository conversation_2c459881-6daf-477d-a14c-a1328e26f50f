import { PackageTypeEnum } from '@shared/lms/constants/enums/packages.enum';
import { CreateEnrollmentPlanPackageLicenseParams } from '@shared/lms/constants/types/enrollmentPlanPackageLicense.type';
import { Exception } from '@shared/lms/core/instances/exception';
import { EnrollmentPlanPackageLicense } from '@shared/lms/models/enrollmentPlanPackageLicense.model';

describe('Test Case: Enrollment Plan Package License Model', () => {
  test('When input is CreateEnrollmentPlanPackageLicenseParams, expect output should be valid', async () => {
    const input: CreateEnrollmentPlanPackageLicenseParams = {
      userId: 'userA',
      enrollmentId: 'enrollmentA',
      planPackageLicenseId: 'planPackageLicenseA',
      packageType: PackageTypeEnum.CONTENT,
    };

    const result = await EnrollmentPlanPackageLicense.new(input);
    expect(result.userId).toBe(input.userId);
    expect(result.enrollmentId).toBe(input.enrollmentId);
    expect(result.planPackageLicenseId).toBe(input.planPackageLicenseId);
    expect(result.packageType).toBe(input.packageType);

    expect(typeof result.id).toBe('string');
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  test('When input is CreateEnrollmentPlanPackageLicenseParams and some value is not match,  expect output should be invalid and throw error', async () => {
    const input = {
      userId: 'userA',
      enrollmentId: 'enrollmentA',
      planPackageLicenseId: 'planPackageLicenseA',
      packageType: 'INVALID_PACKAGE_TYPE',
    };
    await expect(
      EnrollmentPlanPackageLicense.new(input as CreateEnrollmentPlanPackageLicenseParams),
    ).rejects.toBeInstanceOf(Exception);
  });
});
