import { date } from '@iso/helpers/dateUtils';

import { CreateCustomerParams } from '@shared/lms/constants/types/customer.type';
import { Exception } from '@shared/lms/core/instances/exception';
import { Customer } from '@shared/lms/models/customer.model';

describe('Test Case: Customer Model', () => {
  it('When input is CreateCustomerParams, expect output should be valid.', async () => {
    const input: CreateCustomerParams = {
      customerName: 'customerName',
      customerCode: 'customerCode',
      certificatedConfig: {
        logoImageUrl: 'logoImageUrl',
        isDynamicCertificate: true,
        textDynamicCertificate: 'textDynamicCertificate',
      },
      deletedAt: date().toDate(),
    };
    const result = await Customer.new(input);
    expect(result.customerCode).toBe(input.customerCode);
    expect(result.customerName).toBe(input.customerName);
    expect(result.certificatedConfig.logoImageUrl).toBe(input.certificatedConfig.logoImageUrl);
    expect(result.certificatedConfig.isDynamicCertificate).toBe(input.certificatedConfig.isDynamicCertificate);
    expect(result.certificatedConfig.textDynamicCertificate).toBe(input.certificatedConfig.textDynamicCertificate);
    expect(result.deletedAt).toBe(input.deletedAt);

    expect(typeof result.id).toBe('string');
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  it('When input is not CreateCustomerParams, expect output should be invalid.', async () => {
    const input: CreateCustomerParams = {
      customerName: null,
      customerCode: null,
      certificatedConfig: {
        logoImageUrl: 'logoImageUrl',
        isDynamicCertificate: true,
        textDynamicCertificate: 'textDynamicCertificate',
      },
      deletedAt: date().toDate(),
    };
    await expect(Customer.new(input)).rejects.toBeInstanceOf(Exception);
  });
});
