import { date } from '@iso/helpers/dateUtils';

import { ApplicantTypeTHEnum } from '@shared/lms/constants/enums/course.enum';
import { UserLicenseTypeCodeEnum } from '@shared/lms/constants/enums/license.enum';
import { CreateLicenseParams } from '@shared/lms/constants/types/license.type';
import { Exception } from '@shared/lms/core/instances/exception';
import { License } from '@shared/lms/models/license.model';

describe('Test Case: License Model', () => {
  it('When input is CreateLicenseParams, expect output should be valid', async () => {
    const input: CreateLicenseParams = {
      userId: 'userId',
      licenseTypeCode: UserLicenseTypeCodeEnum.OIC_LIFE,
      licenseNo: 'licenseNo',
      type: ApplicantTypeTHEnum.ADVISOR,
      organizationId: 'organizationId',
      expiredAt: date().toDate(),
    };

    const result = await License.new(input);

    expect(result.userId).toBe(input.userId);
    expect(result.type).toBe(input.type);
    expect(result.licenseTypeCode).toBe(input.licenseTypeCode);
    expect(result.licenseNo).toBe(input.licenseNo);
    expect(result.expiredAt).toBe(input.expiredAt);
    expect(result.organizationId).toBe(input.organizationId);
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  it('When input is CreateLicenseParams type and not assign optional value , expect output should be valid in default value', async () => {
    const input: CreateLicenseParams = {
      userId: 'userId',
      organizationId: 'organizationId',
      licenseTypeCode: UserLicenseTypeCodeEnum.OIC_NON_LIFE,
      licenseNo: 'licenseNo',
    };

    const result = await License.new(input);

    expect(result.userId).toBe(input.userId);
    expect(result.organizationId).toBe(input.organizationId);
    expect(result.licenseNo).toBe(input.licenseNo);
    expect(result.licenseTypeCode).toBe(input.licenseTypeCode);
    expect(result.type).toBe(null);
    expect(result.startedAt).toBe(null);
    expect(result.expiredAt).toBe(null);
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  it('When input is CreateLicenseParams and some value is not match,  expect output should be invalid and throw error', async () => {
    const input = {
      licenseTypeCode: UserLicenseTypeCodeEnum.OIC_NON_LIFE,
      licenseNo: 'licenseNo',
    };
    await expect(License.new(input as CreateLicenseParams)).rejects.toBeInstanceOf(Exception);
  });
});
