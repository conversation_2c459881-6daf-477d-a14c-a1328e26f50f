import { date } from '@iso/helpers/dateUtils';

import { PlanStatusEnum, SaleOrderStatusEnum } from '@shared/lms/constants/enums/plan.enum';
import { CreatePlanParams } from '@shared/lms/constants/types/plan.type';
import { Exception } from '@shared/lms/core/instances/exception';
import { Plan } from '@shared/lms/models/plan.model';

describe('Test Case: Plan Model', () => {
  test('When input is CreatePlanParams, expect output should be valid', async () => {
    const input: CreatePlanParams = {
      name: 'plan name',
      organizationId: 'organizationId',
      startDate: date().add(1, 'day').toDate(),
      gracingDate: date().add(3, 'day').toDate(),
      endDate: date().add(5, 'day').toDate(),
      saleOrderStatus: SaleOrderStatusEnum.PENDING,
    };

    const result = await Plan.new(input);
    expect(result.organizationId).toBe(input.organizationId);
    expect(result.gracingDate).toBe(input.gracingDate);
    expect(result.startDate).toBe(input.startDate);
    expect(result.endDate).toBe(input.endDate);
    expect(result.saleOrderStatus).toBe(input.saleOrderStatus);
    expect(result.status).toBe(PlanStatusEnum.NOT_STARTED);

    expect(typeof result.id).toBe('string');
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  test('When input is CreatePlanParams and not assign optional value , expect output should be valid in default value', async () => {
    const input: CreatePlanParams = {
      name: 'plan name',
      organizationId: 'organizationId',
      startDate: date().add(1, 'day').toDate(),
      endDate: date().add(5, 'day').toDate(),
      saleOrderStatus: SaleOrderStatusEnum.PENDING,
    };

    const result = await Plan.new(input);
    expect(result.organizationId).toBe(input.organizationId);
    expect(result.gracingDate).toBe(null);
    expect(result.startDate).toBe(input.startDate);
    expect(result.endDate).toBe(input.endDate);
    expect(result.saleOrderStatus).toBe(input.saleOrderStatus);
    expect(result.status).toBe(PlanStatusEnum.NOT_STARTED);

    expect(typeof result.id).toBe('string');
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
  });

  test('When input is CreatePackageParams and some value is not match,  expect output should be invalid and throw error', async () => {
    const input = {
      organizationId: '',
      startDate: date().add(1, 'day').toDate(),
      endDate: date().add(5, 'day').toDate(),
      saleOrderStatus: 'INVALID_STATUS',
    };
    await expect(Plan.new(input as CreatePlanParams)).rejects.toBeInstanceOf(Exception);
  });
});
