import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsArray, IsBoolean, IsDate, IsEnum, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

import { ExternalContentTypeEnum } from '@shared/lms/constants/enums/course.enum';
import { BusinessTypeEnum, EnrollmentStatusEnum, EnrollTypeEnum } from '@shared/lms/constants/enums/enrollment.enum';
import { CourseItemProgressParams } from '@shared/lms/constants/types/courseItemProgress.type';
import {
  CreateEnrollmentParams,
  EnrollmentParams,
  SnapshotDetailParams,
} from '@shared/lms/constants/types/enrollment.type';
import { BaseModel } from '@shared/lms/core/instances/base';
import { IsGenericId } from '@shared/lms/core/instances/validator';

export class Enrollment extends BaseModel {
  @IsString()
  private _courseId: GenericID;

  @IsString()
  private _courseVersionId: GenericID;

  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _userId: GenericID;

  @IsString()
  private _roundId: GenericID;

  @IsString()
  @IsOptional()
  private _preAssignContentId: GenericID;

  @IsEnum(BusinessTypeEnum)
  private _business: BusinessTypeEnum;

  @IsString()
  private _customerCode: string;

  @IsEnum(EnrollmentStatusEnum)
  private _status: EnrollmentStatusEnum;

  @IsEnum(ExternalContentTypeEnum)
  private _externalContentType: ExternalContentTypeEnum;

  @IsEnum(EnrollTypeEnum)
  @IsOptional()
  private _enrollType: Nullable<EnrollTypeEnum>;

  @IsBoolean()
  private _isUploadHistory: boolean;

  @IsBoolean()
  private _isCountdownArticle: boolean;

  @IsBoolean()
  private _isIdentityVerificationEnabled: boolean;

  @IsNumber()
  private _completedCourseItem: number;

  @IsGenericId()
  @IsOptional()
  private _lastAccessItemId: GenericID;

  @IsArray()
  private _learningProgress: CourseItemProgressParams[];

  @IsDate()
  @IsOptional()
  private _finishedAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _requestedApprovalAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _approvalAt: Nullable<Date>;

  @IsString()
  @IsOptional()
  private _approvalReason: string;

  @IsString()
  @IsOptional()
  private _imageIdCardPath: string;

  @IsString()
  @IsOptional()
  private _imageFacePath: string;

  @IsObject()
  @IsOptional()
  private _snapshot: Nullable<SnapshotDetailParams>;

  @IsString()
  @IsOptional()
  private _remark: string;

  @IsDate()
  @IsOptional()
  private _acceptedAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _passedAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _startedAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _expiredAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _resetAt: Nullable<Date>;

  constructor(props: EnrollmentParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._courseId = props.courseId;
    this._courseVersionId = props.courseVersionId;
    this._organizationId = props.organizationId;
    this._userId = props.userId;
    this._roundId = props.roundId;
    this._preAssignContentId = props.preAssignContentId || null;
    this._business = props.business;
    this._customerCode = props.customerCode ?? '';
    this._status = props.status;
    this._externalContentType = props.externalContentType;
    this._isUploadHistory = props.isUploadHistory ?? false;
    this._isCountdownArticle = props.isCountdownArticle;
    this._isIdentityVerificationEnabled = props.isIdentityVerificationEnabled;
    this._imageIdCardPath = props.imageIdCardPath ?? '';
    this._imageFacePath = props.imageFacePath ?? '';
    this._completedCourseItem = props.completedCourseItem;
    this._lastAccessItemId = props.lastAccessItemId ?? '';
    this._learningProgress = props.learningProgress;
    this._acceptedAt = props.acceptedAt;
    this._finishedAt = props.finishedAt;
    this._requestedApprovalAt = props.requestedApprovalAt;
    this._approvalAt = props.approvalAt;
    this._approvalReason = props.approvalReason;
    this._snapshot = props.snapshot;
    this._passedAt = props.passedAt;
    this._startedAt = props.startedAt;
    this._expiredAt = props.expiredAt;
    this._resetAt = props.resetAt;
    this._remark = props.remark;
    this._enrollType = props.enrollType;
  }

  static async new(props: CreateEnrollmentParams) {
    const entity = new Enrollment({
      ...props,
      userId: props?.userId ?? '',
      roundId: props?.roundId ?? '',
      preAssignContentId: props?.preAssignContentId ?? '',
      customerCode: props.customerCode ?? '',
      isUploadHistory: props?.isUploadHistory ?? false,
      isCountdownArticle: props?.isCountdownArticle ?? false,
      isIdentityVerificationEnabled: props?.isIdentityVerificationEnabled ?? false,
      imageIdCardPath: props?.imageIdCardPath ?? '',
      imageFacePath: props?.imageFacePath ?? '',
      completedCourseItem: props?.completedCourseItem ?? 0,
      lastAccessItemId: props?.lastAccessItemId ?? '',
      learningProgress: props?.learningProgress ?? [],
      acceptedAt: props?.acceptedAt ?? null,
      finishedAt: props?.finishedAt ?? null,
      requestedApprovalAt: props?.requestedApprovalAt ?? null,
      approvalAt: props?.approvalAt ?? null,
      approvalReason: props?.approvalReason ?? '',
      snapshot: props?.snapshot ?? null,
      passedAt: props?.passedAt ?? null,
      startedAt: props?.startedAt ?? date().toDate() ?? null,
      expiredAt: props?.expiredAt ?? null,
      resetAt: props?.resetAt ?? null,
      remark: props?.remark ?? '',
      externalContentType: props?.externalContentType ?? ExternalContentTypeEnum.NONE,
      enrollType: props.enrollType ?? EnrollTypeEnum.VOLUNTARY,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get courseId(): GenericID {
    return this._courseId;
  }

  set courseId(val: GenericID) {
    this._courseId = val;
  }

  @Expose()
  get courseVersionId(): GenericID {
    return this._courseVersionId;
  }

  set courseVersionId(val: GenericID) {
    this._courseVersionId = val;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }
  set userId(val: GenericID) {
    this._userId = val;
  }

  @Expose()
  get roundId(): GenericID {
    return this._roundId;
  }
  set roundId(val: GenericID) {
    this._roundId = val;
  }

  @Expose()
  get preAssignContentId(): GenericID {
    return this._preAssignContentId;
  }
  set preAssignContentId(val: GenericID) {
    this._preAssignContentId = val;
  }

  @Expose()
  get business(): BusinessTypeEnum {
    return this._business;
  }
  set business(val: BusinessTypeEnum) {
    this._business = val;
  }

  @Expose()
  get customerCode(): string {
    return this._customerCode;
  }
  set customerCode(val: string) {
    this._customerCode = val;
  }

  @Expose()
  get status(): EnrollmentStatusEnum {
    return this._status;
  }
  set status(val: EnrollmentStatusEnum) {
    this._status = val;
  }

  @Expose()
  get externalContentType(): ExternalContentTypeEnum {
    return this._externalContentType;
  }
  set externalContentType(val: ExternalContentTypeEnum) {
    this._externalContentType = val;
  }

  @Expose()
  get enrollType(): Nullable<EnrollTypeEnum> {
    return this._enrollType;
  }
  set enrollType(val: Nullable<EnrollTypeEnum>) {
    this._enrollType = val;
  }

  @Expose()
  get isCountdownArticle(): boolean {
    return this._isCountdownArticle;
  }

  set isCountdownArticle(val: boolean) {
    this._isCountdownArticle = val;
  }

  @Expose()
  get imageIdCardPath(): string {
    return this._imageIdCardPath;
  }
  set imageIdCardPath(val: string) {
    this._imageIdCardPath = val;
  }

  @Expose()
  get imageFacePath(): string {
    return this._imageFacePath;
  }
  set imageFacePath(val: string) {
    this._imageFacePath = val;
  }

  @Expose()
  get acceptedAt(): Nullable<Date> {
    return this._acceptedAt;
  }
  set acceptedAt(val: Nullable<Date>) {
    this._acceptedAt = val;
  }

  @Expose()
  get completedCourseItem(): number {
    return this._completedCourseItem;
  }

  set completedCourseItem(val: number) {
    this._completedCourseItem = val;
  }

  @Expose()
  get lastAccessItemId(): GenericID {
    return this._lastAccessItemId;
  }
  set lastAccessItemId(val: GenericID) {
    this._lastAccessItemId = val;
  }

  @Expose()
  get learningProgress(): CourseItemProgressParams[] {
    return this._learningProgress;
  }
  set learningProgress(val: CourseItemProgressParams[]) {
    this._learningProgress = val;
  }

  @Expose()
  get finishedAt(): Nullable<Date> {
    return this._finishedAt;
  }
  set finishedAt(val: Nullable<Date>) {
    this._finishedAt = val;
  }

  @Expose()
  get requestedApprovalAt(): Nullable<Date> {
    return this._requestedApprovalAt;
  }
  set requestedApprovalAt(val: Nullable<Date>) {
    this._requestedApprovalAt = val;
  }

  @Expose()
  get approvalAt(): Nullable<Date> {
    return this._approvalAt;
  }
  set approvalAt(val: Nullable<Date>) {
    this._approvalAt = val;
  }

  @Expose()
  get approvalReason(): string {
    return this._approvalReason;
  }
  set approvalReason(val: string) {
    this._approvalReason = val;
  }

  @Expose()
  get passedAt(): Nullable<Date> {
    return this._passedAt;
  }
  set passedAt(val: Nullable<Date>) {
    this._passedAt = val;
  }

  @Expose()
  get startedAt(): Nullable<Date> {
    return this._startedAt;
  }
  set startedAt(val: Nullable<Date>) {
    this._startedAt = val;
  }

  @Expose()
  get expiredAt(): Nullable<Date> {
    return this._expiredAt;
  }
  set expiredAt(val: Nullable<Date>) {
    this._expiredAt = val;
  }

  @Expose()
  get resetAt(): Nullable<Date> {
    return this._resetAt;
  }
  set resetAt(val: Nullable<Date>) {
    this._resetAt = val;
  }

  @Expose()
  get snapshot(): Nullable<SnapshotDetailParams> {
    return this._snapshot;
  }
  set snapshot(val: Nullable<SnapshotDetailParams>) {
    this._snapshot = val;
  }

  @Expose()
  get remark(): string {
    return this._remark;
  }
  set remark(val: string) {
    this._remark = val;
  }

  @Expose()
  get isIdentityVerificationEnabled(): boolean {
    return this._isIdentityVerificationEnabled;
  }

  set isIdentityVerificationEnabled(val: boolean) {
    this._isIdentityVerificationEnabled = val;
  }

  @Expose()
  get isUploadHistory(): boolean {
    return this._isUploadHistory;
  }

  set isUploadHistory(isUploadHistory: boolean) {
    this._isUploadHistory = isUploadHistory;
  }
}
