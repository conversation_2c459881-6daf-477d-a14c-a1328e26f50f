import { GenericID, Nullable, ObjectValue, Optional } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEmail,
  IsEnum,
  IsInstance,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

import { GenderEnum, UserRoleEnum } from '@shared/lms/constants/enums/user.enum';
import {
  UserProfileParams,
  UserRegulatorProfileParams,
  UserPermissionParams,
  UserTwoFactorSecretParams,
  UserParams,
  CreateUserParams,
  UserPermissionRegulatorParams,
} from '@shared/lms/constants/types/user.type';
import { BaseGuidModel } from '@shared/lms/core/instances/base';

export class UserPermission {
  @IsString()
  private _id: GenericID;

  @IsBoolean()
  private _isGrant: boolean;

  constructor(props: UserPermissionParams) {
    this._id = props.id;
    this._isGrant = props.isGrant;
  }

  @Expose()
  get id(): GenericID {
    return this._id;
  }

  set id(id: GenericID) {
    this._id = id;
  }

  @Expose()
  get isGrant(): boolean {
    return this._isGrant;
  }

  set isGrant(isGrant: boolean) {
    this._isGrant = isGrant;
  }
}

export class UserProfile {
  @IsString()
  private _salute: string;

  @IsString()
  private _firstname: string;

  @IsString()
  private _lastname: string;

  @IsString()
  private _middlename: string;

  @IsString()
  @IsOptional()
  private _avatar: Nullable<string>;

  @IsString()
  private _mobilePhoneNumber: string;

  @IsEnum(GenderEnum)
  @IsOptional()
  private _gender: Nullable<GenderEnum>;

  @IsDate()
  @IsOptional()
  private _dateOfBirth: Nullable<Date>;

  constructor(props: UserProfileParams) {
    this._salute = props.salute;
    this._firstname = props.firstname;
    this._lastname = props.lastname;
    this._middlename = props.middlename;
    this._mobilePhoneNumber = props.mobilePhoneNumber;
    this._gender = props.gender;
    this._avatar = props.avatar;
    this._dateOfBirth = props.dateOfBirth;
  }

  @Expose()
  get salute(): string {
    return this._salute;
  }

  set salute(salute: string) {
    this._salute = salute;
  }

  @Expose()
  get firstname(): string {
    return this._firstname;
  }

  set firstname(firstname: string) {
    this._firstname = firstname;
  }

  @Expose()
  get lastname(): string {
    return this._lastname;
  }

  set lastname(lastname: string) {
    this._lastname = lastname;
  }

  @Expose()
  get middlename(): string {
    return this._middlename;
  }

  set middlename(middlename: string) {
    this._middlename = middlename;
  }

  @Expose()
  get avatar(): Nullable<string> {
    return this._avatar;
  }

  set avatar(avatar: Nullable<string>) {
    this._avatar = avatar;
  }

  @Expose()
  get mobilePhoneNumber(): string {
    return this._mobilePhoneNumber;
  }

  set mobilePhoneNumber(val: string) {
    this._mobilePhoneNumber = val;
  }

  @Expose()
  get gender(): GenderEnum {
    return this._gender;
  }

  set gender(gender: GenderEnum) {
    this._gender = gender;
  }

  @Expose()
  get dateOfBirth(): Nullable<Date> {
    return this._dateOfBirth;
  }

  set dateOfBirth(dateOfBirth: Nullable<Date>) {
    this._dateOfBirth = dateOfBirth;
  }
}

export class UserRegularProfile {
  @IsString()
  private _prefix: string;

  @IsString()
  private _firstname: string;

  @IsString()
  private _lastname: string;

  constructor(props: UserRegulatorProfileParams) {
    this._prefix = props.prefix;
    this._firstname = props.firstname;
    this._lastname = props.lastname;
  }

  @Expose()
  get prefix(): string {
    return this._prefix;
  }

  set prefix(prefix: string) {
    this._prefix = prefix;
  }

  @Expose()
  get firstname(): string {
    return this._firstname;
  }

  set firstname(firstname: string) {
    this._firstname = firstname;
  }

  @Expose()
  get lastname(): string {
    return this._lastname;
  }

  set lastname(lastname: string) {
    this._lastname = lastname;
  }
}

export class UserTwoFactorSecret {
  @IsString()
  private _ascii: string;

  @IsString()
  private _hex: string;

  @IsString()
  private _base32: string;

  @IsString()
  @IsOptional()
  private _otpauth_url: Nullable<string>;

  constructor(props: UserTwoFactorSecretParams) {
    this._ascii = props.ascii;
    this._hex = props.hex;
    this._base32 = props.base32;
    this._otpauth_url = props?.otpauth_url ?? null;
  }

  @Expose()
  get ascii(): string {
    return this._ascii;
  }

  set ascii(ascii: string) {
    this._ascii = ascii;
  }

  @Expose()
  get hex(): string {
    return this._hex;
  }

  set hex(hex: string) {
    this._hex = hex;
  }

  @Expose()
  get base32(): string {
    return this._base32;
  }

  set base32(base32: string) {
    this._base32 = base32;
  }

  @Expose()
  get otpauthurl(): Nullable<string> {
    return this._otpauth_url;
  }

  set otpauthurl(otpauthurl: Nullable<string>) {
    this._otpauth_url = otpauthurl;
  }
}

export class UserPermissionRegulator {
  @IsString()
  private _regulator: string;

  @IsString({ each: true })
  private _trainingCenterCodes: string[];

  constructor(props: UserPermissionRegulatorParams) {
    this._regulator = props.regulator;
    this._trainingCenterCodes = props.trainingCenterCodes;
  }

  @Expose()
  get regulator(): string {
    return this._regulator;
  }

  set regulator(regulator: string) {
    this._regulator = regulator;
  }

  @Expose()
  get trainingCenterCodes(): string[] {
    return this._trainingCenterCodes;
  }

  set trainingCenterCodes(_trainingCenterCodes: string[]) {
    this._trainingCenterCodes = _trainingCenterCodes;
  }
}

export class User extends BaseGuidModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _username: string;

  @IsEmail()
  @IsString()
  private _email: string;

  @IsString()
  private _passwordHash: string;

  @IsString()
  private _citizenId: string;

  @IsString()
  private _last4DigitCitizenId: string;

  @ValidateNested()
  @IsInstance(UserProfile)
  private _profile: UserProfile;

  private _active: boolean;

  @IsBoolean()
  private _verified: boolean;

  @IsBoolean()
  private _isTest: boolean;

  @IsArray()
  @IsString({ each: true })
  private _permissionGroupIds: GenericID[];

  @IsArray()
  @ValidateNested({ each: true })
  @IsInstance(UserPermission, { each: true })
  private _permissions: UserPermission[];

  @IsDate()
  @IsOptional()
  private _suspendedUntil: Nullable<Date>;

  @IsBoolean()
  private _isTwoFactorEnable: boolean;

  @IsBoolean()
  private _isTwoFactorRegister: boolean;

  @IsBoolean()
  private _isTwoFactorLogin: boolean;

  @IsOptional()
  @ValidateNested()
  @IsInstance(UserTwoFactorSecret)
  private _twoFactorSecret: Nullable<UserTwoFactorSecret>;

  @IsOptional()
  @IsString({ each: true })
  private _customerCodes: string[];

  @IsOptional()
  @ValidateNested()
  @IsInstance(UserPermissionRegulator)
  private _permissionRegulator: Nullable<UserPermissionRegulatorParams>;

  @IsString()
  @IsOptional()
  private _lostToken: Optional<string>;

  @IsString()
  private _firstPasswordToken: string;

  @IsObject()
  @IsOptional()
  private _additionalField: Nullable<ObjectValue>;

  @IsOptional()
  @ValidateNested()
  @IsInstance(UserRegularProfile)
  private _regulatorProfile: Nullable<UserRegularProfile>;

  @IsDate()
  @IsOptional()
  private _setPasswordDate: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _firstActiveDate: Nullable<Date>;

  @IsBoolean()
  private _isEnableLocalLogin: boolean;

  @IsString()
  @IsOptional()
  private _returnUrl: Nullable<string>;

  @IsBoolean()
  private _isPassedUlSaleQualify: boolean;

  @IsBoolean()
  private _isTerminated: boolean;

  @IsEnum(UserRoleEnum)
  private _role: UserRoleEnum;

  @IsString()
  @IsOptional()
  private _employeeId: Nullable<string>;

  @IsString()
  @IsOptional()
  private _position: Nullable<string>;

  constructor(props: UserParams) {
    const { guid, createdAt, updatedAt, deletedAt } = props;
    super(guid, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._username = props.username;
    this._email = props.email;
    this._passwordHash = props.passwordHash;
    this._citizenId = props.citizenId;
    this._last4DigitCitizenId = props.last4DigitCitizenId;
    this._profile = new UserProfile(props.profile);
    this._active = props.active;
    this._verified = props.verified;
    this._isTest = props.isTest;
    this._permissionGroupIds = props.permissionGroupIds;
    this._permissions = (props.permissions ?? []).map((permission) => new UserPermission(permission));
    this._suspendedUntil = props.suspendedUntil;
    this._isTwoFactorEnable = props.isTwoFactorEnable;
    this._isTwoFactorRegister = props.isTwoFactorRegister;
    this._isTwoFactorLogin = props.isTwoFactorLogin;
    this._twoFactorSecret = props.twoFactorSecret ? new UserTwoFactorSecret(props.twoFactorSecret) : null;
    this._customerCodes = props.customerCodes;
    this._permissionRegulator = props.permissionRegulator
      ? new UserPermissionRegulator(props.permissionRegulator)
      : null;
    this._lostToken = props.lostToken;
    this._firstPasswordToken = props.firstPasswordToken;
    this._additionalField = props.additionalField;
    this._regulatorProfile = props.regulatorProfile ? new UserRegularProfile(props.regulatorProfile) : null;
    this._setPasswordDate = props.setPasswordDate;
    this._firstActiveDate = props.firstActiveDate;
    this._isEnableLocalLogin = props.isEnableLocalLogin;
    this._returnUrl = props.returnUrl;
    this._isPassedUlSaleQualify = props.isPassedUlSaleQualify;
    this._isTerminated = props.isTerminated;
    this._role = props.role;
    this._employeeId = props.employeeId;
    this._position = props.position;
  }

  static async new(props: CreateUserParams): Promise<User> {
    const userProfile: UserProfileParams = {
      salute: props.profile.salute,
      firstname: props.profile.firstname,
      middlename: props.profile.middlename ?? '',
      lastname: props.profile?.lastname,
      avatar: props.profile?.avatar ?? '',
      gender: props.profile.gender,
      mobilePhoneNumber: props.profile.mobilePhoneNumber ?? '',
      dateOfBirth: props.profile.dateOfBirth ?? null,
    };
    const entity = new User({
      ...props,
      profile: userProfile,
      active: false,
      isTest: props.isTest ?? false,
      permissions: props.permissions ?? [],
      lostToken: props.lostToken,
      permissionGroupIds: props.permissionGroupIds ?? [],
      permissionRegulator: props.permissionRegulator ?? null,
      additionalField: props.additionalField ?? null,
      setPasswordDate: props.setPasswordDate ?? null,
      firstActiveDate: props.firstActiveDate ?? null,
      suspendedUntil: props.suspendedUntil ?? null,
      regulatorProfile: props.regulatorProfile ?? null,
      returnUrl: props.returnUrl ?? null,
      isPassedUlSaleQualify: props.isPassedUlSaleQualify ?? false,
      isTerminated: props.isTerminated ?? false,
      role: props.role ?? UserRoleEnum.TRAINEE,
      employeeId: props.employeeId ?? null,
      position: props.position ?? null,
    });

    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get username(): string {
    return this._username;
  }

  set username(val: string) {
    this._username = val;
  }

  @Expose()
  get permissionGroupIds(): GenericID[] {
    return this._permissionGroupIds;
  }

  set permissionGroupIds(val: GenericID[]) {
    this._permissionGroupIds = val;
  }

  @Expose()
  get passwordHash(): string {
    return this._passwordHash;
  }

  set passwordHash(val: string) {
    this._passwordHash = val;
  }

  @Expose()
  get email(): string {
    return this._email;
  }

  set email(val: string) {
    this._email = val;
  }

  @Expose()
  get citizenId(): string {
    return this._citizenId;
  }

  set citizenId(val: string) {
    this._citizenId = val;
  }
  @Expose()
  get last4DigitCitizenId(): string {
    return this._last4DigitCitizenId;
  }

  set last4DigitCitizenId(val: string) {
    this._last4DigitCitizenId = val;
  }

  @Expose()
  get profile(): UserProfile {
    return this._profile;
  }

  set profile(val: UserProfile) {
    this._profile = val;
  }

  get active(): boolean {
    return this._active;
  }

  set active(val: boolean) {
    this._active = val;
  }

  @Expose()
  get verified(): boolean {
    return this._verified;
  }

  set verified(val: boolean) {
    this._verified = val;
  }

  @Expose()
  get isTest(): boolean {
    return this._isTest;
  }

  set isTest(val: boolean) {
    this._isTest = val;
  }

  @Expose()
  get permissions(): UserPermission[] {
    return this._permissions;
  }

  set permissions(val: UserPermission[]) {
    this._permissions = val;
  }

  @Expose()
  get suspendedUntil(): Nullable<Date> {
    return this._suspendedUntil;
  }

  set suspendedUntil(val: Nullable<Date>) {
    this._suspendedUntil = val;
  }

  @Expose()
  get isTwoFactorEnable(): boolean {
    return this._isTwoFactorEnable;
  }

  set isTwoFactorEnable(val: boolean) {
    this._isTwoFactorEnable = val;
  }

  @Expose()
  get isTwoFactorRegister(): boolean {
    return this._isTwoFactorRegister;
  }

  set isTwoFactorRegister(val: boolean) {
    this._isTwoFactorRegister = val;
  }

  @Expose()
  get isTwoFactorLogin(): boolean {
    return this._isTwoFactorLogin;
  }

  set isTwoFactorLogin(val: boolean) {
    this._isTwoFactorLogin = val;
  }
  @Expose()
  get twoFactorSecret(): Nullable<UserTwoFactorSecret> {
    return this._twoFactorSecret;
  }

  set twoFactorSecret(val: Nullable<UserTwoFactorSecret>) {
    this._twoFactorSecret = val;
  }

  @Expose()
  get customerCodes(): string[] {
    return this._customerCodes;
  }

  set customerCodes(val: string[]) {
    this._customerCodes = val;
  }

  @Expose()
  get permissionRegulator(): Nullable<UserPermissionRegulatorParams> {
    return this._permissionRegulator;
  }

  set permissionRegulator(val: Nullable<UserPermissionRegulatorParams>) {
    this._permissionRegulator = val;
  }

  @Expose()
  get lostToken(): Optional<string> {
    return this._lostToken;
  }

  set lostToken(val: Optional<string>) {
    this._lostToken = val;
  }
  @Expose()
  get firstPasswordToken(): string {
    return this._firstPasswordToken;
  }

  set firstPasswordToken(val: string) {
    this._firstPasswordToken = val;
  }
  @Expose()
  get additionalField(): Nullable<ObjectValue> {
    return this._additionalField;
  }

  set additionalField(val: Nullable<ObjectValue>) {
    this._additionalField = val;
  }
  @Expose()
  get regulatorProfile(): Nullable<UserRegularProfile> {
    return this._regulatorProfile;
  }

  set regulatorProfile(val: Nullable<UserRegularProfile>) {
    this._regulatorProfile = val;
  }
  @Expose()
  get setPasswordDate(): Nullable<Date> {
    return this._setPasswordDate;
  }

  set setPasswordDate(val: Nullable<Date>) {
    this._setPasswordDate = val;
  }
  @Expose()
  get firstActiveDate(): Nullable<Date> {
    return this._firstActiveDate;
  }

  set firstActiveDate(val: Nullable<Date>) {
    this._firstActiveDate = val;
  }

  @Expose()
  get isEnableLocalLogin(): boolean {
    return this._isEnableLocalLogin;
  }

  set isEnableLocalLogin(val: boolean) {
    this._isEnableLocalLogin = val;
  }

  @Expose()
  get returnUrl(): Nullable<string> {
    return this._returnUrl;
  }

  set returnUrl(val: Nullable<string>) {
    this._returnUrl = val;
  }

  @Expose()
  get isPassedUlSaleQualify(): boolean {
    return this._isPassedUlSaleQualify;
  }

  set isPassedUlSaleQualify(val: boolean) {
    this._isPassedUlSaleQualify = val;
  }

  @Expose()
  get isTerminated(): boolean {
    return this._isTerminated;
  }

  set isTerminated(val: boolean) {
    this._isTerminated = val;
  }

  get fullName(): string {
    const { firstname, middlename, lastname } = this.profile;
    return [firstname, middlename, lastname].filter((v) => v).join(' ');
  }

  @Expose()
  get role(): UserRoleEnum {
    return this._role;
  }

  set role(val: UserRoleEnum) {
    this._role = val;
  }

  @Expose()
  get employeeId(): string {
    return this._employeeId;
  }

  set employeeId(val: string) {
    this._employeeId = val;
  }

  @Expose()
  get position(): string {
    return this._position;
  }

  set position(val: string) {
    this._position = val;
  }
}
