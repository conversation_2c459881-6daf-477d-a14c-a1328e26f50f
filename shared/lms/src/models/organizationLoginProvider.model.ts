import { GenericID } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsBoolean, IsString } from 'class-validator';
import { v4 } from 'uuid';

import {
  CreateOrganizationLoginProviderParams,
  OrganizationLoginProviderParams,
} from '@shared/lms/constants/types/organizationLoginProvider.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class OrganizationLoginProvider extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _loginProviderId: GenericID;

  @IsBoolean()
  private _isEnabled: boolean;

  constructor(payload: OrganizationLoginProviderParams) {
    const { id, organizationId, loginProviderId, isEnabled, createdAt, updatedAt } = payload;
    super(id, createdAt, updatedAt);

    this._organizationId = organizationId;
    this._loginProviderId = loginProviderId;
    this._isEnabled = isEnabled;
  }

  static async new(props: CreateOrganizationLoginProviderParams): Promise<OrganizationLoginProvider> {
    const currentDate = date().toDate();
    const entity = new OrganizationLoginProvider({
      id: v4(),
      createdAt: currentDate,
      updatedAt: currentDate,
      ...props,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get loginProviderId(): GenericID {
    return this._loginProviderId;
  }

  set loginProviderId(val: GenericID) {
    this._loginProviderId = val;
  }

  @Expose()
  get isEnabled(): boolean {
    return this._isEnabled;
  }

  set isEnabled(val: boolean) {
    this._isEnabled = val;
  }
}
