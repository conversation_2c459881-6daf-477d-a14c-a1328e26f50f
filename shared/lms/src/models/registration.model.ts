import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsEnum, IsString, IsOptional } from 'class-validator';

import { RegistrationStatusEnum } from '@shared/lms/constants/enums/registration.enum';
import { CreateRegistrationParams, RegistrationParams } from '@shared/lms/constants/types/registration.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class Registration extends BaseModel {
  @IsString()
  private _enrollmentId: GenericID;

  @IsString()
  @IsOptional()
  private _customerPartnerId: Nullable<GenericID>;

  @IsEnum(RegistrationStatusEnum)
  private _status: RegistrationStatusEnum;

  constructor(props: RegistrationParams) {
    const { id, createdAt, updatedAt } = props;
    super(id, createdAt, updatedAt);

    this._enrollmentId = props.enrollmentId;
    this._status = props.status;
    this._customerPartnerId = props.customerPartnerId || null;
  }

  static async new(props: CreateRegistrationParams) {
    const entity = new Registration(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get enrollmentId(): GenericID {
    return this._enrollmentId;
  }

  set enrollmentId(val: string) {
    this._enrollmentId = val;
  }

  @Expose()
  get customerPartnerId(): Nullable<GenericID> {
    return this._customerPartnerId || null;
  }

  set customerPartnerId(val: Nullable<GenericID>) {
    this._customerPartnerId = val || null;
  }

  @Expose()
  get status(): RegistrationStatusEnum {
    return this._status;
  }

  set status(val: RegistrationStatusEnum) {
    this._status = val;
  }
}
