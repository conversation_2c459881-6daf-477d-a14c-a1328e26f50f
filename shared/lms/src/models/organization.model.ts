import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEmail,
  IsEnum,
  IsInstance,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

import { CourseObjectiveTypeEnum } from '@shared/lms/constants/enums/course.enum';
import { HomePagePathEnum, LivenessModeEnum, organizationDomain } from '@shared/lms/constants/enums/organization.enum';
import {
  CreateOrganizationParams,
  OrganizationApplicantTypeParams,
  OrganizationAutoApprovalEnrollmentConfigParams,
  OrganizationAutoApprovalEnrollmentCriteriaParams,
  OrganizationAutoApprovalRangePercentageParams,
  OrganizationAvailableLicenseConfigParams,
  OrganizationCertificateConfigParams,
  OrganizationContactParams,
  OrganizationCourseObjectiveConfigParams,
  OrganizationCustomerTemplateParams,
  OrganizationFeatureConfigParams,
  OrganizationLearningConfigParams,
  OrganizationLicenseRenewalParams,
  OrganizationLicensesConfigurationParams,
  OrganizationLicenseTypeParams,
  OrganizationNotificationConfigParams,
  OrganizationParams,
  OrganizationPasswordPolicyConfigParams,
  OrganizationThemeAppearanceParams,
  OrganizationThemeConfigParams,
  OrganizationThemeContactParams,
  OrganizationThemeEmailParams,
  OrganizationThemeFooterParams,
  OrganizationThemeLoginParams,
  OrganizationTokenConfigParams,
  OrganizationTransactionCounterParams,
  OrganizationUserValidationTemplateParams,
  OrganizationValidationPasswordConfigParams,
  OrganizationVideoAuthenInstructionParams,
  OrganizationThemeColorParams,
  OrganizationRegulatorParams,
  OrganizationTrainingCenterParams,
} from '@shared/lms/constants/types/organization.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class OrganizationVideoAuthenInstruction {
  @IsString()
  private _path: string;

  @IsString()
  private _duration: string;

  constructor(props: OrganizationVideoAuthenInstructionParams) {
    this._path = props.path;
    this._duration = props.duration;
  }

  @Expose()
  get path(): string {
    return this._path;
  }

  set path(val: string) {
    this._path = val;
  }

  @Expose()
  get duration(): string {
    return this._duration;
  }

  set duration(val: string) {
    this._duration = val;
  }
}

export class OrganizationThemeAppearance {
  @IsObject()
  private _theme: OrganizationThemeColorParams;

  @IsString()
  private _logo: string;

  @IsString()
  private _favicon: string;

  @IsString()
  @IsOptional()
  private _updatedByUserId: GenericID;

  @IsDate()
  @IsOptional()
  private _updatedAt: Nullable<Date>;

  constructor(props: OrganizationThemeAppearanceParams) {
    this._theme = props.theme;
    this._logo = props.logo;
    this._favicon = props.favicon;
    this._updatedByUserId = props.updatedByUserId;
    this._updatedAt = props.updatedAt;
  }

  @Expose()
  get theme(): OrganizationThemeColorParams {
    return this._theme;
  }

  set theme(val: OrganizationThemeColorParams) {
    this._theme = val;
  }

  @Expose()
  get logo(): string {
    return this._logo;
  }

  set logo(val: string) {
    this._logo = val;
  }

  @Expose()
  get favicon(): string {
    return this._favicon;
  }

  set favicon(val: string) {
    this._favicon = val;
  }

  @Expose()
  get updatedByUserId(): GenericID {
    return this._updatedByUserId;
  }

  set updatedByUserId(val: GenericID) {
    this._updatedByUserId = val;
  }

  @Expose()
  get updatedAt(): Nullable<Date> {
    return this._updatedAt;
  }

  set updatedAt(val: Nullable<Date>) {
    this._updatedAt = val;
  }
}

export class OrganizationThemeLogin {
  @IsString()
  private _loginTitle: string;

  @IsString()
  private _loginContent: string;

  @IsString()
  private _loginBackground: string;

  @IsString()
  @IsOptional()
  private _updatedByUserId: GenericID;

  @IsDate()
  @IsOptional()
  private _updatedAt: Nullable<Date>;

  constructor(props: OrganizationThemeLoginParams) {
    this._loginTitle = props.loginTitle;
    this._loginContent = props.loginContent;
    this._loginBackground = props.loginBackground;
    this._updatedByUserId = props.updatedByUserId;
    this._updatedAt = props.updatedAt;
  }

  @Expose()
  get loginTitle(): string {
    return this._loginTitle;
  }

  set loginTitle(val: string) {
    this._loginTitle = val;
  }

  @Expose()
  get loginContent(): string {
    return this._loginContent;
  }

  set loginContent(val: string) {
    this._loginContent = val;
  }

  @Expose()
  get loginBackground(): string {
    return this._loginBackground;
  }

  set loginBackground(val: string) {
    this._loginBackground = val;
  }

  @Expose()
  get updatedByUserId(): GenericID {
    return this._updatedByUserId;
  }

  set updatedByUserId(val: GenericID) {
    this._updatedByUserId = val;
  }

  @Expose()
  get updatedAt(): Nullable<Date> {
    return this._updatedAt;
  }

  set updatedAt(val: Nullable<Date>) {
    this._updatedAt = val;
  }
}

export class OrganizationThemeEmail {
  @IsString()
  private _emailBanner: string;

  @IsString()
  private _emailSignature: string;

  @IsString()
  @IsOptional()
  private _updatedByUserId: GenericID;

  @IsDate()
  @IsOptional()
  private _updatedAt: Nullable<Date>;

  constructor(props: OrganizationThemeEmailParams) {
    this._emailBanner = props.emailBanner;
    this._emailSignature = props.emailSignature;
    this._updatedByUserId = props.updatedByUserId;
    this._updatedAt = props.updatedAt;
  }

  @Expose()
  get emailBanner(): string {
    return this._emailBanner;
  }

  set emailBanner(val: string) {
    this._emailBanner = val;
  }

  @Expose()
  get emailSignature(): string {
    return this._emailSignature;
  }

  set emailSignature(val: string) {
    this._emailSignature = val;
  }

  @Expose()
  get updatedByUserId(): GenericID {
    return this._updatedByUserId;
  }

  set updatedByUserId(val: GenericID) {
    this._updatedByUserId = val;
  }

  @Expose()
  get updatedAt(): Nullable<Date> {
    return this._updatedAt;
  }

  set updatedAt(val: Nullable<Date>) {
    this._updatedAt = val;
  }
}

export class OrganizationThemeFooter {
  @IsString()
  @IsOptional()
  private _info1: Nullable<string>;

  @IsString()
  @IsOptional()
  private _info2: Nullable<string>;

  @IsString()
  @IsOptional()
  private _updatedByUserId: GenericID;

  @IsDate()
  @IsOptional()
  private _updatedAt: Nullable<Date>;

  constructor(props: OrganizationThemeFooterParams) {
    this._info1 = props.info1;
    this._info2 = props.info2;
    this._updatedByUserId = props.updatedByUserId;
    this._updatedAt = props.updatedAt;
  }

  @Expose()
  get info1(): Nullable<string> {
    return this._info1;
  }

  set info1(info1: Nullable<string>) {
    this._info1 = info1;
  }

  @Expose()
  get info2(): Nullable<string> {
    return this._info2;
  }

  set info2(info2: Nullable<string>) {
    this._info2 = info2;
  }

  @Expose()
  get updatedByUserId(): GenericID {
    return this._updatedByUserId;
  }

  set updatedByUserId(val: GenericID) {
    this._updatedByUserId = val;
  }

  @Expose()
  get updatedAt(): Nullable<Date> {
    return this._updatedAt;
  }

  set updatedAt(val: Nullable<Date>) {
    this._updatedAt = val;
  }
}

export class OrganizationThemeContact {
  @IsString()
  @IsOptional()
  private _info: Nullable<string>;

  @IsString()
  @IsOptional()
  private _updatedByUserId: GenericID;

  @IsDate()
  @IsOptional()
  private _updatedAt: Nullable<Date>;

  constructor(props: OrganizationThemeContactParams) {
    this._info = props.info;
    this._updatedByUserId = props.updatedByUserId;
    this._updatedAt = props.updatedAt;
  }

  @Expose()
  get info(): Nullable<string> {
    return this._info;
  }

  set info(info: Nullable<string>) {
    this._info = info;
  }

  @Expose()
  get updatedByUserId(): GenericID {
    return this._updatedByUserId;
  }

  set updatedByUserId(val: GenericID) {
    this._updatedByUserId = val;
  }

  @Expose()
  get updatedAt(): Nullable<Date> {
    return this._updatedAt;
  }

  set updatedAt(updatedAt: Nullable<Date>) {
    this._updatedAt = updatedAt;
  }
}

export class OrganizationThemeConfig {
  @IsEnum(HomePagePathEnum)
  private _homePagePath: HomePagePathEnum;

  @ValidateNested()
  @IsInstance(OrganizationThemeAppearance)
  private _appearance: OrganizationThemeAppearance;

  @ValidateNested()
  @IsInstance(OrganizationThemeLogin)
  private _login: OrganizationThemeLogin;

  @ValidateNested()
  @IsInstance(OrganizationThemeEmail)
  private _email: OrganizationThemeEmail;

  @ValidateNested()
  @IsInstance(OrganizationThemeFooter)
  private _footer: OrganizationThemeFooter;

  @ValidateNested()
  @IsInstance(OrganizationThemeContact)
  private _contact: OrganizationThemeContact;

  constructor(props: OrganizationThemeConfigParams) {
    this._homePagePath = props.homePagePath;
    this._appearance = new OrganizationThemeAppearance(props.appearance);
    this._login = new OrganizationThemeLogin(props.login);
    this._email = new OrganizationThemeEmail(props.email);
    this._footer = new OrganizationThemeFooter(props.footer);
    this._contact = new OrganizationThemeContact(props.contact);
  }

  @Expose()
  get homePagePath(): HomePagePathEnum {
    return this._homePagePath;
  }

  set homePagePath(homePagePath: HomePagePathEnum) {
    this._homePagePath = homePagePath;
  }

  @Expose()
  get appearance(): OrganizationThemeAppearance {
    return this._appearance;
  }

  set appearance(val: OrganizationThemeAppearance) {
    this._appearance = val;
  }

  @Expose()
  get login(): OrganizationThemeLogin {
    return this._login;
  }

  set login(val: OrganizationThemeLogin) {
    this._login = val;
  }

  @Expose()
  get email(): OrganizationThemeEmail {
    return this._email;
  }

  set email(val: OrganizationThemeEmail) {
    this._email = val;
  }

  @Expose()
  get footer(): OrganizationThemeFooter {
    return this._footer;
  }

  set footer(val: OrganizationThemeFooter) {
    this._footer = val;
  }

  @Expose()
  get contact(): OrganizationThemeContact {
    return this._contact;
  }

  set contact(val: OrganizationThemeContact) {
    this._contact = val;
  }
}

export class OrganizationNotificationConfig {
  @IsString()
  private _key: string;

  @IsEmail()
  private _senderEmail: string;

  @IsString()
  private _senderName: string;

  @IsBoolean()
  private _isActive: boolean;

  constructor(props: OrganizationNotificationConfigParams) {
    this.key = props.key;
    this.senderEmail = props.senderEmail;
    this.senderName = props.senderName;
    this.isActive = props.isActive;
  }

  @Expose()
  get key(): string {
    return this._key;
  }

  set key(key: string) {
    this._key = key;
  }

  @Expose()
  get senderName(): string {
    return this._senderName;
  }

  set senderName(val: string) {
    this._senderName = val;
  }

  @Expose()
  get senderEmail(): string {
    return this._senderEmail;
  }

  set senderEmail(val: string) {
    this._senderEmail = val;
  }

  @Expose()
  get isActive(): boolean {
    return this._isActive;
  }

  set isActive(val: boolean) {
    this._isActive = val;
  }
}

export class OrganizationValidationPasswordConfig {
  @IsNumber()
  private _min: number;

  @IsNumber()
  private _max: number;

  @IsBoolean()
  private _isRequiredUppercase: boolean;

  @IsBoolean()
  private _isRequiredLowercase: boolean;

  @IsBoolean()
  private _isRequiredNumber: boolean;

  @IsBoolean()
  private _isRequiredSymbol: boolean;

  @IsBoolean()
  private _isNotAllowWhitespace: boolean;

  @IsString({ each: true })
  private _customRequiredSymbols: string[];

  constructor(props: OrganizationValidationPasswordConfigParams) {
    this._min = props.min;
    this._max = props.max;
    this._isRequiredLowercase = props.isRequiredLowercase;
    this._isRequiredUppercase = props.isRequiredUppercase;
    this._isRequiredNumber = props.isRequiredNumber;
    this._isRequiredSymbol = props.isRequiredSymbol;
    this._isNotAllowWhitespace = props.isNotAllowWhitespace;
    this._customRequiredSymbols = props.customRequiredSymbols;
  }

  @Expose()
  get min(): number {
    return this._min;
  }

  set min(val: number) {
    this._min = val;
  }

  @Expose()
  get max(): number {
    return this._max;
  }

  set max(val: number) {
    this._max = val;
  }

  @Expose()
  get isRequiredUppercase(): boolean {
    return this._isRequiredUppercase;
  }

  set isRequiredUppercase(val: boolean) {
    this._isRequiredUppercase = val;
  }

  @Expose()
  get isRequiredLowercase(): boolean {
    return this._isRequiredLowercase;
  }

  set isRequiredLowercase(val: boolean) {
    this._isRequiredLowercase = val;
  }

  @Expose()
  get isRequiredNumber(): boolean {
    return this._isRequiredNumber;
  }

  set isRequiredNumber(val: boolean) {
    this._isRequiredNumber = val;
  }

  @Expose()
  get isRequiredSymbol(): boolean {
    return this._isRequiredSymbol;
  }

  set isRequiredSymbol(val: boolean) {
    this._isRequiredSymbol = val;
  }

  @Expose()
  get isNotAllowWhitespace(): boolean {
    return this._isNotAllowWhitespace;
  }

  set isNotAllowWhitespace(val: boolean) {
    this._isNotAllowWhitespace = val;
  }

  @Expose()
  get customRequiredSymbols(): string[] {
    return this._customRequiredSymbols;
  }

  set customRequiredSymbols(val: string[]) {
    this._customRequiredSymbols = val;
  }
}

export class OrganizationCertificateConfig {
  @IsString()
  private _logoImageUrl: string;

  @IsString()
  private _textDynamicCertificate: string;

  @IsArray()
  private _certificateTypes: Record<string, any>[];

  constructor(props: OrganizationCertificateConfigParams) {
    this._logoImageUrl = props.logoImageUrl;
    this._textDynamicCertificate = props.textDynamicCertificate;
    this._certificateTypes = props.certificateTypes;
  }

  @Expose()
  get logoImageUrl(): string {
    return this._logoImageUrl;
  }

  set logoImageUrl(val: string) {
    this._logoImageUrl = val;
  }

  @Expose()
  get textDynamicCertificate(): string {
    return this._textDynamicCertificate;
  }

  set textDynamicCertificate(val: string) {
    this._textDynamicCertificate = val;
  }

  @Expose()
  get certificateTypes(): Record<string, any>[] {
    return this._certificateTypes;
  }

  set certificateTypes(val: Record<string, any>[]) {
    this._certificateTypes = val;
  }
}

export class OrganizationContact {
  @IsString()
  @IsOptional()
  private _userManual?: string;

  @IsString()
  @IsOptional()
  private _faq?: string;

  constructor(props: OrganizationContactParams) {
    this._userManual = props.userManual;
    this._faq = props.faq;
  }

  @Expose()
  get userManual(): string {
    return this._userManual;
  }

  set userManual(val: string) {
    this._userManual = val;
  }

  @Expose()
  get faq(): string {
    return this._faq;
  }

  set faq(val: string) {
    this._faq = val;
  }
}

export class OrganizationCustomerTemplate {
  @IsString()
  private _customerCode: string;

  @IsString()
  private _path: string;

  constructor(props: OrganizationCustomerTemplateParams) {
    this._customerCode = props.customerCode;
    this._path = props.path;
  }

  @Expose()
  get customerCode(): string {
    return this._customerCode;
  }

  set customerCode(val: string) {
    this._customerCode = val;
  }

  @Expose()
  get path(): string {
    return this._path;
  }

  set path(val: string) {
    this._path = val;
  }
}

export class OrganizationUserValidationTemplate {
  @IsString()
  private _defaultPath: string;

  @ValidateNested({ each: true })
  @IsInstance(OrganizationCustomerTemplate, { each: true })
  private _customTemplate: OrganizationCustomerTemplate[];

  constructor(props: OrganizationUserValidationTemplateParams) {
    this._defaultPath = props.defaultPath;
    this._customTemplate = props.customTemplate?.map((val) => new OrganizationCustomerTemplate(val));
  }

  @Expose()
  get defaultPath(): string {
    return this._defaultPath;
  }

  set defaultPath(val: string) {
    this._defaultPath = val;
  }

  @Expose()
  get customTemplate(): OrganizationCustomerTemplate[] {
    return this._customTemplate;
  }

  set customTemplate(val: OrganizationCustomerTemplate[]) {
    this._customTemplate = val;
  }
}

export class OrganizationTokenConfig {
  @IsNumber()
  private _accessTokenExpiration: number;

  @IsNumber()
  private _refreshTokenExpiration: number;

  constructor(props: OrganizationTokenConfigParams) {
    this._accessTokenExpiration = props.accessTokenExpiration;
    this._refreshTokenExpiration = props.refreshTokenExpiration;
  }

  @Expose()
  get accessTokenExpiration(): number {
    return this._accessTokenExpiration;
  }

  set accessTokenExpiration(val: number) {
    this._accessTokenExpiration = val;
  }

  @Expose()
  get refreshTokenExpiration(): number {
    return this._refreshTokenExpiration;
  }

  set refreshTokenExpiration(val: number) {
    this._refreshTokenExpiration = val;
  }
}

export class OrganizationRegulator {
  @IsString()
  private _name: string;

  @IsArray()
  private _trainingCenterKeys: string[];

  @IsObject()
  @IsOptional()
  private _config: Nullable<Record<string, unknown>>;

  @IsBoolean()
  private _isEnabled: boolean;

  constructor(props: OrganizationRegulatorParams) {
    this._name = props.name;
    this._trainingCenterKeys = props.trainingCenterKeys;
    this._config = props.config ?? { subscriptionKey: null };
    this._isEnabled = props.isEnabled ?? false;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get trainingCenterKeys(): string[] {
    return this._trainingCenterKeys;
  }

  set trainingCenterKeys(val: string[]) {
    this._trainingCenterKeys = val;
  }

  @Expose()
  get config(): Nullable<Record<string, unknown>> {
    return this._config;
  }

  set config(val: Nullable<Record<string, unknown>>) {
    this._config = val;
  }

  @Expose()
  get isEnabled(): boolean {
    return this._isEnabled;
  }

  set isEnabled(val: boolean) {
    this._isEnabled = val;
  }
}

export class OrganizationTrainingCenter {
  @IsString()
  private _key: string;

  @IsString()
  private _name: string;

  @IsString()
  private _regulator: string;

  @IsArray()
  private _applicantTypes: OrganizationApplicantTypeParams[];

  @IsArray()
  private _licenseRenewals: OrganizationLicenseRenewalParams[];

  @IsArray()
  private _licenseTypes: OrganizationLicenseTypeParams[];

  @IsObject()
  private _learningConfig: OrganizationLearningConfigParams;

  constructor(props: OrganizationTrainingCenterParams) {
    this._key = props.key;
    this._name = props.name;
    this._regulator = props.regulator;
    this._applicantTypes = props.applicantTypes;
    this._licenseRenewals = props.licenseRenewals;
    this._licenseTypes = props.licenseTypes;
    this._learningConfig = props.learningConfig;
  }

  @Expose()
  get key(): string {
    return this._key;
  }

  set key(val: string) {
    this._key = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get regulator(): string {
    return this._regulator;
  }

  set regulator(val: string) {
    this._regulator = val;
  }

  @Expose()
  get applicantTypes(): OrganizationApplicantTypeParams[] {
    return this._applicantTypes;
  }

  set applicantTypes(applicantTypes: OrganizationApplicantTypeParams[]) {
    this._applicantTypes = applicantTypes;
  }

  @Expose()
  get licenseRenewals(): OrganizationLicenseRenewalParams[] {
    return this._licenseRenewals;
  }

  set licenseRenewals(val: OrganizationLicenseRenewalParams[]) {
    this._licenseRenewals = val;
  }

  @Expose()
  get licenseTypes(): OrganizationLicenseTypeParams[] {
    return this._licenseTypes;
  }

  set licenseTypes(licenseTypes: OrganizationLicenseTypeParams[]) {
    this._licenseTypes = licenseTypes;
  }

  @Expose()
  get learningConfig(): OrganizationLearningConfigParams {
    return this._learningConfig;
  }

  set learningConfig(learningConfig: OrganizationLearningConfigParams) {
    this._learningConfig = learningConfig;
  }
}

export class OrganizationCourseObjectiveConfig {
  @IsEnum(CourseObjectiveTypeEnum)
  private _objectiveType: CourseObjectiveTypeEnum;

  @IsArray()
  private _regulators: OrganizationRegulator[];

  @IsArray()
  private _trainingCenters: OrganizationTrainingCenter[];

  @IsObject()
  private _defaultLearningConfig: OrganizationLearningConfigParams;

  constructor(props: OrganizationCourseObjectiveConfigParams) {
    this._objectiveType = props.objectiveType;
    this._regulators = props.regulators.map((val) => new OrganizationRegulator(val));
    this._trainingCenters = props.trainingCenters.map((val) => new OrganizationTrainingCenter(val));
    this._defaultLearningConfig = props.defaultLearningConfig;
  }

  @Expose()
  get objectiveType(): CourseObjectiveTypeEnum {
    return this._objectiveType;
  }

  set objectiveType(objectiveType: CourseObjectiveTypeEnum) {
    this._objectiveType = objectiveType;
  }

  @Expose()
  get regulators(): OrganizationRegulator[] {
    return this._regulators;
  }

  set regulators(regulators: OrganizationRegulator[]) {
    this._regulators = regulators;
  }

  @Expose()
  get trainingCenters(): OrganizationTrainingCenter[] {
    return this._trainingCenters;
  }

  set trainingCenters(trainingCenters: OrganizationTrainingCenter[]) {
    this._trainingCenters = trainingCenters;
  }

  @Expose()
  get defaultLearningConfig(): OrganizationLearningConfigParams {
    return this._defaultLearningConfig;
  }

  set defaultLearningConfig(defaultLearningConfig: OrganizationLearningConfigParams) {
    this._defaultLearningConfig = defaultLearningConfig;
  }
}

export class OrganizationAutoApprovalRangePercentage {
  @IsNumber()
  private _minimum: number;

  @IsNumber()
  private _maximum: number;

  constructor(props: OrganizationAutoApprovalRangePercentageParams) {
    this._minimum = props.minimum;
    this._maximum = props.maximum;
  }

  @Expose()
  get minimum(): number {
    return this._minimum;
  }

  set minimum(val: number) {
    this._minimum = val;
  }

  @Expose()
  get maximum(): number {
    return this._maximum;
  }

  set maximum(val: number) {
    this._maximum = val;
  }
}

export class OrganizationAutoApprovalEnrollmentCriteria {
  @IsBoolean()
  private _isActive: boolean;

  @IsBoolean()
  private _isAllDocsIsApprove: boolean;

  @IsBoolean()
  private _isUploadIdCard: boolean;

  @IsNumber()
  private _totalLivenessPercent: number;

  @ValidateNested()
  @IsInstance(OrganizationAutoApprovalRangePercentage)
  private _citizenIdMatchPercent: OrganizationAutoApprovalRangePercentage;

  @ValidateNested()
  @IsInstance(OrganizationAutoApprovalRangePercentage)
  private _fullNameMatchPercent: OrganizationAutoApprovalRangePercentage;

  constructor(props: OrganizationAutoApprovalEnrollmentCriteriaParams) {
    this.isActive = props.isActive;
    this.isAllDocsIsApprove = props.isAllDocsIsApprove;
    this.isUploadIdCard = props.isUploadIdCard;
    this.totalLivenessPercent = props.totalLivenessPercent;
    this.citizenIdMatchPercent = new OrganizationAutoApprovalRangePercentage(props.citizenIdMatchPercent);
    this.fullNameMatchPercent = new OrganizationAutoApprovalRangePercentage(props.fullNameMatchPercent);
  }

  @Expose()
  get isActive(): boolean {
    return this._isActive;
  }

  set isActive(isActive: boolean) {
    this._isActive = isActive;
  }

  @Expose()
  get isAllDocsIsApprove(): boolean {
    return this._isAllDocsIsApprove;
  }

  set isAllDocsIsApprove(isAllDocsIsApprove: boolean) {
    this._isAllDocsIsApprove = isAllDocsIsApprove;
  }

  @Expose()
  get isUploadIdCard(): boolean {
    return this._isUploadIdCard;
  }

  set isUploadIdCard(val: boolean) {
    this._isUploadIdCard = val;
  }

  @Expose()
  get totalLivenessPercent(): number {
    return this._totalLivenessPercent;
  }

  set totalLivenessPercent(val: number) {
    this._totalLivenessPercent = val;
  }

  @Expose()
  get citizenIdMatchPercent(): OrganizationAutoApprovalRangePercentage {
    return this._citizenIdMatchPercent;
  }

  set citizenIdMatchPercent(val: OrganizationAutoApprovalRangePercentage) {
    this._citizenIdMatchPercent = val;
  }

  @Expose()
  get fullNameMatchPercent(): OrganizationAutoApprovalRangePercentage {
    return this._fullNameMatchPercent;
  }

  set fullNameMatchPercent(val: OrganizationAutoApprovalRangePercentage) {
    this._fullNameMatchPercent = val;
  }
}

export class OrganizationAutoApprovalEnrollmentConfig {
  @IsInstance(OrganizationAutoApprovalEnrollmentCriteria)
  private _autoApproveCriteria: OrganizationAutoApprovalEnrollmentCriteria;

  @IsInstance(OrganizationAutoApprovalEnrollmentCriteria)
  private _autoVerifyCriteria: OrganizationAutoApprovalEnrollmentCriteria;

  constructor(props: OrganizationAutoApprovalEnrollmentConfigParams) {
    this._autoApproveCriteria = new OrganizationAutoApprovalEnrollmentCriteria(props.autoApproveCriteria);
    this._autoVerifyCriteria = new OrganizationAutoApprovalEnrollmentCriteria(props.autoVerifyCriteria);
  }

  @Expose()
  get autoApproveCriteria(): OrganizationAutoApprovalEnrollmentCriteria {
    return this._autoApproveCriteria;
  }

  set autoApproveCriteria(val: OrganizationAutoApprovalEnrollmentCriteria) {
    this._autoApproveCriteria = val;
  }

  @Expose()
  get autoVerifyCriteria(): OrganizationAutoApprovalEnrollmentCriteria {
    return this._autoVerifyCriteria;
  }

  set autoVerifyCriteria(val: OrganizationAutoApprovalEnrollmentCriteria) {
    this._autoVerifyCriteria = val;
  }
}

export class OrganizationLicensesConfiguration {
  @IsArray()
  private _availableLicensesTypes: OrganizationAvailableLicenseConfigParams[];

  constructor(prop?: OrganizationLicensesConfigurationParams) {
    this.availableLicensesTypes = prop?.availableLicensesTypes;
  }

  @Expose()
  get availableLicensesTypes(): OrganizationAvailableLicenseConfigParams[] {
    return this._availableLicensesTypes;
  }

  set availableLicensesTypes(availableCourseObjectiveTypes: OrganizationAvailableLicenseConfigParams[]) {
    this._availableLicensesTypes = availableCourseObjectiveTypes;
  }
}

export class OrganizationPasswordPolicyConfig {
  @IsBoolean()
  private _isEnabled: boolean;

  @IsOptional()
  @IsNumber()
  private _passwordExpiryDay: Nullable<number>;

  constructor(props: OrganizationPasswordPolicyConfigParams) {
    this._isEnabled = props.isEnabled;
    this._passwordExpiryDay = props.passwordExpiryDay;
  }

  @Expose()
  get isEnabled(): boolean {
    return this._isEnabled;
  }

  set isEnabled(val: boolean) {
    this._isEnabled = val;
  }

  @Expose()
  get passwordExpiryDay(): Nullable<number> {
    return this._passwordExpiryDay;
  }

  set passwordExpiryDay(val: Nullable<number>) {
    this._passwordExpiryDay = val;
  }
}

export class OrganizationTransactionCounter {
  @IsNumber()
  private _materialMediaVideoCode: number;

  @IsNumber()
  private _materialMediaArticleCode: number;

  @IsNumber()
  private _materialMediaQuizCode: number;

  @IsNumber()
  private _materialMediaSurveyCode: number;

  @IsNumber()
  private _materialMediaClassroomCode: number;

  @IsNumber()
  private _materialMediaAudioCode: number;

  @IsNumber()
  private _materialMediaAttachmentCode: number;

  @IsNumber()
  private _classroomLocationCode: number;

  constructor(props: OrganizationTransactionCounterParams) {
    this._materialMediaVideoCode = props.materialMediaVideoCode;
    this._materialMediaArticleCode = props.materialMediaArticleCode;
    this._materialMediaQuizCode = props.materialMediaQuizCode;
    this._materialMediaSurveyCode = props.materialMediaSurveyCode;
    this._materialMediaClassroomCode = props.materialMediaClassroomCode;
    this._materialMediaAudioCode = props.materialMediaAudioCode;
    this._materialMediaAttachmentCode = props.materialMediaAttachmentCode;
    this._classroomLocationCode = props.classroomLocationCode;
  }

  @Expose()
  get materialMediaVideoCode(): number {
    return this._materialMediaVideoCode;
  }

  set materialMediaVideoCode(val: number) {
    this._materialMediaVideoCode = val;
  }

  @Expose()
  get materialMediaArticleCode(): number {
    return this._materialMediaArticleCode;
  }

  set materialMediaArticleCode(val: number) {
    this._materialMediaArticleCode = val;
  }

  @Expose()
  get materialMediaQuizCode(): number {
    return this._materialMediaQuizCode;
  }

  set materialMediaQuizCode(val: number) {
    this._materialMediaQuizCode = val;
  }

  @Expose()
  get materialMediaSurveyCode(): number {
    return this._materialMediaSurveyCode;
  }

  set materialMediaSurveyCode(val: number) {
    this._materialMediaSurveyCode = val;
  }

  @Expose()
  get materialMediaClassroomCode(): number {
    return this._materialMediaClassroomCode;
  }

  set materialMediaClassroomCode(val: number) {
    this._materialMediaClassroomCode = val;
  }

  @Expose()
  get materialMediaAudioCode(): number {
    return this._materialMediaAudioCode;
  }

  set materialMediaAudioCode(val: number) {
    this._materialMediaAudioCode = val;
  }

  @Expose()
  get materialMediaAttachmentCode(): number {
    return this._materialMediaAttachmentCode;
  }

  set materialMediaAttachmentCode(val: number) {
    this._materialMediaAttachmentCode = val;
  }

  @Expose()
  get classroomLocationCode(): number {
    return this._classroomLocationCode;
  }

  set classroomLocationCode(val: number) {
    this._classroomLocationCode = val;
  }
}

export class Organization extends BaseModel {
  @IsString()
  private _domain: string;

  @IsString()
  private _fqdn: string;

  @IsString()
  private _name: string;

  @IsString()
  private _nameEng: string;

  @IsArray()
  @IsEnum(CourseObjectiveTypeEnum, { each: true })
  private _availableCourseObjectiveTypes: CourseObjectiveTypeEnum[];

  @IsInstance(OrganizationCourseObjectiveConfig, { each: true })
  @IsArray()
  private _courseObjectiveConfigs: OrganizationCourseObjectiveConfig[];

  @ValidateNested()
  @IsInstance(OrganizationThemeConfig)
  private _themeConfig: OrganizationThemeConfig;

  @ValidateNested()
  @IsInstance(OrganizationVideoAuthenInstruction)
  private _videoAuthenInstruction: OrganizationVideoAuthenInstruction;

  @ValidateNested()
  @IsInstance(OrganizationContact)
  private _organizationContact: OrganizationContact;

  @ValidateNested()
  @IsInstance(OrganizationUserValidationTemplate)
  private _userValidateTemplate: OrganizationUserValidationTemplate;

  @ValidateNested()
  @IsInstance(OrganizationCertificateConfig)
  private _certificateConfig: OrganizationCertificateConfig;

  @ValidateNested()
  @IsInstance(OrganizationValidationPasswordConfig)
  private _validationPasswordConfig: OrganizationValidationPasswordConfig;

  @ValidateNested()
  @IsInstance(OrganizationTokenConfig)
  private _tokenConfig: OrganizationTokenConfig;

  @IsOptional()
  @ValidateNested()
  @IsInstance(OrganizationAutoApprovalEnrollmentConfig)
  private _autoApprovalEnrollmentConfig: Nullable<OrganizationAutoApprovalEnrollmentConfig>;

  @IsOptional()
  @ValidateNested()
  @IsInstance(OrganizationNotificationConfig)
  private _notificationConfig: Nullable<OrganizationNotificationConfig>;

  @IsOptional()
  @ValidateNested()
  @IsInstance(OrganizationLicensesConfiguration)
  private _licensesConfiguration: Nullable<OrganizationLicensesConfiguration>;

  @IsOptional()
  @ValidateNested()
  @IsInstance(OrganizationPasswordPolicyConfig)
  private _passwordPolicyConfig: Nullable<OrganizationPasswordPolicyConfig>;

  @ValidateNested()
  @IsInstance(OrganizationTransactionCounter)
  private _transactionCounter: OrganizationTransactionCounter;

  @IsObject()
  private _featureConfig: OrganizationFeatureConfigParams;

  @IsBoolean()
  private _isEnableLocalLogin: boolean;

  @IsBoolean()
  private _isLivenessEnabled: boolean;

  @IsEnum(LivenessModeEnum)
  private _livenessMode: LivenessModeEnum;

  @IsBoolean()
  private _isEnabled: boolean;

  @IsString()
  @IsOptional()
  private _createByAdminUserId: GenericID;

  constructor(props: OrganizationParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    const defaultFeatureConfig: OrganizationFeatureConfigParams = {
      isShowSearchBar: true,
      isDownloadUser: true,
      isCreateUser: true,
      isUpdatePreEnrollment: true,
      isUpdateUser: true,
      isUpdatePlan: true,
      defaultVideoQuality: 'index',
    };

    const defaultTransactionCounter: OrganizationTransactionCounterParams = {
      materialMediaVideoCode: 0,
      materialMediaArticleCode: 0,
      materialMediaQuizCode: 0,
      materialMediaSurveyCode: 0,
      materialMediaClassroomCode: 0,
      materialMediaAudioCode: 0,
      materialMediaAttachmentCode: 0,
      classroomLocationCode: 0,
    };

    this._domain = props.domain;
    this._fqdn = props.fqdn;
    this._name = props.name;
    this._nameEng = props.nameEng;
    this._availableCourseObjectiveTypes = props.availableCourseObjectiveTypes;
    this._courseObjectiveConfigs = props.courseObjectiveConfigs.map(
      (courseObjectiveConfig) => new OrganizationCourseObjectiveConfig(courseObjectiveConfig),
    );
    this._videoAuthenInstruction = new OrganizationVideoAuthenInstruction(props.videoAuthenInstruction);
    this._organizationContact = new OrganizationContact(props.organizationContact);
    this._themeConfig = new OrganizationThemeConfig(props.themeConfig);
    this._userValidateTemplate = new OrganizationUserValidationTemplate(props.userValidateTemplate);
    this._certificateConfig = new OrganizationCertificateConfig(props.certificateConfig);
    this._validationPasswordConfig = new OrganizationValidationPasswordConfig(props.validationPasswordConfig);
    this._tokenConfig = new OrganizationTokenConfig(props.tokenConfig);
    this._autoApprovalEnrollmentConfig = props.autoApprovalEnrollmentConfig
      ? new OrganizationAutoApprovalEnrollmentConfig(props.autoApprovalEnrollmentConfig)
      : null;
    this._notificationConfig = props.notificationConfig
      ? new OrganizationNotificationConfig(props.notificationConfig)
      : null;
    this._licensesConfiguration = props.licensesConfiguration
      ? new OrganizationLicensesConfiguration(props.licensesConfiguration)
      : null;
    this._passwordPolicyConfig = props.passwordPolicyConfig
      ? new OrganizationPasswordPolicyConfig(props.passwordPolicyConfig)
      : null;
    this._transactionCounter = props.transactionCounter
      ? new OrganizationTransactionCounter(props.transactionCounter)
      : new OrganizationTransactionCounter(defaultTransactionCounter);
    this._featureConfig = props.featureConfig ?? defaultFeatureConfig;
    this._isEnableLocalLogin = props.isEnableLocalLogin;
    this._isLivenessEnabled = props.isLivenessEnabled;
    this._livenessMode = props.livenessMode ?? LivenessModeEnum.AWS;
    this._isEnabled = props.isEnabled;
    this._createByAdminUserId = props.createByAdminUserId;
  }

  static async new(props: CreateOrganizationParams): Promise<Organization> {
    const entity: Organization = new Organization(props);

    await entity.validate();
    return entity;
  }

  @Expose()
  get domain(): string {
    return this._domain;
  }

  set domain(val: string) {
    this._domain = val;
  }

  @Expose()
  get fqdn(): string {
    return this._fqdn;
  }

  set fqdn(val: string) {
    this._fqdn = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get nameEng(): string {
    return this._nameEng;
  }

  set nameEng(val: string) {
    this._nameEng = val;
  }

  @Expose()
  get availableCourseObjectiveTypes(): CourseObjectiveTypeEnum[] {
    return this._availableCourseObjectiveTypes;
  }

  set availableCourseObjectiveTypes(val: CourseObjectiveTypeEnum[]) {
    this._availableCourseObjectiveTypes = val;
  }

  @Expose()
  get courseObjectiveConfigs(): OrganizationCourseObjectiveConfig[] {
    return this._courseObjectiveConfigs;
  }

  set courseObjectiveConfigs(val: OrganizationCourseObjectiveConfig[]) {
    this._courseObjectiveConfigs = val;
  }

  @Expose()
  get videoAuthenInstruction(): OrganizationVideoAuthenInstruction {
    return this._videoAuthenInstruction;
  }

  set videoAuthenInstruction(val: OrganizationVideoAuthenInstruction) {
    this._videoAuthenInstruction = val;
  }

  @Expose()
  get organizationContact(): OrganizationContact {
    return this._organizationContact;
  }

  set organizationContact(val: OrganizationContact) {
    this._organizationContact = val;
  }

  @Expose()
  get userValidateTemplate(): OrganizationUserValidationTemplate {
    return this._userValidateTemplate;
  }

  set userValidateTemplate(val: OrganizationUserValidationTemplate) {
    this._userValidateTemplate = val;
  }

  @Expose()
  get certificateConfig(): OrganizationCertificateConfig {
    return this._certificateConfig;
  }

  set certificateConfig(val: OrganizationCertificateConfig) {
    this._certificateConfig = val;
  }

  @Expose()
  get themeConfig(): OrganizationThemeConfig {
    return this._themeConfig;
  }

  set themeConfig(val: OrganizationThemeConfig) {
    this._themeConfig = val;
  }

  @Expose()
  get validationPasswordConfig(): OrganizationValidationPasswordConfig {
    return this._validationPasswordConfig;
  }

  set validationPasswordConfig(val: OrganizationValidationPasswordConfig) {
    this._validationPasswordConfig = val;
  }

  @Expose()
  get tokenConfig(): OrganizationTokenConfig {
    return this._tokenConfig;
  }

  set tokenConfig(val: OrganizationTokenConfig) {
    this._tokenConfig = val;
  }

  @Expose()
  get autoApprovalEnrollmentConfig(): Nullable<OrganizationAutoApprovalEnrollmentConfig> {
    return this._autoApprovalEnrollmentConfig;
  }

  set autoApprovalEnrollmentConfig(val: Nullable<OrganizationAutoApprovalEnrollmentConfig>) {
    this._autoApprovalEnrollmentConfig = val;
  }

  @Expose()
  get featureConfig(): OrganizationFeatureConfigParams {
    return this._featureConfig;
  }

  set featureConfig(val: OrganizationFeatureConfigParams) {
    this._featureConfig = val;
  }

  @Expose()
  get notificationConfig(): Nullable<OrganizationNotificationConfig> {
    return this._notificationConfig;
  }

  set notificationConfig(val: Nullable<OrganizationNotificationConfig>) {
    this._notificationConfig = val;
  }

  @Expose()
  get licensesConfiguration(): Nullable<OrganizationLicensesConfiguration> {
    return this._licensesConfiguration;
  }

  set licensesConfiguration(val: Nullable<OrganizationLicensesConfiguration>) {
    this._licensesConfiguration = val;
  }

  @Expose()
  get passwordPolicyConfig(): Nullable<OrganizationPasswordPolicyConfig> {
    return this._passwordPolicyConfig;
  }

  set passwordPolicyConfig(val: Nullable<OrganizationPasswordPolicyConfig>) {
    this._passwordPolicyConfig = val;
  }

  @Expose()
  get transactionCounter(): OrganizationTransactionCounter {
    return this._transactionCounter;
  }

  set transactionCounter(val: OrganizationTransactionCounter) {
    this._transactionCounter = val;
  }

  @Expose()
  get isEnableLocalLogin(): boolean {
    return this._isEnableLocalLogin;
  }

  set isEnableLocalLogin(val: boolean) {
    this._isEnableLocalLogin = val;
  }

  @Expose()
  get isLivenessEnabled(): boolean {
    return this._isLivenessEnabled;
  }

  set isLivenessEnabled(val: boolean) {
    this._isLivenessEnabled = val;
  }

  @Expose()
  get livenessMode(): LivenessModeEnum {
    return this._livenessMode;
  }

  set livenessMode(val: LivenessModeEnum) {
    this._livenessMode = val;
  }

  @Expose()
  get isEnabled(): boolean {
    return this._isEnabled;
  }

  set isEnabled(val: boolean) {
    this._isEnabled = val;
  }

  get isCpd(): boolean {
    return organizationDomain.CPD.includes(this.domain as (typeof organizationDomain.CPD)[number]);
  }

  @Expose()
  get createByAdminUserId(): GenericID {
    return this._createByAdminUserId;
  }

  set createByAdminUserId(val: GenericID) {
    this._createByAdminUserId = val;
  }
}
