import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, IsString, IsEnum, IsInstance, IsDate } from 'class-validator';

import {
  EnrollByEnum,
  LearningPathContentProgressRegisterFromEnum,
  LearningPathContentProgressStatusEnum,
  LearningPathEnrollmentStatusEnum,
} from '@shared/lms/constants/enums/learningPathEnrollment.enum';
import {
  LearningPathContentProgressParams,
  LearningPathEnrollmentParams,
} from '@shared/lms/constants/types/learningPathEnrollment.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class LearningPathContentProgress {
  @IsString()
  private _enrollmentId: GenericID;

  @IsString()
  private _preEnrollmentId: GenericID;

  @IsString()
  private _courseId: GenericID;

  @IsEnum(LearningPathContentProgressStatusEnum)
  private _status: LearningPathContentProgressStatusEnum;

  @IsEnum(LearningPathContentProgressRegisterFromEnum)
  private _registerFrom: LearningPathContentProgressRegisterFromEnum;

  @IsDate()
  private _createdAt: Date;

  @IsDate()
  private _updatedAt: Date;

  constructor(params: LearningPathContentProgressParams) {
    this.enrollmentId = params.enrollmentId;
    this.preEnrollmentId = params.preEnrollmentId;
    this.courseId = params.courseId;
    this.status = params.status;
    this.registerFrom = params.registerFrom;
  }

  @Expose()
  get enrollmentId(): GenericID {
    return this._enrollmentId;
  }
  set enrollmentId(val: GenericID) {
    this._enrollmentId = val;
  }

  @Expose()
  get preEnrollmentId(): GenericID {
    return this._preEnrollmentId;
  }
  set preEnrollmentId(val: GenericID) {
    this._preEnrollmentId = val;
  }

  @Expose()
  get courseId(): GenericID {
    return this._courseId;
  }
  set courseId(val: GenericID) {
    this._courseId = val;
  }

  @Expose()
  get status(): LearningPathContentProgressStatusEnum {
    return this._status;
  }
  set status(val: LearningPathContentProgressStatusEnum) {
    this._status = val;
  }

  @Expose()
  get registerFrom(): LearningPathContentProgressRegisterFromEnum {
    return this._registerFrom;
  }
  set registerFrom(val: LearningPathContentProgressRegisterFromEnum) {
    this._registerFrom = val;
  }

  @Expose()
  get createdAt(): Date {
    return this._createdAt;
  }
  set createdAt(val: Date) {
    this._createdAt = val;
  }

  @Expose()
  get updatedAt(): Date {
    return this._updatedAt;
  }
  set updatedAt(val: Date) {
    this._updatedAt = val;
  }
}

export class LearningPathEnrollment extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _learningPathId: GenericID;

  @IsNumber()
  private _version: number;

  @IsString()
  private _userId: GenericID;

  @IsOptional()
  @IsString()
  private _roundId: GenericID;

  @IsOptional()
  @IsString()
  private _preAssignContentId: GenericID;

  @IsEnum(LearningPathEnrollmentStatusEnum)
  private _status: LearningPathEnrollmentStatusEnum;

  @IsArray()
  @IsInstance(LearningPathContentProgress, { each: true })
  private _contentProgress: LearningPathContentProgress[];

  @IsEnum(EnrollByEnum)
  private _enrollBy: EnrollByEnum;

  @IsNumber()
  private _completedContentItem: number;

  @IsDate()
  @IsOptional()
  private _startedAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _expiredAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _finishedAt: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _passedAt: Nullable<Date>;

  constructor(params: LearningPathEnrollmentParams) {
    const { id, createdAt, updatedAt, deletedAt } = params;
    super(id, createdAt, updatedAt, deletedAt);

    this.organizationId = params.organizationId;
    this.learningPathId = params.learningPathId;
    this.userId = params.userId;
    this.version = params.version;
    this.contentProgress = (params.contentProgress || []).map(
      (contentProgress) => new LearningPathContentProgress(contentProgress),
    );
    this.enrollBy = params.enrollBy;
    this.status = params.status;
    this.roundId = params?.roundId || null;
    this.preAssignContentId = params?.preAssignContentId || null;
    this.completedContentItem = params.completedContentItem || 0;
    this.startedAt = params.startedAt || null;
    this.expiredAt = params.expiredAt || null;
    this.finishedAt = params.finishedAt || null;
    this.passedAt = params.passedAt || null;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get learningPathId(): GenericID {
    return this._learningPathId;
  }
  set learningPathId(val: GenericID) {
    this._learningPathId = val;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }
  set userId(val: GenericID) {
    this._userId = val;
  }

  @Expose()
  get version(): number {
    return this._version;
  }
  set version(val: number) {
    this._version = val;
  }

  @Expose()
  get contentProgress(): LearningPathContentProgress[] {
    return this._contentProgress;
  }
  set contentProgress(val: LearningPathContentProgress[]) {
    this._contentProgress = val;
  }

  @Expose()
  get enrollBy(): EnrollByEnum {
    return this._enrollBy;
  }
  set enrollBy(val: EnrollByEnum) {
    this._enrollBy = val;
  }

  @Expose()
  get status(): LearningPathEnrollmentStatusEnum {
    return this._status;
  }
  set status(val: LearningPathEnrollmentStatusEnum) {
    this._status = val;
  }

  @Expose()
  get completedContentItem(): number {
    return this._completedContentItem;
  }
  set completedContentItem(val: number) {
    this._completedContentItem = val;
  }

  @Expose()
  get roundId(): GenericID {
    return this._roundId;
  }
  set roundId(roundId: GenericID) {
    this._roundId = roundId;
  }

  @Expose()
  get preAssignContentId(): GenericID {
    return this._preAssignContentId;
  }
  set preAssignContentId(preAssignContentId: GenericID) {
    this._preAssignContentId = preAssignContentId;
  }

  @Expose()
  get startedAt(): Nullable<Date> {
    return this._startedAt;
  }
  set startedAt(val: Nullable<Date>) {
    this._startedAt = val;
  }

  @Expose()
  get expiredAt(): Nullable<Date> {
    return this._expiredAt;
  }
  set expiredAt(val: Nullable<Date>) {
    this._expiredAt = val;
  }

  @Expose()
  get finishedAt(): Nullable<Date> {
    return this._finishedAt;
  }
  set finishedAt(val: Nullable<Date>) {
    this._finishedAt = val;
  }

  @Expose()
  get passedAt(): Nullable<Date> {
    return this._passedAt;
  }
  set passedAt(val: Nullable<Date>) {
    this._passedAt = val;
  }

  static async new(props: LearningPathEnrollmentParams): Promise<LearningPathEnrollment> {
    const entity = new LearningPathEnrollment(props);
    await entity.validate();
    return entity;
  }

  updateContentProgressStatus(enrollmentId: GenericID, status: LearningPathContentProgressStatusEnum): void {
    const index = this.contentProgress.findIndex(
      (contentProgressItem) => contentProgressItem.enrollmentId === enrollmentId,
    );
    if (index < 0) return;

    const contentProgressItem = this.contentProgress[index];

    contentProgressItem.status = status;
    contentProgressItem.updatedAt = date().toDate();

    this.contentProgress[index] = contentProgressItem;
  }
}
