import { GenericID, Nullable, Optional } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsObject, IsOptional, IsString } from 'class-validator';

import { ExternalContentTypeEnum } from '@shared/lms/constants/enums/course.enum';
import { BusinessTypeEnum, EnrollTypeEnum } from '@shared/lms/constants/enums/enrollment.enum';
import {
  PreEnrollmentTransactionAutoBulkOparationEnum,
  PreEnrollmentTransactionEnrollByEnum,
  PreEnrollmentTransactionPaymentTypeEnum,
  PreEnrollmentTransactionStatusEnum,
  PreEnrollmentTransactionWarningStatusEnum,
} from '@shared/lms/constants/enums/preEnrollmentTransaction.enum';
import {
  ContentItemParams,
  OperationExecuteParams,
  PreEnrollmentTransactionParams,
  PreEnrollmentTransactionPayloadParams,
} from '@shared/lms/constants/types/preEnrollmentTransaction.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class PreEnrollmentTransaction extends BaseModel {
  @IsString()
  private _jobId: string;

  @IsString()
  private _organizationId: GenericID;

  @IsString()
  @IsOptional()
  private _roundId: Nullable<GenericID>;

  @IsString()
  @IsOptional()
  private _customerPartnerId: Nullable<GenericID>;

  @IsString()
  @IsOptional()
  private _preAssignContentId: Nullable<GenericID>;

  @IsString()
  @IsOptional()
  private _preEnrollmentReservationId: Nullable<GenericID>;

  @IsEnum(PreEnrollmentTransactionStatusEnum)
  private _status: PreEnrollmentTransactionStatusEnum;

  @IsEnum(ExternalContentTypeEnum)
  private _externalContentType: ExternalContentTypeEnum;

  @IsEnum(EnrollTypeEnum)
  @IsOptional()
  private _enrollType: Nullable<EnrollTypeEnum>;

  @IsBoolean()
  private _isCheckRegulator: boolean;

  @IsString()
  private _errorMsg: string;

  @IsString()
  private _warnMsg: string;

  @IsObject()
  private _payload: PreEnrollmentTransactionPayloadParams;

  @IsArray()
  private _warnList: string[];

  @IsObject()
  private _operationExecute?: OperationExecuteParams;

  @IsEnum(BusinessTypeEnum)
  private _businessType: BusinessTypeEnum;

  @IsArray()
  private _contentItems: ContentItemParams[];

  @IsBoolean()
  private _isUpdatedRetailOrder: boolean;

  @IsEnum(PreEnrollmentTransactionPaymentTypeEnum)
  private _paymentType: PreEnrollmentTransactionPaymentTypeEnum;

  @IsEnum(PreEnrollmentTransactionEnrollByEnum)
  private _enrollBy: PreEnrollmentTransactionEnrollByEnum;

  @IsString()
  @IsOptional()
  private _userId: Nullable<GenericID>;

  @IsArray()
  private _autoBulkOparation: PreEnrollmentTransactionAutoBulkOparationEnum[];

  constructor(props: PreEnrollmentTransactionParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    const autoBulkOparationDefault: PreEnrollmentTransactionAutoBulkOparationEnum[] = [
      PreEnrollmentTransactionAutoBulkOparationEnum.ACTIVATE,
      PreEnrollmentTransactionAutoBulkOparationEnum.EDIT_USER,
      PreEnrollmentTransactionAutoBulkOparationEnum.ENROLLMENT,
      PreEnrollmentTransactionAutoBulkOparationEnum.ENROLLMENT_BUNDLE,
    ];

    const operationExecuteDefault: OperationExecuteParams = {
      isActivateUser: false,
      isEditUser: false,
      isEnrollment: false,
      isEnrollmentBundle: false,
      isAssginPlanPackageLicense: false,
    };

    this._jobId = props.jobId;
    this._organizationId = props.organizationId;
    this._roundId = props.roundId;
    this._preEnrollmentReservationId = props.preEnrollmentReservationId;
    this._preAssignContentId = props.preAssignContentId ?? null;
    this._customerPartnerId = props.customerPartnerId ?? null;
    this._status = props.status;
    this._externalContentType = props?.externalContentType ?? ExternalContentTypeEnum.NONE;
    this._enrollType = props.enrollType ?? null;
    this._isCheckRegulator = props.isCheckRegulator;
    this._errorMsg = props.errorMsg;
    this._warnMsg = props.warnMsg;
    this._payload = props.payload;
    this._warnList = props.warnList;
    this._businessType = props.businessType;
    this._paymentType = props.paymentType;
    this._enrollBy = props.enrollBy;
    this._userId = props.userId;
    this._autoBulkOparation = props.autoBulkOparation ?? autoBulkOparationDefault;
    this._operationExecute = props.operationExecute ?? operationExecuteDefault;
    this._contentItems = props.contentItems;
    this._isUpdatedRetailOrder = props.isUpdatedRetailOrder ?? false;
  }

  static async new(props: PreEnrollmentTransactionParams) {
    const entity = new PreEnrollmentTransaction(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get jobId(): string {
    return this._jobId;
  }

  set jobId(val: string) {
    this._jobId = val;
  }

  @Expose()
  get preAssignContentId(): GenericID {
    return this._preAssignContentId;
  }

  set preAssignContentId(val: GenericID) {
    this._preAssignContentId = val;
  }

  @Expose()
  get preEnrollmentReservationId(): GenericID {
    return this._preEnrollmentReservationId;
  }

  set preEnrollmentReservationId(val: GenericID) {
    this._preEnrollmentReservationId = val;
  }

  @Expose()
  get roundId(): Nullable<GenericID> {
    return this._roundId;
  }

  set roundId(val: Nullable<GenericID>) {
    this._roundId = val;
  }

  @Expose()
  get customerPartnerId(): Nullable<GenericID> {
    return this._customerPartnerId;
  }

  set customerPartnerId(val: Nullable<GenericID>) {
    this._customerPartnerId = val;
  }

  @Expose()
  get status(): PreEnrollmentTransactionStatusEnum {
    return this._status;
  }

  set status(val: PreEnrollmentTransactionStatusEnum) {
    this._status = val;
  }

  @Expose()
  get externalContentType(): ExternalContentTypeEnum {
    return this._externalContentType;
  }
  set externalContentType(val: ExternalContentTypeEnum) {
    this._externalContentType = val;
  }

  @Expose()
  get enrollType(): Nullable<EnrollTypeEnum> {
    return this._enrollType;
  }

  set enrollType(val: Nullable<EnrollTypeEnum>) {
    this._enrollType = val;
  }

  @Expose()
  get isCheckRegulator(): boolean {
    return this._isCheckRegulator;
  }

  set isCheckRegulator(val: boolean) {
    this._isCheckRegulator = val;
  }

  @Expose()
  get payload(): PreEnrollmentTransactionPayloadParams {
    return this._payload;
  }

  set payload(val: PreEnrollmentTransactionPayloadParams) {
    this._payload = val;
  }

  @Expose()
  get errorMsg(): string {
    return this._errorMsg;
  }

  set errorMsg(val: string) {
    this._errorMsg = val;
  }

  @Expose()
  get warnMsg(): string {
    return this._warnMsg;
  }

  set warnMsg(val: string) {
    this._warnMsg = val;
  }

  @Expose()
  get warnList(): string[] {
    return this._warnList;
  }

  set warnList(val: string[]) {
    this._warnList = val;
  }

  @Expose()
  get operationExecute(): Optional<OperationExecuteParams> {
    return this._operationExecute;
  }

  set operationExecute(val: Optional<OperationExecuteParams>) {
    this._operationExecute = val;
  }

  @Expose()
  get autoBulkOparation(): PreEnrollmentTransactionAutoBulkOparationEnum[] {
    return this._autoBulkOparation;
  }

  set autoBulkOparation(val: PreEnrollmentTransactionAutoBulkOparationEnum[]) {
    this._autoBulkOparation = val;
  }

  @Expose()
  get businessType(): BusinessTypeEnum {
    return this._businessType;
  }

  set businessType(val: BusinessTypeEnum) {
    this._businessType = val;
  }

  @Expose()
  get contentItems(): ContentItemParams[] {
    return this._contentItems;
  }

  set contentItems(val: ContentItemParams[]) {
    this._contentItems = val;
  }

  @Expose()
  get isUpdatedRetailOrder(): boolean {
    return this._isUpdatedRetailOrder;
  }

  set isUpdatedRetailOrder(val: boolean) {
    this._isUpdatedRetailOrder = val;
  }

  @Expose()
  get paymentType(): PreEnrollmentTransactionPaymentTypeEnum {
    return this._paymentType;
  }

  set paymentType(val: PreEnrollmentTransactionPaymentTypeEnum) {
    this._paymentType = val;
  }

  @Expose()
  get enrollBy(): PreEnrollmentTransactionEnrollByEnum {
    return this._enrollBy;
  }

  set enrollBy(val: PreEnrollmentTransactionEnrollByEnum) {
    this._enrollBy = val;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }

  set userId(val: GenericID) {
    this._userId = val;
  }

  isDuplicate(): boolean {
    return this._warnList?.includes(PreEnrollmentTransactionWarningStatusEnum.DUPLICATE_RECORD) ?? false;
  }

  cancel(): void {
    this._status = PreEnrollmentTransactionStatusEnum.CANCELED;
  }

  pass(): void {
    this._status = PreEnrollmentTransactionStatusEnum.PASSED;
  }
}
