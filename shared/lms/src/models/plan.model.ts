import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsEnum, IsString, IsOptional, IsDate } from 'class-validator';

import { PlanStatusEnum, SaleOrderStatusEnum } from '@shared/lms/constants/enums/plan.enum';
import { CreatePlanParams, PlanParams } from '@shared/lms/constants/types/plan.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class Plan extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _name: string;

  @IsDate()
  @IsOptional()
  private _gracingDate: Nullable<Date>;

  @IsDate()
  private _startDate: Date;

  @IsDate()
  private _endDate: Date;

  @IsEnum(SaleOrderStatusEnum)
  private _saleOrderStatus: SaleOrderStatusEnum;

  @IsEnum(PlanStatusEnum)
  private _status: PlanStatusEnum;

  constructor(props: PlanParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._name = props.name;
    this._gracingDate = props.gracingDate;
    this._startDate = props.startDate;
    this._endDate = props.endDate;
    this._saleOrderStatus = props.saleOrderStatus;
    this._status = this.getStatus();
  }

  static async new(props: CreatePlanParams) {
    const entity = new Plan({
      ...props,
      gracingDate: props?.gracingDate ?? null,
    });
    await entity.validate();
    return entity;
  }

  getStatus(): PlanStatusEnum {
    const currentDate = date().toDate();
    const startDate = date(this.startDate).toDate();
    const endDate = date(this.endDate).toDate();

    if (currentDate < startDate) {
      return PlanStatusEnum.NOT_STARTED;
    }

    if (currentDate > startDate && currentDate < endDate) {
      return PlanStatusEnum.AVAILABLE;
    }

    if (currentDate > endDate) {
      return PlanStatusEnum.EXPIRED;
    }

    return PlanStatusEnum.NOT_STARTED;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get gracingDate(): Nullable<Date> {
    return this._gracingDate;
  }

  set gracingDate(val: Nullable<Date>) {
    this._gracingDate = val;
  }

  @Expose()
  get startDate(): Date {
    return this._startDate;
  }

  set startDate(val: Date) {
    this._startDate = val;
  }

  @Expose()
  get endDate(): Date {
    return this._endDate;
  }

  set endDate(val: Date) {
    this._endDate = val;
  }

  @Expose()
  get saleOrderStatus(): SaleOrderStatusEnum {
    return this._saleOrderStatus;
  }

  set saleOrderStatus(val: SaleOrderStatusEnum) {
    this._saleOrderStatus = val;
  }

  @Expose()
  get status(): PlanStatusEnum {
    return this._status;
  }

  set status(val: PlanStatusEnum) {
    this._status = val;
  }
}
