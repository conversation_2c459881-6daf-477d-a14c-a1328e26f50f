import { GenericID } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsOptional, IsString, IsArray } from 'class-validator';
import { v4 } from 'uuid';

import { CreatePermissionGroupParams, PermissionGroupParams } from '@shared/lms/constants/types/permissionGroup.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class PermissionGroup extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _name: string;

  @IsString()
  @IsOptional()
  private _description?: string;

  @IsArray()
  private _permissionIds: GenericID[];

  constructor(payload: PermissionGroupParams) {
    const { id, name, description, organizationId, permissionIds, createdAt, updatedAt } = payload;
    super(id, createdAt, updatedAt);

    this._name = name;
    this._description = description || '';
    this._organizationId = organizationId;
    this._permissionIds = permissionIds || [];
  }

  static async new(props: CreatePermissionGroupParams): Promise<PermissionGroup> {
    const currentDate = date().toDate();
    const entity = new PermissionGroup({ id: v4(), createdAt: currentDate, updatedAt: currentDate, ...props });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(organizationId: GenericID) {
    this._organizationId = organizationId;
  }

  @Expose()
  get name(): string {
    return this._name;
  }

  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get description(): string {
    return this._description;
  }

  set description(val: string) {
    this._description = val;
  }

  @Expose()
  get permissionIds(): GenericID[] {
    return this._permissionIds;
  }

  set permissionIds(val: GenericID[]) {
    this._permissionIds = val;
  }
}
