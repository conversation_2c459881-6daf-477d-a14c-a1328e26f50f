import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsEnum, IsObject, IsString, IsOptional } from 'class-validator';

import { SurveyTypeEnum } from '@shared/lms/constants/enums/survey.enum';
import {
  CreateSurveySubmissionParams,
  SurveySubmissionParams,
} from '@shared/lms/constants/types/surveySubmission.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class SurveySubmission extends BaseModel {
  private _itemId: GenericID;

  @IsOptional()
  @IsString()
  private _materialMediaId: Nullable<GenericID>;

  @IsString()
  private _enrollmentId: GenericID;

  @IsEnum(SurveyTypeEnum)
  private _type: SurveyTypeEnum;

  @IsObject()
  private _payload: Record<string, unknown>;

  constructor(props: SurveySubmissionParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._itemId = props.itemId;
    this._materialMediaId = props.materialMediaId;
    this._enrollmentId = props.enrollmentId;
    this._type = props.type;
    this._payload = props.payload;
  }

  static async new(props: CreateSurveySubmissionParams) {
    const entity = new SurveySubmission({
      ...props,
      materialMediaId: props?.materialMediaId ?? null,
      type: props?.type ?? SurveyTypeEnum.SELF,
      payload: props?.payload ?? {},
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get itemId(): GenericID {
    return this._itemId;
  }

  set itemId(value: GenericID) {
    this._itemId = value;
  }

  @Expose()
  get materialMediaId(): Nullable<GenericID> {
    return this._materialMediaId;
  }

  set materialMediaId(value: Nullable<GenericID>) {
    this._materialMediaId = value;
  }

  @Expose()
  get enrollmentId(): GenericID {
    return this._enrollmentId;
  }

  set enrollmentId(value: GenericID) {
    this._enrollmentId = value;
  }

  @Expose()
  get type(): SurveyTypeEnum {
    return this._type;
  }

  set type(value: SurveyTypeEnum) {
    this._type = value;
  }

  @Expose()
  get payload(): Record<string, unknown> {
    return this._payload;
  }

  set payload(value: Record<string, unknown>) {
    this._payload = value;
  }
}
