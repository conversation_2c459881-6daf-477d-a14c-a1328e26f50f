import { Generic<PERSON>, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsArray, IsBoolean, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

import { LivenessModeEnum } from '@shared/lms/constants/enums/organization.enum';
import { LogFaceComparisonParams, TargetFaceLabels } from '@shared/lms/constants/types/logFaceComparison.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class LogFaceComparison extends BaseModel {
  @IsString()
  private _enrollmentId: GenericID;

  @IsNumber()
  @Min(0)
  @Max(100)
  private _percentSimilarity: number;

  @IsString()
  @IsOptional()
  private _imageFacePath: string;

  @IsString()
  @IsOptional()
  private _imageIdCardPath: string;

  @IsString()
  @IsOptional()
  private _imageComparePath: string;

  @IsBoolean()
  private _isFaceLivenessMode: boolean;

  @IsBoolean()
  private _isPass: boolean;

  @IsBoolean()
  @IsOptional()
  private _isFraud: boolean;

  @IsNumber()
  @IsOptional()
  private _fraudScore: number;

  @IsArray()
  private _targetFaceLabels: TargetFaceLabels;

  @IsOptional()
  private _response: Nullable<any>;

  @IsOptional()
  private _mode: LivenessModeEnum;

  constructor(payload: LogFaceComparisonParams) {
    const { id, createdAt, updatedAt, deletedAt } = payload;

    super(id, createdAt, updatedAt, deletedAt);

    this._enrollmentId = payload.enrollmentId;
    this._percentSimilarity = payload.percentSimilarity || 0;
    this._imageFacePath = payload.imageFacePath || '';
    this._imageIdCardPath = payload.imageIdCardPath || '';
    this._imageComparePath = payload.imageComparePath || '';
    this._isFaceLivenessMode = payload.isFaceLivenessMode;
    this._isPass = payload.isPass;
    this._isFraud = payload.isFraud || false;
    this._fraudScore = payload.fraudScore || 0;
    this._targetFaceLabels = payload.targetFaceLabels || [];
    this._response = payload.response || null;
    this._mode = payload.mode ?? null;
  }

  static async new(props: LogFaceComparisonParams): Promise<LogFaceComparison> {
    const entity: LogFaceComparison = new LogFaceComparison(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get enrollmentId(): GenericID {
    return this._enrollmentId;
  }
  set enrollmentId(val: GenericID) {
    this._enrollmentId = val;
  }

  @Expose()
  get percentSimilarity(): number {
    return this._percentSimilarity;
  }
  set percentSimilarity(val: number) {
    this._percentSimilarity = val;
  }

  @Expose()
  get imageFacePath(): string {
    return this._imageFacePath;
  }
  set imageFacePath(val: string) {
    this._imageFacePath = val;
  }

  @Expose()
  get imageIdCardPath(): string {
    return this._imageIdCardPath;
  }
  set imageIdCardPath(val: string) {
    this._imageIdCardPath = val;
  }

  @Expose()
  get imageComparePath(): string {
    return this._imageComparePath;
  }
  set imageComparePath(val: string) {
    this._imageComparePath = val;
  }

  @Expose()
  get isFaceLivenessMode(): boolean {
    return this._isFaceLivenessMode;
  }
  set isFaceLivenessMode(val: boolean) {
    this._isFaceLivenessMode = val;
  }

  @Expose()
  get isPass(): boolean {
    return this._isPass;
  }
  set isPass(val: boolean) {
    this._isPass = val;
  }

  @Expose()
  get isFraud(): boolean {
    return this._isFraud;
  }
  set isFraud(val: boolean) {
    this._isFraud = val;
  }

  @Expose()
  get fraudScore(): number {
    return this._fraudScore;
  }
  set fraudScore(val: number) {
    this._fraudScore = val;
  }

  @Expose()
  get targetFaceLabels(): TargetFaceLabels {
    return this._targetFaceLabels;
  }
  set targetFaceLabels(val: TargetFaceLabels) {
    this._targetFaceLabels = val;
  }

  @Expose()
  get response(): Nullable<any> {
    return this._response;
  }
  set response(val: Nullable<any>) {
    this._response = val;
  }

  @Expose()
  get mode(): LivenessModeEnum {
    return this._mode;
  }
  set mode(val: LivenessModeEnum) {
    this._mode = val;
  }
}
