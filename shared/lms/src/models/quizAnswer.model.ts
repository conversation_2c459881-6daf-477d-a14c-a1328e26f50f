import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsArray, IsDate, IsNumber, IsObject, IsOptional, IsString, ValidateIf } from 'class-validator';

import {
  CreateQuizAnswerParams,
  QuestionAnswerParams,
  QuizAnswerConfigParams,
  QuizAnswerCriteriaParams,
  QuizAnswerParams,
} from '@shared/lms/constants/types/quizAnswer.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class QuizAnswer extends BaseModel {
  @IsString()
  private _enrollmentId: GenericID;

  @ValidateIf((o) => typeof o._quizId === 'string')
  @IsString()
  @ValidateIf((o) => typeof o._quizId === 'number')
  @IsNumber()
  private _quizId: GenericID;

  @IsString()
  private _quizName: string;

  @IsString()
  private _testType: string;

  @IsObject()
  private _quizConfig: QuizAnswerConfigParams;

  @IsNumber()
  private _timeSpent: number;

  @IsArray()
  private _questions: QuestionAnswerParams[];

  @IsNumber()
  private _userPoint: number;

  @IsNumber()
  private _totalPoint: number;

  @IsNumber()
  private _scorePercents: number;

  @IsObject()
  @IsOptional()
  private _criteriaCertificate?: Nullable<QuizAnswerCriteriaParams>;

  @IsDate()
  private _quizStartDate: Date;

  @IsDate()
  private _quizPublishedAt: Date;

  @IsDate()
  @IsOptional()
  private _finishedAt: Nullable<Date>;

  constructor(props: QuizAnswerParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._enrollmentId = props.enrollmentId;
    this._quizId = props.quizId;
    this._quizName = props.quizName;
    this._testType = props.testType;
    this._quizConfig = props.quizConfig;
    this._timeSpent = props.timeSpent;
    this._questions = props.questions;
    this._userPoint = props.userPoint;
    this._totalPoint = props.totalPoint;
    this._scorePercents = props.scorePercents;
    this._criteriaCertificate = props.criteriaCertificate;
    this._quizStartDate = props.quizStartDate;
    this._quizPublishedAt = props.quizPublishedAt;
    this._finishedAt = props.finishedAt;
  }

  static async new(props: CreateQuizAnswerParams) {
    const entity = new QuizAnswer(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get enrollmentId(): GenericID {
    return this._enrollmentId;
  }

  set enrollmentId(enrollmentId: GenericID) {
    this._enrollmentId = enrollmentId;
  }

  @Expose()
  get quizId(): GenericID {
    return this._quizId;
  }

  set quizId(quizId: GenericID) {
    this._quizId = quizId;
  }

  @Expose()
  get quizName(): string {
    return this._quizName;
  }

  set quizName(quizName: string) {
    this._quizName = quizName;
  }

  @Expose()
  get testType(): string {
    return this._testType;
  }

  set testType(testType: string) {
    this._testType = testType;
  }

  @Expose()
  get quizConfig(): QuizAnswerConfigParams {
    return this._quizConfig;
  }

  set quizConfig(quizConfig: QuizAnswerConfigParams) {
    this._quizConfig = quizConfig;
  }

  @Expose()
  get timeSpent(): number {
    return this._timeSpent;
  }

  set timeSpent(timeSpent: number) {
    this._timeSpent = timeSpent;
  }

  @Expose()
  get questions(): QuestionAnswerParams[] {
    return this._questions;
  }

  set questions(questions: QuestionAnswerParams[]) {
    this._questions = questions;
  }

  @Expose()
  get userPoint(): number {
    return this._userPoint;
  }

  set userPoint(userPoint: number) {
    this._userPoint = userPoint;
  }

  @Expose()
  get totalPoint(): number {
    return this._totalPoint;
  }

  set totalPoint(totalPoint: number) {
    this._totalPoint = totalPoint;
  }

  @Expose()
  get scorePercents(): number {
    return this._scorePercents;
  }

  set scorePercents(scorePercents: number) {
    this._scorePercents = scorePercents;
  }

  @Expose()
  get criteriaCertificate(): Nullable<QuizAnswerCriteriaParams> {
    return this._criteriaCertificate;
  }

  set criteriaCertificate(criteriaCertificate: Nullable<QuizAnswerCriteriaParams>) {
    this._criteriaCertificate = criteriaCertificate;
  }

  @Expose()
  get quizStartDate(): Date {
    return this._quizStartDate;
  }

  set quizStartDate(quizStartDate: Date) {
    this._quizStartDate = quizStartDate;
  }

  @Expose()
  get quizPublishedAt(): Date {
    return this._quizPublishedAt;
  }

  set quizPublishedAt(quizPublishedAt: Date) {
    this._quizPublishedAt = quizPublishedAt;
  }

  @Expose()
  get finishedAt(): Nullable<Date> {
    return this._finishedAt;
  }

  set finishedAt(val: Nullable<Date>) {
    this._finishedAt = val;
  }
}
