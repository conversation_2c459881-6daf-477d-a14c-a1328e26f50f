import { Nullable, Optional } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsObject, IsNumber, IsArray, IsDate, IsString } from 'class-validator';

import { PurchaseOrderStatusEnum } from '@shared/lms/constants/enums/purchaseOrder.enum';
import {
  CreatePurchaseOrderParams,
  PurchaseOrderParams,
  SaleOrderItemParams,
} from '@shared/lms/constants/types/purchaseOrder.type';
import { UploadFileResponseParams } from '@shared/lms/constants/types/uploadFile.type';
import { BaseModel } from '@shared/lms/core/instances/base';

// Note: PurchaseOrder = SaleOrderItem On ERP

export class PurchaseOrder extends BaseModel {
  @IsString()
  private _customerCode: string;

  @IsString()
  private _purchaseOrderNo: string;

  @IsNumber()
  private _point: number;

  @IsNumber()
  private _remainPoint: number;

  @IsNumber()
  private _price: number;

  @IsDate()
  private _startedAt: Date;

  @IsDate()
  private _expiredAt: Date;

  @IsArray()
  private _uploadFiles: UploadFileResponseParams[];

  @IsString()
  private _note: string;

  @IsObject()
  private _saleOrderItem: Nullable<SaleOrderItemParams>;

  constructor(props: PurchaseOrderParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._customerCode = props.customerCode;
    this._purchaseOrderNo = props.purchaseOrderNo;
    this._point = props.point;
    this._remainPoint = props.remainPoint ?? props.point; // total point of history-points collection
    this._price = props.price ?? 0;
    this._startedAt = props.startedAt;
    this._expiredAt = props.expiredAt;
    this._uploadFiles = props.uploadFiles;
    this._note = props.note ?? '';
    this._saleOrderItem = props.saleOrderItem ?? null;
  }

  static async new(props: CreatePurchaseOrderParams) {
    const entity = new PurchaseOrder(props);
    await entity.validate();
    return entity;
  }

  getStatus(): PurchaseOrderStatusEnum {
    const currentDate = date().toDate();

    if (currentDate < date(this.startedAt).toDate() && this.remainPoint > 0) {
      return PurchaseOrderStatusEnum.NOT_STARTED;
    }

    if (currentDate > date(this.expiredAt).toDate()) {
      return PurchaseOrderStatusEnum.EXPIRED;
    }

    const isOutOfPoint = this.remainPoint <= 0;
    if (!isOutOfPoint && currentDate > date(this.startedAt).toDate() && currentDate < date(this.expiredAt).toDate()) {
      return PurchaseOrderStatusEnum.AVAILABLE;
    }

    return PurchaseOrderStatusEnum.OUT_OF_POINTS;
  }

  @Expose()
  get customerCode(): string {
    return this._customerCode;
  }

  set customerCode(val: string) {
    this._customerCode = val;
  }

  @Expose()
  get purchaseOrderNo(): string {
    return this._purchaseOrderNo;
  }

  set purchaseOrderNo(val: string) {
    this._purchaseOrderNo = val;
  }

  @Expose()
  get point(): number {
    return this._point;
  }

  set point(val: number) {
    this._point = val;
  }

  @Expose()
  get remainPoint(): number {
    return this._remainPoint;
  }

  set remainPoint(val: number) {
    this._remainPoint = val;
  }

  @Expose()
  get price(): Optional<number> {
    return this._price;
  }

  set price(val: Optional<number>) {
    this._price = val || 0;
  }

  @Expose()
  get startedAt(): Date {
    return this._startedAt;
  }

  set startedAt(val: Date) {
    this._startedAt = val;
  }

  @Expose()
  get expiredAt(): Date {
    return this._expiredAt;
  }

  set expiredAt(val: Date) {
    this._expiredAt = val;
  }

  @Expose()
  get uploadFiles(): UploadFileResponseParams[] {
    return this._uploadFiles;
  }

  set uploadFiles(val: UploadFileResponseParams[]) {
    this._uploadFiles = val;
  }

  @Expose()
  get note(): Optional<string> {
    return this._note;
  }

  set note(val: Optional<string>) {
    this._note = val || '';
  }

  @Expose()
  get saleOrderItem(): Nullable<SaleOrderItemParams> {
    return this._saleOrderItem;
  }

  set saleOrderItem(val: Nullable<SaleOrderItemParams>) {
    this._saleOrderItem = val;
  }
}
