import { GenericID } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsArray, IsBoolean, IsNumber, IsString } from 'class-validator';

import { CourseCategoryParams, CreateCourseCategoryParams } from '@shared/lms/constants/types/courseCategory.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class CourseCategory extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _name: string;

  @IsString()
  private _code: string;

  @IsString()
  private _description: string;

  @IsString()
  private _parentId: GenericID;

  @IsBoolean()
  private _isActive: boolean;

  @IsArray()
  private _courseIds: GenericID[];

  @IsNumber()
  private _position: number;

  constructor(props: CourseCategoryParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._name = props.name;
    this._code = props.code;
    this._description = props.description ?? '';
    this._parentId = props.parentId ?? '';
    this._isActive = props.isActive ?? false;
    this._courseIds = props.courseIds ?? [];
    this._position = props.position ?? 0;
  }

  static async new(props: CreateCourseCategoryParams) {
    const entity = new CourseCategory({
      organizationId: props.organizationId,
      name: props.name ?? '',
      code: props.code ?? '',
      description: props.description ?? '',
      parentId: props.parentId ?? '',
      isActive: props.isActive ?? false,
      courseIds: props.courseIds ?? [],
      position: props.position ?? 0,
    });

    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }
  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get code(): string {
    return this._code;
  }
  set code(val: string) {
    this._code = val;
  }

  @Expose()
  get description(): string {
    return this._description;
  }
  set description(val: string) {
    this._description = val;
  }

  @Expose()
  get parentId(): GenericID {
    return this._parentId;
  }
  set parentId(val: GenericID) {
    this._parentId = val;
  }

  @Expose()
  get isActive(): boolean {
    return this._isActive;
  }
  set isActive(val: boolean) {
    this._isActive = val;
  }

  @Expose()
  get courseIds(): GenericID[] {
    return this._courseIds;
  }
  set courseIds(val: GenericID[]) {
    this._courseIds = val;
  }

  @Expose()
  get position(): number {
    return this._position;
  }
  set position(val: number) {
    this._position = val;
  }
}
