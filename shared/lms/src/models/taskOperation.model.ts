import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';

import {
  CreateTaskOperationParams,
  TaskOperationKeyParams,
  TaskOperationParams,
} from '@shared/lms/constants/types/taskOperation.type';
import { BaseSchema } from '@shared/lms/core/instances/base';
import { IsTaskOperationKey } from '@shared/lms/core/instances/validator';

export class TaskOperation extends BaseSchema {
  @IsTaskOperationKey()
  private _key: TaskOperationKeyParams;

  @IsNumber()
  private _total: number;

  @IsNumber()
  private _success: number;

  @IsNumber()
  private _error: number;

  constructor(params: TaskOperationParams) {
    super();
    this._key = params.key;
    this._total = params.total;
    this._success = params.success;
    this._error = params.error;
  }

  static async new(param: CreateTaskOperationParams) {
    const entity = new TaskOperation({
      ...param,
      total: param?.total ?? 0,
      success: param?.success ?? 0,
      error: param?.error ?? 0,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get total(): number {
    return this._total;
  }

  set total(total: number) {
    this._total = total;
  }

  @Expose()
  get success(): number {
    return this._success;
  }

  set success(success: number) {
    this._success = success;
  }

  @Expose()
  get error(): number {
    return this._error;
  }

  set error(error: number) {
    this._error = error;
  }

  @Expose()
  get key(): TaskOperationKeyParams {
    return this._key;
  }

  set key(key: TaskOperationKeyParams) {
    this._key = key;
  }
}
