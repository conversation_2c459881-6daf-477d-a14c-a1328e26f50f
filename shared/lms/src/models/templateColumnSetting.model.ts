import { GenericID } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsBoolean, IsNumber, IsString, IsArray, IsEnum, IsInstance } from 'class-validator';

import { ColumnSettingTemplateEnum, ColumnSettingModuleEnum } from '@shared/lms/constants/enums/columnSetting.enum';
import {
  ColumnSettingInTemplateParams,
  CreateTemplateColumnSettingParams,
  TemplateColumnSettingParams,
} from '@shared/lms/constants/types/templateColumnSetting.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class ColumnSettingField {
  @IsString()
  _key: string;

  @IsBoolean()
  _isActive: boolean;

  @IsBoolean()
  _isFilter: boolean;

  @IsBoolean()
  _isRequired: boolean;

  @IsBoolean()
  _isDefault: boolean;

  @IsNumber()
  _order: number;

  constructor(props: ColumnSettingInTemplateParams) {
    this._key = props.key;
    this._isActive = props.isActive;
    this._isFilter = props.isFilter;
    this._isRequired = props.isRequired;
    this._isDefault = props.isDefault;
    this._order = props.order;
  }

  @Expose()
  get key(): string {
    return this._key;
  }

  set key(val: string) {
    this._key = val;
  }

  @Expose()
  get isActive(): boolean {
    return this._isActive;
  }

  set isActive(val: boolean) {
    this._isActive = val;
  }

  @Expose()
  get isFilter(): boolean {
    return this._isFilter;
  }

  set isFilter(val: boolean) {
    this._isFilter = val;
  }

  @Expose()
  get isRequired(): boolean {
    return this._isRequired;
  }

  set isRequired(val: boolean) {
    this._isRequired = val;
  }

  @Expose()
  get isDefault(): boolean {
    return this._isDefault;
  }

  set isDefault(val: boolean) {
    this._isDefault = val;
  }

  @Expose()
  get order(): number {
    return this._order;
  }

  set order(val: number) {
    this._order = val;
  }
}

export class TemplateColumnSetting extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _name: string;

  @IsEnum(ColumnSettingTemplateEnum)
  private _code: ColumnSettingTemplateEnum;

  @IsArray()
  private _module: ColumnSettingModuleEnum[];

  @IsArray()
  @IsInstance(ColumnSettingField, { each: true })
  private _columnSetting: ColumnSettingInTemplateParams[];

  @IsArray()
  private _mainFilter: string[];

  constructor(props: TemplateColumnSettingParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._name = props.name;
    this._code = props.code;
    this._module = props.module;
    this._columnSetting = props.columnSetting.map((item) => new ColumnSettingField({ ...item }));
    this._mainFilter = props.mainFilter?.length > 0 ? props.mainFilter : [];
  }

  static async new(props: CreateTemplateColumnSettingParams) {
    const entity = new TemplateColumnSetting(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }
  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get code(): ColumnSettingTemplateEnum {
    return this._code;
  }
  set code(val: ColumnSettingTemplateEnum) {
    this._code = val;
  }

  @Expose()
  get module(): ColumnSettingModuleEnum[] {
    return this._module;
  }
  set module(val: ColumnSettingModuleEnum[]) {
    this.module = val;
  }

  @Expose()
  get columnSetting(): ColumnSettingInTemplateParams[] {
    return this._columnSetting;
  }
  set columnSetting(val: ColumnSettingInTemplateParams[]) {
    this._columnSetting = val;
  }

  @Expose()
  get mainFilter(): string[] {
    return this._mainFilter;
  }
  set mainFilter(val: string[]) {
    this._mainFilter = val;
  }
}
