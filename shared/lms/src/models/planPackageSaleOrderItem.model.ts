import { GenericID } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsDate, IsNumber, IsString } from 'class-validator';

import {
  CreatePlanPackageSaleOrderItemParams,
  PlanPackageSaleOrderItemParams,
} from '@shared/lms/constants/types/planPackageSaleOrderItem.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class PlanPackageSaleOrderItem extends BaseModel {
  @IsString()
  private _orderId: string;

  @IsNumber()
  private _orderItemId: number;

  @IsString()
  private _saleOrderNo: string;

  @IsString()
  private _planPackageId: GenericID;

  @IsString()
  private _note: string;

  @IsDate()
  private _startDate: Date;

  @IsDate()
  private _endDate: Date;

  constructor(props: PlanPackageSaleOrderItemParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._orderId = props.orderId;
    this._orderItemId = props.orderItemId;
    this._saleOrderNo = props.saleOrderNo;
    this._planPackageId = props.planPackageId;
    this._note = props.note;
    this._startDate = props.startDate;
    this._endDate = props.endDate;
  }

  static async new(props: CreatePlanPackageSaleOrderItemParams) {
    const entity = new PlanPackageSaleOrderItem({
      ...props,
      note: props?.note ?? '',
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get orderId(): string {
    return this._orderId;
  }

  set orderId(val: string) {
    this._orderId = val;
  }

  @Expose()
  get orderItemId(): number {
    return this._orderItemId;
  }

  set orderItemId(val: number) {
    this._orderItemId = val;
  }

  @Expose()
  get saleOrderNo(): string {
    return this._saleOrderNo;
  }

  set saleOrderNo(val: string) {
    this._saleOrderNo = val;
  }

  @Expose()
  get planPackageId(): GenericID {
    return this._planPackageId;
  }

  set planPackageId(val: GenericID) {
    this._planPackageId = val;
  }

  @Expose()
  get note(): string {
    return this._note;
  }

  set note(val: string) {
    this.note = val;
  }

  @Expose()
  get startDate(): Date {
    return this._startDate;
  }

  set startDate(val: Date) {
    this._startDate = val;
  }

  @Expose()
  get endDate(): Date {
    return this._endDate;
  }

  set endDate(val: Date) {
    this._endDate = val;
  }
}
