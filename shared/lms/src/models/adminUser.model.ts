import { Nullable, GenericID } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsBoolean, IsObject, IsOptional, IsString } from 'class-validator';
import { v4 } from 'uuid';

import {
  AdminUserParams,
  AdminUserProfileParams,
  CreateAdminUserParams,
  TokenParams,
  TwoFactorSecretParams,
} from '@shared/lms/constants/types/adminUser.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class AdminUser extends BaseModel {
  @IsString()
  @IsOptional()
  private _email: string;

  @IsString()
  private _idsUserId: GenericID;

  @IsObject()
  private _profile: AdminUserProfileParams;

  @IsString()
  @IsOptional()
  private _mobile: string;

  @IsBoolean()
  private _isActive: boolean;

  @IsBoolean()
  private _isTwoFactorRegister: boolean;

  @IsBoolean()
  private _isTwoFactorLogin: boolean;

  @IsObject()
  @IsOptional()
  private _token: Nullable<TokenParams>;

  @IsObject()
  @IsOptional()
  private _twoFactorSecret: Nullable<TwoFactorSecretParams>;

  constructor(payload: AdminUserParams) {
    super(payload.id, payload.createdAt, payload.updatedAt);

    this._email = payload.email;
    this._idsUserId = payload.idsUserId;
    this._profile = payload.profile;
    this._mobile = payload.mobile;
    this._isActive = payload.isActive;
    this._isTwoFactorRegister = payload.isTwoFactorRegister;
    this._isTwoFactorLogin = payload.isTwoFactorLogin;
    this._token = payload.token ?? null;
    this._twoFactorSecret = payload.twoFactorSecret ?? null;
  }

  static async new(props: CreateAdminUserParams): Promise<AdminUser> {
    const currentDate = date().toDate();
    const entity = new AdminUser({ id: v4(), createdAt: currentDate, updatedAt: currentDate, ...props });
    await entity.validate();
    return entity;
  }

  @Expose()
  get email(): string {
    return this._email;
  }

  set email(val: string) {
    this._email = val;
  }

  @Expose()
  get idsUserId(): GenericID {
    return this._idsUserId;
  }

  set idsUserId(val: GenericID) {
    this._idsUserId = val;
  }

  @Expose()
  get profile(): AdminUserProfileParams {
    return this._profile;
  }

  set profile(val: AdminUserProfileParams) {
    this._profile = val;
  }

  @Expose()
  get mobile(): string {
    return this._mobile;
  }

  set mobile(val: string) {
    this._mobile = val;
  }

  @Expose()
  get isActive(): boolean {
    return this._isActive;
  }

  set isActive(val: boolean) {
    this._isActive = val;
  }

  @Expose()
  get isTwoFactorRegister(): boolean {
    return this._isTwoFactorRegister;
  }

  set isTwoFactorRegister(val: boolean) {
    this._isTwoFactorRegister = val;
  }

  @Expose()
  get isTwoFactorLogin(): boolean {
    return this._isTwoFactorLogin;
  }

  set isTwoFactorLogin(val: boolean) {
    this._isTwoFactorLogin = val;
  }

  @Expose()
  get token(): Nullable<TokenParams> {
    return this._token;
  }

  set token(val: Nullable<TokenParams>) {
    this._token = val;
  }

  @Expose()
  get twoFactorSecret(): Nullable<TwoFactorSecretParams> {
    return this._twoFactorSecret;
  }

  set twoFactorSecret(val: Nullable<TwoFactorSecretParams>) {
    this._twoFactorSecret = val;
  }

  get fullName(): string {
    return [this._profile.firstname ?? '', this._profile.lastname ?? ''].join(' ').trim();
  }
}
