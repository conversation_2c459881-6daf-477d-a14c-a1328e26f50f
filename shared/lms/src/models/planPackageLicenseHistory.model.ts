import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsString, IsOptional, IsDate, IsEnum } from 'class-validator';

import { PlanPackageHistoryStatusEnum } from '@shared/lms/constants/enums/planPackageLicenseHistory.enum';
import {
  CreatePlanPackageLicenseHistoryParams,
  PlanPackageLicenseHistoryParams,
} from '@shared/lms/constants/types/planPackageLicenseHistory.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class PlanPackageLicenseHistory extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _planPackageLicenseId: GenericID;

  @IsString()
  private _userId: GenericID;

  @IsEnum(PlanPackageHistoryStatusEnum)
  private _status: PlanPackageHistoryStatusEnum;

  @IsDate()
  @IsOptional()
  private _startedAt?: Nullable<Date>;

  @IsDate()
  @IsOptional()
  private _expiredAt?: Nullable<Date>;

  constructor(props: PlanPackageLicenseHistoryParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._planPackageLicenseId = props.planPackageLicenseId;
    this._userId = props.userId;
    this._status = props.status;
    this._startedAt = props.startedAt;
    this._expiredAt = props.expiredAt;
  }

  static async new(props: CreatePlanPackageLicenseHistoryParams) {
    const entity = new PlanPackageLicenseHistory({
      ...props,
      startedAt: props?.startedAt ?? null,
      expiredAt: props?.expiredAt ?? null,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get planPackageLicenseId(): GenericID {
    return this._planPackageLicenseId;
  }

  set planPackageLicenseId(val: GenericID) {
    this._planPackageLicenseId = val;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }

  set userId(val: GenericID) {
    this._userId = val;
  }

  @Expose()
  get status(): PlanPackageHistoryStatusEnum {
    return this._status;
  }

  set status(val: PlanPackageHistoryStatusEnum) {
    this._status = val;
  }

  @Expose()
  get startedAt(): Date {
    return this._startedAt;
  }

  set startedAt(val: Date) {
    this._startedAt = val;
  }

  @Expose()
  get expiredAt(): Date {
    return this._expiredAt;
  }

  set expiredAt(val: Date) {
    this._expiredAt = val;
  }
}
