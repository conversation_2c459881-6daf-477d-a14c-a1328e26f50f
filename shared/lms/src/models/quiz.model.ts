import { GenericID } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsArray, IsBoolean, IsIn, IsString, IsOptional, IsObject } from 'class-validator';

import { QuizTestTypeEnum } from '@shared/lms/constants/enums/quiz.enum';
import {
  CreateQuizParams,
  QuestionParams,
  QuizLimitTimeDurationParams,
  QuizParams,
  QuizRetestParams,
  QuizShowAnswerParams,
} from '@shared/lms/constants/types/quiz.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class Quiz extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _materialMediaId: GenericID;

  @IsString()
  @IsOptional()
  private _name: string;

  @IsString()
  @IsOptional()
  private _description: string;

  @IsOptional()
  @IsIn(Object.values(QuizTestTypeEnum))
  private _type: QuizTestTypeEnum;

  @IsOptional()
  @IsObject()
  private _retest: QuizRetestParams;

  @IsOptional()
  @IsObject()
  private _limitTimeDuration: QuizLimitTimeDurationParams;

  @IsOptional()
  @IsObject()
  private _showAnswer: QuizShowAnswerParams;

  @IsBoolean()
  @IsOptional()
  private _isEnabled: boolean;

  @IsArray()
  @IsOptional()
  private _questions: QuestionParams[];

  constructor(props: QuizParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._materialMediaId = props.materialMediaId;

    this._name = props.name;
    this._description = props.description;
    this._type = props.type;
    this._questions = props.questions;
    this._retest = props.retest;
    this._limitTimeDuration = props.limitTimeDuration;
    this._showAnswer = props.showAnswer;
    this._isEnabled = props.isEnabled;
  }

  static async new(props: CreateQuizParams) {
    const entity = new Quiz({
      ...props,
      type: props.type ?? QuizTestTypeEnum.REGULAR,
      questions: props.questions ?? [],
      retest: props.retest,
      limitTimeDuration: props.limitTimeDuration,
      showAnswer: props.showAnswer,
      isEnabled: props.isEnabled ?? false,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get materialMediaId(): GenericID {
    return this._materialMediaId;
  }
  set materialMediaId(val: GenericID) {
    this._materialMediaId = val;
  }

  @Expose()
  get name(): string {
    return this._name;
  }
  set name(val: string) {
    this._name = val;
  }

  @Expose()
  get description(): string {
    return this._description;
  }
  set description(val: string) {
    this._description = val;
  }

  @Expose()
  get type(): QuizTestTypeEnum {
    return this._type;
  }
  set type(val: QuizTestTypeEnum) {
    this._type = val;
  }

  @Expose()
  get retest(): QuizRetestParams {
    return this._retest;
  }
  set retest(val: QuizRetestParams) {
    this._retest = val;
  }

  @Expose()
  get limitTimeDuration(): QuizLimitTimeDurationParams {
    return this._limitTimeDuration;
  }
  set limitTimeDuration(val: QuizLimitTimeDurationParams) {
    this._limitTimeDuration = val;
  }

  @Expose()
  get showAnswer(): QuizShowAnswerParams {
    return this._showAnswer;
  }
  set showAnswer(val: QuizShowAnswerParams) {
    this._showAnswer = val;
  }

  @Expose()
  get isEnabled(): boolean {
    return this._isEnabled;
  }
  set isEnabled(val: boolean) {
    this._isEnabled = val;
  }

  @Expose()
  get questions(): QuestionParams[] {
    return this._questions;
  }
  set questions(val: QuestionParams[]) {
    this._questions = val;
  }
}
