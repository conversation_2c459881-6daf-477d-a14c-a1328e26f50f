import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsEnum, IsObject, IsOptional, IsString } from 'class-validator';

import { SurveyTypeEnum } from '@shared/lms/constants/enums/survey.enum';
import { CreateSurveyParams, SurveyParams } from '@shared/lms/constants/types/survey.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class Survey extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _materialMediaId: GenericID;

  @IsEnum(SurveyTypeEnum)
  @IsOptional()
  private _type: SurveyTypeEnum;

  @IsString()
  @IsOptional()
  private _contentHtml: string;

  @IsObject()
  @IsOptional()
  private _formSchema: Nullable<Record<string, unknown>>;

  constructor(props: SurveyParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._materialMediaId = props.materialMediaId;

    this._type = props.type;
    this._contentHtml = props.contentHtml;
    this._formSchema = props.formSchema;
  }

  static async new(props: CreateSurveyParams) {
    const entity = new Survey({
      ...props,
      type: props.type ?? SurveyTypeEnum.SELF,
      contentHtml: props.contentHtml ?? null,
      formSchema: props.formSchema ?? null,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(val: GenericID) {
    this._organizationId = val;
  }

  @Expose()
  get materialMediaId(): GenericID {
    return this._materialMediaId;
  }
  set materialMediaId(val: GenericID) {
    this._materialMediaId = val;
  }

  @Expose()
  get type(): SurveyTypeEnum {
    return this._type;
  }
  set type(val: SurveyTypeEnum) {
    this._type = val;
  }

  @Expose()
  get contentHtml(): string {
    return this._contentHtml;
  }
  set contentHtml(val: string) {
    this._contentHtml = val;
  }

  @Expose()
  get formSchema(): Nullable<Record<string, any>> {
    return this._formSchema;
  }
  set formSchema(val: Nullable<Record<string, any>>) {
    this._formSchema = val;
  }
}
