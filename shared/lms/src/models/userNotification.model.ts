import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { date } from '@iso/helpers/dateUtils';
import { Expose } from 'class-transformer';
import { IsBoolean, IsDate, IsEnum, IsInstance, IsObject, IsOptional, IsString } from 'class-validator';

import {
  UserNotificationParams,
  UserNotificationPayloadParams,
} from '@shared/lms/constants/types/userNotification.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class UserNotificationPayload {
  @IsString()
  @IsOptional()
  private _mediaId: GenericID;

  @IsString()
  private _message: string;

  @IsObject()
  @IsOptional()
  private _url: Nullable<object>;

  constructor(props: UserNotificationPayloadParams) {
    this.mediaId = props?.mediaId ?? null;
    this.message = props.message;
    this.url = props?.url ?? null;
  }

  @Expose()
  get mediaId(): GenericID {
    return this._mediaId;
  }
  set mediaId(mediaId: GenericID) {
    this._mediaId = mediaId;
  }

  @Expose()
  get message(): string {
    return this._message;
  }
  set message(message: string) {
    this._message = message;
  }

  @Expose()
  get url(): Nullable<object> {
    return this._url;
  }
  set url(uri: Nullable<object>) {
    this._url = uri;
  }
}

export class UserNotification extends BaseModel {
  @IsString()
  private _userId: GenericID;

  @IsString()
  private _organizationId: GenericID;

  @IsBoolean()
  private _isView: boolean;

  @IsBoolean()
  private _isRead: boolean;

  @IsEnum(UserNotificationTypeEnum)
  private _type: UserNotificationTypeEnum;

  @IsInstance(UserNotificationPayload)
  private _payload: UserNotificationPayload;

  @IsDate()
  private _publishedAt: Date;

  constructor(props: UserNotificationParams) {
    super(props?.id, props?.createdAt, props?.updatedAt, props?.deletedAt);
    this.userId = props.userId;
    this.organizationId = props.organizationId;
    this.type = props.type;
    this.isView = props.isView ?? false;
    this.isRead = props.isRead ?? false;
    this.payload = new UserNotificationPayload(props.payload);
    this.publishedAt = props?.publishedAt ?? date().toDate();
  }

  static async new(props: UserNotificationParams): Promise<UserNotification> {
    const entity: UserNotification = new UserNotification(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }
  set userId(userId: GenericID) {
    this._userId = userId;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }
  set organizationId(organizationId: GenericID) {
    this._organizationId = organizationId;
  }

  @Expose()
  get isView(): boolean {
    return this._isView;
  }
  set isView(isView: boolean) {
    this._isView = isView;
  }

  @Expose()
  get isRead(): boolean {
    return this._isRead;
  }
  set isRead(isRead: boolean) {
    this._isRead = isRead;
  }

  @Expose()
  get type(): UserNotificationTypeEnum {
    return this._type;
  }
  set type(type: UserNotificationTypeEnum) {
    this._type = type;
  }

  @Expose()
  get payload(): UserNotificationPayload {
    return this._payload;
  }
  set payload(payload: UserNotificationPayload) {
    this._payload = payload;
  }

  @Expose()
  get publishedAt(): Date {
    return this._publishedAt;
  }
  set publishedAt(publishedAt: Date) {
    this._publishedAt = publishedAt;
  }
}
