import { GenericID } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { <PERSON>Array, IsEnum, IsInstance, IsString, ValidateNested } from 'class-validator';

import {
  ReportHistoryStatusEnum,
  ReportHistoryDownloadFileTypeEnum,
  ReportHistoryTypeEnum,
} from '@shared/lms/constants/enums/reportHistory.enum';
import {
  CreateReportHistoryParams,
  ReportHistoryFileParams,
  ReportHistoryParams,
} from '@shared/lms/constants/types/reportHistory.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class ReportHistoryFile {
  @IsString()
  private _fileName: string;

  @IsString()
  private _filePath: string;

  constructor(props: ReportHistoryFileParams) {
    this._fileName = props.fileName;
    this._filePath = props.filePath;
  }

  @Expose()
  get fileName(): string {
    return this._fileName;
  }

  set fileName(fileName: string) {
    this._fileName = fileName;
  }

  @Expose()
  get filePath(): string {
    return this._filePath;
  }

  set filePath(filePath: string) {
    this._filePath = filePath;
  }
}

export class ReportHistory extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsString()
  private _userId: GenericID;

  @IsEnum(ReportHistoryStatusEnum)
  private _status: ReportHistoryStatusEnum;

  @IsEnum(ReportHistoryTypeEnum)
  private _type: ReportHistoryTypeEnum;

  @IsString()
  private _fileName: string;

  @IsEnum(ReportHistoryDownloadFileTypeEnum)
  private _downloadFileType: ReportHistoryDownloadFileTypeEnum;

  @IsArray()
  @IsInstance(ReportHistoryFile, { each: true })
  @ValidateNested({ each: true })
  private _files: ReportHistoryFile[];

  constructor(props: ReportHistoryParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._userId = props.userId;

    this._status = props.status;
    this._type = props.type;
    this._fileName = props.fileName;
    this._downloadFileType = props.downloadFileType;
    this._files = props.files.map((file) => new ReportHistoryFile(file));
  }

  static async new(props: CreateReportHistoryParams) {
    const entity = new ReportHistory({
      ...props,
      files: [],
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(organizationId: GenericID) {
    this._organizationId = organizationId;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }

  set userId(userId: GenericID) {
    this._userId = userId;
  }

  @Expose()
  get status(): ReportHistoryStatusEnum {
    return this._status;
  }

  set status(status: ReportHistoryStatusEnum) {
    this._status = status;
  }

  @Expose()
  get type(): ReportHistoryTypeEnum {
    return this._type;
  }

  set type(type: ReportHistoryTypeEnum) {
    this._type = type;
  }

  @Expose()
  get fileName(): string {
    return this._fileName;
  }

  set fileName(fileName: string) {
    this._fileName = fileName;
  }

  @Expose()
  get downloadFileType(): ReportHistoryDownloadFileTypeEnum {
    return this._downloadFileType;
  }

  set downloadFileType(downloadFileType: ReportHistoryDownloadFileTypeEnum) {
    this._downloadFileType = downloadFileType;
  }

  @Expose()
  get files(): ReportHistoryFile[] {
    return this._files;
  }

  set files(files: ReportHistoryFile[]) {
    this._files = files;
  }
}
