import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

import { CreateUserLoginParams, UserLoginParams } from '@shared/lms/constants/types/userLogin.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class UserLogin extends BaseModel {
  @IsString()
  private _organizationLoginProviderId: GenericID;

  @IsString()
  @IsOptional()
  private _loginProviderKey: Nullable<string>;

  @IsString()
  private _userId: GenericID;

  @IsBoolean()
  private _isEnabled: boolean;

  constructor(props: UserLoginParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationLoginProviderId = props.organizationLoginProviderId;
    this._userId = props.userId;
    this._loginProviderKey = props.loginProviderKey;
    this._isEnabled = props.isEnabled;
  }

  static async new(props: CreateUserLoginParams) {
    const entity = new UserLogin({
      loginProviderKey: props.loginProviderKey,
      isEnabled: props.isEnabled || false,
      ...props,
    });
    await entity.validate();
    return entity;
  }

  @Expose()
  get organizationLoginProviderId(): GenericID {
    return this._organizationLoginProviderId;
  }

  set organizationLoginProviderId(val: GenericID) {
    this._organizationLoginProviderId = val;
  }

  @Expose()
  get loginProviderKey(): string {
    return this._loginProviderKey;
  }

  set loginProviderKey(val: string) {
    this._loginProviderKey = val;
  }

  @Expose()
  get userId(): GenericID {
    return this._userId;
  }

  set userId(val: GenericID) {
    this._userId = val;
  }

  @Expose()
  get isEnabled(): boolean {
    return this._isEnabled;
  }

  set isEnabled(val: boolean) {
    this._isEnabled = val;
  }
}
