import { GenericID } from '@iso/constants/commonTypes';
import { Expose } from 'class-transformer';
import { IsArray, IsDate, IsString } from 'class-validator';

import { CreateRoundParams, RoundParams } from '@shared/lms/constants/types/round.type';
import { BaseModel } from '@shared/lms/core/instances/base';

export class Round extends BaseModel {
  @IsString()
  private _organizationId: GenericID;

  @IsDate()
  private _roundDate: Date;

  @IsDate()
  private _firstRegistrationDate: Date;

  @IsDate()
  private _lastRegistrationDate: Date;

  @IsArray()
  private _courseIds: GenericID[];

  @IsArray()
  private _learningPathIds: GenericID[];

  constructor(props: RoundParams) {
    const { id, createdAt, updatedAt, deletedAt } = props;
    super(id, createdAt, updatedAt, deletedAt);

    this._organizationId = props.organizationId;
    this._roundDate = props.roundDate;
    this._firstRegistrationDate = props.firstRegistrationDate;
    this._lastRegistrationDate = props.lastRegistrationDate;
    this._courseIds = props.courseIds;
    this._learningPathIds = props.learningPathIds;
  }

  static async new(props: CreateRoundParams) {
    const entity = new Round(props);
    await entity.validate();
    return entity;
  }

  @Expose()
  get roundDate(): Date {
    return this._roundDate;
  }

  set roundDate(roundDate: Date) {
    this._roundDate = roundDate;
  }

  @Expose()
  get firstRegistrationDate(): Date {
    return this._firstRegistrationDate;
  }

  set firstRegistrationDate(firstRegistrationDate: Date) {
    this._firstRegistrationDate = firstRegistrationDate;
  }

  @Expose()
  get lastRegistrationDate(): Date {
    return this._lastRegistrationDate;
  }

  set lastRegistrationDate(lastRegistrationDate: Date) {
    this._lastRegistrationDate = lastRegistrationDate;
  }

  @Expose()
  get courseIds(): GenericID[] {
    return this._courseIds;
  }

  set courseIds(courseIds: GenericID[]) {
    this._courseIds = courseIds;
  }
  @Expose()
  get learningPathIds(): GenericID[] {
    return this._learningPathIds;
  }

  set learningPathIds(learningPathIds: GenericID[]) {
    this._learningPathIds = learningPathIds;
  }
  @Expose()
  get organizationId(): GenericID {
    return this._organizationId;
  }

  set organizationId(organizationId: GenericID) {
    this._organizationId = organizationId;
  }
}
