<div class="title-text">
  ยินดีต้อนรับสู่ {{ organizationName }} <br/> ตั้งรหัสผ่านเพื่อเริ่มต้นใช้งานบัญชีของคุณ
</div>
<div class="to-text">เรียน คุณ{{ fullName }}</div>
<div class="description" style="margin-top: 8px">
  กรุณาตั้งรหัสผ่านเพื่อเริ่มต้นใช้งานบัญชีของคุณ โดยคลิกที่ปุ่ม “ตั้งรหัสผ่านครั้งแรก”
  <br/> และทำตามขั้นตอนที่แนะนำเพื่อเริ่มต้นใช้งานบัญชีของคุณ
</div>
<div style="margin-top: 40px">
  <img alt="banner" src="{{ instructionBanner }}" style="width: 100%"/>
</div>

{% if planPackageLicenseTextList and planPackageLicenseTextList | length > 0 %}
  <div style="text-align: center; margin-top: 40px;">
    <div style="display: inline-block; border: 1px solid #ccc; border-radius: 8px; width: 600px; margin-top: 40px; text-align: left;">
      <div style="padding: 16px; font-size: 16px; font-weight: bold;">
        ข้อมูลแพ็กเกจที่ได้รับ
      </div>
      <div style="border-top: 1px solid #ccc; padding: 16px 24px 24px 24px;">
        {% for planPackageLicenseText in planPackageLicenseTextList %}
          <div style="margin-top: 8px; font-weight: bold;">
            {{ planPackageLicenseText.planName }}
          </div>
              
          {% if planPackageLicenseText.mainPackage %}
            <div> {{ planPackageLicenseText.mainPackage.name }} </div>
            <div style="color: #6a6a6a;"> วันที่เริ่ม-สิ้นสุดการใช้ไลเซนส์: {{ planPackageLicenseText.mainPackage.dateText }} </div>
          {% endif %}

          {% if planPackageLicenseText.subPackages and planPackageLicenseText.subPackages | length > 0 %}
            <ul style="margin: 8px 0 20px 20px; padding: 0;">
              {% for subPackage in planPackageLicenseText.subPackages %}
                <li>
                  <span>
                    <span style="font-weight: bold;"> {{ subPackage.name }} </span> 
                    ({{ subPackage.type }})
                  </span>
                  <div style="color: #6a6a6a;"> วันที่เริ่ม-สิ้นสุดการใช้ไลเซนส์: {{ subPackage.dateText }} </div>
                </li>
              {% endfor %}
            </ul>
          {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
{% endif %}

<div style="margin-top: 50px">
  <div style="text-align: center; font-size: 18px">
    <div class="button-title">ชื่อบัญชีผู้ใช้ :
      <span>{{ username }}</span>
    </div>
  </div>
  <div style="margin-top: 16px; padding: 0px; gap: 16px">
    <div class="button-container bg-primary">
      <a href="{{ url }}" class="button-text-link">
        ตั้งรหัสผ่านครั้งแรก
      </a>
    </div>
  </div>
  <div class="button-extra-container">
    ตั้งรหัสผ่านครั้งแรกของคุณโดยการคลิก
    <a href="{{ url }}" style="text-decoration: none;" class="color-text-primary" title="รหัสผ่านครั้งแรก">
      “<span style="text-decoration: underline">ตั้งรหัสผ่านครั้งแรก</span>”
    </a>
  </div>
</div>