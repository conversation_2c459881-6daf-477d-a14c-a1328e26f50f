<div class="title-text">แพ็กเกจของคุณสามารถใช้งานได้</div>
<div class="to-text">เรียน คุณ{{ fullName }}</div>
<div class="description" style="margin-top: 8px">
  <div class="color-text">
    คุณได้รับไลเซนส์ของแพ็กเกจ และสามารถเข้าเรียนได้แล้ว กรุณาคลิกที่ปุ่ม “เข้าสู่ระบบ” เพื่อเข้าใช้งานระบบ
    <br/> หากไม่สามารถจำรหัสผ่านได้ กรุณาคลิกที่ปุ่ม “ลืมรหัสผ่าน” ในหน้าเข้าสู่ระบบเพื่อตั้งรหัสผ่านใหม่อีกครั้ง
    <br/> หรือหากต้องการสอบถามข้อมูลเพิ่มเติม กรุณาติดต่อเจ้าหน้าที่
  </div>

  <div style="text-align: center; margin-top: 40px;">
    <div style="display: inline-block; border: 1px solid #ccc; border-radius: 8px; width: 600px; margin-top: 40px; text-align: left;">
      <div style="padding: 16px; font-size: 16px; font-weight: bold;">
        ข้อมูลแพ็กเกจที่ได้รับ
      </div>
      <div style="border-top: 1px solid #ccc; padding: 16px 24px 24px 24px;">
        {% if planPackageLicenseTextList and planPackageLicenseTextList | length > 0 %}
          {% for planPackageLicenseText in planPackageLicenseTextList %}
            <div style="margin-top: 8px; font-weight: bold; font-size: 14px;">
              {{ planPackageLicenseText.planName }}
            </div>
            
            {% if planPackageLicenseText.mainPackage %}
              <div> {{ planPackageLicenseText.mainPackage.name }} </div>
              <div style="color: #6a6a6a;"> วันที่เริ่ม-สิ้นสุดการใช้ไลเซนส์: {{ planPackageLicenseText.mainPackage.dateText }} </div>
            {% endif %}

            {% if planPackageLicenseText.subPackages and planPackageLicenseText.subPackages | length > 0 %}
              <ul style="margin: 8px 0 20px 20px; padding: 0;">
                {% for subPackage in planPackageLicenseText.subPackages %}
                  <li>
                    <span> {{ subPackage.name }} </span> 
                    <span style="color: #6a6a6a;"> ({{ subPackage.type }}) </span> 
                    <div style="color: #6a6a6a;"> วันที่เริ่ม-สิ้นสุดการใช้ไลเซนส์: {{ subPackage.dateText }} </div>
                  </li>
                {% endfor %}
              </ul>
            {% endif %}
          {% endfor %}
        {% endif %}
      </div>
    </div>
  </div>
</div>

<div style="margin-top: 50px; margin-bottom: 50px">
  <div style="margin-top: 16px; padding: 0px; gap: 16px">
    <div class="button-container bg-primary">
      <a href="{{ url }}" class="button-text-link">
        เข้าสู่ระบบ
      </a>
    </div>
  </div>
  <div class="button-extra-container">
    หรือดำเนินการโดยการคลิก
    <a href="{{ url }}" style="text-decoration: none;" class="color-text-primary" title="เข้าสู่ระบบ">
      “<span style="text-decoration: underline">เข้าสู่ระบบ</span>”
    </a>
  </div>
</div>
