import { IEmailTemplateBuilder } from '../interface/emailTemplateBuilder.interface';
import { NunjucksPathMapping, TemplateNameEnum, TemplateNameEnumType } from '../constants/template';

import {
  AdditionalAttachmentParams,
  DiscussionBoardNotificationParams,
  EnrollmentApprovedParams,
  EnrollmentCancelParams,
  EnrollmentCertificateParams,
  EnrollmentCompleteAndCertificateParams,
  EnrollmentRejectParams,
  EnrollmentWelcomeParams,
  FirstPasswordParams,
  ForgotPasswordParams,
  SftpImportResultParams,
  PreAssignContentParams,
  PreAssignContentCompleteParams,
  PreAssignContentCancelParams,
  InActiveUserParams,
  ActiveUserParams,
  PreAssignLearningPathParams,
  AssignLearningPathParams,
  LearningPathEnrollmentCompleteParams,
  LearningPathEnrollmentCertificateParams,
  LearningPathEnrollmentExpiredParams,
  LearningPathEnrollmentCompleteAndCertificateParams,
  LearningPathExtendExpireDateParams,
  PreEnrollmentRejectParams,
  PreEnrollmentWelcomeParams,
  PromoteContentParams,
  RemindClassroomRoundStartParams,
  EditClassroomRoundContentParams,
  CancelClassroomRoundContentParams,
  CancelClassroomWaitingParams,
  ClassroomRegisterSuccessParams,
  ClassroomRegisterWaitingParams,
  ChangeClassroomRoundParams,
  RemindEnrollmentExpiredParams,
  CancelLearningPathPreEnrollmentParams,
  LearningPathSelfEnrollmentParams,
  LearningPathSelfPreEnrollmentParams,
  CancelCourseApprovalParams,
  CancelCoursePassResultParams,
  CancelLearningPathCertificateParams,
  AssignEnrollmentCompletedParams,
  ResetLearningProgressParams,
  AssignPlanPackageLicenseParams,
  PlanPackageLicenseExpiredParams,
  AchievementCompletedParams,
  AchievementAdditionalCompletedParams,
  BadgeAdditionalCompletedParams,
} from '../constants/mailer';
import { EmailTemplateBuilder } from '../services/emailTemplateBuilder';

type Params =
  | AdditionalAttachmentParams
  | DiscussionBoardNotificationParams
  | EnrollmentApprovedParams
  | EnrollmentCancelParams
  | EnrollmentCertificateParams
  | EnrollmentCompleteAndCertificateParams
  | EnrollmentRejectParams
  | EnrollmentWelcomeParams
  | PreEnrollmentWelcomeParams
  | PreEnrollmentRejectParams
  | FirstPasswordParams
  | ForgotPasswordParams
  | SftpImportResultParams
  | PreAssignContentParams
  | PreAssignContentCompleteParams
  | PreAssignContentCancelParams
  | InActiveUserParams
  | ActiveUserParams
  | PreAssignLearningPathParams
  | AssignLearningPathParams
  | LearningPathEnrollmentCompleteParams
  | LearningPathEnrollmentCertificateParams
  | LearningPathEnrollmentExpiredParams
  | LearningPathEnrollmentCompleteAndCertificateParams
  | LearningPathExtendExpireDateParams
  | RemindClassroomRoundStartParams
  | ClassroomRegisterWaitingParams
  | EditClassroomRoundContentParams
  | CancelClassroomRoundContentParams
  | CancelClassroomWaitingParams
  | ClassroomRegisterSuccessParams
  | ChangeClassroomRoundParams
  | RemindEnrollmentExpiredParams
  | CancelLearningPathPreEnrollmentParams
  | LearningPathSelfPreEnrollmentParams
  | LearningPathSelfEnrollmentParams
  | CancelCourseApprovalParams
  | CancelCoursePassResultParams
  | CancelLearningPathCertificateParams
  | AssignEnrollmentCompletedParams
  | ResetLearningProgressParams
  | AssignPlanPackageLicenseParams
  | PlanPackageLicenseExpiredParams
  | AchievementCompletedParams
  | AchievementAdditionalCompletedParams;

export class EmailTemplateFactory {
  static createTemplate(name: TemplateNameEnumType): IEmailTemplateBuilder<Params> {
    const path = NunjucksPathMapping[name];

    switch (name) {
      case TemplateNameEnum.ADDITIONAL_ATTACHMENT:
        return new EmailTemplateBuilder<AdditionalAttachmentParams>(__dirname, path);
      case TemplateNameEnum.DISCUSSION_BOARD_NOTIFICATION:
        return new EmailTemplateBuilder<DiscussionBoardNotificationParams>(__dirname, path);
      case TemplateNameEnum.ENROLLMENT_APPROVED:
        return new EmailTemplateBuilder<EnrollmentApprovedParams>(__dirname, path);
      case TemplateNameEnum.ENROLLMENT_CANCEL:
        return new EmailTemplateBuilder<EnrollmentCancelParams>(__dirname, path);
      case TemplateNameEnum.ENROLLMENT_CERTIFICATE:
        return new EmailTemplateBuilder<EnrollmentCertificateParams>(__dirname, path);
      case TemplateNameEnum.ENROLLMENT_COMPLETE_AND_CERTIFICATE:
        return new EmailTemplateBuilder<EnrollmentCompleteAndCertificateParams>(__dirname, path);
      case TemplateNameEnum.ENROLLMENT_REJECT:
        return new EmailTemplateBuilder<EnrollmentRejectParams>(__dirname, path);
      case TemplateNameEnum.ENROLLMENT_WELCOME:
        return new EmailTemplateBuilder<EnrollmentWelcomeParams>(__dirname, path);
      case TemplateNameEnum.PRE_ENROLLMENT_WELCOME:
        return new EmailTemplateBuilder<PreEnrollmentWelcomeParams>(__dirname, path);
      case TemplateNameEnum.PRE_ENROLLMENT_REJECT:
        return new EmailTemplateBuilder<PreEnrollmentRejectParams>(__dirname, path);
      case TemplateNameEnum.FIRST_PASSWORD:
        return new EmailTemplateBuilder<FirstPasswordParams>(__dirname, path);
      case TemplateNameEnum.FORGOT_PASSWORD:
        return new EmailTemplateBuilder<ForgotPasswordParams>(__dirname, path);
      case TemplateNameEnum.SFTP_IMPORT_RESULT:
        return new EmailTemplateBuilder<SftpImportResultParams>(__dirname, path);
      case TemplateNameEnum.PRE_ASSIGN_CONTENT:
        return new EmailTemplateBuilder<PreAssignContentParams>(__dirname, path);
      case TemplateNameEnum.PRE_ASSIGN_CONTENT_COMPLETE:
        return new EmailTemplateBuilder<PreAssignContentCompleteParams>(__dirname, path);
      case TemplateNameEnum.PRE_ASSIGN_CONTENT_CANCELED:
        return new EmailTemplateBuilder<PreAssignContentCancelParams>(__dirname, path);
      case TemplateNameEnum.IN_ACTIVE_USER:
        return new EmailTemplateBuilder<InActiveUserParams>(__dirname, path);
      case TemplateNameEnum.ACTIVE_USER:
        return new EmailTemplateBuilder<ActiveUserParams>(__dirname, path);
      case TemplateNameEnum.LEARNING_PATH_ENROLLMENT_COMPLETE:
        return new EmailTemplateBuilder<LearningPathEnrollmentCompleteParams>(__dirname, path);
      case TemplateNameEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATE:
        return new EmailTemplateBuilder<LearningPathEnrollmentCertificateParams>(__dirname, path);
      case TemplateNameEnum.LEARNING_PATH_ENROLLMENT_EXPIRED:
        return new EmailTemplateBuilder<LearningPathEnrollmentExpiredParams>(__dirname, path);
      case TemplateNameEnum.LEARNING_PATH_ENROLLMENT_COMPLETE_AND_CERTIFICATE:
        return new EmailTemplateBuilder<LearningPathEnrollmentCompleteAndCertificateParams>(__dirname, path);
      case TemplateNameEnum.LEARNING_PATH_EXTEND_EXPIRE_DATE:
        return new EmailTemplateBuilder<LearningPathExtendExpireDateParams>(__dirname, path);
      case TemplateNameEnum.CANCELED_LEARNING_PATH_PRE_ENROLLMENT:
        return new EmailTemplateBuilder<CancelLearningPathPreEnrollmentParams>(__dirname, path);
      case TemplateNameEnum.LEARNING_PATH_SELF_ENROLLMENT:
        return new EmailTemplateBuilder<LearningPathSelfEnrollmentParams>(__dirname, path);
      case TemplateNameEnum.LEARNING_PATH_SELF_PRE_ENROLLMENT:
        return new EmailTemplateBuilder<LearningPathSelfPreEnrollmentParams>(__dirname, path);
      case TemplateNameEnum.PRE_ASSIGN_LEARNING_PATH:
        return new EmailTemplateBuilder<PreAssignLearningPathParams>(__dirname, path);
      case TemplateNameEnum.ASSIGN_LEARNING_PATH:
        return new EmailTemplateBuilder<AssignLearningPathParams>(__dirname, path);
      case TemplateNameEnum.PROMOTE_CONTENT:
        return new EmailTemplateBuilder<PromoteContentParams>(__dirname, path);
      case TemplateNameEnum.REMIND_CLASSROOM_ROUND_START:
        return new EmailTemplateBuilder<RemindClassroomRoundStartParams>(__dirname, path);
      case TemplateNameEnum.EDIT_CLASSROOM_ROUND:
        return new EmailTemplateBuilder<EditClassroomRoundContentParams>(__dirname, path);
      case TemplateNameEnum.CANCEL_CLASSROOM_ROUND:
        return new EmailTemplateBuilder<CancelClassroomRoundContentParams>(__dirname, path);
      case TemplateNameEnum.CANCELED_CLASSROOM_WAITING:
        return new EmailTemplateBuilder<CancelClassroomWaitingParams>(__dirname, path);
      case TemplateNameEnum.CLASSROOM_REGISTER_SUCCESS:
        return new EmailTemplateBuilder<ClassroomRegisterSuccessParams>(__dirname, path);
      case TemplateNameEnum.CLASSROOM_REGISTER_WAITING:
        return new EmailTemplateBuilder<ClassroomRegisterWaitingParams>(__dirname, path);
      case TemplateNameEnum.CHANGE_CLASSROOM_ROUND:
        return new EmailTemplateBuilder<ChangeClassroomRoundParams>(__dirname, path);
      case TemplateNameEnum.REMIND_ENROLLMENT_EXPIRED:
        return new EmailTemplateBuilder<RemindEnrollmentExpiredParams>(__dirname, path);
      case TemplateNameEnum.CANCEL_COURSE_APPROVAL:
        return new EmailTemplateBuilder<CancelCourseApprovalParams>(__dirname, path);
      case TemplateNameEnum.CANCEL_COURSE_PASS_RESULT:
        return new EmailTemplateBuilder<CancelCoursePassResultParams>(__dirname, path);
      case TemplateNameEnum.CANCEL_LEARNING_PATH_CERTIFICATE:
        return new EmailTemplateBuilder<CancelLearningPathCertificateParams>(__dirname, path);
      case TemplateNameEnum.ASSIGN_ENROLLMENT_COMPLETED:
        return new EmailTemplateBuilder<AssignEnrollmentCompletedParams>(__dirname, path);
      case TemplateNameEnum.RESET_LEARNING_PROGRESS:
        return new EmailTemplateBuilder<ResetLearningProgressParams>(__dirname, path);
      case TemplateNameEnum.ACHIEVEMENT_COMPLETED:
        return new EmailTemplateBuilder<ResetLearningProgressParams>(__dirname, path);
      case TemplateNameEnum.ASSIGN_PLAN_PACKAGE_LICENSE_EXISTING_USER:
        return new EmailTemplateBuilder<AssignPlanPackageLicenseParams>(__dirname, path);
      case TemplateNameEnum.PLAN_PACKAGE_LICENSE_EXPIRED:
        return new EmailTemplateBuilder<PlanPackageLicenseExpiredParams>(__dirname, path);
      case TemplateNameEnum.ACHIEVEMENT_COMPLETED:
        return new EmailTemplateBuilder<AchievementCompletedParams>(__dirname, path);
      case TemplateNameEnum.ACHIEVEMENT_ADDITIONAL_COMPLETED:
        return new EmailTemplateBuilder<AchievementAdditionalCompletedParams>(__dirname, path);
      case TemplateNameEnum.BADGE_ADDITIONAL_COMPLETED:
        return new EmailTemplateBuilder<BadgeAdditionalCompletedParams>(__dirname, path);

      default:
        throw new Error(`Template ${name} is not implement`);
    }
  }
}
