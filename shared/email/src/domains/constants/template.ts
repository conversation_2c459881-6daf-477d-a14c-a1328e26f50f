export const TemplateNameEnum = {
  BASE_LAYOUT_TEMPLATE: 'baseLayoutTemplate',
  DISCUSSION_BOARD_NOTIFICATION: 'discussionBoardNotification',
  ADDITIONAL_ATTACHMENT: 'additionalAttachment',
  ENROLLMENT_APPROVED: 'enrollmentApproved',
  ENROLLMENT_CANCEL: 'enrollmentCancel',
  ENROLLMENT_CERTIFICATE: 'enrollmentCertificate',
  ENROLLMENT_COMPLETE_AND_CERTIFICATE: 'enrollmentCompleteAndCertificate',
  ENROLLMENT_REJECT: 'enrollmentReject',
  ENROLLMENT_WELCOME: 'enrollmentWelcome',
  FIRST_PASSWORD: 'firstPassword',
  FORGOT_PASSWORD: 'forgotPassword',
  PRE_ENROLLMENT_REJECT: 'preEnrollmentReject',
  PRE_ENROLLMENT_WELCOME: 'preEnrollmentWelcome',
  SFTP_IMPORT_RESULT: 'sftpImportResult',
  PRE_ASSIGN_CONTENT: 'preAssignContent',
  PRE_ASSIGN_CONTENT_COMPLETE: 'preAssignContentComplete',
  PRE_ASSIGN_CONTENT_CANCELED: 'preAssignContentCanceled',
  IN_ACTIVE_USER: 'inActiveUser',
  ACTIVE_USER: 'activeUser',
  LEARNING_PATH_ENROLLMENT_COMPLETE: 'learningPathEnrollmentComplete',
  LEARNING_PATH_ENROLLMENT_CERTIFICATE: 'learningPathEnrollmentCertificate',
  LEARNING_PATH_ENROLLMENT_EXPIRED: 'learningPathEnrollmentExpired',
  LEARNING_PATH_ENROLLMENT_COMPLETE_AND_CERTIFICATE: 'learningPathEnrollmentCompleteAndCertificate',
  LEARNING_PATH_EXTEND_EXPIRE_DATE: 'learningPathExtendExpireDate',
  LEARNING_PATH_SELF_ENROLLMENT: 'learningPathSelfEnrollment',
  LEARNING_PATH_SELF_PRE_ENROLLMENT: 'learningPathSelfPreEnrollment',
  CANCELED_LEARNING_PATH_PRE_ENROLLMENT: 'cancelLearningPathPreEnrollment',
  PRE_ASSIGN_LEARNING_PATH: 'preAssignLearningPath',
  ASSIGN_LEARNING_PATH: 'assignLearningPath',
  PROMOTE_CONTENT: 'promoteNotification',
  REMIND_CLASSROOM_ROUND_START: 'remindClassroomRoundStart',
  EDIT_CLASSROOM_ROUND: 'editClassroomRound',
  CANCEL_CLASSROOM_ROUND: 'cancelClassroomRound',
  CANCELED_CLASSROOM_WAITING: 'cancelClassroomWaiting',
  CLASSROOM_REGISTER_SUCCESS: 'classroomRegisterSuccess',
  CLASSROOM_REGISTER_WAITING: 'classroomRegisterWaiting',
  CHANGE_CLASSROOM_ROUND: 'changeClassroomRound',
  REMIND_ENROLLMENT_EXPIRED: 'remindEnrollmentExpired',
  RESET_LEARNING_PROGRESS: 'resetLearningProgress',
  CANCEL_COURSE_APPROVAL: 'cancelCourseApproval',
  CANCEL_COURSE_PASS_RESULT: 'cancelCoursePassResult',
  CANCEL_LEARNING_PATH_CERTIFICATE: 'cancelLearningPathCertificate',
  ASSIGN_ENROLLMENT_COMPLETED: 'assignEnrollmentCompleted',
  ACHIEVEMENT_COMPLETED: 'achievementCompleted',
  ASSIGN_PLAN_PACKAGE_LICENSE_EXISTING_USER: 'assignPlanPackageLicenseExistingUser',
  PLAN_PACKAGE_LICENSE_EXPIRED: 'planPackageLicenseExpired',
  ACHIEVEMENT_ADDITIONAL_COMPLETED: 'achievementAdditionalCompleted',
  BADGE_ADDITIONAL_COMPLETED: 'badgeAdditionalCompleted',
} as const;

export const NunjucksPathMapping: Record<TemplateNameEnumType, string> = {
  [TemplateNameEnum.BASE_LAYOUT_TEMPLATE]: '/templates/core/emailLayoutTemplate.njk',
  [TemplateNameEnum.DISCUSSION_BOARD_NOTIFICATION]: '/templates/discussionBoardNotification.njk',
  [TemplateNameEnum.ADDITIONAL_ATTACHMENT]: '/templates/additionalAttachment.njk',
  [TemplateNameEnum.ENROLLMENT_APPROVED]: '/templates/enrollmentApproved.njk',
  [TemplateNameEnum.ENROLLMENT_CANCEL]: '/templates/enrollmentCancel.njk',
  [TemplateNameEnum.ENROLLMENT_CERTIFICATE]: '/templates/enrollmentCertificate.njk',
  [TemplateNameEnum.ENROLLMENT_COMPLETE_AND_CERTIFICATE]: '/templates/enrollmentCompleteAndCertificate.njk',
  [TemplateNameEnum.ENROLLMENT_REJECT]: '/templates/enrollmentReject.njk',
  [TemplateNameEnum.ENROLLMENT_WELCOME]: '/templates/enrollmentWelcome.njk',
  [TemplateNameEnum.FIRST_PASSWORD]: '/templates/firstPassword.njk',
  [TemplateNameEnum.FORGOT_PASSWORD]: '/templates/forgotPassword.njk',
  [TemplateNameEnum.PRE_ENROLLMENT_REJECT]: '/templates/preEnrollmentReject.njk',
  [TemplateNameEnum.PRE_ENROLLMENT_WELCOME]: '/templates/preEnrollmentWelcome.njk',
  [TemplateNameEnum.SFTP_IMPORT_RESULT]: '/templates/sftpImportResult.njk',
  [TemplateNameEnum.PRE_ASSIGN_CONTENT]: '/templates/preAssignContent.njk',
  [TemplateNameEnum.PRE_ASSIGN_CONTENT_COMPLETE]: '/templates/preAssignContentComplete.njk',
  [TemplateNameEnum.PRE_ASSIGN_CONTENT_CANCELED]: '/templates/preAssignContentCanceled.njk',
  [TemplateNameEnum.IN_ACTIVE_USER]: '/templates/inActiveUser.njk',
  [TemplateNameEnum.ACTIVE_USER]: '/templates/activeUser.njk',
  [TemplateNameEnum.LEARNING_PATH_ENROLLMENT_COMPLETE]: '/templates/learningPathEnrollmentComplete.njk',
  [TemplateNameEnum.LEARNING_PATH_ENROLLMENT_CERTIFICATE]: '/templates/learningPathEnrollmentCertificate.njk',
  [TemplateNameEnum.LEARNING_PATH_ENROLLMENT_EXPIRED]: '/templates/learningPathEnrollmentExpired.njk',
  [TemplateNameEnum.LEARNING_PATH_ENROLLMENT_COMPLETE_AND_CERTIFICATE]:
    '/templates/learningPathEnrollmentCompleteAndCertificate.njk',
  [TemplateNameEnum.LEARNING_PATH_EXTEND_EXPIRE_DATE]: '/templates/learningPathExtendExpireDate.njk',
  [TemplateNameEnum.LEARNING_PATH_SELF_ENROLLMENT]: '/templates/learningPathSelfEnrollment.njk',
  [TemplateNameEnum.LEARNING_PATH_SELF_PRE_ENROLLMENT]: '/templates/learningPathSelfPreEnrollment.njk',
  [TemplateNameEnum.CANCELED_LEARNING_PATH_PRE_ENROLLMENT]: '/templates/cancelLearningPathPreEnrollment.njk',
  [TemplateNameEnum.PRE_ASSIGN_LEARNING_PATH]: '/templates/preAssignLearningPath.njk',
  [TemplateNameEnum.ASSIGN_LEARNING_PATH]: '/templates/assignLearningPath.njk',
  [TemplateNameEnum.PROMOTE_CONTENT]: '/templates/promoteContent.njk',
  [TemplateNameEnum.REMIND_CLASSROOM_ROUND_START]: '/templates/remindClassroomRoundStart.njk',
  [TemplateNameEnum.EDIT_CLASSROOM_ROUND]: '/templates/editClassroomRound.njk',
  [TemplateNameEnum.CANCEL_CLASSROOM_ROUND]: '/templates/cancelClassroomRound.njk',
  [TemplateNameEnum.CANCELED_CLASSROOM_WAITING]: '/templates/cancelClassroomWaiting.njk',
  [TemplateNameEnum.CLASSROOM_REGISTER_SUCCESS]: '/templates/classroomRegisterSuccess.njk',
  [TemplateNameEnum.CLASSROOM_REGISTER_WAITING]: '/templates/classroomRegisterWaiting.njk',
  [TemplateNameEnum.CHANGE_CLASSROOM_ROUND]: '/templates/changeClassroomRound.njk',
  [TemplateNameEnum.REMIND_ENROLLMENT_EXPIRED]: '/templates/remindEnrollmentExpired.njk',
  [TemplateNameEnum.RESET_LEARNING_PROGRESS]: '/templates/resetLearningProgress.njk',
  [TemplateNameEnum.CANCEL_COURSE_APPROVAL]: '/templates/cancelCourseApproval.njk',
  [TemplateNameEnum.CANCEL_COURSE_PASS_RESULT]: '/templates/cancelCoursePassResult.njk',
  [TemplateNameEnum.CANCEL_LEARNING_PATH_CERTIFICATE]: '/templates/cancelLearningPathCertificate.njk',
  [TemplateNameEnum.ASSIGN_ENROLLMENT_COMPLETED]: '/templates/assignEnrollmentCompleted.njk',
  [TemplateNameEnum.ACHIEVEMENT_COMPLETED]: '/templates/achievementCompleted.njk',
  [TemplateNameEnum.ASSIGN_PLAN_PACKAGE_LICENSE_EXISTING_USER]: '/templates/assignPlanPackageLicenseExistingUser.njk',
  [TemplateNameEnum.PLAN_PACKAGE_LICENSE_EXPIRED]: '/templates/planPackageLicenseExpired.njk',
  [TemplateNameEnum.ACHIEVEMENT_ADDITIONAL_COMPLETED]: '/templates/achievementAdditionalCompleted.njk',
  [TemplateNameEnum.BADGE_ADDITIONAL_COMPLETED]: '/templates/badgeAdditionalCompleted.njk',
};

type Keys = keyof typeof TemplateNameEnum;
export type TemplateNameEnumType = (typeof TemplateNameEnum)[Keys];
