import { Nullable } from './common';

export type EmailThemeTemplate = {
  logo: string;
  mainBanner: string;
  colorPrimary: string;
  emailSignature?: string;
};

export type AdditionalAttachmentParams = {
  title: string;
  fullName: string;
  descriptionLine1: Nullable<string>;
  descriptionLine2: Nullable<string>;
  descriptionLine3: Nullable<string>;
  additional: Nullable<string>;
  attachmentURL: string;
  buttonWidth: string;
  buttonText: string;
};

export type LatestAuthor = {
  salute: string;
  fullName: string;
};

export type Author = {
  avatar?: string;
  fullName?: string;
};

export type ReplyParams = {
  author: Author;
  replyDate: string;
  content: string;
};

export type BuildNotificationAchievementPayloadParams = {
  name: string;
  badges: {
    name: string;
  }[];
  certificate?: { name: string; certificatePDFUrl?: string; certificateCode?: string; certificateUrl?: string };
};

export type DiscussionBoardNotificationParams = {
  subjectAction: string;
  salutation: string;
  description: string;
  author: Author;
  content: string;
  buttonUrl: string;
  buttonWidth: string;
  buttonText: string;
  replies: ReplyParams[];
  createdAt: string;
};

export type EnrollmentApprovedParams = {
  fullName: string;
  isDeduct: boolean;
  isDeductApproved: boolean;
  attachmentURL: string;
  description: string;
  operationExpiredDate: string;
};

export type EnrollmentCancelParams = {
  fullName: string;
  courseName: string;
  courseDetail: string;
  isBundle: boolean;
};

export type EnrollmentCertificateParams = {
  fullName: string;
  courseName: string;
  refName: string;
  isRegular: boolean;
  operationExpiredDate: string;
  certificateURL: string;
  colorPrimary: string;
};

export type EnrollmentCompleteAndCertificateParams = {
  fullName: string;
  courseName: string;
  refName: string;
  isDeduct: boolean;
  isDeductApproved: boolean;
  attachmentURL: string;
};

export type EnrollmentRejectParams = {
  courseName: string;
  fullName: string;
};

export type EnrollmentWelcomeParams = {
  fullName: string;
  description: string;
  courseImage: string;
  courseURL: string;
  isOIC4Plus: boolean;
  isRegular: boolean;
  expiredDateTime: string;
  isEnableCriteria: boolean;
  criteriaList?: Nullable<string[]>;
};

export type FirstPasswordParams = {
  colorPrimary: string;
  organizationName: string;
  fullName: string;
  instructionBanner: string;
  username: string;
  url: string;
  planPackageLicenseTextList?: object[];
};

export type ForgotPasswordParams = {
  fullName: string;
  url: string;
};

export type PreEnrollmentRejectParams = {
  fullName: string;
  courseName: string;
  startDate: string;
  endDate: string;
};

export type PreEnrollmentWelcomeParams = {
  fullName: string;
  courseName: string;
  startDate: string;
  endDate: string;
  courseURL: string;
};

export enum SftpImportResultCategoryEnum {
  CREATE = 'create',
  UPDATE = 'update',
}

export enum SftpImportResultStatusEnum {
  SUCCESS = 'success',
  FAILED = 'failed',
}

export type SftpImportResultParams = {
  filename: string;
  category?: SftpImportResultCategoryEnum;
  status?: SftpImportResultStatusEnum;
  total: number;
  totalSuccess: number;
  totalFailed: number;
  url: string;
  error?: Record<string, string>;
};

export type PreAssignContentParams = {
  title: string;
  fullName: string;
  descriptionLine1: Nullable<string>;
  descriptionLine2: Nullable<string>;
  buttonUrl: string;
  buttonWidth: string;
  buttonText: string;
};

export type PreAssignContentCompleteParams = {
  title: string;
  fullName: string;
  descriptionLine1: Nullable<string>;
  descriptionLine2: Nullable<string>;
  buttonUrl: string;
  buttonWidth: string;
  buttonText: string;
};

export type PreAssignContentCancelParams = {
  title: string;
  fullName: string;
  descriptionLine1: Nullable<string>;
  descriptionLine2: Nullable<string>;
  buttonUrl: string;
  buttonWidth: string;
  buttonText: string;
};

export type InActiveUserParams = {
  fullName: string;
};

export type ActiveUserParams = {
  fullName: string;
  url: string;
};

export type CancelLearningPathPreEnrollmentParams = {
  fullName: string;
  description: string;
  buttonWidth: string;
  buttonUrl: string;
  buttonText: string;
};

export type LearningPathEnrollmentCompleteParams = {
  fullName: string;
  learningPathName: string;
  learningPathURL: string;
};

export type LearningPathEnrollmentCertificateParams = {
  fullName: string;
  learningPathName: string;
  refName: string;
  certificateURL: string;
};

export type LearningPathEnrollmentCompleteAndCertificateParams = LearningPathEnrollmentCertificateParams;

export type LearningPathEnrollmentExpiredParams = LearningPathEnrollmentCompleteParams;
export type LearningPathExtendExpireDateParams = {
  fullName: string;
  learningPathName: string;
  courseNames: string[];
  expiredDate: Date;
  url: string;
};

export type LearningPathSelfEnrollmentParams = {
  title: string;
  description: string;
  fullName: string;
  learningPathName: string;
  learningPathCode: string;
  courseNames: string[];
  startDate: Date;
  expiredDate: Date;
  learningPathImage: string;
  learningPathDetailUrl: string;
  buttonName: string;
};

export type LearningPathSelfPreEnrollmentParams = {
  title: string;
  fullName: string;
  learningPathName: string;
  learningPathCode: string;
  courseNames: string[];
  startDate: Date;
  expiredDate: Date;
  learningPathImage: string;
  learningPathDetailUrl: string;
};

export type course = {
  name: string;
};

export type PreAssignLearningPathParams = {
  fullName: string;
  learningPathName: string;
  courseNames: string[];
  startDate: string;
  expiredDate: string;
  learningPathImage: string;
  learningPathDetailUrl: string;
};

export type AssignLearningPathParams = {
  title: string;
  fullName: string;
  learningPathName: string;
  courseNames: string[];
  isRound: boolean;
  startDate: string;
  expiredDate: Nullable<string>;
  buttonName: string;
  description: string;
  description2: Nullable<string>;
  learningPathImage: string;
  learningPathDetailUrl: string;
};

export type PromoteContentParams = {
  title: string;
  description: string;
  fullName: string;
  contents: {
    imageUrl: string;
    title: string;
    subtitle1: string[];
    subtitle2: string;
    url: string;
  }[][];
  url: string;
  textButton: string;
};

export type EditClassroomRoundContentParams = {
  title: string;
  description: string;
  fullName: string;
  contents: Array<{
    imageUrl: string;
    title: string;
    subtitle1: string[];
    subtitle2: string;
    url: string;
  }>;
  url: string;
  textButton: string;
};

export type CancelClassroomRoundContentParams = {
  title: string;
  description: string;
  fullName: string;
  contents: Array<{
    imageUrl: string;
    title: string;
    subtitle1: string[];
    subtitle2: string;
    url: string;
  }>;
  url: string;
  textButton: string;
};

export type CancelClassroomWaitingParams = {
  fullName: string;
  classroomName: string;
  courseName: string;
  courseImage: string;
  courseUrl: string;
  roundDateText: string;
  classroomType: string;
  classroomLocation: string;
  instructors: string;
};

export type RemindClassroomRoundStartParams = {
  fullName: string;
  classroomName: string;
  courseName: string;
  courseImage: string;
  courseUrl: string;
  roundDateText: string;
  classroomType: string;
  classroomLocation: string;
  instructors: string;
  waitingNo?: number;
};

export type ClassroomRegisterSuccessParams = {
  fullName: string;
  enrollBy: string;
  classroomName: string;
  courseName: string;
  courseImage: string;
  courseCode: string;
  roundDate: string;
  classroomType: string;
  classroomLocation: string;
  instructors: string;
};

export type ClassroomRegisterWaitingParams = {
  fullName: string;
  waitList: number;
  classroomName: string;
  courseName: string;
  courseImage: string;
  courseCode: string;
  roundDate: string;
  classroomType: string;
  classroomLocation: string;
  instructors: string;
};

export type ChangeClassroomRoundParams = {
  changeRoundBy: string;
  fullName: string;
  classroomName: string;
  courseName: string;
  courseImage: string;
  courseUrl: string;
  roundDateText: string;
  classroomType: string;
  classroomLocation: string;
  instructors: string;
};

export type RemindEnrollmentExpiredParams = {
  fullName: string;
  contentName: string;
  courseImage: string;
  courseURL: string;
  isExpired: boolean;
  expiredDateTime: string;
  description: string;
};

export type CancelCourseApprovalParams = {
  fullName: string;
  courseName: string;
  certificateCode: string;
  isExpired: boolean;
  expiredDate: string;
  courseImageURL: string;
  courseURL: string;
  isReEnroll: boolean;
};

export type CancelCoursePassResultParams = {
  fullName: string;
  courseName: string;
  certificateCode: string;
  isExpired: boolean;
  expiredDate: string;
  courseImageURL: string;
  courseURL: string;
  isRound: boolean;
  isReEnroll: boolean;
};

export type CancelLearningPathCertificateParams = {
  fullName: string;
  certificateCode: string;
  learningPathName: string;
};

export type AssignEnrollmentCompletedParams = {
  title: string;
  fullName: string;
  descriptionLine1: string;
  descriptionLine2: string;
  buttonText: boolean;
  buttonWidth: string;
  buttonUrl: string;
};

export type ResetLearningProgressParams = {
  fullName: string;
  courseName: string;
  courseImage: string;
  courseURL: string;
  expiredAt: string;
  isEnableCriteria: boolean;
  criteriaList?: Nullable<string[]>;
};

export type TransformedAchievementEmailDataParams = {
  name: string;
  badges: {
    name: string;
  }[];
  certificate?: string;
};

export type AchievementCompletedParams = {
  fullName: string;
  achievementName: string;
  courseName: string;
  url: string;
  achievementList: TransformedAchievementEmailDataParams[];
  isTest?: boolean;
};

export type AchievementAdditionalCompletedParams = Omit<AchievementCompletedParams, 'isTest'>;

export type BadgeAdditionalCompletedParams = Omit<AchievementAdditionalCompletedParams, 'achievementName'> & {
  badgeName: string;
};

export type AssignPlanPackageLicenseParams = {
  fullName: string;
  planPackageLicenseTextList: object[];
  url: string;
};

export type PlanPackageLicenseExpiredParams = {
  fullName: string;
  planPackageLicenseTextList: object[];
  url: string;
};
